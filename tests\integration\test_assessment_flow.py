"""
项目评估流程集成测试

该模块包含项目评估流程的集成测试。
"""

import unittest
import os
import sys
import json
import logging
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from assessment.assessment_agent import AssessmentAgent
from discovery.models.project import Project, ProjectStatus, TokenInfo, SocialChannel, TeamMember, Requirement
from discovery.models.enums import ProjectType, BlockchainPlatform, SocialChannelType, RequirementDifficulty


class TestAssessmentFlow(unittest.TestCase):
    """项目评估流程集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类前的准备工作"""
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 加载配置
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'assessment_config.json')
        
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                cls.config = json.load(f).get('assessment', {})
        else:
            # 使用默认配置
            cls.config = {
                'assessment_interval': 300,
                'max_projects_per_batch': 10,
                'min_confidence_score': 0.6,
                'verification': {
                    'team': {},
                    'social': {},
                    'project': {}
                },
                'security': {
                    'contract': {},
                    'vulnerability': {},
                    'permission': {}
                },
                'risk': {
                    'risk': {},
                    'reward': {},
                    'score': {}
                }
            }
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试项目
        self.low_risk_project = self._create_low_risk_project()
        self.medium_risk_project = self._create_medium_risk_project()
        self.high_risk_project = self._create_high_risk_project()
        
        # 创建模拟验证器和分析器
        self._setup_mocks()
    
    def _create_low_risk_project(self):
        """创建低风险测试项目"""
        # 创建团队成员
        team_members = [
            TeamMember(
                name="John Doe",
                role="CEO & Founder",
                bio="Blockchain enthusiast with 5 years of experience in DeFi projects.",
                links=["https://linkedin.com/in/johndoe", "https://github.com/johndoe"]
            ),
            TeamMember(
                name="Jane Smith",
                role="CTO",
                bio="Software engineer with expertise in smart contract development.",
                links=["https://linkedin.com/in/janesmith", "https://github.com/janesmith"]
            ),
            TeamMember(
                name="Mike Johnson",
                role="CMO",
                bio="Marketing specialist with experience in crypto projects.",
                links=["https://linkedin.com/in/mikejohnson"]
            )
        ]
        
        # 创建社交媒体渠道
        social_channels = [
            SocialChannel(
                channel_type=SocialChannelType.TWITTER,
                url="https://twitter.com/lowriskproject",
                followers=10000
            ),
            SocialChannel(
                channel_type=SocialChannelType.TELEGRAM,
                url="https://t.me/lowriskproject",
                followers=5000
            ),
            SocialChannel(
                channel_type=SocialChannelType.DISCORD,
                url="https://discord.gg/lowriskproject",
                followers=3000
            ),
            SocialChannel(
                channel_type=SocialChannelType.MEDIUM,
                url="https://medium.com/@lowriskproject",
                followers=2000
            )
        ]
        
        # 创建参与要求
        requirements = [
            Requirement(
                description="Complete testnet tasks",
                difficulty=RequirementDifficulty.MEDIUM,
                estimated_time=60
            ),
            Requirement(
                description="Join Discord and Telegram groups",
                difficulty=RequirementDifficulty.EASY,
                estimated_time=10
            ),
            Requirement(
                description="Follow Twitter and retweet announcement",
                difficulty=RequirementDifficulty.EASY,
                estimated_time=5
            )
        ]
        
        # 创建代币信息
        token_info = TokenInfo(
            name="Low Risk Token",
            symbol="LRT",
            blockchain=BlockchainPlatform.ETHEREUM,
            contract_address="******************************************",
            total_supply=1000000000,
            airdrop_amount=1000000,
            estimated_value=5.0
        )
        
        # 创建项目
        project = Project(
            id="low-risk-project-001",
            name="Low Risk Project",
            description="""
            Low Risk Project is a decentralized finance platform that enables users to trade, stake, and earn rewards.
            
            Our mission is to provide a secure and user-friendly DeFi experience for everyone.
            
            ## Features
            - Decentralized exchange
            - Yield farming
            - NFT marketplace
            - Cross-chain bridge
            
            ## Team
            Our team consists of experienced blockchain developers and finance professionals.
            
            John Doe - CEO & Founder
            Jane Smith - CTO
            Mike Johnson - CMO
            
            ## Roadmap
            Q1 2023: Testnet launch
            Q2 2023: Mainnet launch
            Q3 2023: Mobile app release
            Q4 2023: Cross-chain integration
            
            ## Tokenomics
            Total supply: 1,000,000,000 LRT
            Airdrop allocation: 10%
            Team allocation: 20%
            Community rewards: 30%
            Liquidity: 40%
            """,
            url="https://lowriskproject.io",
            project_type=ProjectType.AIRDROP,
            blockchain=BlockchainPlatform.ETHEREUM,
            status=ProjectStatus.NEW,
            team=team_members,
            social_channels=social_channels,
            requirements=requirements,
            token_info=token_info,
            tags=["defi", "airdrop", "ethereum"],
            notes="Promising project with experienced team"
        )
        
        return project
    
    def _create_medium_risk_project(self):
        """创建中风险测试项目"""
        # 创建团队成员
        team_members = [
            TeamMember(
                name="John Smith",
                role="Founder",
                bio="Crypto enthusiast with 2 years of experience.",
                links=["https://linkedin.com/in/johnsmith"]
            ),
            TeamMember(
                name="Alice Brown",
                role="Developer",
                bio="Smart contract developer.",
                links=["https://github.com/alicebrown"]
            )
        ]
        
        # 创建社交媒体渠道
        social_channels = [
            SocialChannel(
                channel_type=SocialChannelType.TWITTER,
                url="https://twitter.com/mediumriskproject",
                followers=2000
            ),
            SocialChannel(
                channel_type=SocialChannelType.TELEGRAM,
                url="https://t.me/mediumriskproject",
                followers=1000
            )
        ]
        
        # 创建参与要求
        requirements = [
            Requirement(
                description="Complete testnet tasks",
                difficulty=RequirementDifficulty.HARD,
                estimated_time=120
            ),
            Requirement(
                description="Join Telegram group",
                difficulty=RequirementDifficulty.EASY,
                estimated_time=5
            )
        ]
        
        # 创建代币信息
        token_info = TokenInfo(
            name="Medium Risk Token",
            symbol="MRT",
            blockchain=BlockchainPlatform.BSC,
            contract_address="0x2345678901234567890123456789012345678901",
            total_supply=100000000,
            airdrop_amount=10000000,
            estimated_value=0.5
        )
        
        # 创建项目
        project = Project(
            id="medium-risk-project-001",
            name="Medium Risk Project",
            description="""
            Medium Risk Project is a new DeFi platform on Binance Smart Chain.
            
            ## Features
            - Staking
            - Farming
            - Lottery
            
            ## Team
            John Smith - Founder
            Alice Brown - Developer
            
            ## Roadmap
            Q1 2023: Beta launch
            Q2 2023: Full launch
            
            ## Tokenomics
            Total supply: 100,000,000 MRT
            Airdrop: 10%
            Team: 30%
            Liquidity: 60%
            """,
            url="https://mediumriskproject.xyz",
            project_type=ProjectType.AIRDROP,
            blockchain=BlockchainPlatform.BSC,
            status=ProjectStatus.NEW,
            team=team_members,
            social_channels=social_channels,
            requirements=requirements,
            token_info=token_info,
            tags=["defi", "airdrop", "bsc"],
            notes="New project with small team"
        )
        
        return project
    
    def _create_high_risk_project(self):
        """创建高风险测试项目"""
        # 创建团队成员
        team_members = [
            TeamMember(
                name="Anonymous",
                role="Developer",
                bio="Anonymous developer.",
                links=[]
            )
        ]
        
        # 创建社交媒体渠道
        social_channels = [
            SocialChannel(
                channel_type=SocialChannelType.TELEGRAM,
                url="https://t.me/highriskproject",
                followers=500
            )
        ]
        
        # 创建参与要求
        requirements = [
            Requirement(
                description="Join Telegram group and invite 10 friends",
                difficulty=RequirementDifficulty.MEDIUM,
                estimated_time=30
            ),
            Requirement(
                description="Follow Twitter and retweet announcement",
                difficulty=RequirementDifficulty.EASY,
                estimated_time=5
            )
        ]
        
        # 创建代币信息
        token_info = TokenInfo(
            name="High Risk Token",
            symbol="HRT",
            blockchain=BlockchainPlatform.BSC,
            contract_address="0x3456789012345678901234567890123456789012",
            total_supply=1000000000000,
            airdrop_amount=100000000000,
            estimated_value=0.0001
        )
        
        # 创建项目
        project = Project(
            id="high-risk-project-001",
            name="High Risk Project",
            description="""
            High Risk Project - 1000x potential! Get rich quick!
            
            Join our Telegram group and invite friends to earn free tokens!
            
            Limited time offer! Don't miss out!
            
            ## Tokenomics
            Total supply: 1,000,000,000,000 HRT
            Airdrop: 10%
            Team: 50%
            Liquidity: 40%
            """,
            url="https://highriskproject.tk",
            project_type=ProjectType.AIRDROP,
            blockchain=BlockchainPlatform.BSC,
            status=ProjectStatus.NEW,
            team=team_members,
            social_channels=social_channels,
            requirements=requirements,
            token_info=token_info,
            tags=["airdrop", "bsc", "new"],
            notes="Anonymous team, suspicious marketing"
        )
        
        return project
    
    def _setup_mocks(self):
        """设置模拟对象"""
        # 创建模拟验证器和分析器
        self.mock_team_verifier = MagicMock()
        self.mock_social_verifier = MagicMock()
        self.mock_project_verifier = MagicMock()
        self.mock_contract_analyzer = MagicMock()
        self.mock_vulnerability_scanner = MagicMock()
        self.mock_permission_analyzer = MagicMock()
        
        # 设置低风险项目的模拟返回值
        self.mock_team_verifier.verify.side_effect = lambda project: {
            'verified': True,
            'confidence': 0.9,
            'team_size': len(project.team),
            'known_members': len(project.team) if project.id == 'low-risk-project-001' else (1 if project.id == 'medium-risk-project-001' else 0),
            'has_technical_members': True,
            'has_business_members': project.id == 'low-risk-project-001',
            'has_known_scammers': project.id == 'high-risk-project-001',
            'average_experience': 4.0 if project.id == 'low-risk-project-001' else (2.0 if project.id == 'medium-risk-project-001' else 0.0)
        }
        
        self.mock_social_verifier.verify.side_effect = lambda project: {
            'verified': project.id != 'high-risk-project-001',
            'confidence': 0.9 if project.id == 'low-risk-project-001' else (0.6 if project.id == 'medium-risk-project-001' else 0.2),
            'channel_count': len(project.social_channels),
            'verified_channels': len(project.social_channels) if project.id == 'low-risk-project-001' else (1 if project.id == 'medium-risk-project-001' else 0),
            'total_followers': sum(channel.followers for channel in project.social_channels),
            'has_suspicious_content': project.id == 'high-risk-project-001',
            'activity_level': 'high' if project.id == 'low-risk-project-001' else ('medium' if project.id == 'medium-risk-project-001' else 'low')
        }
        
        self.mock_project_verifier.verify.side_effect = lambda project: {
            'verified': project.id != 'high-risk-project-001',
            'confidence': 0.9 if project.id == 'low-risk-project-001' else (0.6 if project.id == 'medium-risk-project-001' else 0.2),
            'website_available': project.id != 'high-risk-project-001',
            'website_age_days': 180 if project.id == 'low-risk-project-001' else (30 if project.id == 'medium-risk-project-001' else 5),
            'has_required_pages': project.id == 'low-risk-project-001',
            'content_quality': 'high' if project.id == 'low-risk-project-001' else ('medium' if project.id == 'medium-risk-project-001' else 'poor'),
            'has_suspicious_content': project.id == 'high-risk-project-001'
        }
        
        self.mock_contract_analyzer.analyze.side_effect = lambda project: {
            'analyzed': True,
            'contract_verified': project.id != 'high-risk-project-001',
            'contract_name': project.token_info.name if project.token_info else '',
            'compiler_version': '0.8.0' if project.id == 'low-risk-project-001' else ('0.7.0' if project.id == 'medium-risk-project-001' else '0.5.0'),
            'risk_level': 'low' if project.id == 'low-risk-project-001' else ('medium' if project.id == 'medium-risk-project-001' else 'high'),
            'high_risk_functions': [] if project.id == 'low-risk-project-001' else ([] if project.id == 'medium-risk-project-001' else [{'name': 'selfdestruct', 'pattern': 'selfdestruct'}]),
            'medium_risk_functions': [] if project.id == 'low-risk-project-001' else ([{'name': 'transfer', 'pattern': 'transfer'}] if project.id == 'medium-risk-project-001' else [{'name': 'transfer', 'pattern': 'transfer'}, {'name': 'call', 'pattern': 'call'}])
        }
        
        self.mock_vulnerability_scanner.scan.side_effect = lambda project: {
            'scanned': True,
            'critical_vulnerabilities': 0 if project.id == 'low-risk-project-001' else (0 if project.id == 'medium-risk-project-001' else 2),
            'high_vulnerabilities': 0 if project.id == 'low-risk-project-001' else (1 if project.id == 'medium-risk-project-001' else 3),
            'medium_vulnerabilities': 1 if project.id == 'low-risk-project-001' else (2 if project.id == 'medium-risk-project-001' else 5),
            'low_vulnerabilities': 2 if project.id == 'low-risk-project-001' else (3 if project.id == 'medium-risk-project-001' else 10)
        }
        
        self.mock_permission_analyzer.analyze.side_effect = lambda project: {
            'analyzed': True,
            'centralization_level': 'low' if project.id == 'low-risk-project-001' else ('medium' if project.id == 'medium-risk-project-001' else 'high'),
            'owner_privileges': ['mint', 'burn'] if project.id == 'low-risk-project-001' else (['mint', 'burn', 'pause'] if project.id == 'medium-risk-project-001' else ['mint', 'burn', 'pause', 'upgrade', 'transfer_ownership']),
            'has_proxy': False if project.id == 'low-risk-project-001' else (False if project.id == 'medium-risk-project-001' else True),
            'has_pausable': True if project.id == 'low-risk-project-001' else (True if project.id == 'medium-risk-project-001' else True)
        }
    
    @patch('assessment.assessment_agent.TeamVerifier')
    @patch('assessment.assessment_agent.SocialVerifier')
    @patch('assessment.assessment_agent.ProjectVerifier')
    @patch('assessment.assessment_agent.ContractAnalyzer')
    @patch('assessment.assessment_agent.VulnerabilityScanner')
    @patch('assessment.assessment_agent.PermissionAnalyzer')
    def test_assess_low_risk_project(self, mock_perm_ana, mock_vuln_scan, mock_contract_ana, 
                                    mock_proj_ver, mock_social_ver, mock_team_ver):
        """测试评估低风险项目"""
        # 设置模拟对象
        mock_team_ver.return_value = self.mock_team_verifier
        mock_social_ver.return_value = self.mock_social_verifier
        mock_proj_ver.return_value = self.mock_project_verifier
        mock_contract_ana.return_value = self.mock_contract_analyzer
        mock_vuln_scan.return_value = self.mock_vulnerability_scanner
        mock_perm_ana.return_value = self.mock_permission_analyzer
        
        # 创建评估智能体
        agent = AssessmentAgent(self.config)
        
        # 评估低风险项目
        assessed_project = agent.assess_project(self.low_risk_project)
        
        # 验证评估结果
        self.assertIsNotNone(assessed_project.risk_score)
        self.assertIsNotNone(assessed_project.potential_reward)
        self.assertIsNotNone(assessed_project.community_score)
        self.assertIsNotNone(assessed_project.team_score)
        
        # 低风险项目应该有较低的风险分数
        self.assertLessEqual(assessed_project.risk_score, 40.0)
        
        # 验证组件调用
        self.mock_team_verifier.verify.assert_called_with(self.low_risk_project)
        self.mock_social_verifier.verify.assert_called_with(self.low_risk_project)
        self.mock_project_verifier.verify.assert_called_with(self.low_risk_project)
        self.mock_contract_analyzer.analyze.assert_called_with(self.low_risk_project)
        self.mock_vulnerability_scanner.scan.assert_called_with(self.low_risk_project)
        self.mock_permission_analyzer.analyze.assert_called_with(self.low_risk_project)
    
    @patch('assessment.assessment_agent.TeamVerifier')
    @patch('assessment.assessment_agent.SocialVerifier')
    @patch('assessment.assessment_agent.ProjectVerifier')
    @patch('assessment.assessment_agent.ContractAnalyzer')
    @patch('assessment.assessment_agent.VulnerabilityScanner')
    @patch('assessment.assessment_agent.PermissionAnalyzer')
    def test_assess_medium_risk_project(self, mock_perm_ana, mock_vuln_scan, mock_contract_ana, 
                                       mock_proj_ver, mock_social_ver, mock_team_ver):
        """测试评估中风险项目"""
        # 设置模拟对象
        mock_team_ver.return_value = self.mock_team_verifier
        mock_social_ver.return_value = self.mock_social_verifier
        mock_proj_ver.return_value = self.mock_project_verifier
        mock_contract_ana.return_value = self.mock_contract_analyzer
        mock_vuln_scan.return_value = self.mock_vulnerability_scanner
        mock_perm_ana.return_value = self.mock_permission_analyzer
        
        # 创建评估智能体
        agent = AssessmentAgent(self.config)
        
        # 评估中风险项目
        assessed_project = agent.assess_project(self.medium_risk_project)
        
        # 验证评估结果
        self.assertIsNotNone(assessed_project.risk_score)
        self.assertIsNotNone(assessed_project.potential_reward)
        self.assertIsNotNone(assessed_project.community_score)
        self.assertIsNotNone(assessed_project.team_score)
        
        # 中风险项目应该有中等的风险分数
        self.assertGreaterEqual(assessed_project.risk_score, 40.0)
        self.assertLessEqual(assessed_project.risk_score, 70.0)
        
        # 验证组件调用
        self.mock_team_verifier.verify.assert_called_with(self.medium_risk_project)
        self.mock_social_verifier.verify.assert_called_with(self.medium_risk_project)
        self.mock_project_verifier.verify.assert_called_with(self.medium_risk_project)
        self.mock_contract_analyzer.analyze.assert_called_with(self.medium_risk_project)
        self.mock_vulnerability_scanner.scan.assert_called_with(self.medium_risk_project)
        self.mock_permission_analyzer.analyze.assert_called_with(self.medium_risk_project)
    
    @patch('assessment.assessment_agent.TeamVerifier')
    @patch('assessment.assessment_agent.SocialVerifier')
    @patch('assessment.assessment_agent.ProjectVerifier')
    @patch('assessment.assessment_agent.ContractAnalyzer')
    @patch('assessment.assessment_agent.VulnerabilityScanner')
    @patch('assessment.assessment_agent.PermissionAnalyzer')
    def test_assess_high_risk_project(self, mock_perm_ana, mock_vuln_scan, mock_contract_ana, 
                                     mock_proj_ver, mock_social_ver, mock_team_ver):
        """测试评估高风险项目"""
        # 设置模拟对象
        mock_team_ver.return_value = self.mock_team_verifier
        mock_social_ver.return_value = self.mock_social_verifier
        mock_proj_ver.return_value = self.mock_project_verifier
        mock_contract_ana.return_value = self.mock_contract_analyzer
        mock_vuln_scan.return_value = self.mock_vulnerability_scanner
        mock_perm_ana.return_value = self.mock_permission_analyzer
        
        # 创建评估智能体
        agent = AssessmentAgent(self.config)
        
        # 评估高风险项目
        assessed_project = agent.assess_project(self.high_risk_project)
        
        # 验证评估结果
        self.assertIsNotNone(assessed_project.risk_score)
        self.assertIsNotNone(assessed_project.potential_reward)
        self.assertIsNotNone(assessed_project.community_score)
        self.assertIsNotNone(assessed_project.team_score)
        
        # 高风险项目应该有较高的风险分数
        self.assertGreaterEqual(assessed_project.risk_score, 70.0)
        
        # 验证组件调用
        self.mock_team_verifier.verify.assert_called_with(self.high_risk_project)
        self.mock_social_verifier.verify.assert_called_with(self.high_risk_project)
        self.mock_project_verifier.verify.assert_called_with(self.high_risk_project)
        self.mock_contract_analyzer.analyze.assert_called_with(self.high_risk_project)
        self.mock_vulnerability_scanner.scan.assert_called_with(self.high_risk_project)
        self.mock_permission_analyzer.analyze.assert_called_with(self.high_risk_project)
    
    @patch('assessment.assessment_agent.TeamVerifier')
    @patch('assessment.assessment_agent.SocialVerifier')
    @patch('assessment.assessment_agent.ProjectVerifier')
    @patch('assessment.assessment_agent.ContractAnalyzer')
    @patch('assessment.assessment_agent.VulnerabilityScanner')
    @patch('assessment.assessment_agent.PermissionAnalyzer')
    def test_assess_projects_batch(self, mock_perm_ana, mock_vuln_scan, mock_contract_ana, 
                                  mock_proj_ver, mock_social_ver, mock_team_ver):
        """测试批量评估项目"""
        # 设置模拟对象
        mock_team_ver.return_value = self.mock_team_verifier
        mock_social_ver.return_value = self.mock_social_verifier
        mock_proj_ver.return_value = self.mock_project_verifier
        mock_contract_ana.return_value = self.mock_contract_analyzer
        mock_vuln_scan.return_value = self.mock_vulnerability_scanner
        mock_perm_ana.return_value = self.mock_permission_analyzer
        
        # 创建评估智能体
        agent = AssessmentAgent(self.config)
        
        # 批量评估项目
        projects = [self.low_risk_project, self.medium_risk_project, self.high_risk_project]
        assessed_projects = agent.assess_projects(projects)
        
        # 验证评估结果
        self.assertEqual(len(assessed_projects), 3)
        
        # 验证每个项目的评估结果
        for project in assessed_projects:
            self.assertIsNotNone(project.risk_score)
            self.assertIsNotNone(project.potential_reward)
            self.assertIsNotNone(project.community_score)
            self.assertIsNotNone(project.team_score)
        
        # 验证组件调用次数
        self.assertEqual(self.mock_team_verifier.verify.call_count, 3)
        self.assertEqual(self.mock_social_verifier.verify.call_count, 3)
        self.assertEqual(self.mock_project_verifier.verify.call_count, 3)
        self.assertEqual(self.mock_contract_analyzer.analyze.call_count, 3)
        self.assertEqual(self.mock_vulnerability_scanner.scan.call_count, 3)
        self.assertEqual(self.mock_permission_analyzer.analyze.call_count, 3)


if __name__ == '__main__':
    unittest.main()