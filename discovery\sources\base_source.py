"""
基础项目源

该模块定义了项目源的基类。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any

class BaseProjectSource(ABC):
    """项目源基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目源
        
        Args:
            config: 配置字典，包含项目源的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        pass