"""
代理工具函数

该模块包含代理智能体使用的工具函数。
"""

import logging
import socket
import struct
import requests
from typing import Dict, Any, Optional, Tuple

from proxy.models.proxy import Proxy


def is_valid_ip(ip: str) -> bool:
    """
    检查 IP 地址是否有效
    
    Args:
        ip: IP 地址
        
    Returns:
        如果 IP 地址有效则返回 True，否则返回 False
    """
    try:
        socket.inet_aton(ip)
        return True
    except socket.error:
        return False


def is_valid_port(port: int) -> bool:
    """
    检查端口是否有效
    
    Args:
        port: 端口号
        
    Returns:
        如果端口有效则返回 True，否则返回 False
    """
    return 0 < port < 65536


def get_ip_info(ip: str) -> Optional[Dict[str, Any]]:
    """
    获取 IP 地址信息
    
    Args:
        ip: IP 地址
        
    Returns:
        IP 地址信息字典，如果获取失败则返回 None
    """
    try:
        response = requests.get(f"http://ip-api.com/json/{ip}", timeout=5)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logging.error(f"获取 IP 信息时出错: {str(e)}")
        return None


def format_proxy_url(proxy: Proxy) -> str:
    """
    格式化代理 URL
    
    Args:
        proxy: 代理对象
        
    Returns:
        格式化的代理 URL
    """
    auth = f"{proxy.username}:{proxy.password}@" if proxy.username and proxy.password else ""
    return f"{proxy.protocol}://{auth}{proxy.host}:{proxy.port}"


def parse_proxy_url(url: str) -> Optional[Proxy]:
    """
    解析代理 URL
    
    Args:
        url: 代理 URL
        
    Returns:
        代理对象，如果解析失败则返回 None
    """
    try:
        # 解析协议
        protocol, rest = url.split('://', 1)
        
        # 解析认证信息
        if '@' in rest:
            auth, hostport = rest.split('@', 1)
            username, password = auth.split(':', 1)
        else:
            hostport = rest
            username = None
            password = None
        
        # 解析主机和端口
        host, port_str = hostport.split(':', 1)
        port = int(port_str)
        
        # 创建代理对象
        return Proxy(
            host=host,
            port=port,
            protocol=protocol,
            username=username,
            password=password
        )
    
    except Exception as e:
        logging.error(f"解析代理 URL 时出错: {str(e)}")
        return None


def ip_to_int(ip: str) -> int:
    """
    将 IP 地址转换为整数
    
    Args:
        ip: IP 地址
        
    Returns:
        IP 地址的整数表示
    """
    return struct.unpack('!I', socket.inet_aton(ip))[0]


def int_to_ip(ip_int: int) -> str:
    """
    将整数转换为 IP 地址
    
    Args:
        ip_int: IP 地址的整数表示
        
    Returns:
        IP 地址
    """
    return socket.inet_ntoa(struct.pack('!I', ip_int))


def calculate_proxy_score(proxy: Proxy) -> float:
    """
    计算代理得分
    
    Args:
        proxy: 代理对象
        
    Returns:
        代理得分
    """
    # 基础分数
    score = 0.0
    
    # 成功率
    success_rate = proxy.success_rate
    score += success_rate * 50.0
    
    # 响应时间
    avg_response_time = proxy.average_response_time
    if avg_response_time:
        # 响应时间越短，分数越高
        response_score = max(0, 30.0 - avg_response_time * 10.0)
        score += response_score
    
    # 匿名级别
    if proxy.anonymity == 'elite':
        score += 20.0
    elif proxy.anonymity == 'anonymous':
        score += 10.0
    
    # 协议
    if proxy.protocol == 'https':
        score += 5.0
    elif proxy.protocol == 'socks5':
        score += 3.0
    
    return score


def group_proxies_by_country(proxies: list[Proxy]) -> Dict[str, list[Proxy]]:
    """
    按国家分组代理
    
    Args:
        proxies: 代理列表
        
    Returns:
        按国家分组的代理字典
    """
    result = {}
    
    for proxy in proxies:
        country = proxy.country or 'Unknown'
        if country not in result:
            result[country] = []
        
        result[country].append(proxy)
    
    return result


def group_proxies_by_protocol(proxies: list[Proxy]) -> Dict[str, list[Proxy]]:
    """
    按协议分组代理
    
    Args:
        proxies: 代理列表
        
    Returns:
        按协议分组的代理字典
    """
    result = {}
    
    for proxy in proxies:
        protocol = proxy.protocol
        if protocol not in result:
            result[protocol] = []
        
        result[protocol].append(proxy)
    
    return result