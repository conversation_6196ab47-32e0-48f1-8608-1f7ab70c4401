{"system": {"name": "AirHunter", "version": "1.0.0", "environment": "development", "log_level": "INFO"}, "coordinator": {"enabled": true, "max_agents": 10, "heartbeat_interval": 30, "resources": {"max_memory": 1024, "max_cpu": 80}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}}, "discovery_agent": {"discovery_interval": 3600, "max_projects_per_source": 10, "storage_file": "data/projects.json", "storage_enabled": true, "sources": {"enabled_sources": ["twitter", "discord", "telegram", "medium", "github", "blockchain"], "twitter": {"weight": 1.0, "api_key": "", "api_secret": "", "access_token": "", "access_token_secret": "", "bearer_token": "", "use_api": false, "timeout": 10, "search_terms": ["airdrop", "crypto airdrop", "token airdrop", "free tokens", "blockchain airdrop", "defi airdrop", "nft airdrop"], "accounts_to_follow": ["AirdropAlert", "airdropinspect", "DappRadar", "Airdrop_Finder", "CoinMarketCap", "coingecko", "binance", "cryptocom"]}, "discord": {"weight": 0.8, "token": "", "use_api": false, "servers": [], "channels": [], "search_terms": ["airdrop", "crypto airdrop", "token airdrop", "free tokens", "blockchain airdrop", "defi airdrop", "nft airdrop"]}, "telegram": {"weight": 0.8, "api_id": 0, "api_hash": "", "phone": "", "use_api": false, "channels": [], "search_terms": ["airdrop", "crypto airdrop", "token airdrop", "free tokens", "blockchain airdrop", "defi airdrop", "nft airdrop"]}, "medium": {"weight": 0.6, "timeout": 10, "search_terms": ["airdrop", "crypto airdrop", "token airdrop", "free tokens", "blockchain airdrop", "defi airdrop", "nft airdrop"], "publications": ["coinmonks", "coinsbench", "coinbyte", "cryptolinks", "levelup-gitconnected"]}, "github": {"weight": 0.5, "api_token": "", "use_api": true, "timeout": 10, "search_terms": ["airdrop", "crypto airdrop", "token airdrop", "blockchain airdrop", "defi airdrop", "nft airdrop"], "organizations": ["ethereum", "solana-labs", "bnb-chain", "maticnetwork", "avalancheavax", "arbitrum", "ethereum-optimism", "base-org"]}, "blockchain": {"weight": 0.7, "timeout": 10, "platforms": ["ethereum", "binance", "solana", "polygon", "avalanche", "arbitrum", "optimism", "base"], "etherscan_api_key": "", "bscscan_api_key": "", "polygonscan_api_key": ""}}, "filters": {"keyword_filter_enabled": true, "relevance_filter_enabled": true, "duplicate_filter_enabled": true, "keyword_filter": {"required_keywords": ["airdrop", "token", "crypto", "blockchain", "defi", "nft"], "excluded_keywords": ["scam", "fake", "phishing", "spam", "virus", "malware"], "min_required_matches": 1, "case_sensitive": false}, "relevance_filter": {"relevance_keywords": {"airdrop": 10, "token": 8, "crypto": 7, "blockchain": 7, "defi": 6, "nft": 6, "free": 5, "claim": 5, "reward": 5, "earn": 4, "testnet": 4, "launch": 3, "project": 3, "community": 3, "ecosystem": 2, "protocol": 2, "platform": 2, "decentralized": 2, "finance": 1, "application": 1}, "min_relevance_score": 10, "case_sensitive": false, "use_regex": false}, "duplicate_filter": {"url_filter_enabled": true, "content_filter_enabled": true, "content_similarity_threshold": 0.8}}, "collectors": {"web_collector_enabled": true, "social_collector_enabled": true, "blockchain_collector_enabled": true, "web_collector": {"timeout": 10, "max_retries": 3, "retry_delay": 1.0, "extract_social_links": true, "extract_requirements": true, "extract_token_info": true}, "social_collector": {"timeout": 10, "max_retries": 3, "retry_delay": 1.0, "twitter_api_key": "", "twitter_api_secret": "", "twitter_bearer_token": "", "use_twitter_api": false, "telegram_api_id": 0, "telegram_api_hash": "", "use_telegram_api": false, "discord_token": "", "use_discord_api": false}, "blockchain_collector": {"timeout": 10, "max_retries": 3, "retry_delay": 1.0, "etherscan_api_key": "", "bscscan_api_key": "", "polygonscan_api_key": ""}}}, "assessment_agent": {"enabled": true, "analysis_timeout": 300, "risk_thresholds": {"low": 30, "medium": 60, "high": 80}}, "monitoring_agent": {"enabled": true, "check_interval": 300, "alert_thresholds": {"response_time": 5000, "error_rate": 0.05}}, "fund_management_agent": {"enabled": true, "max_wallets": 100, "security_level": "high"}, "task_planning_agent": {"enabled": true, "max_concurrent_tasks": 50, "planning_algorithm": "priority_based"}, "task_execution_agent": {"enabled": true, "max_workers": 10, "execution_timeout": 600}, "proxy_agent": {"enabled": true, "rotation_interval": 300, "max_proxies": 100}, "anti_sybil_agent": {"enabled": true, "detection_sensitivity": "medium", "analysis_depth": "standard"}, "profit_optimization_agent": {"enabled": true, "optimization_interval": 3600, "risk_tolerance": "medium"}}