"""
Emergency Handler

紧急情况处理器，负责处理系统紧急情况和安全事件。
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from enum import Enum


class EmergencyType(Enum):
    """紧急情况类型"""
    SECURITY_BREACH = "security_breach"
    FRAUD_DETECTED = "fraud_detected"
    SYSTEM_FAILURE = "system_failure"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    FUND_LOSS = "fund_loss"
    CRITICAL_ERROR = "critical_error"


class EmergencyHandler:
    """
    紧急情况处理器
    
    负责检测、响应和处理各种紧急安全情况。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化紧急情况处理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 紧急情况记录
        self.emergency_incidents: List[Dict] = []
        
        # 响应处理器
        self.response_handlers: Dict[EmergencyType, List[Callable]] = {}
        
        # 紧急配置
        self.emergency_config = {
            "auto_lockdown_threshold": config.get("auto_lockdown_threshold", 3),
            "notification_channels": config.get("notification_channels", ["email", "sms"]),
            "escalation_timeout_minutes": config.get("escalation_timeout", 15),
            "max_incident_history": config.get("max_history", 1000)
        }
        
        # 系统状态
        self.system_status = {
            "is_locked_down": False,
            "lockdown_reason": None,
            "lockdown_time": None,
            "active_incidents": 0,
            "last_incident_time": None
        }
        
        # 统计信息
        self.emergency_stats = {
            'total_incidents': 0,
            'resolved_incidents': 0,
            'active_incidents': 0,
            'auto_lockdowns': 0,
            'manual_interventions': 0
        }
    
    async def initialize(self) -> bool:
        """
        初始化紧急情况处理器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 注册默认响应处理器
            await self._register_default_handlers()
            
            self.logger.info("Emergency Handler initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Emergency Handler: {e}")
            return False
    
    async def trigger_emergency(self, emergency_type: EmergencyType, 
                              incident_data: Dict[str, Any],
                              severity: str = "medium") -> str:
        """
        触发紧急情况
        
        Args:
            emergency_type: 紧急情况类型
            incident_data: 事件数据
            severity: 严重程度 (low/medium/high/critical)
            
        Returns:
            str: 事件ID
        """
        try:
            incident_id = f"emergency_{len(self.emergency_incidents) + 1}"
            
            incident = {
                "incident_id": incident_id,
                "type": emergency_type.value,
                "severity": severity,
                "data": incident_data,
                "triggered_at": datetime.utcnow().isoformat(),
                "status": "active",
                "response_actions": [],
                "resolved_at": None,
                "resolution_notes": ""
            }
            
            self.emergency_incidents.append(incident)
            self.emergency_stats['total_incidents'] += 1
            self.emergency_stats['active_incidents'] += 1
            self.system_status['active_incidents'] += 1
            self.system_status['last_incident_time'] = datetime.utcnow().isoformat()
            
            # 执行响应处理
            await self._execute_emergency_response(emergency_type, incident)
            
            # 检查是否需要自动锁定
            if severity in ["high", "critical"]:
                await self._check_auto_lockdown(incident)
            
            self.logger.critical(f"Emergency triggered: {emergency_type.value} - {incident_id}")
            return incident_id
            
        except Exception as e:
            self.logger.error(f"Error triggering emergency: {e}")
            return ""
    
    async def resolve_incident(self, incident_id: str, resolution_notes: str = "") -> bool:
        """
        解决事件
        
        Args:
            incident_id: 事件ID
            resolution_notes: 解决说明
            
        Returns:
            bool: 解决是否成功
        """
        try:
            for incident in self.emergency_incidents:
                if incident["incident_id"] == incident_id:
                    if incident["status"] == "active":
                        incident["status"] = "resolved"
                        incident["resolved_at"] = datetime.utcnow().isoformat()
                        incident["resolution_notes"] = resolution_notes
                        
                        self.emergency_stats['resolved_incidents'] += 1
                        self.emergency_stats['active_incidents'] -= 1
                        self.system_status['active_incidents'] -= 1
                        
                        self.logger.info(f"Incident {incident_id} resolved: {resolution_notes}")
                        return True
                    else:
                        self.logger.warning(f"Incident {incident_id} is not active")
                        return False
            
            self.logger.error(f"Incident {incident_id} not found")
            return False
            
        except Exception as e:
            self.logger.error(f"Error resolving incident: {e}")
            return False
    
    async def initiate_lockdown(self, reason: str, duration_minutes: int = None) -> bool:
        """
        启动系统锁定
        
        Args:
            reason: 锁定原因
            duration_minutes: 锁定持续时间（分钟）
            
        Returns:
            bool: 锁定是否成功
        """
        try:
            if self.system_status["is_locked_down"]:
                self.logger.warning("System is already in lockdown")
                return False
            
            self.system_status["is_locked_down"] = True
            self.system_status["lockdown_reason"] = reason
            self.system_status["lockdown_time"] = datetime.utcnow().isoformat()
            
            # 记录锁定动作
            lockdown_action = {
                "action": "system_lockdown",
                "reason": reason,
                "duration_minutes": duration_minutes,
                "initiated_at": datetime.utcnow().isoformat()
            }
            
            # 如果有持续时间，设置自动解锁
            if duration_minutes:
                asyncio.create_task(self._auto_unlock_after_delay(duration_minutes))
            
            # 发送通知
            await self._send_emergency_notification("System Lockdown Initiated", {
                "reason": reason,
                "duration": duration_minutes,
                "time": datetime.utcnow().isoformat()
            })
            
            self.logger.critical(f"System lockdown initiated: {reason}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initiating lockdown: {e}")
            return False
    
    async def lift_lockdown(self, reason: str = "") -> bool:
        """
        解除系统锁定
        
        Args:
            reason: 解除原因
            
        Returns:
            bool: 解除是否成功
        """
        try:
            if not self.system_status["is_locked_down"]:
                self.logger.warning("System is not in lockdown")
                return False
            
            self.system_status["is_locked_down"] = False
            self.system_status["lockdown_reason"] = None
            self.system_status["lockdown_time"] = None
            
            # 发送通知
            await self._send_emergency_notification("System Lockdown Lifted", {
                "reason": reason,
                "time": datetime.utcnow().isoformat()
            })
            
            self.logger.info(f"System lockdown lifted: {reason}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error lifting lockdown: {e}")
            return False
    
    async def _execute_emergency_response(self, emergency_type: EmergencyType, incident: Dict):
        """执行紧急响应"""
        try:
            if emergency_type in self.response_handlers:
                for handler in self.response_handlers[emergency_type]:
                    try:
                        await handler(incident)
                        incident["response_actions"].append({
                            "handler": handler.__name__,
                            "executed_at": datetime.utcnow().isoformat(),
                            "status": "success"
                        })
                    except Exception as e:
                        self.logger.error(f"Error in response handler {handler.__name__}: {e}")
                        incident["response_actions"].append({
                            "handler": handler.__name__,
                            "executed_at": datetime.utcnow().isoformat(),
                            "status": "failed",
                            "error": str(e)
                        })
        except Exception as e:
            self.logger.error(f"Error executing emergency response: {e}")
    
    async def _check_auto_lockdown(self, incident: Dict):
        """检查是否需要自动锁定"""
        try:
            threshold = self.emergency_config["auto_lockdown_threshold"]
            
            # 计算最近的高严重性事件数量
            recent_critical_incidents = 0
            current_time = datetime.utcnow()
            
            for inc in self.emergency_incidents:
                if inc["severity"] in ["high", "critical"]:
                    incident_time = datetime.fromisoformat(inc["triggered_at"])
                    if (current_time - incident_time).seconds <= 3600:  # 1小时内
                        recent_critical_incidents += 1
            
            if recent_critical_incidents >= threshold:
                await self.initiate_lockdown(
                    f"Auto-lockdown triggered: {recent_critical_incidents} critical incidents in 1 hour",
                    duration_minutes=60
                )
                self.emergency_stats['auto_lockdowns'] += 1
                
        except Exception as e:
            self.logger.error(f"Error checking auto lockdown: {e}")
    
    async def _auto_unlock_after_delay(self, delay_minutes: int):
        """延迟后自动解锁"""
        try:
            await asyncio.sleep(delay_minutes * 60)
            if self.system_status["is_locked_down"]:
                await self.lift_lockdown("Auto-unlock after timeout")
        except Exception as e:
            self.logger.error(f"Error in auto unlock: {e}")
    
    async def _send_emergency_notification(self, title: str, data: Dict):
        """发送紧急通知"""
        try:
            # 模拟发送通知
            self.logger.info(f"Emergency notification: {title} - {data}")
        except Exception as e:
            self.logger.error(f"Error sending notification: {e}")
    
    async def _register_default_handlers(self):
        """注册默认响应处理器"""
        try:
            # 注册各种紧急情况的默认处理器
            self.response_handlers[EmergencyType.FRAUD_DETECTED] = [self._handle_fraud_detected]
            self.response_handlers[EmergencyType.SECURITY_BREACH] = [self._handle_security_breach]
            self.response_handlers[EmergencyType.UNAUTHORIZED_ACCESS] = [self._handle_unauthorized_access]
        except Exception as e:
            self.logger.error(f"Error registering default handlers: {e}")
    
    async def _handle_fraud_detected(self, incident: Dict):
        """处理欺诈检测"""
        self.logger.warning(f"Handling fraud detection: {incident['incident_id']}")
    
    async def _handle_security_breach(self, incident: Dict):
        """处理安全漏洞"""
        self.logger.critical(f"Handling security breach: {incident['incident_id']}")
    
    async def _handle_unauthorized_access(self, incident: Dict):
        """处理未授权访问"""
        self.logger.warning(f"Handling unauthorized access: {incident['incident_id']}")
