"""
代理模型

该模块定义了代理的数据模型。
"""

import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field


@dataclass
class Proxy:
    """代理数据模型"""
    
    # 基本信息
    host: str
    port: int
    protocol: str  # http, https, socks4, socks5
    
    # 可选信息
    username: Optional[str] = None
    password: Optional[str] = None
    country: Optional[str] = None
    city: Optional[str] = None
    anonymity: Optional[str] = None  # transparent, anonymous, elite
    source: Optional[str] = None
    
    # 状态信息
    is_valid: bool = True
    last_checked: float = field(default_factory=time.time)
    last_used: Optional[float] = None
    response_times: List[float] = field(default_factory=list)
    success_count: int = 0
    fail_count: int = 0
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保协议是小写的
        self.protocol = self.protocol.lower()
        
        # 确保匿名级别是小写的
        if self.anonymity:
            self.anonymity = self.anonymity.lower()
    
    @property
    def url(self) -> str:
        """
        获取代理URL
        
        Returns:
            代理URL
        """
        auth = f"{self.username}:{self.password}@" if self.username and self.password else ""
        return f"{self.protocol}://{auth}{self.host}:{self.port}"
    
    @property
    def average_response_time(self) -> Optional[float]:
        """
        获取平均响应时间
        
        Returns:
            平均响应时间，如果没有响应时间记录则返回None
        """
        if not self.response_times:
            return None
        
        return sum(self.response_times) / len(self.response_times)
    
    @property
    def success_rate(self) -> float:
        """
        获取成功率
        
        Returns:
            成功率（0-1）
        """
        total = self.success_count + self.fail_count
        if total == 0:
            return 0.0
        
        return self.success_count / total
    
    @property
    def age(self) -> float:
        """
        获取代理年龄（秒）
        
        Returns:
            代理年龄
        """
        return time.time() - self.last_checked
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            代理字典
        """
        return {
            "host": self.host,
            "port": self.port,
            "protocol": self.protocol,
            "username": self.username,
            "password": self.password,
            "country": self.country,
            "city": self.city,
            "anonymity": self.anonymity,
            "source": self.source,
            "is_valid": self.is_valid,
            "last_checked": self.last_checked,
            "last_used": self.last_used,
            "response_times": self.response_times,
            "success_count": self.success_count,
            "fail_count": self.fail_count,
            "url": self.url,
            "average_response_time": self.average_response_time,
            "success_rate": self.success_rate,
            "age": self.age
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Proxy':
        """
        从字典创建代理
        
        Args:
            data: 代理字典
            
        Returns:
            代理对象
        """
        # 提取基本字段
        proxy = cls(
            host=data["host"],
            port=data["port"],
            protocol=data["protocol"],
            username=data.get("username"),
            password=data.get("password"),
            country=data.get("country"),
            city=data.get("city"),
            anonymity=data.get("anonymity"),
            source=data.get("source")
        )
        
        # 设置状态字段
        proxy.is_valid = data.get("is_valid", True)
        proxy.last_checked = data.get("last_checked", time.time())
        proxy.last_used = data.get("last_used")
        proxy.response_times = data.get("response_times", [])
        proxy.success_count = data.get("success_count", 0)
        proxy.fail_count = data.get("fail_count", 0)
        
        return proxy
    
    def __eq__(self, other):
        """
        比较两个代理是否相等
        
        Args:
            other: 另一个代理
            
        Returns:
            如果相等则返回True，否则返回False
        """
        if not isinstance(other, Proxy):
            return False
        
        return (self.host == other.host and
                self.port == other.port and
                self.protocol == other.protocol)
    
    def __hash__(self):
        """
        获取代理的哈希值
        
        Returns:
            哈希值
        """
        return hash((self.host, self.port, self.protocol))
    
    def __str__(self):
        """
        获取代理的字符串表示
        
        Returns:
            字符串表示
        """
        return self.url