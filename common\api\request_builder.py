"""
Request builder for API requests

This module provides utilities for building HTTP requests with proper formatting and validation.
"""

import json
import base64
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlencode, quote

class RequestBuilder:
    """Builder for HTTP requests"""
    
    def __init__(self):
        """Initialize request builder"""
        self._headers = {}
        self._params = {}
        self._data = {}
        self._json = None
        self._auth = None
        self._files = []
        self._timeout = 30
    
    def with_headers(self, headers: Dict[str, str]) -> 'RequestBuilder':
        """
        Add headers to request
        
        Args:
            headers: Headers to add
            
        Returns:
            RequestBuilder: Self for chaining
        """
        self._headers.update(headers)
        return self
    
    def with_auth(self, username: str, password: str) -> 'RequestBuilder':
        """
        Add basic authentication
        
        Args:
            username: Username
            password: Password
            
        Returns:
            RequestBuilder: Self for chaining
        """
        credentials = base64.b64encode(
            f"{username}:{password}".encode()
        ).decode()
        self._headers['Authorization'] = f"Basic {credentials}"
        return self
    
    def with_bearer_token(self, token: str) -> 'RequestBuilder':
        """
        Add bearer token authentication
        
        Args:
            token: Bearer token
            
        Returns:
            RequestBuilder: Self for chaining
        """
        self._headers['Authorization'] = f"Bearer {token}"
        return self
    
    def with_params(self, params: Dict[str, Any]) -> 'RequestBuilder':
        """
        Add query parameters
        
        Args:
            params: Query parameters
            
        Returns:
            RequestBuilder: Self for chaining
        """
        self._params.update(params)
        return self
    
    def with_json(self, data: Any) -> 'RequestBuilder':
        """
        Add JSON body
        
        Args:
            data: Data to send as JSON
            
        Returns:
            RequestBuilder: Self for chaining
        """
        self._json = data
        self._headers['Content-Type'] = 'application/json'
        return self
    
    def with_form_data(self, data: Dict[str, Any]) -> 'RequestBuilder':
        """
        Add form data
        
        Args:
            data: Form data
            
        Returns:
            RequestBuilder: Self for chaining
        """
        self._data.update(data)
        return self
    
    def with_file(
        self,
        field_name: str,
        file_path: str,
        mime_type: Optional[str] = None
    ) -> 'RequestBuilder':
        """
        Add file to request
        
        Args:
            field_name: Form field name
            file_path: Path to file
            mime_type: Optional MIME type
            
        Returns:
            RequestBuilder: Self for chaining
        """
        self._files.append((field_name, file_path, mime_type))
        return self
    
    def with_timeout(self, timeout: int) -> 'RequestBuilder':
        """
        Set request timeout
        
        Args:
            timeout: Timeout in seconds
            
        Returns:
            RequestBuilder: Self for chaining
        """
        self._timeout = timeout
        return self
    
    def build_url(self, base_url: str, endpoint: str) -> str:
        """
        Build full URL with parameters
        
        Args:
            base_url: Base URL
            endpoint: API endpoint
            
        Returns:
            str: Complete URL
        """
        url = f"{base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        if self._params:
            # Ensure proper URL encoding
            encoded_params = urlencode(self._params, doseq=True)
            url = f"{url}?{encoded_params}"
        
        return url
    
    def build_body(self) -> Optional[Union[Dict, str]]:
        """
        Build request body
        
        Returns:
            Optional[Union[Dict, str]]: Request body
        """
        if self._json is not None:
            return json.dumps(self._json)
        
        if self._data:
            return self._data
        
        return None
    
    def build_files(self) -> Optional[List[tuple]]:
        """
        Build files for multipart request
        
        Returns:
            Optional[List[tuple]]: List of file tuples
        """
        if not self._files:
            return None
            
        files = []
        for field_name, file_path, mime_type in self._files:
            files.append(
                (field_name, (file_path, open(file_path, 'rb'), mime_type))
            )
        return files
    
    def build(self) -> Dict[str, Any]:
        """
        Build final request configuration
        
        Returns:
            Dict[str, Any]: Request configuration
        """
        request_config = {
            'headers': self._headers,
            'timeout': self._timeout
        }
        
        if self._params:
            request_config['params'] = self._params
        
        body = self.build_body()
        if body:
            if isinstance(body, str):
                request_config['data'] = body
            else:
                request_config['json'] = body
        
        files = self.build_files()
        if files:
            request_config['files'] = files
        
        return request_config
    
    def reset(self) -> None:
        """Reset builder state"""
        self._headers = {}
        self._params = {}
        self._data = {}
        self._json = None
        self._auth = None
        self._files = []
        self._timeout = 30

# Create global request builder instance
request_builder = RequestBuilder()
