"""
Twitter 项目源

该模块实现了从 Twitter 获取项目信息的功能。
"""

import logging
import requests
import time
import random
import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup

from discovery.sources.base_source import BaseProjectSource


class TwitterSource(BaseProjectSource):
    """Twitter 项目源"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 Twitter 项目源
        
        Args:
            config: 配置字典，包含项目源的配置信息
        """
        super().__init__(config)
        self.api_key = config.get('api_key', '')
        self.api_secret = config.get('api_secret', '')
        self.access_token = config.get('access_token', '')
        self.access_token_secret = config.get('access_token_secret', '')
        self.bearer_token = config.get('bearer_token', '')
        self.use_api = config.get('use_api', False)
        self.timeout = config.get('timeout', 10)
        self.search_terms = config.get('search_terms', [
            'airdrop', 'crypto airdrop', 'token airdrop', 'free tokens',
            'blockchain airdrop', 'defi airdrop', 'nft airdrop'
        ])
        self.accounts_to_follow = config.get('accounts_to_follow', [
            'AirdropAlert', 'airdropinspect', 'DappRadar', 'Airdrop_Finder',
            'CoinMarketCap', 'coingecko', 'binance', 'cryptocom'
        ])
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/******** Firefox/89.0'
        ])
        
        # 初始化 Twitter API 客户端
        if self.use_api and self.bearer_token:
            self._init_api_client()
    
    def _init_api_client(self) -> None:
        """初始化 Twitter API 客户端"""
        try:
            import tweepy
            self.api_client = tweepy.Client(
                bearer_token=self.bearer_token,
                consumer_key=self.api_key,
                consumer_secret=self.api_secret,
                access_token=self.access_token,
                access_token_secret=self.access_token_secret
            )
            self.logger.info("Twitter API 客户端初始化成功")
        except ImportError:
            self.logger.warning("无法导入 tweepy 库，将使用网页抓取方式")
            self.use_api = False
        except Exception as e:
            self.logger.error(f"初始化 Twitter API 客户端时出错: {str(e)}")
            self.use_api = False
    
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        从 Twitter 获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        self.logger.info(f"从 Twitter 获取 {count} 个项目")
        
        if self.use_api:
            return self._get_projects_via_api(count)
        else:
            return self._get_projects_via_scraping(count)
    
    def _get_projects_via_api(self, count: int) -> List[Dict[str, Any]]:
        """
        通过 API 获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            import tweepy
            
            projects = []
            
            # 计算每个搜索词需要获取的推文数量
            terms_count = len(self.search_terms)
            tweets_per_term = max(1, count // terms_count)
            
            # 搜索推文
            for term in self.search_terms:
                try:
                    query = f"{term} -is:retweet -is:reply"
                    tweets = self.api_client.search_recent_tweets(
                        query=query,
                        max_results=min(100, tweets_per_term),
                        tweet_fields=['created_at', 'public_metrics', 'entities', 'author_id'],
                        user_fields=['name', 'username', 'profile_image_url', 'public_metrics'],
                        expansions=['author_id']
                    )
                    
                    if not tweets.data:
                        continue
                    
                    # 创建用户ID到用户对象的映射
                    users = {user.id: user for user in tweets.includes['users']} if 'users' in tweets.includes else {}
                    
                    for tweet in tweets.data:
                        # 获取推文作者
                        author = users.get(tweet.author_id)
                        
                        # 提取URL
                        urls = []
                        if tweet.entities and 'urls' in tweet.entities:
                            for url_obj in tweet.entities['urls']:
                                if 'expanded_url' in url_obj:
                                    urls.append(url_obj['expanded_url'])
                        
                        # 提取项目信息
                        project_info = self._extract_project_info_from_tweet(tweet.text, urls)
                        
                        if project_info:
                            # 添加来源信息
                            project_info['source_url'] = f"https://twitter.com/{author.username}/status/{tweet.id}" if author else f"https://twitter.com/i/status/{tweet.id}"
                            project_info['discovery_source'] = 'twitter'
                            
                            # 添加社交媒体信息
                            if author:
                                project_info['social_channels'] = [
                                    {
                                        'platform': 'twitter',
                                        'url': f"https://twitter.com/{author.username}",
                                        'followers': author.public_metrics.get('followers_count') if hasattr(author, 'public_metrics') else None
                                    }
                                ]
                            
                            projects.append(project_info)
                            
                            # 如果已经获取到足够的项目，就停止
                            if len(projects) >= count:
                                break
                
                except Exception as e:
                    self.logger.error(f"搜索推文 '{term}' 时出错: {str(e)}")
                
                # 如果已经获取到足够的项目，就停止
                if len(projects) >= count:
                    break
            
            self.logger.info(f"从 Twitter API 获取到 {len(projects)} 个项目")
            return projects
        
        except Exception as e:
            self.logger.error(f"通过 API 获取项目时出错: {str(e)}")
            return []
    
    def _get_projects_via_scraping(self, count: int) -> List[Dict[str, Any]]:
        """
        通过网页抓取获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            projects = []
            
            # 计算每个搜索词需要获取的推文数量
            terms_count = len(self.search_terms)
            tweets_per_term = max(1, count // terms_count)
            
            # 搜索推文
            for term in self.search_terms:
                try:
                    # 构建搜索URL
                    search_url = f"https://nitter.net/search?f=tweets&q={term}"
                    
                    # 随机选择一个 User-Agent
                    headers = {
                        'User-Agent': random.choice(self.user_agents)
                    }
                    
                    # 发送请求
                    response = requests.get(search_url, headers=headers, timeout=self.timeout)
                    response.raise_for_status()
                    
                    # 解析 HTML
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 查找推文
                    tweet_elements = soup.select('.timeline-item')
                    
                    for tweet_element in tweet_elements[:tweets_per_term]:
                        try:
                            # 获取推文文本
                            tweet_text_element = tweet_element.select_one('.tweet-content')
                            if not tweet_text_element:
                                continue
                            
                            tweet_text = tweet_text_element.get_text(strip=True)
                            
                            # 获取推文链接
                            tweet_link_element = tweet_element.select_one('.tweet-link')
                            tweet_link = tweet_link_element['href'] if tweet_link_element else None
                            
                            if not tweet_link:
                                continue
                            
                            # 获取推文作者
                            author_element = tweet_element.select_one('.username')
                            author = author_element.get_text(strip=True) if author_element else None
                            
                            # 提取URL
                            urls = []
                            url_elements = tweet_element.select('.tweet-content a')
                            for url_element in url_elements:
                                if 'href' in url_element.attrs:
                                    urls.append(url_element['href'])
                            
                            # 提取项目信息
                            project_info = self._extract_project_info_from_tweet(tweet_text, urls)
                            
                            if project_info:
                                # 添加来源信息
                                project_info['source_url'] = f"https://twitter.com{tweet_link}" if tweet_link.startswith('/') else tweet_link
                                project_info['discovery_source'] = 'twitter'
                                
                                # 添加社交媒体信息
                                if author:
                                    project_info['social_channels'] = [
                                        {
                                            'platform': 'twitter',
                                            'url': f"https://twitter.com/{author}",
                                            'followers': None
                                        }
                                    ]
                                
                                projects.append(project_info)
                                
                                # 如果已经获取到足够的项目，就停止
                                if len(projects) >= count:
                                    break
                        
                        except Exception as e:
                            self.logger.error(f"解析推文时出错: {str(e)}")
                
                except Exception as e:
                    self.logger.error(f"搜索推文 '{term}' 时出错: {str(e)}")
                
                # 如果已经获取到足够的项目，就停止
                if len(projects) >= count:
                    break
            
            self.logger.info(f"从 Twitter 网页抓取获取到 {len(projects)} 个项目")
            return projects
        
        except Exception as e:
            self.logger.error(f"通过网页抓取获取项目时出错: {str(e)}")
            return []
    
    def _extract_project_info_from_tweet(self, tweet_text: str, urls: List[str]) -> Optional[Dict[str, Any]]:
        """
        从推文中提取项目信息
        
        Args:
            tweet_text: 推文文本
            urls: 推文中的URL列表
            
        Returns:
            项目信息字典，如果无法提取则返回None
        """
        # 检查推文是否包含关键词
        keywords = ['airdrop', 'token', 'crypto', 'blockchain', 'defi', 'nft']
        if not any(keyword in tweet_text.lower() for keyword in keywords):
            return None
        
        # 提取项目名称
        name_match = re.search(r'(?i)(?:airdrop|token|project)[\s:]+([A-Za-z0-9\s]+)', tweet_text)
        name = name_match.group(1).strip() if name_match else "Unknown Project"
        
        # 提取项目URL
        project_url = None
        for url in urls:
            # 排除社交媒体链接
            if not any(domain in url.lower() for domain in ['twitter.com', 't.co', 'facebook.com', 'instagram.com', 'linkedin.com']):
                project_url = url
                break
        
        if not project_url and urls:
            project_url = urls[0]
        
        if not project_url:
            return None
        
        # 创建项目信息
        project_info = {
            'name': name,
            'description': tweet_text[:200] + ('...' if len(tweet_text) > 200 else ''),
            'url': project_url,
            'project_type': 'airdrop',
            'blockchain': 'other'
        }
        
        # 尝试识别区块链平台
        blockchain_keywords = {
            'ethereum': ['ethereum', 'eth', 'erc20', 'erc721'],
            'binance': ['binance', 'bsc', 'bnb', 'bep20'],
            'solana': ['solana', 'sol'],
            'polygon': ['polygon', 'matic'],
            'avalanche': ['avalanche', 'avax'],
            'arbitrum': ['arbitrum', 'arb'],
            'optimism': ['optimism', 'op'],
            'base': ['base'],
            'cosmos': ['cosmos', 'atom'],
            'polkadot': ['polkadot', 'dot'],
            'near': ['near'],
            'aptos': ['aptos', 'apt'],
            'sui': ['sui']
        }
        
        for blockchain, keywords in blockchain_keywords.items():
            if any(keyword in tweet_text.lower() for keyword in keywords):
                project_info['blockchain'] = blockchain
                break
        
        return project_info