"""
Anti-Sybil Agent

防女巫智能体系统，负责管理浏览器指纹，设计差异化的账户行为模式，
模拟自然的人类行为。实现完全闭环系统，能够自主创建和维护多个独特的数字身份。
"""

from .anti_sybil_agent import AntiSybilAgent

# Import only existing modules
try:
    from .identity import IdentityManager
except ImportError:
    IdentityManager = None

try:
    from .fingerprints import BrowserFingerprint
except ImportError:
    BrowserFingerprint = None

try:
    from .behaviors import BehaviorDesigner
except ImportError:
    BehaviorDesigner = None

try:
    from .simulators import HumanSimulator
except ImportError:
    HumanSimulator = None

try:
    from .detection_evasion import BotDetectorAnalyzer
except ImportError:
    BotDetectorAnalyzer = None

try:
    from .analytics import DetectionRiskAnalyzer
except ImportError:
    DetectionRiskAnalyzer = None

__version__ = "1.0.0"
__all__ = [
    "AntiSybilAgent",
    "IdentityManager",
    "BrowserFingerprint",
    "BehaviorDesigner",
    "HumanSimulator",
    "BotDetectorAnalyzer",
    "DetectionRiskAnalyzer"
]
