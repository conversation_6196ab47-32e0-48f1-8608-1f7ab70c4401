"""
AirHunter 主入口文件

该文件是AirHunter系统的主入口点，负责初始化和启动整个系统。
"""

import os
import sys
import json
import logging
import argparse
import time
from typing import Dict, Any

# 导入协调器
from coordinator.coordinator import Coordinator

# 导入各个智能体
from discovery.discovery_agent import DiscoveryAgent
from assessment.assessment_agent import AssessmentAgent
from task_planning.task_planning_agent import TaskPlanningAgent
from task_execution.task_execution_agent import TaskExecutionAgent
from monitoring.monitoring_agent import MonitoringAgent
from fund_management.fund_management_agent import FundManagementAgent
from sybil_defense.sybil_defense_agent import SybilDefenseAgent
from profit_optimization.profit_optimization_agent import ProfitOptimizationAgent

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("data/logs/airhunter.log")
    ]
)
logger = logging.getLogger("AirHunter")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="AirHunter - 空投猎人系统")
    parser.add_argument("--config", type=str, default="config/config.json",
                        help="配置文件路径")
    parser.add_argument("--mode", type=str, choices=["full", "discovery", "execution", "monitoring"],
                        default="full", help="运行模式")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            logger.warning(f"配置文件不存在: {config_path}，将使用默认配置")
            return {}
    except Exception as e:
        logger.error(f"加载配置文件出错: {e}")
        return {}

def initialize_system(config: Dict[str, Any], mode: str) -> Coordinator:
    """
    初始化系统
    
    Args:
        config: 系统配置
        mode: 运行模式
        
    Returns:
        初始化后的协调器实例
    """
    # 创建协调器
    coordinator = Coordinator(config)
    
    # 根据运行模式初始化不同的智能体
    if mode in ["full", "discovery"]:
        # 初始化发现智能体
        discovery_agent = DiscoveryAgent(config.get("discovery_config_path"))
        coordinator.register_agent("discovery", discovery_agent)
        
        # 初始化评估智能体
        assessment_agent = AssessmentAgent(config.get("assessment_config_path"))
        coordinator.register_agent("assessment", assessment_agent)
    
    if mode in ["full", "execution"]:
        # 初始化任务规划智能体
        task_planning_agent = TaskPlanningAgent(config.get("task_planning_config_path"))
        coordinator.register_agent("task_planning", task_planning_agent)
        
        # 初始化任务执行智能体
        task_execution_agent = TaskExecutionAgent(config.get("task_execution_config_path"))
        coordinator.register_agent("task_execution", task_execution_agent)
        
        # 初始化防女巫智能体
        sybil_defense_agent = SybilDefenseAgent(config.get("sybil_defense_config_path"))
        coordinator.register_agent("sybil_defense", sybil_defense_agent)
    
    if mode in ["full", "monitoring"]:
        # 初始化监控智能体
        monitoring_agent = MonitoringAgent(config.get("monitoring_config_path"))
        coordinator.register_agent("monitoring", monitoring_agent)
    
    if mode == "full":
        # 初始化资金管理智能体
        fund_management_agent = FundManagementAgent(config.get("fund_management_config_path"))
        coordinator.register_agent("fund_management", fund_management_agent)
        
        # 初始化收益优化智能体
        profit_optimization_agent = ProfitOptimizationAgent(config.get("profit_optimization_config_path"))
        coordinator.register_agent("profit_optimization", profit_optimization_agent)
    
    return coordinator

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 确保日志目录存在
    os.makedirs("data/logs", exist_ok=True)
    
    logger.info("启动 AirHunter 系统...")
    
    # 加载配置
    config = load_config(args.config)
    
    # 初始化系统
    coordinator = initialize_system(config, args.mode)
    
    try:
        # 启动系统
        coordinator.start()
        
        logger.info(f"AirHunter 系统已启动，运行模式: {args.mode}")
        
        # 在这里可以添加命令行交互或者其他控制逻辑
        
        # 保持主线程运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在停止系统...")
    except Exception as e:
        logger.error(f"系统运行出错: {e}")
    finally:
        # 停止系统
        coordinator.stop()
        logger.info("AirHunter 系统已停止")

if __name__ == "__main__":
    main()