#!/usr/bin/env python3
"""
AirHunter Main Application

Main entry point for the AirHunter intelligent airdrop hunter system.
"""

import sys
import logging
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging(level: str = "INFO"):
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('airhunter.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(description="AirHunter - Intelligent Airdrop Hunter")
    parser.add_argument("--mode", choices=["gui", "cli", "daemon"], default="gui",
                       help="Application mode")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       default="INFO", help="Logging level")
    parser.add_argument("--config", help="Configuration file path")

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)

    logger.info("Starting AirHunter application")

    try:
        if args.mode == "gui":
            # Start GUI application
            from ui.main_window import MainWindow
            try:
                from PyQt6.QtWidgets import QApplication
                app = QApplication(sys.argv)
                window = MainWindow()
                window.show()
                sys.exit(app.exec())
            except ImportError:
                logger.error("PyQt6 not available. Please install PyQt6 or use CLI mode.")
                sys.exit(1)

        elif args.mode == "cli":
            # Start CLI application
            logger.info("CLI mode not yet implemented")

        elif args.mode == "daemon":
            # Start daemon mode
            logger.info("Daemon mode not yet implemented")

    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
