"""
代理智能体的主类

该模块包含代理智能体的主类，负责获取、验证、管理和分发代理。
"""

import logging
import threading
import time
from typing import Dict, List, Any, Optional, Set, Tuple

from proxy.acquisition.proxy_source_manager import ProxySourceManager
from proxy.verification.proxy_verifier import ProxyVerifier
from proxy.management.proxy_pool import ProxyPool
from proxy.distribution.proxy_distributor import ProxyDistributor
from proxy.models.proxy import Proxy


class ProxyAgent:
    """代理智能体的主类，负责获取、验证、管理和分发代理"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化代理智能体
        
        Args:
            config: 配置字典，包含代理智能体的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("初始化代理智能体...")
        
        # 初始化组件
        self.source_manager = ProxySourceManager(config.get('sources', {}))
        self.verifier = ProxyVerifier(config.get('verification', {}))
        self.pool = ProxyPool(config.get('pool', {}))
        self.distributor = ProxyDistributor(config.get('distribution', {}), self.pool)
        
        # 运行状态
        self._running = False
        self._lock = threading.RLock()
        self._acquisition_thread = None
        self._verification_thread = None
        self._cleanup_thread = None
        
        # 配置参数
        self.acquisition_interval = config.get('acquisition_interval', 3600)  # 默认每小时获取一次
        self.verification_interval = config.get('verification_interval', 300)  # 默认每5分钟验证一次
        self.cleanup_interval = config.get('cleanup_interval', 1800)  # 默认每30分钟清理一次
        self.min_pool_size = config.get('min_pool_size', 20)  # 最小池大小
        self.max_pool_size = config.get('max_pool_size', 200)  # 最大池大小
        
        self.logger.info("代理智能体初始化完成")
    
    def start(self) -> None:
        """启动代理智能体"""
        with self._lock:
            if self._running:
                self.logger.warning("代理智能体已经在运行")
                return
            
            self._running = True
            
            # 启动获取线程
            self._acquisition_thread = threading.Thread(
                target=self._acquisition_loop,
                name="ProxyAcquisition",
                daemon=True
            )
            self._acquisition_thread.start()
            
            # 启动验证线程
            self._verification_thread = threading.Thread(
                target=self._verification_loop,
                name="ProxyVerification",
                daemon=True
            )
            self._verification_thread.start()
            
            # 启动清理线程
            self._cleanup_thread = threading.Thread(
                target=self._cleanup_loop,
                name="ProxyCleanup",
                daemon=True
            )
            self._cleanup_thread.start()
            
            self.logger.info("代理智能体已启动")
    
    def stop(self) -> None:
        """停止代理智能体"""
        with self._lock:
            if not self._running:
                self.logger.warning("代理智能体未在运行")
                return
            
            self._running = False
            
            # 等待线程结束
            if self._acquisition_thread:
                self._acquisition_thread.join(timeout=5.0)
            
            if self._verification_thread:
                self._verification_thread.join(timeout=5.0)
            
            if self._cleanup_thread:
                self._cleanup_thread.join(timeout=5.0)
            
            self._acquisition_thread = None
            self._verification_thread = None
            self._cleanup_thread = None
            
            self.logger.info("代理智能体已停止")
    
    def get_proxy(self, protocol: str = None, country: str = None, anonymity: str = None) -> Optional[Proxy]:
        """
        获取代理
        
        Args:
            protocol: 代理协议，如 'http', 'https', 'socks4', 'socks5'
            country: 代理国家/地区
            anonymity: 代理匿名级别，如 'transparent', 'anonymous', 'elite'
            
        Returns:
            代理对象，如果没有可用的代理则返回None
        """
        return self.distributor.get_proxy(protocol, country, anonymity)
    
    def get_proxies(self, count: int, protocol: str = None, country: str = None, anonymity: str = None) -> List[Proxy]:
        """
        获取多个代理
        
        Args:
            count: 要获取的代理数量
            protocol: 代理协议，如 'http', 'https', 'socks4', 'socks5'
            country: 代理国家/地区
            anonymity: 代理匿名级别，如 'transparent', 'anonymous', 'elite'
            
        Returns:
            代理对象列表
        """
        return self.distributor.get_proxies(count, protocol, country, anonymity)
    
    def report_proxy_status(self, proxy: Proxy, success: bool, response_time: float = None) -> None:
        """
        报告代理状态
        
        Args:
            proxy: 代理对象
            success: 是否成功使用
            response_time: 响应时间（秒）
        """
        self.distributor.report_proxy_status(proxy, success, response_time)
    
    def add_proxy(self, proxy: Proxy) -> bool:
        """
        添加代理到池中
        
        Args:
            proxy: 代理对象
            
        Returns:
            如果成功添加则返回True，否则返回False
        """
        # 验证代理
        if not self.verifier.verify_proxy(proxy):
            self.logger.debug(f"代理验证失败: {proxy}")
            return False
        
        # 添加到池中
        return self.pool.add_proxy(proxy)
    
    def remove_proxy(self, proxy: Proxy) -> bool:
        """
        从池中移除代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            如果成功移除则返回True，否则返回False
        """
        return self.pool.remove_proxy(proxy)
    
    def get_pool_status(self) -> Dict[str, Any]:
        """
        获取代理池状态
        
        Returns:
            代理池状态字典
        """
        return self.pool.get_status()
    
    def get_source_status(self) -> Dict[str, Any]:
        """
        获取代理源状态
        
        Returns:
            代理源状态字典
        """
        return self.source_manager.get_status()
    
    def _acquisition_loop(self) -> None:
        """代理获取循环"""
        self.logger.info("代理获取循环已启动")
        
        # 立即获取一次代理
        self._acquire_proxies()
        
        while self._running:
            try:
                # 等待下一次获取
                for _ in range(int(self.acquisition_interval * 2)):  # 分成更小的间隔，以便更快地响应停止信号
                    if not self._running:
                        break
                    time.sleep(0.5)
                
                if not self._running:
                    break
                
                # 获取代理
                self._acquire_proxies()
            
            except Exception as e:
                self.logger.error(f"代理获取循环出错: {str(e)}")
                time.sleep(60.0)  # 出错后等待一段时间再继续
        
        self.logger.info("代理获取循环已停止")
    
    def _verification_loop(self) -> None:
        """代理验证循环"""
        self.logger.info("代理验证循环已启动")
        
        while self._running:
            try:
                # 等待下一次验证
                for _ in range(int(self.verification_interval * 2)):  # 分成更小的间隔，以便更快地响应停止信号
                    if not self._running:
                        break
                    time.sleep(0.5)
                
                if not self._running:
                    break
                
                # 验证代理
                self._verify_proxies()
            
            except Exception as e:
                self.logger.error(f"代理验证循环出错: {str(e)}")
                time.sleep(60.0)  # 出错后等待一段时间再继续
        
        self.logger.info("代理验证循环已停止")
    
    def _cleanup_loop(self) -> None:
        """代理清理循环"""
        self.logger.info("代理清理循环已启动")
        
        while self._running:
            try:
                # 等待下一次清理
                for _ in range(int(self.cleanup_interval * 2)):  # 分成更小的间隔，以便更快地响应停止信号
                    if not self._running:
                        break
                    time.sleep(0.5)
                
                if not self._running:
                    break
                
                # 清理代理
                self._cleanup_proxies()
            
            except Exception as e:
                self.logger.error(f"代理清理循环出错: {str(e)}")
                time.sleep(60.0)  # 出错后等待一段时间再继续
        
        self.logger.info("代理清理循环已停止")
    
    def _acquire_proxies(self) -> None:
        """获取代理"""
        self.logger.info("开始获取代理...")
        
        # 检查池大小
        pool_status = self.pool.get_status()
        current_size = pool_status.get("total", 0)
        
        if current_size >= self.max_pool_size:
            self.logger.info(f"代理池已满 ({current_size}/{self.max_pool_size})，跳过获取")
            return
        
        # 计算需要获取的代理数量
        to_acquire = max(0, self.min_pool_size - current_size)
        
        if to_acquire <= 0:
            self.logger.info(f"代理池大小足够 ({current_size}/{self.min_pool_size})，跳过获取")
            return
        
        self.logger.info(f"需要获取 {to_acquire} 个代理")
        
        # 获取代理
        proxies = self.source_manager.get_proxies(to_acquire)
        
        if not proxies:
            self.logger.warning("没有获取到代理")
            return
        
        self.logger.info(f"获取到 {len(proxies)} 个代理")
        
        # 验证代理
        valid_proxies = []
        for proxy in proxies:
            if self.verifier.verify_proxy(proxy):
                valid_proxies.append(proxy)
        
        self.logger.info(f"验证通过 {len(valid_proxies)}/{len(proxies)} 个代理")
        
        # 添加到池中
        added_count = 0
        for proxy in valid_proxies:
            if self.pool.add_proxy(proxy):
                added_count += 1
        
        self.logger.info(f"添加了 {added_count}/{len(valid_proxies)} 个代理到池中")
    
    def _verify_proxies(self) -> None:
        """验证代理"""
        self.logger.info("开始验证代理...")
        
        # 获取所有代理
        proxies = self.pool.get_all_proxies()
        
        if not proxies:
            self.logger.info("代理池为空，跳过验证")
            return
        
        self.logger.info(f"验证 {len(proxies)} 个代理")
        
        # 验证代理
        invalid_proxies = []
        for proxy in proxies:
            if not self.verifier.verify_proxy(proxy):
                invalid_proxies.append(proxy)
        
        self.logger.info(f"发现 {len(invalid_proxies)}/{len(proxies)} 个无效代理")
        
        # 移除无效代理
        removed_count = 0
        for proxy in invalid_proxies:
            if self.pool.remove_proxy(proxy):
                removed_count += 1
        
        self.logger.info(f"移除了 {removed_count}/{len(invalid_proxies)} 个无效代理")
    
    def _cleanup_proxies(self) -> None:
        """清理代理"""
        self.logger.info("开始清理代理...")
        
        # 清理过期代理
        expired_count = self.pool.cleanup_expired_proxies()
        
        # 清理失败次数过多的代理
        failed_count = self.pool.cleanup_failed_proxies()
        
        self.logger.info(f"清理了 {expired_count} 个过期代理和 {failed_count} 个失败代理")