"""
Query Builder

Provides a fluent interface for building SQL queries programmatically
with support for complex conditions, joins, and aggregations.
"""

from typing import List, Dict, Any, Optional, Union
from enum import Enum


class JoinType(Enum):
    """SQL join types."""
    INNER = "INNER JOIN"
    LEFT = "LEFT JOIN"
    RIGHT = "RIGHT JOIN"
    FULL = "FULL OUTER JOIN"


class QueryBuilder:
    """
    SQL query builder with fluent interface.
    
    Provides methods to build SELECT, INSERT, UPDATE, and DELETE queries
    programmatically with proper parameter binding.
    """
    
    def __init__(self):
        """Initialize query builder."""
        self.reset()
    
    def reset(self):
        """Reset builder to initial state."""
        self._select_fields = []
        self._from_table = ""
        self._joins = []
        self._where_conditions = []
        self._group_by = []
        self._having_conditions = []
        self._order_by = []
        self._limit_value = None
        self._offset_value = None
        self._parameters = []
        return self
    
    def select(self, *fields: str) -> 'QueryBuilder':
        """
        Add SELECT fields.
        
        Args:
            *fields: Field names to select
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._select_fields.extend(fields)
        return self
    
    def from_table(self, table: str) -> 'QueryBuilder':
        """
        Set FROM table.
        
        Args:
            table: Table name
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._from_table = table
        return self
    
    def join(self, table: str, condition: str, join_type: JoinType = JoinType.INNER) -> 'QueryBuilder':
        """
        Add JOIN clause.
        
        Args:
            table: Table to join
            condition: Join condition
            join_type: Type of join
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._joins.append(f"{join_type.value} {table} ON {condition}")
        return self
    
    def where(self, condition: str, *params) -> 'QueryBuilder':
        """
        Add WHERE condition.
        
        Args:
            condition: WHERE condition with ? placeholders
            *params: Parameters for condition
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._where_conditions.append(condition)
        self._parameters.extend(params)
        return self
    
    def where_in(self, field: str, values: List[Any]) -> 'QueryBuilder':
        """
        Add WHERE IN condition.
        
        Args:
            field: Field name
            values: List of values
            
        Returns:
            QueryBuilder: Self for chaining
        """
        if values:
            placeholders = ",".join(["?" for _ in values])
            self._where_conditions.append(f"{field} IN ({placeholders})")
            self._parameters.extend(values)
        return self
    
    def where_between(self, field: str, start: Any, end: Any) -> 'QueryBuilder':
        """
        Add WHERE BETWEEN condition.
        
        Args:
            field: Field name
            start: Start value
            end: End value
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._where_conditions.append(f"{field} BETWEEN ? AND ?")
        self._parameters.extend([start, end])
        return self
    
    def where_like(self, field: str, pattern: str) -> 'QueryBuilder':
        """
        Add WHERE LIKE condition.
        
        Args:
            field: Field name
            pattern: LIKE pattern
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._where_conditions.append(f"{field} LIKE ?")
        self._parameters.append(pattern)
        return self
    
    def group_by(self, *fields: str) -> 'QueryBuilder':
        """
        Add GROUP BY fields.
        
        Args:
            *fields: Field names to group by
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._group_by.extend(fields)
        return self
    
    def having(self, condition: str, *params) -> 'QueryBuilder':
        """
        Add HAVING condition.
        
        Args:
            condition: HAVING condition with ? placeholders
            *params: Parameters for condition
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._having_conditions.append(condition)
        self._parameters.extend(params)
        return self
    
    def order_by(self, field: str, direction: str = "ASC") -> 'QueryBuilder':
        """
        Add ORDER BY clause.
        
        Args:
            field: Field name to order by
            direction: Sort direction (ASC or DESC)
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._order_by.append(f"{field} {direction.upper()}")
        return self
    
    def limit(self, count: int) -> 'QueryBuilder':
        """
        Add LIMIT clause.
        
        Args:
            count: Maximum number of rows
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._limit_value = count
        return self
    
    def offset(self, count: int) -> 'QueryBuilder':
        """
        Add OFFSET clause.
        
        Args:
            count: Number of rows to skip
            
        Returns:
            QueryBuilder: Self for chaining
        """
        self._offset_value = count
        return self
    
    def build_select(self) -> tuple[str, tuple]:
        """
        Build SELECT query.
        
        Returns:
            tuple: (query_string, parameters)
        """
        if not self._from_table:
            raise ValueError("FROM table is required for SELECT query")
        
        # Build SELECT clause
        if self._select_fields:
            select_clause = f"SELECT {', '.join(self._select_fields)}"
        else:
            select_clause = "SELECT *"
        
        # Build FROM clause
        from_clause = f"FROM {self._from_table}"
        
        # Build query parts
        query_parts = [select_clause, from_clause]
        
        # Add JOINs
        if self._joins:
            query_parts.extend(self._joins)
        
        # Add WHERE
        if self._where_conditions:
            where_clause = f"WHERE {' AND '.join(self._where_conditions)}"
            query_parts.append(where_clause)
        
        # Add GROUP BY
        if self._group_by:
            group_clause = f"GROUP BY {', '.join(self._group_by)}"
            query_parts.append(group_clause)
        
        # Add HAVING
        if self._having_conditions:
            having_clause = f"HAVING {' AND '.join(self._having_conditions)}"
            query_parts.append(having_clause)
        
        # Add ORDER BY
        if self._order_by:
            order_clause = f"ORDER BY {', '.join(self._order_by)}"
            query_parts.append(order_clause)
        
        # Add LIMIT
        if self._limit_value is not None:
            query_parts.append(f"LIMIT {self._limit_value}")
        
        # Add OFFSET
        if self._offset_value is not None:
            query_parts.append(f"OFFSET {self._offset_value}")
        
        query = " ".join(query_parts)
        return query, tuple(self._parameters)
    
    def build_insert(self, table: str, data: Dict[str, Any]) -> tuple[str, tuple]:
        """
        Build INSERT query.
        
        Args:
            table: Table name
            data: Data to insert
            
        Returns:
            tuple: (query_string, parameters)
        """
        if not data:
            raise ValueError("Data is required for INSERT query")
        
        fields = list(data.keys())
        values = list(data.values())
        placeholders = ",".join(["?" for _ in fields])
        
        query = f"INSERT INTO {table} ({', '.join(fields)}) VALUES ({placeholders})"
        return query, tuple(values)
    
    def build_update(self, table: str, data: Dict[str, Any]) -> tuple[str, tuple]:
        """
        Build UPDATE query.
        
        Args:
            table: Table name
            data: Data to update
            
        Returns:
            tuple: (query_string, parameters)
        """
        if not data:
            raise ValueError("Data is required for UPDATE query")
        
        set_clauses = [f"{field} = ?" for field in data.keys()]
        parameters = list(data.values()) + self._parameters
        
        query_parts = [
            f"UPDATE {table}",
            f"SET {', '.join(set_clauses)}"
        ]
        
        if self._where_conditions:
            where_clause = f"WHERE {' AND '.join(self._where_conditions)}"
            query_parts.append(where_clause)
        
        query = " ".join(query_parts)
        return query, tuple(parameters)
    
    def build_delete(self, table: str) -> tuple[str, tuple]:
        """
        Build DELETE query.
        
        Args:
            table: Table name
            
        Returns:
            tuple: (query_string, parameters)
        """
        query_parts = [f"DELETE FROM {table}"]
        
        if self._where_conditions:
            where_clause = f"WHERE {' AND '.join(self._where_conditions)}"
            query_parts.append(where_clause)
        
        query = " ".join(query_parts)
        return query, tuple(self._parameters)
