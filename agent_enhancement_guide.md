# 智能体增强指南

## 监控智能体增强

# 在 monitoring_agent.py 中添加以下代码

from common.mixins.continuous_operation import ContinuousOperationMixin, MultiAccountMixin

class MonitoringAgent(ContinuousOperationMixin, MultiAccountMixin):
    """增强的监控智能体，支持持续运行和多账号"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 监控配置
        self.loop_interval = config.get('check_interval', 300)  # 5分钟检查一次
        self.max_errors = config.get('max_errors', 5)
        
        # 监控目标
        self.monitoring_targets = {}
        self.alert_thresholds = config.get('alert_thresholds', {})
        
        # 添加恢复策略
        self.add_recovery_strategy(self._restart_monitoring_services)
        self.add_recovery_strategy(self._clear_error_state)
    
    async def _execute_operation_cycle(self) -> bool:
        """执行监控周期"""
        try:
            # 检查所有监控目标
            for target_id, target_config in self.monitoring_targets.items():
                await self._check_target(target_id, target_config)
            
            # 轮换账号（如果启用）
            if self._account_rotation_enabled:
                self.rotate_account()
            
            return True
            
        except Exception as e:
            self.logger.error(f"监控周期执行失败: {e}")
            return False
    
    async def _check_target(self, target_id: str, target_config: Dict[str, Any]):
        """检查监控目标"""
        try:
            # 实现具体的监控逻辑
            # 例如：检查项目状态、网站可用性、社交媒体活动等
            
            # 使用当前账号进行检查
            current_account = self.get_current_account()
            if current_account:
                # 使用账号信息进行监控
                pass
            
            # 更新账号统计
            if self._current_account:
                self.update_account_stats(self._current_account, True)
            
        except Exception as e:
            self.logger.error(f"检查目标 {target_id} 失败: {e}")
            if self._current_account:
                self.update_account_stats(self._current_account, False)
            raise
    
    async def _restart_monitoring_services(self) -> bool:
        """重启监控服务"""
        try:
            # 重启监控相关服务
            self.logger.info("重启监控服务...")
            await asyncio.sleep(1)  # 模拟重启
            return True
        except Exception as e:
            self.logger.error(f"重启监控服务失败: {e}")
            return False
    
    async def _clear_error_state(self) -> bool:
        """清理错误状态"""
        try:
            # 清理错误状态
            self.logger.info("清理错误状态...")
            return True
        except Exception as e:
            self.logger.error(f"清理错误状态失败: {e}")
            return False


## 评估智能体增强

# 在 assessment_agent.py 中添加以下代码

from common.mixins.continuous_operation import ContinuousOperationMixin, MultiAccountMixin

class AssessmentAgent(ContinuousOperationMixin, MultiAccountMixin):
    """增强的评估智能体，支持持续运行和多账号"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 评估配置
        self.loop_interval = config.get('assessment_interval', 1800)  # 30分钟评估一次
        self.max_errors = config.get('max_errors', 3)
        
        # 评估队列
        self.assessment_queue = []
        self.completed_assessments = {}
        
        # 添加恢复策略
        self.add_recovery_strategy(self._reset_assessment_state)
        self.add_recovery_strategy(self._reload_assessment_models)
    
    async def _execute_operation_cycle(self) -> bool:
        """执行评估周期"""
        try:
            # 处理评估队列
            if self.assessment_queue:
                project = self.assessment_queue.pop(0)
                await self._assess_project(project)
            
            # 定期重新评估已完成的项目
            await self._reasses_existing_projects()
            
            # 轮换账号
            if self._account_rotation_enabled:
                self.rotate_account()
            
            return True
            
        except Exception as e:
            self.logger.error(f"评估周期执行失败: {e}")
            return False
    
    async def _assess_project(self, project: Dict[str, Any]):
        """评估项目"""
        try:
            project_id = project.get('id')
            
            # 使用当前账号进行评估
            current_account = self.get_current_account()
            
            # 执行风险评估
            risk_score = await self._calculate_risk_score(project, current_account)
            
            # 执行奖励评估
            reward_score = await self._calculate_reward_score(project, current_account)
            
            # 保存评估结果
            self.completed_assessments[project_id] = {
                'risk_score': risk_score,
                'reward_score': reward_score,
                'assessed_at': datetime.now(),
                'account_used': self._current_account
            }
            
            # 更新账号统计
            if self._current_account:
                self.update_account_stats(self._current_account, True)
            
        except Exception as e:
            self.logger.error(f"评估项目失败: {e}")
            if self._current_account:
                self.update_account_stats(self._current_account, False)
            raise
    
    async def _calculate_risk_score(self, project: Dict[str, Any], account: Dict[str, Any]) -> float:
        """计算风险分数"""
        # 实现风险评估逻辑
        return 50.0  # 占位符
    
    async def _calculate_reward_score(self, project: Dict[str, Any], account: Dict[str, Any]) -> float:
        """计算奖励分数"""
        # 实现奖励评估逻辑
        return 75.0  # 占位符
    
    async def _reasses_existing_projects(self):
        """重新评估现有项目"""
        # 定期重新评估项目，更新评估结果
        pass
    
    async def _reset_assessment_state(self) -> bool:
        """重置评估状态"""
        try:
            self.logger.info("重置评估状态...")
            return True
        except Exception as e:
            self.logger.error(f"重置评估状态失败: {e}")
            return False
    
    async def _reload_assessment_models(self) -> bool:
        """重新加载评估模型"""
        try:
            self.logger.info("重新加载评估模型...")
            return True
        except Exception as e:
            self.logger.error(f"重新加载评估模型失败: {e}")
            return False


## 其他智能体增强
类似地，可以为其他智能体（Task Planning, Task Execution, Proxy, Profit Optimization）
添加相同的混入类来实现持续运行和多账号支持。

## 使用方法
1. 将 ContinuousOperationMixin 和 MultiAccountMixin 添加到智能体类的继承列表中
2. 重写 _execute_operation_cycle 方法实现具体的周期性操作
3. 配置循环间隔、错误处理和恢复策略
4. 添加账号并启用账号轮换功能
