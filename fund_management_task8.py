#!/usr/bin/env python3
"""
Fund Management Agent - Task 8

完成Balance Tracker并创建Token Manager
"""

from pathlib import Path

def create_file(path: str, content: str):
    """Create a file with the given content."""
    file_path = Path(path)
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Created: {path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create {path}: {e}")
        return False

def complete_balance_tracker():
    """完成Balance Tracker的剩余方法"""
    
    additional_content = '''
    async def start_tracking(self):
        """启动余额跟踪"""
        try:
            if not self.is_tracking:
                self.is_tracking = True
                self.tracking_task = asyncio.create_task(self._tracking_loop())
                self.logger.info("Balance tracking started")
                
        except Exception as e:
            self.logger.error(f"Failed to start tracking: {e}")
    
    async def stop_tracking(self):
        """停止余额跟踪"""
        try:
            self.is_tracking = False
            if self.tracking_task:
                self.tracking_task.cancel()
                try:
                    await self.tracking_task
                except asyncio.CancelledError:
                    pass
                self.tracking_task = None
            
            self.logger.info("Balance tracking stopped")
            
        except Exception as e:
            self.logger.error(f"Failed to stop tracking: {e}")
    
    async def _tracking_loop(self):
        """跟踪循环"""
        while self.is_tracking:
            try:
                await self._update_all_balances()
                await asyncio.sleep(self.tracking_config["update_interval"])
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in tracking loop: {e}")
                await asyncio.sleep(10)  # 错误后等待
    
    async def _update_all_balances(self):
        """更新所有地址的余额"""
        try:
            for address in list(self.tracked_addresses.keys()):
                await self._update_address_balance(address)
            
            self.tracking_stats['last_update_time'] = datetime.utcnow().isoformat()
            
        except Exception as e:
            self.logger.error(f"Error updating all balances: {e}")
    
    async def _update_address_balance(self, address: str):
        """更新单个地址的余额"""
        try:
            tracking_data = self.tracked_addresses[address]
            
            # 保存之前的余额
            tracking_data["previous_balances"] = tracking_data.get("current_balances", {}).copy()
            
            # 获取新余额
            new_balances = await self._fetch_balances(address, tracking_data["tokens_to_track"])
            tracking_data["current_balances"] = new_balances
            tracking_data["last_updated"] = datetime.utcnow()
            
            # 检查余额变化
            balance_changed = self._check_balance_changes(
                tracking_data["previous_balances"], 
                new_balances
            )
            
            if balance_changed:
                self.tracking_stats['balance_changes_detected'] += 1
                
                # 记录到历史
                history_record = {
                    "timestamp": datetime.utcnow().isoformat(),
                    "balances": new_balances.copy(),
                    "changes": self._calculate_balance_changes(
                        tracking_data["previous_balances"], 
                        new_balances
                    )
                }
                
                self.balance_history[address].append(history_record)
                
                # 清理旧历史记录
                await self._cleanup_old_history(address)
            
            self.tracking_stats['total_updates'] += 1
            
        except Exception as e:
            self.logger.error(f"Error updating balance for {address}: {e}")
    
    async def _fetch_balances(self, address: str, token_addresses: List[str]) -> Dict[str, float]:
        """
        获取地址的余额
        
        Args:
            address: 钱包地址
            token_addresses: 代币合约地址列表
            
        Returns:
            Dict[str, float]: 余额字典
        """
        try:
            balances = {}
            
            # 模拟获取ETH余额
            # 实际实现需要调用区块链RPC
            import random
            balances["ETH"] = round(random.uniform(0.1, 10.0), 6)
            
            # 模拟获取代币余额
            for token_address in token_addresses:
                balances[token_address] = round(random.uniform(100, 10000), 2)
            
            return balances
            
        except Exception as e:
            self.logger.error(f"Error fetching balances: {e}")
            return {}
    
    def _check_balance_changes(self, previous_balances: Dict, current_balances: Dict) -> bool:
        """检查余额是否有显著变化"""
        try:
            min_change = self.tracking_config["min_balance_change"]
            
            for token, current_balance in current_balances.items():
                previous_balance = previous_balances.get(token, 0.0)
                change = abs(current_balance - previous_balance)
                
                if change >= min_change:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking balance changes: {e}")
            return False
    
    def _calculate_balance_changes(self, previous_balances: Dict, current_balances: Dict) -> Dict:
        """计算余额变化"""
        try:
            changes = {}
            
            all_tokens = set(previous_balances.keys()) | set(current_balances.keys())
            
            for token in all_tokens:
                previous = previous_balances.get(token, 0.0)
                current = current_balances.get(token, 0.0)
                change = current - previous
                
                if change != 0:
                    changes[token] = {
                        "previous": previous,
                        "current": current,
                        "change": change,
                        "change_percent": (change / previous * 100) if previous > 0 else 0
                    }
            
            return changes
            
        except Exception as e:
            self.logger.error(f"Error calculating balance changes: {e}")
            return {}
    
    async def _cleanup_old_history(self, address: str):
        """清理旧的历史记录"""
        try:
            retention_days = self.tracking_config["history_retention_days"]
            cutoff_time = datetime.utcnow() - timedelta(days=retention_days)
            
            if address in self.balance_history:
                self.balance_history[address] = [
                    record for record in self.balance_history[address]
                    if datetime.fromisoformat(record["timestamp"]) >= cutoff_time
                ]
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old history: {e}")
    
    def _is_valid_address(self, address: str) -> bool:
        """验证以太坊地址格式"""
        if not isinstance(address, str):
            return False
        if not address.startswith("0x"):
            return False
        if len(address) != 42:
            return False
        try:
            int(address[2:], 16)
            return True
        except ValueError:
            return False
    
    async def get_tracking_statistics(self) -> Dict[str, Any]:
        """
        获取跟踪统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            return {
                'tracking_stats': self.tracking_stats,
                'tracked_addresses_count': len(self.tracked_addresses),
                'total_history_records': sum(len(history) for history in self.balance_history.values()),
                'tracking_config': self.tracking_config,
                'is_tracking': self.is_tracking
            }
            
        except Exception as e:
            self.logger.error(f"Error getting tracking statistics: {e}")
            return {}
'''
    
    return additional_content

def create_token_manager():
    """创建Token Manager"""
    
    token_manager_content = '''"""
Token Manager

代币管理器，负责管理代币信息、元数据和交互操作。
"""

import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime


class TokenManager:
    """
    代币管理器
    
    负责管理代币的基本信息、元数据、价格信息和交互操作。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化代币管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 代币信息存储
        self.tokens: Dict[str, Dict] = {}
        
        # 代币价格缓存
        self.price_cache: Dict[str, Dict] = {}
        
        # 管理统计
        self.manager_stats = {
            'total_tokens': 0,
            'price_updates': 0,
            'metadata_updates': 0,
            'last_sync_time': None
        }
        
        # 默认代币列表
        self.default_tokens = {
            "******************************************": {
                "symbol": "USDC",
                "name": "USD Coin",
                "decimals": 6,
                "type": "stablecoin"
            },
            "******************************************": {
                "symbol": "USDT",
                "name": "Tether USD",
                "decimals": 6,
                "type": "stablecoin"
            }
        }
    
    async def initialize(self) -> bool:
        """
        初始化代币管理器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 加载默认代币
            await self._load_default_tokens()
            
            self.logger.info("Token Manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Token Manager: {e}")
            return False
    
    async def add_token(self, token_address: str, token_info: Dict[str, Any] = None) -> bool:
        """
        添加代币到管理器
        
        Args:
            token_address: 代币合约地址
            token_info: 代币信息
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if not self._is_valid_address(token_address):
                self.logger.error(f"Invalid token address: {token_address}")
                return False
            
            # 如果没有提供信息，尝试从链上获取
            if not token_info:
                token_info = await self._fetch_token_metadata(token_address)
            
            # 构建完整的代币信息
            complete_token_info = {
                "address": token_address,
                "symbol": token_info.get("symbol", "UNKNOWN"),
                "name": token_info.get("name", "Unknown Token"),
                "decimals": token_info.get("decimals", 18),
                "type": token_info.get("type", "erc20"),
                "total_supply": token_info.get("total_supply"),
                "added_at": datetime.utcnow().isoformat(),
                "last_updated": datetime.utcnow().isoformat(),
                "metadata": token_info.get("metadata", {}),
                "price_info": {},
                "is_verified": token_info.get("is_verified", False)
            }
            
            self.tokens[token_address] = complete_token_info
            self.manager_stats['total_tokens'] += 1
            
            self.logger.info(f"Added token {token_info.get('symbol', 'UNKNOWN')} at {token_address}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add token: {e}")
            return False
    
    async def get_token_info(self, token_address: str) -> Optional[Dict[str, Any]]:
        """
        获取代币信息
        
        Args:
            token_address: 代币合约地址
            
        Returns:
            Optional[Dict[str, Any]]: 代币信息
        """
        try:
            return self.tokens.get(token_address)
        except Exception as e:
            self.logger.error(f"Error getting token info: {e}")
            return None
    
    async def update_token_price(self, token_address: str, price_data: Dict[str, Any]) -> bool:
        """
        更新代币价格信息
        
        Args:
            token_address: 代币合约地址
            price_data: 价格数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if token_address not in self.tokens:
                self.logger.error(f"Token {token_address} not found")
                return False
            
            # 更新价格信息
            self.tokens[token_address]["price_info"] = {
                "price_usd": price_data.get("price_usd", 0.0),
                "price_eth": price_data.get("price_eth", 0.0),
                "market_cap": price_data.get("market_cap", 0.0),
                "volume_24h": price_data.get("volume_24h", 0.0),
                "change_24h": price_data.get("change_24h", 0.0),
                "last_updated": datetime.utcnow().isoformat()
            }
            
            # 更新价格缓存
            self.price_cache[token_address] = self.tokens[token_address]["price_info"].copy()
            
            self.tokens[token_address]["last_updated"] = datetime.utcnow().isoformat()
            self.manager_stats['price_updates'] += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update token price: {e}")
            return False
'''
    
    return token_manager_content

def main():
    """主函数"""
    print("🚀 Fund Management Agent - Task 8")
    print("=" * 50)
    
    # 完成Balance Tracker
    additional_content = complete_balance_tracker()
    
    try:
        with open("fund_management/assets/balance_tracker.py", 'a', encoding='utf-8') as f:
            f.write(additional_content)
        print("✅ Completed Balance Tracker methods")
    except Exception as e:
        print(f"❌ Failed to complete Balance Tracker: {e}")
    
    # 创建Token Manager
    token_content = create_token_manager()
    success = create_file("fund_management/assets/token_manager.py", token_content)
    
    if success:
        print("✅ Task 8 completed: Token Manager created!")
    else:
        print("❌ Task 8 failed")

if __name__ == "__main__":
    main()
