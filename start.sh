#!/bin/bash

echo "==================================="
echo "AirHunter 空投猎人系统启动脚本"
echo "==================================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "[错误] 未找到Python。请安装Python 3.8或更高版本。"
    exit 1
fi

# 检查虚拟环境是否存在
if [ ! -d ".venv" ]; then
    echo "[信息] 创建虚拟环境..."
    python3 -m venv .venv
    if [ $? -ne 0 ]; then
        echo "[错误] 创建虚拟环境失败。"
        exit 1
    fi
fi

# 激活虚拟环境
echo "[信息] 激活虚拟环境..."
source .venv/bin/activate

# 检查依赖是否安装
if [ ! -d ".venv/lib/python3.8/site-packages/requests" ] && [ ! -d ".venv/lib/python3.9/site-packages/requests" ] && [ ! -d ".venv/lib/python3.10/site-packages/requests" ]; then
    echo "[信息] 安装依赖..."
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "[错误] 安装依赖失败。"
        exit 1
    fi
fi

# 初始化数据目录
echo "[信息] 初始化数据目录..."
python scripts/initialize_data_directories.py

# 检查配置文件
if [ ! -f "config/config.json" ]; then
    echo "[信息] 创建配置文件..."
    if [ -f "config/config.example.json" ]; then
        cp config/config.example.json config/config.json
    else
        echo "[错误] 示例配置文件不存在。"
        exit 1
    fi
fi

# 运行系统健康检查
echo "[信息] 运行系统健康检查..."
python scripts/system_check.py
if [ $? -ne 0 ]; then
    echo "[警告] 系统健康检查未通过。是否继续? (Y/N)"
    read choice
    if [ "$choice" != "Y" ] && [ "$choice" != "y" ]; then
        echo "[信息] 已取消启动。"
        exit 1
    fi
fi

# 启动系统
echo "[信息] 启动AirHunter系统..."
python main.py "$@"