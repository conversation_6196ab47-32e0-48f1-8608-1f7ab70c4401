"""
GitHub 项目源

该模块实现了从 GitHub 获取项目信息的功能。
"""

import logging
import requests
import time
import random
import re
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from discovery.sources.base_source import BaseProjectSource


class GitHubSource(BaseProjectSource):
    """GitHub 项目源"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 GitHub 项目源
        
        Args:
            config: 配置字典，包含项目源的配置信息
        """
        super().__init__(config)
        self.api_token = config.get('api_token', '')
        self.use_api = config.get('use_api', True)
        self.timeout = config.get('timeout', 10)
        self.search_terms = config.get('search_terms', [
            'airdrop', 'crypto airdrop', 'token airdrop',
            'blockchain airdrop', 'defi airdrop', 'nft airdrop'
        ])
        self.organizations = config.get('organizations', [
            'ethereum', 'solana-labs', 'bnb-chain', 'maticnetwork',
            'avalancheavax', 'arbitrum', 'ethereum-optimism', 'base-org'
        ])
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
    
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        从 GitHub 获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        self.logger.info(f"从 GitHub 获取 {count} 个项目")
        
        if self.use_api:
            return self._get_projects_via_api(count)
        else:
            return self._get_projects_via_scraping(count)
    
    def _get_projects_via_api(self, count: int) -> List[Dict[str, Any]]:
        """
        通过 API 获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            projects = []
            
            # 计算每个搜索词需要获取的仓库数量
            terms_count = len(self.search_terms)
            repos_per_term = max(1, count // terms_count)
            
            # 设置 API 请求头
            headers = {
                'Accept': 'application/vnd.github.v3+json'
            }
            
            if self.api_token:
                headers['Authorization'] = f"token {self.api_token}"
            
            # 获取最近一周的项目
            date_filter = datetime.now() - timedelta(days=30)
            date_str = date_filter.strftime('%Y-%m-%d')
            
            # 搜索仓库
            for term in self.search_terms:
                try:
                    # 构建搜索URL
                    search_url = f"https://api.github.com/search/repositories?q={term}+created:>{date_str}&sort=updated&order=desc&per_page={repos_per_term}"
                    
                    # 发送请求
                    response = requests.get(search_url, headers=headers, timeout=self.timeout)
                    response.raise_for_status()
                    
                    # 解析响应
                    data = response.json()
                    
                    if 'items' not in data:
                        self.logger.warning(f"搜索 '{term}' 没有返回项目")
                        continue
                    
                    # 处理每个仓库
                    for repo in data['items']:
                        try:
                            # 提取项目信息
                            project_info = self._extract_project_info_from_repo(repo)
                            
                            if project_info:
                                projects.append(project_info)
                                
                                # 如果已经获取到足够的项目，就停止
                                if len(projects) >= count:
                                    break
                        
                        except Exception as e:
                            self.logger.error(f"处理仓库时出错: {str(e)}")
                
                except Exception as e:
                    self.logger.error(f"搜索仓库 '{term}' 时出错: {str(e)}")
                
                # 如果已经获取到足够的项目，就停止
                if len(projects) >= count:
                    break
                
                # 避免请求过于频繁
                time.sleep(1)
            
            # 如果还没有足够的项目，尝试从特定组织获取
            if len(projects) < count:
                for org in self.organizations:
                    try:
                        # 构建组织仓库URL
                        org_url = f"https://api.github.com/orgs/{org}/repos?sort=updated&direction=desc&per_page={repos_per_term}"
                        
                        # 发送请求
                        response = requests.get(org_url, headers=headers, timeout=self.timeout)
                        response.raise_for_status()
                        
                        # 解析响应
                        repos = response.json()
                        
                        # 处理每个仓库
                        for repo in repos:
                            try:
                                # 提取项目信息
                                project_info = self._extract_project_info_from_repo(repo)
                                
                                if project_info:
                                    projects.append(project_info)
                                    
                                    # 如果已经获取到足够的项目，就停止
                                    if len(projects) >= count:
                                        break
                            
                            except Exception as e:
                                self.logger.error(f"处理仓库时出错: {str(e)}")
                        
                        # 如果已经获取到足够的项目，就停止
                        if len(projects) >= count:
                            break
                        
                        # 避免请求过于频繁
                        time.sleep(1)
                    
                    except Exception as e:
                        self.logger.error(f"获取组织 '{org}' 的仓库时出错: {str(e)}")
                    
                    # 如果已经获取到足够的项目，就停止
                    if len(projects) >= count:
                        break
            
            self.logger.info(f"从 GitHub API 获取到 {len(projects)} 个项目")
            return projects[:count]
        
        except Exception as e:
            self.logger.error(f"通过 API 获取项目时出错: {str(e)}")
            return []
    
    def _get_projects_via_scraping(self, count: int) -> List[Dict[str, Any]]:
        """
        通过网页抓取获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            projects = []
            
            # 计算每个搜索词需要获取的仓库数量
            terms_count = len(self.search_terms)
            repos_per_term = max(1, count // terms_count)
            
            # 搜索仓库
            for term in self.search_terms:
                try:
                    # 构建搜索URL
                    search_url = f"https://github.com/search?q={term}&type=repositories&s=updated&o=desc"
                    
                    # 随机选择一个 User-Agent
                    headers = {
                        'User-Agent': random.choice(self.user_agents)
                    }
                    
                    # 发送请求
                    response = requests.get(search_url, headers=headers, timeout=self.timeout)
                    response.raise_for_status()
                    
                    # 使用正则表达式提取仓库信息
                    repo_pattern = r'<a class="v-align-middle" href="([^"]+)">(.*?)</a>'
                    repo_matches = re.findall(repo_pattern, response.text)
                    
                    for i, (repo_path, repo_name) in enumerate(repo_matches):
                        if i >= repos_per_term:
                            break
                        
                        try:
                            # 获取仓库详情
                            repo_url = f"https://github.com{repo_path}"
                            repo_response = requests.get(repo_url, headers=headers, timeout=self.timeout)
                            repo_response.raise_for_status()
                            
                            # 提取仓库描述
                            desc_pattern = r'<p class="f4 my-3">(.*?)</p>'
                            desc_match = re.search(desc_pattern, repo_response.text)
                            description = desc_match.group(1).strip() if desc_match else ""
                            
                            # 提取项目信息
                            project_info = self._extract_project_info_from_html(repo_name, description, repo_url, repo_response.text)
                            
                            if project_info:
                                projects.append(project_info)
                                
                                # 如果已经获取到足够的项目，就停止
                                if len(projects) >= count:
                                    break
                        
                        except Exception as e:
                            self.logger.error(f"处理仓库 '{repo_name}' 时出错: {str(e)}")
                        
                        # 避免请求过于频繁
                        time.sleep(1)
                
                except Exception as e:
                    self.logger.error(f"搜索仓库 '{term}' 时出错: {str(e)}")
                
                # 如果已经获取到足够的项目，就停止
                if len(projects) >= count:
                    break
            
            self.logger.info(f"从 GitHub 网页抓取获取到 {len(projects)} 个项目")
            return projects[:count]
        
        except Exception as e:
            self.logger.error(f"通过网页抓取获取项目时出错: {str(e)}")
            return []
    
    def _extract_project_info_from_repo(self, repo: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        从仓库信息中提取项目信息
        
        Args:
            repo: 仓库信息字典
            
        Returns:
            项目信息字典，如果无法提取则返回None
        """
        # 检查仓库是否包含关键词
        keywords = ['airdrop', 'token', 'crypto', 'blockchain', 'defi', 'nft']
        repo_text = f"{repo.get('name', '')} {repo.get('description', '')} {repo.get('topics', [])}".lower()
        
        if not any(keyword in repo_text for keyword in keywords):
            return None
        
        # 获取仓库信息
        name = repo.get('name', 'Unknown Project')
        description = repo.get('description', '')
        html_url = repo.get('html_url', '')
        homepage = repo.get('homepage', '')
        
        # 使用主页URL或仓库URL
        project_url = homepage if homepage else html_url
        
        # 创建项目信息
        project_info = {
            'name': name,
            'description': description[:200] + ('...' if len(description) > 200 else ''),
            'url': project_url,
            'project_type': 'airdrop',
            'blockchain': 'other',
            'source_url': html_url,
            'discovery_source': 'github',
            'social_channels': [
                {
                    'platform': 'github',
                    'url': html_url,
                    'followers': repo.get('stargazers_count')
                }
            ]
        }
        
        # 尝试识别区块链平台
        blockchain_keywords = {
            'ethereum': ['ethereum', 'eth', 'erc20', 'erc721'],
            'binance': ['binance', 'bsc', 'bnb', 'bep20'],
            'solana': ['solana', 'sol'],
            'polygon': ['polygon', 'matic'],
            'avalanche': ['avalanche', 'avax'],
            'arbitrum': ['arbitrum', 'arb'],
            'optimism': ['optimism', 'op'],
            'base': ['base'],
            'cosmos': ['cosmos', 'atom'],
            'polkadot': ['polkadot', 'dot'],
            'near': ['near'],
            'aptos': ['aptos', 'apt'],
            'sui': ['sui']
        }
        
        for blockchain, keywords in blockchain_keywords.items():
            if any(keyword in repo_text for keyword in keywords):
                project_info['blockchain'] = blockchain
                break
        
        # 提取项目类型
        if 'testnet' in repo_text:
            project_info['project_type'] = 'testnet'
        elif 'presale' in repo_text or 'ico' in repo_text or 'ido' in repo_text:
            project_info['project_type'] = 'presale'
        elif 'farming' in repo_text or 'yield' in repo_text:
            project_info['project_type'] = 'farming'
        elif 'staking' in repo_text:
            project_info['project_type'] = 'staking'
        
        return project_info
    
    def _extract_project_info_from_html(self, name: str, description: str, repo_url: str, html_content: str) -> Optional[Dict[str, Any]]:
        """
        从HTML内容中提取项目信息
        
        Args:
            name: 仓库名称
            description: 仓库描述
            repo_url: 仓库URL
            html_content: HTML内容
            
        Returns:
            项目信息字典，如果无法提取则返回None
        """
        # 检查仓库是否包含关键词
        keywords = ['airdrop', 'token', 'crypto', 'blockchain', 'defi', 'nft']
        repo_text = f"{name} {description} {html_content}".lower()
        
        if not any(keyword in repo_text for keyword in keywords):
            return None
        
        # 提取主页URL
        homepage_pattern = r'<a[^>]*?href="([^"]+)"[^>]*?rel="nofollow"[^>]*?>([^<]+)</a>'
        homepage_match = re.search(homepage_pattern, html_content)
        homepage = homepage_match.group(1) if homepage_match else ""
        
        # 使用主页URL或仓库URL
        project_url = homepage if homepage else repo_url
        
        # 提取星标数
        stars_pattern = r'<a[^>]*?href="[^"]+/stargazers"[^>]*?>\s*<svg[^>]*?>\s*<path[^>]*?>\s*</path>\s*</svg>\s*<span[^>]*?>\s*([0-9,]+)\s*</span>'
        stars_match = re.search(stars_pattern, html_content)
        stars = stars_match.group(1).replace(',', '') if stars_match else "0"
        
        # 创建项目信息
        project_info = {
            'name': name,
            'description': description[:200] + ('...' if len(description) > 200 else ''),
            'url': project_url,
            'project_type': 'airdrop',
            'blockchain': 'other',
            'source_url': repo_url,
            'discovery_source': 'github',
            'social_channels': [
                {
                    'platform': 'github',
                    'url': repo_url,
                    'followers': int(stars)
                }
            ]
        }
        
        # 尝试识别区块链平台
        blockchain_keywords = {
            'ethereum': ['ethereum', 'eth', 'erc20', 'erc721'],
            'binance': ['binance', 'bsc', 'bnb', 'bep20'],
            'solana': ['solana', 'sol'],
            'polygon': ['polygon', 'matic'],
            'avalanche': ['avalanche', 'avax'],
            'arbitrum': ['arbitrum', 'arb'],
            'optimism': ['optimism', 'op'],
            'base': ['base'],
            'cosmos': ['cosmos', 'atom'],
            'polkadot': ['polkadot', 'dot'],
            'near': ['near'],
            'aptos': ['aptos', 'apt'],
            'sui': ['sui']
        }
        
        for blockchain, keywords in blockchain_keywords.items():
            if any(keyword in repo_text for keyword in keywords):
                project_info['blockchain'] = blockchain
                break
        
        # 提取项目类型
        if 'testnet' in repo_text:
            project_info['project_type'] = 'testnet'
        elif 'presale' in repo_text or 'ico' in repo_text or 'ido' in repo_text:
            project_info['project_type'] = 'presale'
        elif 'farming' in repo_text or 'yield' in repo_text:
            project_info['project_type'] = 'farming'
        elif 'staking' in repo_text:
            project_info['project_type'] = 'staking'
        
        return project_info