#!/usr/bin/env python3
"""
Test Anti-Sybil Agent

测试防女巫智能体的功能和模块导入。
"""

import sys
import asyncio
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """测试模块导入"""
    print("🧪 Testing Anti-Sybil Agent Module Imports")
    print("=" * 50)
    
    try:
        # 测试主模块导入
        print("📁 Testing main module...")
        from anti_sybil import AntiSybilAgent
        print("✅ AntiSybilAgent imported successfully")
        
        # 测试身份管理模块
        print("\\n👤 Testing identity modules...")
        from anti_sybil.identity import IdentityManager
        print("✅ IdentityManager imported successfully")
        
        # 测试指纹模块
        print("\\n🖱️ Testing fingerprint modules...")
        from anti_sybil.fingerprints import BrowserFingerprint
        print("✅ BrowserFingerprint imported successfully")
        
        # 测试行为模块
        print("\\n🎭 Testing behavior modules...")
        from anti_sybil.behaviors import BehaviorDesigner
        print("✅ BehaviorDesigner imported successfully")
        
        # 测试模拟器模块
        print("\\n🤖 Testing simulator modules...")
        from anti_sybil.simulators import HumanSimulator
        print("✅ HumanSimulator imported successfully")
        
        # 测试检测规避模块
        print("\\n🔍 Testing detection evasion modules...")
        from anti_sybil.detection_evasion import BotDetectorAnalyzer
        print("✅ BotDetectorAnalyzer imported successfully")
        
        # 测试分析模块
        print("\\n📊 Testing analytics modules...")
        from anti_sybil.analytics import DetectionRiskAnalyzer
        print("✅ DetectionRiskAnalyzer imported successfully")
        
        print("\\n🎉 All module imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_functionality():
    """测试基本功能"""
    print("\\n🧪 Testing Anti-Sybil Agent Functionality")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from anti_sybil import AntiSybilAgent
        
        # 创建配置
        config = {
            'identity': {
                'max_identities_per_project': 10,
                'storage_path': 'data/anti_sybil/test_identities.json'
            },
            'fingerprints': {
                'rotation_interval': 3600
            },
            'behaviors': {
                'adaptation_enabled': True
            },
            'simulators': {
                'human_delay_range': (0.1, 0.5)
            },
            'detection_evasion': {
                'captcha_solving_enabled': False
            },
            'analytics': {
                'risk_threshold': 0.7
            }
        }
        
        # 初始化智能体
        print("🚀 Initializing Anti-Sybil Agent...")
        agent = AntiSybilAgent(config)
        
        success = await agent.initialize()
        if success:
            print("✅ Anti-Sybil Agent initialized successfully")
        else:
            print("❌ Failed to initialize Anti-Sybil Agent")
            return False
        
        # 测试身份创建
        print("\\n👤 Testing identity creation...")
        identity_id = await agent.create_identity("test_project_001", "normal")
        if identity_id:
            print(f"✅ Identity created: {identity_id}")
        else:
            print("❌ Failed to create identity")
            return False
        
        # 测试会话启动
        print("\\n🔗 Testing session start...")
        session_id = await agent.start_session(identity_id, "https://example.com")
        if session_id:
            print(f"✅ Session started: {session_id}")
        else:
            print("❌ Failed to start session")
            return False
        
        # 测试任务执行
        print("\\n⚡ Testing task execution...")
        task_data = {
            "type": "click_button",
            "target": "#submit-btn",
            "delay": 1.0
        }
        
        task_success = await agent.execute_task(task_data)
        if task_success:
            print("✅ Task executed successfully")
        else:
            print("❌ Failed to execute task")
        
        # 测试统计信息
        print("\\n📊 Testing statistics...")
        stats = await agent.get_statistics()
        if stats:
            print("✅ Statistics retrieved:")
            print(f"   - Agent stats: {stats.get('agent_stats', {})}")
            print(f"   - Identity stats: {stats.get('identity_stats', {})}")
            print(f"   - Current identity: {stats.get('current_identity')}")
        else:
            print("❌ Failed to get statistics")
        
        # 测试会话结束
        print("\\n🔚 Testing session end...")
        end_success = await agent.end_session()
        if end_success:
            print("✅ Session ended successfully")
        else:
            print("❌ Failed to end session")
        
        print("\\n🎉 All functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Functionality test error: {e}")
        logger.exception("Detailed error:")
        return False

async def test_individual_modules():
    """测试各个模块的独立功能"""
    print("\\n🧪 Testing Individual Module Functionality")
    print("=" * 50)
    
    try:
        # 测试身份管理器
        print("👤 Testing IdentityManager...")
        from anti_sybil.identity import IdentityManager
        
        identity_config = {'storage_path': 'data/anti_sybil/test_identities.json'}
        identity_manager = IdentityManager(identity_config)
        
        await identity_manager.initialize()
        identity = await identity_manager.create_identity("test_project", "normal")
        
        if identity:
            print(f"✅ Identity created: {identity.id}")
        else:
            print("❌ Failed to create identity")
        
        # 测试浏览器指纹
        print("\\n🖱️ Testing BrowserFingerprint...")
        from anti_sybil.fingerprints import BrowserFingerprint
        
        fingerprint_config = {}
        fingerprint_manager = BrowserFingerprint(fingerprint_config)
        
        await fingerprint_manager.initialize()
        fingerprint = await fingerprint_manager.generate_fingerprint("test_identity")
        
        if fingerprint:
            print(f"✅ Fingerprint generated: {fingerprint['id']}")
            print(f"   - User Agent: {fingerprint['user_agent'][:50]}...")
            print(f"   - Screen Resolution: {fingerprint['screen_resolution']}")
            print(f"   - Language: {fingerprint['language']}")
        else:
            print("❌ Failed to generate fingerprint")
        
        # 测试行为设计器
        print("\\n🎭 Testing BehaviorDesigner...")
        from anti_sybil.behaviors import BehaviorDesigner
        
        behavior_config = {}
        behavior_designer = BehaviorDesigner(behavior_config)
        
        await behavior_designer.initialize()
        behavior_profile = await behavior_designer.design_behavior("test_identity", "normal")
        
        if behavior_profile:
            print(f"✅ Behavior profile created for identity: {behavior_profile['identity_id']}")
            print(f"   - Mouse Speed: {behavior_profile['mouse_speed']:.2f}")
            print(f"   - Typing Speed: {behavior_profile['typing_speed']} WPM")
            print(f"   - Browsing Style: {behavior_profile['browsing_style']}")
        else:
            print("❌ Failed to create behavior profile")
        
        # 测试风险分析器
        print("\\n📊 Testing DetectionRiskAnalyzer...")
        from anti_sybil.analytics import DetectionRiskAnalyzer
        
        analytics_config = {}
        risk_analyzer = DetectionRiskAnalyzer(analytics_config)
        
        await risk_analyzer.initialize()
        risk_level = await risk_analyzer.analyze_risk("https://example.com", identity)
        
        if risk_level is not None:
            print(f"✅ Risk analysis completed: {risk_level:.2f}")
        else:
            print("❌ Failed to analyze risk")
        
        print("\\n🎉 All individual module tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Individual module test error: {e}")
        logger.exception("Detailed error:")
        return False

def check_file_structure():
    """检查文件结构完整性"""
    print("\\n🧪 Checking Anti-Sybil File Structure")
    print("=" * 50)
    
    required_files = [
        "anti_sybil/__init__.py",
        "anti_sybil/anti_sybil_agent.py",
        "anti_sybil/identity/__init__.py",
        "anti_sybil/identity/identity_manager.py",
        "anti_sybil/fingerprints/__init__.py", 
        "anti_sybil/fingerprints/browser_fingerprint.py",
        "anti_sybil/behaviors/__init__.py",
        "anti_sybil/behaviors/behavior_designer.py",
        "anti_sybil/simulators/__init__.py",
        "anti_sybil/simulators/human_simulator.py",
        "anti_sybil/detection_evasion/__init__.py",
        "anti_sybil/detection_evasion/bot_detector_analyzer.py",
        "anti_sybil/analytics/__init__.py",
        "anti_sybil/analytics/detection_risk_analyzer.py"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            existing_files.append(file_path)
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path} - MISSING")
    
    print(f"\\n📊 File Structure Summary:")
    print(f"   - Existing files: {len(existing_files)}")
    print(f"   - Missing files: {len(missing_files)}")
    
    if missing_files:
        print(f"\\n⚠️ Missing files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("\\n🎉 All required files exist!")
        return True

async def main():
    """主测试函数"""
    print("🛡️ Anti-Sybil Agent Testing Suite")
    print("=" * 60)
    
    # 检查文件结构
    structure_ok = check_file_structure()
    
    # 测试模块导入
    imports_ok = test_imports()
    
    if not imports_ok:
        print("\\n❌ Module import tests failed. Cannot proceed with functionality tests.")
        return False
    
    # 测试基本功能
    functionality_ok = await test_functionality()
    
    # 测试各个模块
    modules_ok = await test_individual_modules()
    
    # 总结
    print("\\n" + "=" * 60)
    print("🏆 Anti-Sybil Agent Test Results:")
    print(f"   - File Structure: {'✅ PASS' if structure_ok else '❌ FAIL'}")
    print(f"   - Module Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"   - Basic Functionality: {'✅ PASS' if functionality_ok else '❌ FAIL'}")
    print(f"   - Individual Modules: {'✅ PASS' if modules_ok else '❌ FAIL'}")
    
    all_passed = structure_ok and imports_ok and functionality_ok and modules_ok
    
    if all_passed:
        print("\\n🎉 All tests passed! Anti-Sybil Agent is working correctly!")
    else:
        print("\\n⚠️ Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())
