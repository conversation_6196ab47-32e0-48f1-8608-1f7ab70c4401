"""
修复permission_analyzer.py文件
"""

import os

def fix_file():
    file_path = 'assessment/security/permission_analyzer.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 确保文件以return语句结束
    if len(lines) > 950:
        lines = lines[:950]
    
    with open(file_path + '.new', 'w', encoding='utf-8') as f:
        for line in lines:
            f.write(line)
    
    # 先删除原文件，再重命名
    if os.path.exists(file_path):
        os.remove(file_path)
    os.rename(file_path + '.new', file_path)
    print('文件修复完成')

if __name__ == '__main__':
    fix_file()