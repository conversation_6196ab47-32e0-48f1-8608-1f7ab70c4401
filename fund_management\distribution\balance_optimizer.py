"""
Balance Optimizer

余额优化器，负责优化账户间的余额分布。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime


class BalanceOptimizer:
    """
    余额优化器
    
    负责分析和优化多个账户之间的余额分布，确保资金使用效率最大化。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化余额优化器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 优化历史
        self.optimization_history: List[Dict] = []
        
        # 优化统计
        self.optimization_stats = {
            'total_optimizations': 0,
            'successful_optimizations': 0,
            'total_savings': 0.0,
            'efficiency_improvements': 0.0
        }
    
    async def initialize(self) -> bool:
        """
        初始化余额优化器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("Balance Optimizer initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Balance Optimizer: {e}")
            return False
    
    async def optimize_balances(self, accounts: List[Dict], 
                              optimization_goal: str = "efficiency") -> Dict[str, Any]:
        """
        优化账户余额
        
        Args:
            accounts: 账户列表
            optimization_goal: 优化目标 (efficiency/risk/cost)
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        try:
            self.optimization_stats['total_optimizations'] += 1
            
            # 分析当前余额分布
            current_analysis = await self._analyze_current_distribution(accounts)
            
            # 生成优化建议
            optimization_plan = await self._generate_optimization_plan(
                accounts, current_analysis, optimization_goal
            )
            
            # 计算优化效果
            optimization_impact = await self._calculate_optimization_impact(
                current_analysis, optimization_plan
            )
            
            optimization_result = {
                "optimization_id": f"opt_{len(self.optimization_history) + 1}",
                "goal": optimization_goal,
                "current_analysis": current_analysis,
                "optimization_plan": optimization_plan,
                "expected_impact": optimization_impact,
                "optimized_at": datetime.utcnow().isoformat()
            }
            
            self.optimization_history.append(optimization_result)
            
            if optimization_impact["efficiency_gain"] > 0:
                self.optimization_stats['successful_optimizations'] += 1
                self.optimization_stats['efficiency_improvements'] += optimization_impact["efficiency_gain"]
            
            self.logger.info(f"Balance optimization completed: {optimization_impact['efficiency_gain']:.2%} efficiency gain")
            return optimization_result
            
        except Exception as e:
            self.logger.error(f"Error optimizing balances: {e}")
            return {"success": False, "error": str(e)}
    
    async def _analyze_current_distribution(self, accounts: List[Dict]) -> Dict[str, Any]:
        """分析当前余额分布"""
        try:
            total_balance = sum(acc.get("balance", 0.0) for acc in accounts)
            
            if total_balance == 0:
                return {"total_balance": 0, "distribution_score": 0, "imbalance_ratio": 0}
            
            # 计算分布均匀性
            balances = [acc.get("balance", 0.0) for acc in accounts]
            avg_balance = total_balance / len(accounts)
            
            # 计算标准差
            variance = sum((b - avg_balance) ** 2 for b in balances) / len(balances)
            std_dev = variance ** 0.5
            
            # 计算分布评分 (0-1, 1为完全均匀)
            distribution_score = max(0, 1 - (std_dev / avg_balance)) if avg_balance > 0 else 0
            
            # 计算不平衡比率
            max_balance = max(balances)
            min_balance = min(balances)
            imbalance_ratio = (max_balance - min_balance) / total_balance if total_balance > 0 else 0
            
            return {
                "total_balance": total_balance,
                "average_balance": avg_balance,
                "distribution_score": round(distribution_score, 3),
                "imbalance_ratio": round(imbalance_ratio, 3),
                "std_deviation": round(std_dev, 2),
                "account_count": len(accounts)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing current distribution: {e}")
            return {}
    
    async def _generate_optimization_plan(self, accounts: List[Dict], 
                                        current_analysis: Dict, goal: str) -> Dict[str, Any]:
        """生成优化计划"""
        try:
            if goal == "efficiency":
                return await self._generate_efficiency_plan(accounts, current_analysis)
            elif goal == "risk":
                return await self._generate_risk_plan(accounts, current_analysis)
            elif goal == "cost":
                return await self._generate_cost_plan(accounts, current_analysis)
            else:
                return await self._generate_efficiency_plan(accounts, current_analysis)
                
        except Exception as e:
            self.logger.error(f"Error generating optimization plan: {e}")
            return {}
    
    async def _generate_efficiency_plan(self, accounts: List[Dict], analysis: Dict) -> Dict[str, Any]:
        """生成效率优化计划"""
        try:
            target_balance = analysis["average_balance"]
            transfers = []
            
            for account in accounts:
                current_balance = account.get("balance", 0.0)
                difference = current_balance - target_balance
                
                if abs(difference) > target_balance * 0.1:  # 10%阈值
                    if difference > 0:
                        # 余额过多，需要转出
                        transfers.append({
                            "from_account": account.get("id"),
                            "action": "transfer_out",
                            "amount": difference * 0.8,  # 转出80%的超额部分
                            "reason": "Balance too high"
                        })
                    else:
                        # 余额不足，需要转入
                        transfers.append({
                            "to_account": account.get("id"),
                            "action": "transfer_in",
                            "amount": abs(difference) * 0.8,
                            "reason": "Balance too low"
                        })
            
            return {
                "strategy": "efficiency",
                "target_distribution": "equal",
                "transfers": transfers,
                "expected_distribution_score": 0.9
            }
            
        except Exception as e:
            self.logger.error(f"Error generating efficiency plan: {e}")
            return {}
    
    async def _generate_risk_plan(self, accounts: List[Dict], analysis: Dict) -> Dict[str, Any]:
        """生成风险优化计划"""
        try:
            # 基于风险级别分配资金
            low_risk_accounts = [acc for acc in accounts if acc.get("risk_level", 0.5) < 0.3]
            high_risk_accounts = [acc for acc in accounts if acc.get("risk_level", 0.5) > 0.7]
            
            transfers = []
            
            # 从高风险账户转移资金到低风险账户
            for high_risk_acc in high_risk_accounts:
                current_balance = high_risk_acc.get("balance", 0.0)
                if current_balance > analysis["average_balance"]:
                    transfer_amount = current_balance * 0.3  # 转移30%
                    
                    transfers.append({
                        "from_account": high_risk_acc.get("id"),
                        "action": "transfer_out",
                        "amount": transfer_amount,
                        "reason": "High risk account"
                    })
            
            return {
                "strategy": "risk_minimization",
                "target_distribution": "risk_weighted",
                "transfers": transfers,
                "expected_risk_reduction": 0.2
            }
            
        except Exception as e:
            self.logger.error(f"Error generating risk plan: {e}")
            return {}
    
    async def _generate_cost_plan(self, accounts: List[Dict], analysis: Dict) -> Dict[str, Any]:
        """生成成本优化计划"""
        try:
            # 基于交易费用优化
            low_fee_accounts = [acc for acc in accounts if acc.get("transaction_fee", 0.01) < 0.005]
            
            transfers = []
            
            # 将资金集中到低费用账户
            for account in accounts:
                if account not in low_fee_accounts:
                    current_balance = account.get("balance", 0.0)
                    if current_balance > 0:
                        transfers.append({
                            "from_account": account.get("id"),
                            "action": "transfer_out",
                            "amount": current_balance * 0.5,
                            "reason": "High transaction fees"
                        })
            
            return {
                "strategy": "cost_minimization",
                "target_distribution": "fee_optimized",
                "transfers": transfers,
                "expected_cost_savings": 0.15
            }
            
        except Exception as e:
            self.logger.error(f"Error generating cost plan: {e}")
            return {}
    
    async def _calculate_optimization_impact(self, current_analysis: Dict, 
                                           optimization_plan: Dict) -> Dict[str, Any]:
        """计算优化影响"""
        try:
            current_score = current_analysis.get("distribution_score", 0)
            expected_score = optimization_plan.get("expected_distribution_score", current_score)
            
            efficiency_gain = expected_score - current_score
            cost_savings = optimization_plan.get("expected_cost_savings", 0)
            risk_reduction = optimization_plan.get("expected_risk_reduction", 0)
            
            return {
                "efficiency_gain": efficiency_gain,
                "cost_savings": cost_savings,
                "risk_reduction": risk_reduction,
                "transfer_count": len(optimization_plan.get("transfers", [])),
                "overall_improvement": (efficiency_gain + cost_savings + risk_reduction) / 3
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating optimization impact: {e}")
            return {"efficiency_gain": 0, "cost_savings": 0, "risk_reduction": 0}
