"""
Browser profile manager

This module provides utilities for managing browser profiles.
"""

import os
import json
import shutil
import logging
from typing import Dict, List, Optional
from pathlib import Path
import sqlite3
import tempfile

class ProfileManager:
    """Manager for browser profiles"""
    
    def __init__(self, profiles_dir: str):
        """
        Initialize profile manager
        
        Args:
            profiles_dir: Base directory for profiles
        """
        self.profiles_dir = profiles_dir
        self.profiles = {}
        self.logger = logging.getLogger(__name__)
        os.makedirs(profiles_dir, exist_ok=True)
    
    def create_profile(
        self,
        profile_name: str,
        settings: Optional[Dict] = None
    ) -> str:
        """
        Create new browser profile
        
        Args:
            profile_name: Name of the profile
            settings: Optional profile settings
            
        Returns:
            str: Profile directory path
        """
        try:
            profile_dir = os.path.join(self.profiles_dir, profile_name)
            os.makedirs(profile_dir, exist_ok=True)
            
            # Initialize profile structure
            os.makedirs(os.path.join(profile_dir, 'Default'), exist_ok=True)
            
            # Create basic profile files
            self._create_preferences(profile_dir, settings or {})
            self._create_bookmarks(profile_dir)
            self._create_secure_preferences(profile_dir)
            
            self.profiles[profile_name] = {
                'path': profile_dir,
                'settings': settings or {}
            }
            
            self.logger.info(f"Created profile {profile_name}")
            return profile_dir
            
        except Exception as e:
            self.logger.error(f"Failed to create profile: {str(e)}")
            raise
    
    def _create_preferences(self, profile_dir: str, settings: Dict) -> None:
        """
        Create preferences file
        
        Args:
            profile_dir: Profile directory
            settings: Profile settings
        """
        preferences = {
            'browser': {
                'custom_chrome_frame': True,
                'window_placement': {}
            },
            'profile': {
                'name': os.path.basename(profile_dir),
                'avatar_index': 0
            },
            'extensions': {
                'settings': {}
            }
        }
        preferences.update(settings)
        
        prefs_file = os.path.join(profile_dir, 'Default', 'Preferences')
        with open(prefs_file, 'w') as f:
            json.dump(preferences, f, indent=4)
    
    def _create_bookmarks(self, profile_dir: str) -> None:
        """
        Create bookmarks file
        
        Args:
            profile_dir: Profile directory
        """
        bookmarks = {
            'version': 1,
            'roots': {
                'bookmark_bar': {
                    'children': [],
                    'type': 'folder',
                    'name': 'Bookmarks Bar'
                },
                'other': {
                    'children': [],
                    'type': 'folder',
                    'name': 'Other Bookmarks'
                }
            }
        }
        
        bookmarks_file = os.path.join(profile_dir, 'Default', 'Bookmarks')
        with open(bookmarks_file, 'w') as f:
            json.dump(bookmarks, f, indent=4)
    
    def _create_secure_preferences(self, profile_dir: str) -> None:
        """
        Create secure preferences file
        
        Args:
            profile_dir: Profile directory
        """
        secure_prefs = {
            'protection': {
                'macs': {}
            },
            'version': 1
        }
        
        prefs_file = os.path.join(profile_dir, 'Default', 'Secure Preferences')
        with open(prefs_file, 'w') as f:
            json.dump(secure_prefs, f, indent=4)
    
    def import_profile(
        self,
        source_path: str,
        profile_name: str
    ) -> str:
        """
        Import existing profile
        
        Args:
            source_path: Source profile directory
            profile_name: Name for imported profile
            
        Returns:
            str: Imported profile directory path
        """
        try:
            profile_dir = os.path.join(self.profiles_dir, profile_name)
            shutil.copytree(source_path, profile_dir)
            
            # Load profile settings
            prefs_file = os.path.join(profile_dir, 'Default', 'Preferences')
            with open(prefs_file, 'r') as f:
                settings = json.load(f)
            
            self.profiles[profile_name] = {
                'path': profile_dir,
                'settings': settings
            }
            
            self.logger.info(f"Imported profile {profile_name}")
            return profile_dir
            
        except Exception as e:
            self.logger.error(f"Failed to import profile: {str(e)}")
            raise
    
    def export_profile(
        self,
        profile_name: str,
        destination_path: str
    ) -> None:
        """
        Export profile
        
        Args:
            profile_name: Profile to export
            destination_path: Export destination
        """
        try:
            profile_dir = self.get_profile_path(profile_name)
            if profile_dir:
                shutil.copytree(profile_dir, destination_path)
                self.logger.info(f"Exported profile {profile_name}")
            else:
                raise ValueError(f"Profile {profile_name} not found")
                
        except Exception as e:
            self.logger.error(f"Failed to export profile: {str(e)}")
            raise
    
    def delete_profile(self, profile_name: str) -> None:
        """
        Delete profile
        
        Args:
            profile_name: Profile to delete
        """
        try:
            profile_dir = self.get_profile_path(profile_name)
            if profile_dir:
                shutil.rmtree(profile_dir)
                self.profiles.pop(profile_name, None)
                self.logger.info(f"Deleted profile {profile_name}")
            else:
                self.logger.warning(f"Profile {profile_name} not found")
                
        except Exception as e:
            self.logger.error(f"Failed to delete profile: {str(e)}")
            raise
    
    def get_profile_path(self, profile_name: str) -> Optional[str]:
        """
        Get profile directory path
        
        Args:
            profile_name: Profile name
            
        Returns:
            Optional[str]: Profile directory path or None if not found
        """
        profile = self.profiles.get(profile_name)
        return profile['path'] if profile else None
    
    def list_profiles(self) -> Dict[str, Dict]:
        """
        List available profiles
        
        Returns:
            Dict[str, Dict]: Dictionary of profiles
        """
        return self.profiles
    
    def update_settings(
        self,
        profile_name: str,
        settings: Dict
    ) -> None:
        """
        Update profile settings
        
        Args:
            profile_name: Profile to update
            settings: New settings
        """
        try:
            profile_dir = self.get_profile_path(profile_name)
            if not profile_dir:
                raise ValueError(f"Profile {profile_name} not found")
                
            # Update preferences file
            prefs_file = os.path.join(profile_dir, 'Default', 'Preferences')
            with open(prefs_file, 'r') as f:
                preferences = json.load(f)
            
            preferences.update(settings)
            
            with open(prefs_file, 'w') as f:
                json.dump(preferences, f, indent=4)
            
            # Update profile settings
            self.profiles[profile_name]['settings'].update(settings)
            
            self.logger.info(f"Updated settings for profile {profile_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to update profile settings: {str(e)}")
            raise
    
    def cleanup(self) -> None:
        """Clean up all profiles"""
        try:
            if os.path.exists(self.profiles_dir):
                shutil.rmtree(self.profiles_dir)
            self.profiles = {}
            self.logger.info("Cleaned up all profiles")
        except Exception as e:
            self.logger.error(f"Failed to cleanup profiles: {str(e)}")

# Create global profile manager instance
profile_manager = ProfileManager(os.path.join(os.getcwd(), 'data', 'browser_profiles'))
