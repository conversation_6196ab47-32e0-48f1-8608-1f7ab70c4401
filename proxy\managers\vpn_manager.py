"""VPN Manager - VPN管理器"""
import logging

class VPNManager:
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.active_vpn = None
    
    def connect_vpn(self, vpn_config: dict) -> bool:
        """连接VPN"""
        try:
            self.active_vpn = vpn_config
            self.logger.info(f"Connected to VPN: {vpn_config.get('name')}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect VPN: {e}")
            return False
    
    def disconnect_vpn(self) -> bool:
        """断开VPN"""
        try:
            if self.active_vpn:
                self.logger.info(f"Disconnected from VPN: {self.active_vpn.get('name')}")
                self.active_vpn = None
            return True
        except Exception as e:
            self.logger.error(f"Failed to disconnect VPN: {e}")
            return False
    
    def get_vpn_status(self) -> dict:
        """获取VPN状态"""
        return {
            "connected": self.active_vpn is not None,
            "active_vpn": self.active_vpn
        }
