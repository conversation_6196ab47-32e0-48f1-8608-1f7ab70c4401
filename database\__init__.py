"""
AirHunter Database System

This module provides comprehensive database management capabilities for the AirHunter system,
including data models, connection management, migrations, and data services.
"""

from .core.db_manager import DatabaseManager
from .models import (
    ProjectModel,
    WalletModel,
    ProxyModel,
    TaskModel,
    AccountModel
)
from .services import (
    DataService,
    CacheService,
    BackupService,
    RecoveryService
)

__version__ = "1.0.0"
__all__ = [
    "DatabaseManager",
    "ProjectModel",
    "WalletModel", 
    "ProxyModel",
    "TaskModel",
    "AccountModel",
    "DataService",
    "CacheService",
    "BackupService",
    "RecoveryService"
]
