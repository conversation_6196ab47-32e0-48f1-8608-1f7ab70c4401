"""
负载均衡器

该模块提供负载均衡功能，负责在多个智能体之间分配工作负载。
"""

import logging
import threading
import time
import random
from typing import Dict, List, Any, Optional, Set, Tuple

from coordinator.core.agent_registry import AgentRegistry


class LoadBalancer:
    """负载均衡器，负责在多个智能体之间分配工作负载"""
    
    def __init__(self, agent_registry: AgentRegistry):
        """
        初始化负载均衡器
        
        Args:
            agent_registry: 智能体注册表实例
        """
        self.logger = logging.getLogger(__name__)
        self.agent_registry = agent_registry
        self._lock = threading.RLock()
        self._agent_loads: Dict[str, float] = {}  # agent_name -> load
        self._agent_capacities: Dict[str, float] = {}  # agent_name -> capacity
        self._agent_groups: Dict[str, Set[str]] = {}  # group_name -> set of agent_names
    
    def register_agent(self, agent_name: str, capacity: float = 100.0) -> None:
        """
        注册智能体
        
        Args:
            agent_name: 智能体名称
            capacity: 智能体容量
        """
        with self._lock:
            if not self.agent_registry.exists(agent_name):
                self.logger.warning(f"智能体 '{agent_name}' 不存在")
                return
            
            self._agent_loads[agent_name] = 0.0
            self._agent_capacities[agent_name] = capacity
            
            self.logger.info(f"已注册智能体 '{agent_name}' (容量: {capacity})")
    
    def unregister_agent(self, agent_name: str) -> bool:
        """
        注销智能体
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_loads:
                self.logger.warning(f"智能体 '{agent_name}' 未注册")
                return False
            
            del self._agent_loads[agent_name]
            del self._agent_capacities[agent_name]
            
            # 从所有组中移除
            for group_name, agents in self._agent_groups.items():
                if agent_name in agents:
                    agents.remove(agent_name)
            
            self.logger.info(f"已注销智能体 '{agent_name}'")
            return True
    
    def create_group(self, group_name: str, agent_names: List[str] = None) -> None:
        """
        创建智能体组
        
        Args:
            group_name: 组名称
            agent_names: 组内智能体名称列表
        """
        with self._lock:
            if group_name in self._agent_groups:
                self.logger.warning(f"组 '{group_name}' 已存在，将被覆盖")
            
            self._agent_groups[group_name] = set()
            
            if agent_names:
                for agent_name in agent_names:
                    if agent_name in self._agent_loads:
                        self._agent_groups[group_name].add(agent_name)
                    else:
                        self.logger.warning(f"智能体 '{agent_name}' 未注册，无法添加到组 '{group_name}'")
            
            self.logger.info(f"已创建组 '{group_name}' (智能体数量: {len(self._agent_groups[group_name])})")
    
    def delete_group(self, group_name: str) -> bool:
        """
        删除智能体组
        
        Args:
            group_name: 组名称
            
        Returns:
            如果成功删除则返回True，否则返回False
        """
        with self._lock:
            if group_name not in self._agent_groups:
                self.logger.warning(f"组 '{group_name}' 不存在")
                return False
            
            del self._agent_groups[group_name]
            
            self.logger.info(f"已删除组 '{group_name}'")
            return True
    
    def add_agent_to_group(self, agent_name: str, group_name: str) -> bool:
        """
        将智能体添加到组
        
        Args:
            agent_name: 智能体名称
            group_name: 组名称
            
        Returns:
            如果成功添加则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_loads:
                self.logger.warning(f"智能体 '{agent_name}' 未注册")
                return False
            
            if group_name not in self._agent_groups:
                self.logger.warning(f"组 '{group_name}' 不存在")
                return False
            
            self._agent_groups[group_name].add(agent_name)
            
            self.logger.info(f"已将智能体 '{agent_name}' 添加到组 '{group_name}'")
            return True
    
    def remove_agent_from_group(self, agent_name: str, group_name: str) -> bool:
        """
        从组中移除智能体
        
        Args:
            agent_name: 智能体名称
            group_name: 组名称
            
        Returns:
            如果成功移除则返回True，否则返回False
        """
        with self._lock:
            if group_name not in self._agent_groups:
                self.logger.warning(f"组 '{group_name}' 不存在")
                return False
            
            if agent_name not in self._agent_groups[group_name]:
                self.logger.warning(f"智能体 '{agent_name}' 不在组 '{group_name}' 中")
                return False
            
            self._agent_groups[group_name].remove(agent_name)
            
            self.logger.info(f"已从组 '{group_name}' 中移除智能体 '{agent_name}'")
            return True
    
    def update_agent_load(self, agent_name: str, load: float) -> bool:
        """
        更新智能体负载
        
        Args:
            agent_name: 智能体名称
            load: 新的负载值
            
        Returns:
            如果成功更新则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_loads:
                self.logger.warning(f"智能体 '{agent_name}' 未注册")
                return False
            
            self._agent_loads[agent_name] = load
            
            self.logger.debug(f"已更新智能体 '{agent_name}' 的负载为 {load}")
            return True
    
    def update_agent_capacity(self, agent_name: str, capacity: float) -> bool:
        """
        更新智能体容量
        
        Args:
            agent_name: 智能体名称
            capacity: 新的容量值
            
        Returns:
            如果成功更新则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_capacities:
                self.logger.warning(f"智能体 '{agent_name}' 未注册")
                return False
            
            self._agent_capacities[agent_name] = capacity
            
            self.logger.info(f"已更新智能体 '{agent_name}' 的容量为 {capacity}")
            return True
    
    def select_agent(self, group_name: Optional[str] = None, strategy: str = "least_loaded") -> Optional[str]:
        """
        选择智能体
        
        Args:
            group_name: 可选的组名称，如果提供则从该组中选择
            strategy: 选择策略，可以是 "least_loaded", "round_robin", "random"
            
        Returns:
            选择的智能体名称，如果没有可用的智能体则返回None
        """
        with self._lock:
            # 确定候选智能体
            candidates = []
            if group_name:
                if group_name not in self._agent_groups:
                    self.logger.warning(f"组 '{group_name}' 不存在")
                    return None
                
                candidates = [agent for agent in self._agent_groups[group_name] if agent in self._agent_loads]
            else:
                candidates = list(self._agent_loads.keys())
            
            if not candidates:
                self.logger.warning("没有可用的智能体")
                return None
            
            # 根据策略选择智能体
            if strategy == "least_loaded":
                # 选择负载最小的智能体
                return min(candidates, key=lambda agent: self._agent_loads[agent] / self._agent_capacities[agent])
            
            elif strategy == "round_robin":
                # 轮询选择
                # 注意：这个实现不是真正的轮询，因为它没有维护状态
                # 在实际实现中，你需要为每个组维护一个索引
                return candidates[0]
            
            elif strategy == "random":
                # 随机选择
                return random.choice(candidates)
            
            else:
                self.logger.warning(f"未知的选择策略: {strategy}")
                return None
    
    def select_agents(self, count: int, group_name: Optional[str] = None, strategy: str = "least_loaded") -> List[str]:
        """
        选择多个智能体
        
        Args:
            count: 要选择的智能体数量
            group_name: 可选的组名称，如果提供则从该组中选择
            strategy: 选择策略，可以是 "least_loaded", "balanced", "random"
            
        Returns:
            选择的智能体名称列表
        """
        with self._lock:
            # 确定候选智能体
            candidates = []
            if group_name:
                if group_name not in self._agent_groups:
                    self.logger.warning(f"组 '{group_name}' 不存在")
                    return []
                
                candidates = [agent for agent in self._agent_groups[group_name] if agent in self._agent_loads]
            else:
                candidates = list(self._agent_loads.keys())
            
            if not candidates:
                self.logger.warning("没有可用的智能体")
                return []
            
            # 限制选择数量
            count = min(count, len(candidates))
            
            # 根据策略选择智能体
            if strategy == "least_loaded":
                # 选择负载最小的智能体
                return sorted(candidates, key=lambda agent: self._agent_loads[agent] / self._agent_capacities[agent])[:count]
            
            elif strategy == "balanced":
                # 平衡负载
                # 计算每个智能体的负载比例
                load_ratios = [(agent, self._agent_loads[agent] / self._agent_capacities[agent]) for agent in candidates]
                # 按负载比例排序
                load_ratios.sort(key=lambda x: x[1])
                # 返回负载最小的智能体
                return [agent for agent, _ in load_ratios[:count]]
            
            elif strategy == "random":
                # 随机选择
                return random.sample(candidates, count)
            
            else:
                self.logger.warning(f"未知的选择策略: {strategy}")
                return []
    
    def get_agent_load(self, agent_name: str) -> Optional[float]:
        """
        获取智能体负载
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            智能体负载，如果智能体未注册则返回None
        """
        with self._lock:
            return self._agent_loads.get(agent_name)
    
    def get_agent_capacity(self, agent_name: str) -> Optional[float]:
        """
        获取智能体容量
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            智能体容量，如果智能体未注册则返回None
        """
        with self._lock:
            return self._agent_capacities.get(agent_name)
    
    def get_agent_load_ratio(self, agent_name: str) -> Optional[float]:
        """
        获取智能体负载比例
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            智能体负载比例（负载/容量），如果智能体未注册则返回None
        """
        with self._lock:
            if agent_name not in self._agent_loads or agent_name not in self._agent_capacities:
                return None
            
            capacity = self._agent_capacities[agent_name]
            if capacity <= 0:
                return float('inf')
            
            return self._agent_loads[agent_name] / capacity
    
    def get_all_agent_loads(self) -> Dict[str, float]:
        """
        获取所有智能体负载
        
        Returns:
            智能体负载字典，键为智能体名称，值为负载
        """
        with self._lock:
            return self._agent_loads.copy()
    
    def get_all_agent_capacities(self) -> Dict[str, float]:
        """
        获取所有智能体容量
        
        Returns:
            智能体容量字典，键为智能体名称，值为容量
        """
        with self._lock:
            return self._agent_capacities.copy()
    
    def get_all_agent_load_ratios(self) -> Dict[str, float]:
        """
        获取所有智能体负载比例
        
        Returns:
            智能体负载比例字典，键为智能体名称，值为负载比例
        """
        with self._lock:
            ratios = {}
            for agent_name in self._agent_loads:
                if agent_name in self._agent_capacities:
                    capacity = self._agent_capacities[agent_name]
                    if capacity <= 0:
                        ratios[agent_name] = float('inf')
                    else:
                        ratios[agent_name] = self._agent_loads[agent_name] / capacity
            
            return ratios
    
    def get_group(self, group_name: str) -> Optional[Set[str]]:
        """
        获取组内智能体
        
        Args:
            group_name: 组名称
            
        Returns:
            组内智能体集合，如果组不存在则返回None
        """
        with self._lock:
            if group_name not in self._agent_groups:
                return None
            
            return self._agent_groups[group_name].copy()
    
    def get_all_groups(self) -> Dict[str, Set[str]]:
        """
        获取所有组
        
        Returns:
            组字典，键为组名称，值为组内智能体集合
        """
        with self._lock:
            return {group_name: agents.copy() for group_name, agents in self._agent_groups.items()}