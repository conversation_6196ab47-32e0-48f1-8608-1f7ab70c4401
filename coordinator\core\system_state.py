"""
系统状态管理器

该模块提供系统状态管理功能，跟踪系统的整体状态和各个组件的状态。
"""

import logging
import threading
import time
from typing import Dict, Any, List, Optional


class SystemState:
    """系统状态管理器，负责跟踪系统的整体状态和各个组件的状态"""
    
    def __init__(self):
        """初始化系统状态管理器"""
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        self._state: Dict[str, Any] = {
            'system': {
                'status': 'initializing',  # initializing, running, degraded, error, stopping, stopped
                'start_time': None,
                'uptime': 0,
                'last_update': time.time(),
                'version': '0.1.0',
            },
            'agents': {},
            'resources': {
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'disk_usage': 0.0,
                'network_usage': 0.0,
            },
            'errors': [],
            'warnings': [],
        }
    
    def set_system_status(self, status: str) -> None:
        """
        设置系统状态
        
        Args:
            status: 系统状态，可以是 'initializing', 'running', 'degraded', 'error', 'stopping', 'stopped'
        """
        with self._lock:
            old_status = self._state['system']['status']
            self._state['system']['status'] = status
            self._state['system']['last_update'] = time.time()
            
            if status == 'running' and old_status == 'initializing':
                self._state['system']['start_time'] = time.time()
            
            self.logger.info(f"系统状态从 '{old_status}' 变更为 '{status}'")
    
    def set_agent_status(self, agent_name: str, status: str) -> None:
        """
        设置智能体状态
        
        Args:
            agent_name: 智能体名称
            status: 智能体状态，可以是 'initializing', 'running', 'degraded', 'error', 'stopping', 'stopped'
        """
        with self._lock:
            if agent_name not in self._state['agents']:
                self._state['agents'][agent_name] = {
                    'status': status,
                    'last_update': time.time(),
                    'errors': [],
                    'warnings': [],
                    'metrics': {},
                }
            else:
                old_status = self._state['agents'][agent_name]['status']
                self._state['agents'][agent_name]['status'] = status
                self._state['agents'][agent_name]['last_update'] = time.time()
                
                self.logger.info(f"智能体 '{agent_name}' 状态从 '{old_status}' 变更为 '{status}'")
    
    def set_agent_metric(self, agent_name: str, metric_name: str, value: Any) -> None:
        """
        设置智能体指标
        
        Args:
            agent_name: 智能体名称
            metric_name: 指标名称
            value: 指标值
        """
        with self._lock:
            if agent_name not in self._state['agents']:
                self.set_agent_status(agent_name, 'unknown')
            
            self._state['agents'][agent_name]['metrics'][metric_name] = value
            self._state['agents'][agent_name]['last_update'] = time.time()
    
    def add_error(self, error_message: str, source: str = 'system', details: Optional[Dict[str, Any]] = None) -> None:
        """
        添加错误信息
        
        Args:
            error_message: 错误信息
            source: 错误来源，可以是 'system' 或智能体名称
            details: 错误详情
        """
        with self._lock:
            error = {
                'message': error_message,
                'timestamp': time.time(),
                'source': source,
                'details': details or {},
            }
            
            self._state['errors'].append(error)
            
            # 限制错误列表大小
            if len(self._state['errors']) > 100:
                self._state['errors'] = self._state['errors'][-100:]
            
            if source != 'system' and source in self._state['agents']:
                self._state['agents'][source]['errors'].append(error)
                
                # 限制智能体错误列表大小
                if len(self._state['agents'][source]['errors']) > 20:
                    self._state['agents'][source]['errors'] = self._state['agents'][source]['errors'][-20:]
            
            self.logger.error(f"[{source}] {error_message}")
    
    def add_warning(self, warning_message: str, source: str = 'system', details: Optional[Dict[str, Any]] = None) -> None:
        """
        添加警告信息
        
        Args:
            warning_message: 警告信息
            source: 警告来源，可以是 'system' 或智能体名称
            details: 警告详情
        """
        with self._lock:
            warning = {
                'message': warning_message,
                'timestamp': time.time(),
                'source': source,
                'details': details or {},
            }
            
            self._state['warnings'].append(warning)
            
            # 限制警告列表大小
            if len(self._state['warnings']) > 100:
                self._state['warnings'] = self._state['warnings'][-100:]
            
            if source != 'system' and source in self._state['agents']:
                self._state['agents'][source]['warnings'].append(warning)
                
                # 限制智能体警告列表大小
                if len(self._state['agents'][source]['warnings']) > 20:
                    self._state['agents'][source]['warnings'] = self._state['agents'][source]['warnings'][-20:]
            
            self.logger.warning(f"[{source}] {warning_message}")
    
    def update_resources(self, cpu_usage: float, memory_usage: float, disk_usage: float, network_usage: float) -> None:
        """
        更新资源使用情况
        
        Args:
            cpu_usage: CPU使用率（0-100）
            memory_usage: 内存使用率（0-100）
            disk_usage: 磁盘使用率（0-100）
            network_usage: 网络使用率（0-100）
        """
        with self._lock:
            self._state['resources']['cpu_usage'] = cpu_usage
            self._state['resources']['memory_usage'] = memory_usage
            self._state['resources']['disk_usage'] = disk_usage
            self._state['resources']['network_usage'] = network_usage
            self._state['system']['last_update'] = time.time()
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            包含系统状态信息的字典
        """
        with self._lock:
            # 更新运行时间
            if self._state['system']['start_time'] is not None:
                self._state['system']['uptime'] = time.time() - self._state['system']['start_time']
            
            # 返回状态的副本
            return self._state.copy()
    
    def get_agent_status(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """
        获取智能体状态
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            包含智能体状态信息的字典，如果智能体不存在则返回None
        """
        with self._lock:
            if agent_name not in self._state['agents']:
                return None
            
            return self._state['agents'][agent_name].copy()
    
    def get_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的错误信息
        
        Args:
            limit: 返回的错误数量限制
            
        Returns:
            包含错误信息的列表
        """
        with self._lock:
            return self._state['errors'][-limit:]
    
    def get_warnings(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的警告信息
        
        Args:
            limit: 返回的警告数量限制
            
        Returns:
            包含警告信息的列表
        """
        with self._lock:
            return self._state['warnings'][-limit:]
    
    def clear_errors(self) -> None:
        """清除所有错误信息"""
        with self._lock:
            self._state['errors'] = []
            
            for agent_name in self._state['agents']:
                self._state['agents'][agent_name]['errors'] = []
    
    def clear_warnings(self) -> None:
        """清除所有警告信息"""
        with self._lock:
            self._state['warnings'] = []
            
            for agent_name in self._state['agents']:
                self._state['agents'][agent_name]['warnings'] = []