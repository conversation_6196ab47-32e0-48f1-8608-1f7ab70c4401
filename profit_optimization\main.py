"""
Profit Optimization Agent Main Module

This is the main entry point for the Profit Optimization Agent.
"""

import os
import sys
import json
import logging
import argparse
import time
from typing import Dict, Any, Optional

# Add parent directory to path to allow imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from profit_optimization.profit_optimization_agent import ProfitOptimizationAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("data/logs/profit_optimization.log")
    ]
)
logger = logging.getLogger("ProfitOptimizationMain")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Profit Optimization Agent")
    parser.add_argument("--config", type=str, default="config/profit_optimization_config.json",
                        help="Path to configuration file")
    parser.add_argument("--action", type=str, 
                        choices=["start", "stop", "status", "add_project", "update_project", 
                                "optimize", "update_roi", "profit_recommendation", 
                                "list_projects", "list_strategies", "portfolio_summary"],
                        default="start", help="Action to perform")
    parser.add_argument("--project_id", type=str, help="Project ID for project-specific actions")
    parser.add_argument("--strategy_id", type=str, help="Strategy ID for strategy-specific actions")
    parser.add_argument("--data_file", type=str, help="JSON file containing data for create/update operations")
    parser.add_argument("--current_value", type=float, help="Current value for ROI update")
    parser.add_argument("--investment", type=float, help="Initial investment for ROI update")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from file."""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        else:
            logger.warning(f"Config file not found: {config_path}")
            return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def load_data_file(data_file: str) -> Dict[str, Any]:
    """Load data from file."""
    try:
        if os.path.exists(data_file):
            with open(data_file, 'r') as f:
                return json.load(f)
        else:
            logger.error(f"Data file not found: {data_file}")
            return {}
    except Exception as e:
        logger.error(f"Error loading data file: {e}")
        return {}

def main():
    """Main entry point for the Profit Optimization Agent."""
    # Parse arguments
    args = parse_arguments()
    
    # Ensure logs directory exists
    os.makedirs("data/logs", exist_ok=True)
    
    # Load configuration
    config = load_config(args.config)
    
    # Create agent
    agent = ProfitOptimizationAgent(args.config if os.path.exists(args.config) else None)
    
    # Perform requested action
    if args.action == "start":
        logger.info("Starting Profit Optimization Agent")
        success = agent.start()
        logger.info(f"Agent started: {success}")
    
    elif args.action == "stop":
        logger.info("Stopping Profit Optimization Agent")
        success = agent.stop()
        logger.info(f"Agent stopped: {success}")
    
    elif args.action == "status":
        agent.start()
        status = agent.status()
        logger.info(f"Agent status: {status}")
        print(json.dumps(status, indent=2))
    
    elif args.action == "add_project":
        agent.start()
        
        if not args.data_file:
            logger.error("Data file is required for add_project action")
            return
        
        project_data = load_data_file(args.data_file)
        if not project_data:
            return
        
        logger.info(f"Adding project: {project_data.get('name', 'Unnamed project')}")
        project_id = agent.add_project(project_data)
        
        if project_id:
            logger.info(f"Project added with ID: {project_id}")
            print(f"Project added with ID: {project_id}")
        else:
            logger.error("Failed to add project")
    
    elif args.action == "update_project":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for update_project action")
            return
        
        if not args.data_file:
            logger.error("Data file is required for update_project action")
            return
        
        project_data = load_data_file(args.data_file)
        if not project_data:
            return
        
        logger.info(f"Updating project: {args.project_id}")
        success = agent.update_project(args.project_id, project_data)
        
        if success:
            logger.info(f"Project updated: {args.project_id}")
            print(f"Project updated: {args.project_id}")
        else:
            logger.error(f"Failed to update project: {args.project_id}")
    
    elif args.action == "optimize":
        agent.start()
        
        logger.info("Optimizing resource allocation")
        result = agent.optimize_resource_allocation(args.strategy_id)
        
        if result["success"]:
            logger.info(f"Optimization completed: {result['total_projects']} projects optimized")
            print(json.dumps(result, indent=2))
        else:
            logger.error(f"Optimization failed: {result.get('error', 'Unknown error')}")
    
    elif args.action == "update_roi":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for update_roi action")
            return
        
        if args.current_value is None:
            logger.error("Current value is required for update_roi action")
            return
        
        logger.info(f"Updating ROI for project {args.project_id}")
        result = agent.update_roi(args.project_id, args.current_value, args.investment)
        
        if result["success"]:
            logger.info(f"ROI updated: {result['roi']:.2f}")
            print(json.dumps(result, indent=2))
        else:
            logger.error(f"Failed to update ROI: {result.get('error', 'Unknown error')}")
    
    elif args.action == "profit_recommendation":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for profit_recommendation action")
            return
        
        logger.info(f"Getting profit taking recommendation for project {args.project_id}")
        result = agent.get_profit_taking_recommendation(args.project_id)
        
        if result["success"]:
            recommendation = result["recommendation"]
            logger.info(f"Recommendation: {recommendation['action']} ({recommendation['percentage']*100:.0f}%)")
            print(json.dumps(recommendation, indent=2))
        else:
            logger.error(f"Failed to get recommendation: {result.get('error', 'Unknown error')}")
    
    elif args.action == "list_projects":
        agent.start()
        
        projects = agent.list_projects()
        
        logger.info(f"Found {len(projects)} projects")
        print(f"Projects ({len(projects)}):")
        for project in projects:
            print(f"- {project['id']}: {project['name']}")
            print(f"  Status: {project['status']}")
            print(f"  Category: {project.get('category', 'N/A')}")
            
            roi = agent.get_project_roi(project['id'])
            print(f"  ROI: {roi:.2f}")
            
            if "analysis" in project:
                print(f"  Adjusted ROI: {project['analysis'].get('adjusted_roi', 0):.2f}")
            
            print()
    
    elif args.action == "list_strategies":
        agent.start()
        
        strategies = agent.list_strategies()
        
        logger.info(f"Found {len(strategies)} strategies")
        print(f"Strategies ({len(strategies)}):")
        for strategy in strategies:
            print(f"- {strategy['id']}: {strategy['name']}")
            print(f"  Description: {strategy['description']}")
            print(f"  Risk tolerance: {strategy['risk_tolerance']}")
            print()
    
    elif args.action == "portfolio_summary":
        agent.start()
        
        summary = agent.get_portfolio_summary()
        
        logger.info("Generated portfolio summary")
        print("Portfolio Summary:")
        print(f"- Total projects: {summary['total_projects']}")
        print(f"- Active projects: {summary['active_projects']}")
        print(f"- Total investment: ${summary['total_investment']:.2f}")
        print(f"- Current value: ${summary['total_current_value']:.2f}")
        print(f"- Portfolio ROI: {summary['portfolio_roi']:.2f}")
        
        if summary.get('best_performing_projects'):
            print("\nBest performing projects:")
            for project in summary['best_performing_projects']:
                print(f"- {project['name']}: ROI {project['roi']:.2f}")
        
        if summary.get('worst_performing_projects'):
            print("\nWorst performing projects:")
            for project in summary['worst_performing_projects']:
                print(f"- {project['name']}: ROI {project['roi']:.2f}")

if __name__ == "__main__":
    main()

import os
import sys
import json
import logging
import argparse
import time
from typing import Dict, Any, Optional

# Add parent directory to path to allow imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from profit_optimization.profit_optimization_agent import ProfitOptimizationAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("data/logs/profit_optimization.log")
    ]
)
logger = logging.getLogger("ProfitOptimizationMain")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Profit Optimization Agent")
    parser.add_argument("--config", type=str, default="config/profit_optimization_config.json",
                        help="Path to configuration file")
    parser.add_argument("--action", type=str, 
                        choices=["start", "stop", "status", "add_project", "update_project", 
                                "optimize", "update_roi", "profit_recommendation", 
                                "list_projects", "list_strategies", "portfolio_summary"],
                        default="start", help="Action to perform")
    parser.add_argument("--project_id", type=str, help="Project ID for project-specific actions")
    parser.add_argument("--strategy_id", type=str, help="Strategy ID for strategy-specific actions")
    parser.add_argument("--data_file", type=str, help="JSON file containing data for create/update operations")
    parser.add_argument("--current_value", type=float, help="Current value for ROI update")
    parser.add_argument("--investment", type=float, help="Initial investment for ROI update")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from file."""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        else:
            logger.warning(f"Config file not found: {config_path}")
            return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def load_data_file(data_file: str) -> Dict[str, Any]:
    """Load data from file."""
    try:
        if os.path.exists(data_file):
            with open(data_file, 'r') as f:
                return json.load(f)
        else:
            logger.error(f"Data file not found: {data_file}")
            return {}
    except Exception as e:
        logger.error(f"Error loading data file: {e}")
        return {}

def main():
    """Main entry point for the Profit Optimization Agent."""
    # Parse arguments
    args = parse_arguments()
    
    # Ensure logs directory exists
    os.makedirs("data/logs", exist_ok=True)
    
    # Load configuration
    config = load_config(args.config)
    
    # Create agent
    agent = ProfitOptimizationAgent(args.config if os.path.exists(args.config) else None)
    
    # Perform requested action
    if args.action == "start":
        logger.info("Starting Profit Optimization Agent")
        success = agent.start()
        logger.info(f"Agent started: {success}")
    
    elif args.action == "stop":
        logger.info("Stopping Profit Optimization Agent")
        success = agent.stop()
        logger.info(f"Agent stopped: {success}")
    
    elif args.action == "status":
        agent.start()
        status = agent.status()
        logger.info(f"Agent status: {status}")
        print(json.dumps(status, indent=2))
    
    elif args.action == "add_project":
        agent.start()
        
        if not args.data_file:
            logger.error("Data file is required for add_project action")
            return
        
        project_data = load_data_file(args.data_file)
        if not project_data:
            return
        
        logger.info(f"Adding project: {project_data.get('name', 'Unnamed project')}")
        project_id = agent.add_project(project_data)
        
        if project_id:
            logger.info(f"Project added with ID: {project_id}")
            print(f"Project added with ID: {project_id}")
        else:
            logger.error("Failed to add project")
    
    elif args.action == "update_project":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for update_project action")
            return
        
        if not args.data_file:
            logger.error("Data file is required for update_project action")
            return
        
        project_data = load_data_file(args.data_file)
        if not project_data:
            return
        
        logger.info(f"Updating project: {args.project_id}")
        success = agent.update_project(args.project_id, project_data)
        
        if success:
            logger.info(f"Project updated: {args.project_id}")
            print(f"Project updated: {args.project_id}")
        else:
            logger.error(f"Failed to update project: {args.project_id}")
    
    elif args.action == "optimize":
        agent.start()
        
        logger.info("Optimizing resource allocation")
        result = agent.optimize_resource_allocation(args.strategy_id)
        
        if result["success"]:
            logger.info(f"Optimization completed: {result['total_projects']} projects optimized")
            print(json.dumps(result, indent=2))
        else:
            logger.error(f"Optimization failed: {result.get('error', 'Unknown error')}")
    
    elif args.action == "update_roi":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for update_roi action")
            return
        
        if args.current_value is None:
            logger.error("Current value is required for update_roi action")
            return
        
        logger.info(f"Updating ROI for project {args.project_id}")
        result = agent.update_roi(args.project_id, args.current_value, args.investment)
        
        if result["success"]:
            logger.info(f"ROI updated: {result['roi']:.2f}")
            print(json.dumps(result, indent=2))
        else:
            logger.error(f"Failed to update ROI: {result.get('error', 'Unknown error')}")
    
    elif args.action == "profit_recommendation":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for profit_recommendation action")
            return
        
        logger.info(f"Getting profit taking recommendation for project {args.project_id}")
        result = agent.get_profit_taking_recommendation(args.project_id)
        
        if result["success"]:
            recommendation = result["recommendation"]
            logger.info(f"Recommendation: {recommendation['action']} ({recommendation['percentage']*100:.0f}%)")
            print(json.dumps(recommendation, indent=2))
        else:
            logger.error(f"Failed to get recommendation: {result.get('error', 'Unknown error')}")
    
    elif args.action == "list_projects":
        agent.start()
        
        projects = agent.list_projects()
        
        logger.info(f"Found {len(projects)} projects")
        print(f"Projects ({len(projects)}):")
        for project in projects:
            print(f"- {project['id']}: {project['name']}")
            print(f"  Status: {project['status']}")
            print(f"  Category: {project.get('category', 'N/A')}")
            
            roi = agent.get_project_roi(project['id'])
            print(f"  ROI: {roi:.2f}")
            
            if "analysis" in project:
                print(f"  Adjusted ROI: {project['analysis'].get('adjusted_roi', 0):.2f}")
            
            print()
    
    elif args.action == "list_strategies":
        agent.start()
        
        strategies = agent.list_strategies()
        
        logger.info(f"Found {len(strategies)} strategies")
        print(f"Strategies ({len(strategies)}):")
        for strategy in strategies:
            print(f"- {strategy['id']}: {strategy['name']}")
            print(f"  Description: {strategy['description']}")
            print(f"  Risk tolerance: {strategy['risk_tolerance']}")
            print()
    
    elif args.action == "portfolio_summary":
        agent.start()
        
        summary = agent.get_portfolio_summary()
        
        logger.info("Generated portfolio summary")
        print("Portfolio Summary:")
        print(f"- Total projects: {summary['total_projects']}")
        print(f"- Active projects: {summary['active_projects']}")
        print(f"- Total investment: ${summary['total_investment']:.2f}")
        print(f"- Current value: ${summary['total_current_value']:.2f}")
        print(f"- Portfolio ROI: {summary['portfolio_roi']:.2f}")
        
        if summary.get('best_performing_projects'):
            print("\nBest performing projects:")
            for project in summary['best_performing_projects']:
                print(f"- {project['name']}: ROI {project['roi']:.2f}")
        
        if summary.get('worst_performing_projects'):
            print("\nWorst performing projects:")
            for project in summary['worst_performing_projects']:
                print(f"- {project['name']}: ROI {project['roi']:.2f}")

if __name__ == "__main__":
    main()

import os
import sys
import json
import logging
import argparse
import time
from typing import Dict, Any, Optional

# Add parent directory to path to allow imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from profit_optimization.profit_optimization_agent import ProfitOptimizationAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("data/logs/profit_optimization.log")
    ]
)
logger = logging.getLogger("ProfitOptimizationMain")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Profit Optimization Agent")
    parser.add_argument("--config", type=str, default="config/profit_optimization_config.json",
                        help="Path to configuration file")
    parser.add_argument("--action", type=str, 
                        choices=["start", "stop", "status", "add_project", "update_project", 
                                "optimize", "update_roi", "profit_recommendation", 
                                "list_projects", "list_strategies", "portfolio_summary"],
                        default="start", help="Action to perform")
    parser.add_argument("--project_id", type=str, help="Project ID for project-specific actions")
    parser.add_argument("--strategy_id", type=str, help="Strategy ID for strategy-specific actions")
    parser.add_argument("--data_file", type=str, help="JSON file containing data for create/update operations")
    parser.add_argument("--current_value", type=float, help="Current value for ROI update")
    parser.add_argument("--investment", type=float, help="Initial investment for ROI update")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from file."""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        else:
            logger.warning(f"Config file not found: {config_path}")
            return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def load_data_file(data_file: str) -> Dict[str, Any]:
    """Load data from file."""
    try:
        if os.path.exists(data_file):
            with open(data_file, 'r') as f:
                return json.load(f)
        else:
            logger.error(f"Data file not found: {data_file}")
            return {}
    except Exception as e:
        logger.error(f"Error loading data file: {e}")
        return {}

def main():
    """Main entry point for the Profit Optimization Agent."""
    # Parse arguments
    args = parse_arguments()
    
    # Ensure logs directory exists
    os.makedirs("data/logs", exist_ok=True)
    
    # Load configuration
    config = load_config(args.config)
    
    # Create agent
    agent = ProfitOptimizationAgent(args.config if os.path.exists(args.config) else None)
    
    # Perform requested action
    if args.action == "start":
        logger.info("Starting Profit Optimization Agent")
        success = agent.start()
        logger.info(f"Agent started: {success}")
    
    elif args.action == "stop":
        logger.info("Stopping Profit Optimization Agent")
        success = agent.stop()
        logger.info(f"Agent stopped: {success}")
    
    elif args.action == "status":
        agent.start()
        status = agent.status()
        logger.info(f"Agent status: {status}")
        print(json.dumps(status, indent=2))
    
    elif args.action == "add_project":
        agent.start()
        
        if not args.data_file:
            logger.error("Data file is required for add_project action")
            return
        
        project_data = load_data_file(args.data_file)
        if not project_data:
            return
        
        logger.info(f"Adding project: {project_data.get('name', 'Unnamed project')}")
        project_id = agent.add_project(project_data)
        
        if project_id:
            logger.info(f"Project added with ID: {project_id}")
            print(f"Project added with ID: {project_id}")
        else:
            logger.error("Failed to add project")
    
    elif args.action == "update_project":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for update_project action")
            return
        
        if not args.data_file:
            logger.error("Data file is required for update_project action")
            return
        
        project_data = load_data_file(args.data_file)
        if not project_data:
            return
        
        logger.info(f"Updating project: {args.project_id}")
        success = agent.update_project(args.project_id, project_data)
        
        if success:
            logger.info(f"Project updated: {args.project_id}")
            print(f"Project updated: {args.project_id}")
        else:
            logger.error(f"Failed to update project: {args.project_id}")
    
    elif args.action == "optimize":
        agent.start()
        
        logger.info("Optimizing resource allocation")
        result = agent.optimize_resource_allocation(args.strategy_id)
        
        if result["success"]:
            logger.info(f"Optimization completed: {result['total_projects']} projects optimized")
            print(json.dumps(result, indent=2))
        else:
            logger.error(f"Optimization failed: {result.get('error', 'Unknown error')}")
    
    elif args.action == "update_roi":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for update_roi action")
            return
        
        if args.current_value is None:
            logger.error("Current value is required for update_roi action")
            return
        
        logger.info(f"Updating ROI for project {args.project_id}")
        result = agent.update_roi(args.project_id, args.current_value, args.investment)
        
        if result["success"]:
            logger.info(f"ROI updated: {result['roi']:.2f}")
            print(json.dumps(result, indent=2))
        else:
            logger.error(f"Failed to update ROI: {result.get('error', 'Unknown error')}")
    
    elif args.action == "profit_recommendation":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for profit_recommendation action")
            return
        
        logger.info(f"Getting profit taking recommendation for project {args.project_id}")
        result = agent.get_profit_taking_recommendation(args.project_id)
        
        if result["success"]:
            recommendation = result["recommendation"]
            logger.info(f"Recommendation: {recommendation['action']} ({recommendation['percentage']*100:.0f}%)")
            print(json.dumps(recommendation, indent=2))
        else:
            logger.error(f"Failed to get recommendation: {result.get('error', 'Unknown error')}")
    
    elif args.action == "list_projects":
        agent.start()
        
        projects = agent.list_projects()
        
        logger.info(f"Found {len(projects)} projects")
        print(f"Projects ({len(projects)}):")
        for project in projects:
            print(f"- {project['id']}: {project['name']}")
            print(f"  Status: {project['status']}")
            print(f"  Category: {project.get('category', 'N/A')}")
            
            roi = agent.get_project_roi(project['id'])
            print(f"  ROI: {roi:.2f}")
            
            if "analysis" in project:
                print(f"  Adjusted ROI: {project['analysis'].get('adjusted_roi', 0):.2f}")
            
            print()
    
    elif args.action == "list_strategies":
        agent.start()
        
        strategies = agent.list_strategies()
        
        logger.info(f"Found {len(strategies)} strategies")
        print(f"Strategies ({len(strategies)}):")
        for strategy in strategies:
            print(f"- {strategy['id']}: {strategy['name']}")
            print(f"  Description: {strategy['description']}")
            print(f"  Risk tolerance: {strategy['risk_tolerance']}")
            print()
    
    elif args.action == "portfolio_summary":
        agent.start()
        
        summary = agent.get_portfolio_summary()
        
        logger.info("Generated portfolio summary")
        print("Portfolio Summary:")
        print(f"- Total projects: {summary['total_projects']}")
        print(f"- Active projects: {summary['active_projects']}")
        print(f"- Total investment: ${summary['total_investment']:.2f}")
        print(f"- Current value: ${summary['total_current_value']:.2f}")
        print(f"- Portfolio ROI: {summary['portfolio_roi']:.2f}")
        
        if summary.get('best_performing_projects'):
            print("\nBest performing projects:")
            for project in summary['best_performing_projects']:
                print(f"- {project['name']}: ROI {project['roi']:.2f}")
        
        if summary.get('worst_performing_projects'):
            print("\nWorst performing projects:")
            for project in summary['worst_performing_projects']:
                print(f"- {project['name']}: ROI {project['roi']:.2f}")

if __name__ == "__main__":
    main()