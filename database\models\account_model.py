"""
Account Model

Database model for social media and platform account management
including authentication, profile data, and usage tracking.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum
import json


class AccountPlatform(Enum):
    """Account platform enumeration."""
    TWITTER = "twitter"
    DISCORD = "discord"
    TELEGRAM = "telegram"
    GITHUB = "github"
    MEDIUM = "medium"
    REDDIT = "reddit"


@dataclass
class AccountModel:
    """Account data model."""
    
    id: Optional[int] = None
    platform: AccountPlatform = AccountPlatform.TWITTER
    username: str = ""
    email: str = ""
    password_hash: str = ""
    auth_token: str = ""
    profile_data: Dict[str, Any] = None
    is_active: bool = True
    is_verified: bool = False
    created_at: datetime = None
    updated_at: datetime = None
    last_used: Optional[datetime] = None
    failure_count: int = 0
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        if self.profile_data is None:
            self.profile_data = {}
    
    @classmethod
    def create_table_sql(cls) -> str:
        """Get SQL for creating accounts table."""
        return """
        CREATE TABLE IF NOT EXISTS accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform TEXT NOT NULL,
            username TEXT NOT NULL,
            email TEXT,
            password_hash TEXT,
            auth_token TEXT,
            profile_data TEXT,
            is_active BOOLEAN DEFAULT 1,
            is_verified BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_used TIMESTAMP,
            failure_count INTEGER DEFAULT 0,
            UNIQUE(platform, username)
        )
        """
