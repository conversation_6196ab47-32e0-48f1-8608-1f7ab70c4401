"""
Decision Maker

Machine learning component for making intelligent decisions based on
patterns, anomalies, and historical data to optimize system performance.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from enum import Enum
import json
import random


class DecisionType(Enum):
    """Types of decisions the system can make."""
    PROJECT_APPROVAL = "project_approval"
    TASK_PRIORITIZATION = "task_prioritization"
    RESOURCE_ALLOCATION = "resource_allocation"
    RISK_ASSESSMENT = "risk_assessment"
    TIMING_OPTIMIZATION = "timing_optimization"


class ConfidenceLevel(Enum):
    """Confidence levels for decisions."""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.9


class Decision:
    """Represents a decision made by the system."""
    
    def __init__(self, decision_type: DecisionType, recommendation: str,
                 confidence: float, reasoning: List[str], data: Dict[str, Any] = None):
        self.decision_type = decision_type
        self.recommendation = recommendation
        self.confidence = confidence
        self.reasoning = reasoning
        self.data = data or {}
        self.timestamp = datetime.utcnow()
        self.executed = False
        self.outcome = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert decision to dictionary."""
        return {
            'decision_type': self.decision_type.value,
            'recommendation': self.recommendation,
            'confidence': self.confidence,
            'reasoning': self.reasoning,
            'data': self.data,
            'timestamp': self.timestamp.isoformat(),
            'executed': self.executed,
            'outcome': self.outcome
        }


class DecisionMaker:
    """
    Intelligent decision making system for AirHunter.
    
    Makes decisions based on:
    - Historical patterns and outcomes
    - Current system state and performance
    - Risk assessment and confidence levels
    - Resource availability and constraints
    """
    
    def __init__(self, min_confidence: float = 0.6):
        """
        Initialize decision maker.
        
        Args:
            min_confidence: Minimum confidence threshold for decisions
        """
        self.min_confidence = min_confidence
        self.logger = logging.getLogger(__name__)
        
        # Decision history
        self.decision_history: List[Decision] = []
        
        # Decision rules and weights
        self.decision_rules = {
            DecisionType.PROJECT_APPROVAL: {
                'risk_score': -0.4,
                'reward_score': 0.3,
                'team_reputation': 0.2,
                'community_size': 0.1
            },
            DecisionType.TASK_PRIORITIZATION: {
                'deadline_urgency': 0.3,
                'reward_potential': 0.25,
                'resource_requirement': -0.2,
                'success_probability': 0.25
            },
            DecisionType.RESOURCE_ALLOCATION: {
                'current_load': -0.3,
                'priority_level': 0.4,
                'efficiency_score': 0.3
            }
        }
        
        # Learning parameters
        self.learning_rate = 0.1
        self.success_threshold = 0.7
        
        # Statistics
        self.stats = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'failed_decisions': 0,
            'accuracy_rate': 0.0,
            'last_decision': None
        }
    
    def make_decision(self, decision_type: DecisionType, 
                     input_data: Dict[str, Any]) -> Optional[Decision]:
        """
        Make a decision based on input data.
        
        Args:
            decision_type: Type of decision to make
            input_data: Input data for decision making
            
        Returns:
            Optional[Decision]: Decision object or None if confidence too low
        """
        try:
            if decision_type == DecisionType.PROJECT_APPROVAL:
                return self._decide_project_approval(input_data)
            elif decision_type == DecisionType.TASK_PRIORITIZATION:
                return self._decide_task_prioritization(input_data)
            elif decision_type == DecisionType.RESOURCE_ALLOCATION:
                return self._decide_resource_allocation(input_data)
            elif decision_type == DecisionType.RISK_ASSESSMENT:
                return self._decide_risk_assessment(input_data)
            elif decision_type == DecisionType.TIMING_OPTIMIZATION:
                return self._decide_timing_optimization(input_data)
            else:
                self.logger.warning(f"Unknown decision type: {decision_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error making decision: {e}")
            return None
    
    def record_outcome(self, decision: Decision, outcome: bool, 
                      feedback: Dict[str, Any] = None):
        """
        Record the outcome of a decision for learning.
        
        Args:
            decision: The decision that was executed
            outcome: Whether the decision was successful
            feedback: Additional feedback data
        """
        try:
            decision.executed = True
            decision.outcome = outcome
            
            # Update statistics
            self.stats['total_decisions'] += 1
            if outcome:
                self.stats['successful_decisions'] += 1
            else:
                self.stats['failed_decisions'] += 1
            
            self.stats['accuracy_rate'] = (
                self.stats['successful_decisions'] / self.stats['total_decisions']
            )
            
            # Learn from outcome
            self._learn_from_outcome(decision, outcome, feedback)
            
            self.logger.info(
                f"Recorded outcome for {decision.decision_type.value}: "
                f"{'success' if outcome else 'failure'}"
            )
            
        except Exception as e:
            self.logger.error(f"Error recording outcome: {e}")
    
    def get_decision_insights(self, decision_type: DecisionType = None) -> Dict[str, Any]:
        """
        Get insights about decision making performance.
        
        Args:
            decision_type: Filter by decision type
            
        Returns:
            Dict[str, Any]: Decision insights
        """
        try:
            decisions = self.decision_history
            if decision_type:
                decisions = [d for d in decisions if d.decision_type == decision_type]
            
            if not decisions:
                return {"message": "No decisions found"}
            
            # Calculate metrics
            total = len(decisions)
            executed = len([d for d in decisions if d.executed])
            successful = len([d for d in decisions if d.outcome is True])
            
            avg_confidence = sum(d.confidence for d in decisions) / total
            
            # Confidence vs success correlation
            high_conf_decisions = [d for d in decisions if d.confidence > 0.8]
            high_conf_success_rate = 0
            if high_conf_decisions:
                high_conf_successful = len([d for d in high_conf_decisions if d.outcome is True])
                high_conf_success_rate = high_conf_successful / len(high_conf_decisions)
            
            return {
                "total_decisions": total,
                "executed_decisions": executed,
                "successful_decisions": successful,
                "success_rate": successful / executed if executed > 0 else 0,
                "average_confidence": avg_confidence,
                "high_confidence_success_rate": high_conf_success_rate,
                "decision_type_distribution": self._get_type_distribution(decisions),
                "recent_decisions": [d.to_dict() for d in decisions[-5:]],
                "statistics": self.stats
            }
            
        except Exception as e:
            self.logger.error(f"Error generating insights: {e}")
            return {"error": str(e)}
    
    def _decide_project_approval(self, data: Dict[str, Any]) -> Optional[Decision]:
        """Make project approval decision."""
        try:
            # Extract features
            risk_score = data.get('risk_score', 0.5)
            reward_score = data.get('reward_score', 0.5)
            team_reputation = data.get('team_reputation', 0.5)
            community_size = data.get('community_size', 0)
            
            # Normalize community size
            normalized_community = min(1.0, community_size / 10000)
            
            # Calculate decision score
            rules = self.decision_rules[DecisionType.PROJECT_APPROVAL]
            score = (
                risk_score * rules['risk_score'] +
                reward_score * rules['reward_score'] +
                team_reputation * rules['team_reputation'] +
                normalized_community * rules['community_size']
            )
            
            # Adjust score to 0-1 range
            score = (score + 1) / 2
            
            # Make recommendation
            if score > 0.6:
                recommendation = "APPROVE"
                confidence = min(0.95, score + 0.1)
            elif score > 0.4:
                recommendation = "REVIEW"
                confidence = 0.5
            else:
                recommendation = "REJECT"
                confidence = min(0.95, 1 - score + 0.1)
            
            # Build reasoning
            reasoning = []
            if risk_score < 0.3:
                reasoning.append("Low risk score indicates safe project")
            elif risk_score > 0.7:
                reasoning.append("High risk score raises concerns")
            
            if reward_score > 0.7:
                reasoning.append("High reward potential")
            elif reward_score < 0.3:
                reasoning.append("Low reward potential")
            
            if team_reputation > 0.8:
                reasoning.append("Strong team reputation")
            elif team_reputation < 0.3:
                reasoning.append("Weak team reputation")
            
            if confidence >= self.min_confidence:
                decision = Decision(
                    DecisionType.PROJECT_APPROVAL,
                    recommendation,
                    confidence,
                    reasoning,
                    data
                )
                self.decision_history.append(decision)
                return decision
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error in project approval decision: {e}")
            return None
    
    def _decide_task_prioritization(self, data: Dict[str, Any]) -> Optional[Decision]:
        """Make task prioritization decision."""
        try:
            tasks = data.get('tasks', [])
            if not tasks:
                return None
            
            # Score each task
            scored_tasks = []
            for task in tasks:
                deadline_urgency = self._calculate_urgency(task.get('deadline'))
                reward_potential = task.get('reward_potential', 0.5)
                resource_requirement = task.get('resource_requirement', 0.5)
                success_probability = task.get('success_probability', 0.5)
                
                rules = self.decision_rules[DecisionType.TASK_PRIORITIZATION]
                score = (
                    deadline_urgency * rules['deadline_urgency'] +
                    reward_potential * rules['reward_potential'] +
                    resource_requirement * rules['resource_requirement'] +
                    success_probability * rules['success_probability']
                )
                
                scored_tasks.append({
                    'task': task,
                    'score': score,
                    'priority_rank': 0
                })
            
            # Sort by score and assign ranks
            scored_tasks.sort(key=lambda x: x['score'], reverse=True)
            for i, task_data in enumerate(scored_tasks):
                task_data['priority_rank'] = i + 1
            
            # Create recommendation
            top_tasks = scored_tasks[:3]
            recommendation = f"Prioritize tasks: {', '.join([t['task'].get('name', f'Task {t['priority_rank']}') for t in top_tasks])}"
            
            confidence = 0.8 if len(tasks) > 1 else 0.6
            
            reasoning = [
                f"Analyzed {len(tasks)} tasks",
                f"Top priority: {top_tasks[0]['task'].get('name', 'Task 1')} (score: {top_tasks[0]['score']:.2f})"
            ]
            
            decision = Decision(
                DecisionType.TASK_PRIORITIZATION,
                recommendation,
                confidence,
                reasoning,
                {'prioritized_tasks': scored_tasks}
            )
            
            self.decision_history.append(decision)
            return decision
            
        except Exception as e:
            self.logger.error(f"Error in task prioritization decision: {e}")
            return None
    
    def _decide_resource_allocation(self, data: Dict[str, Any]) -> Optional[Decision]:
        """Make resource allocation decision."""
        try:
            available_resources = data.get('available_resources', {})
            resource_requests = data.get('resource_requests', [])
            
            if not resource_requests:
                return None
            
            # Simple allocation strategy based on priority and efficiency
            allocations = []
            remaining_resources = available_resources.copy()
            
            # Sort requests by priority
            sorted_requests = sorted(
                resource_requests,
                key=lambda x: x.get('priority', 5),
                reverse=True
            )
            
            for request in sorted_requests:
                resource_type = request.get('resource_type')
                amount_needed = request.get('amount', 0)
                available = remaining_resources.get(resource_type, 0)
                
                if available >= amount_needed:
                    allocations.append({
                        'request_id': request.get('id'),
                        'allocated': amount_needed,
                        'status': 'approved'
                    })
                    remaining_resources[resource_type] -= amount_needed
                else:
                    allocations.append({
                        'request_id': request.get('id'),
                        'allocated': available,
                        'status': 'partial' if available > 0 else 'denied'
                    })
                    remaining_resources[resource_type] = 0
            
            approved_count = len([a for a in allocations if a['status'] == 'approved'])
            confidence = min(0.9, approved_count / len(resource_requests) + 0.3)
            
            recommendation = f"Allocate resources to {approved_count}/{len(resource_requests)} requests"
            
            reasoning = [
                f"Processed {len(resource_requests)} resource requests",
                f"Approved {approved_count} requests fully",
                f"Resource utilization optimized based on priority"
            ]
            
            decision = Decision(
                DecisionType.RESOURCE_ALLOCATION,
                recommendation,
                confidence,
                reasoning,
                {'allocations': allocations, 'remaining_resources': remaining_resources}
            )
            
            self.decision_history.append(decision)
            return decision
            
        except Exception as e:
            self.logger.error(f"Error in resource allocation decision: {e}")
            return None
    
    def _decide_risk_assessment(self, data: Dict[str, Any]) -> Optional[Decision]:
        """Make risk assessment decision."""
        try:
            # Simple risk assessment based on multiple factors
            factors = {
                'smart_contract_risk': data.get('smart_contract_risk', 0.5),
                'team_risk': data.get('team_risk', 0.5),
                'market_risk': data.get('market_risk', 0.5),
                'technical_risk': data.get('technical_risk', 0.5)
            }
            
            # Calculate overall risk
            overall_risk = sum(factors.values()) / len(factors)
            
            # Determine risk level
            if overall_risk < 0.3:
                risk_level = "LOW"
                recommendation = "PROCEED"
                confidence = 0.8
            elif overall_risk < 0.6:
                risk_level = "MEDIUM"
                recommendation = "PROCEED_WITH_CAUTION"
                confidence = 0.7
            else:
                risk_level = "HIGH"
                recommendation = "AVOID"
                confidence = 0.9
            
            # Build reasoning
            reasoning = [f"Overall risk level: {risk_level} ({overall_risk:.2f})"]
            
            highest_risk_factor = max(factors.items(), key=lambda x: x[1])
            reasoning.append(f"Highest risk factor: {highest_risk_factor[0]} ({highest_risk_factor[1]:.2f})")
            
            decision = Decision(
                DecisionType.RISK_ASSESSMENT,
                recommendation,
                confidence,
                reasoning,
                {'risk_level': risk_level, 'overall_risk': overall_risk, 'risk_factors': factors}
            )
            
            self.decision_history.append(decision)
            return decision
            
        except Exception as e:
            self.logger.error(f"Error in risk assessment decision: {e}")
            return None
    
    def _decide_timing_optimization(self, data: Dict[str, Any]) -> Optional[Decision]:
        """Make timing optimization decision."""
        try:
            current_hour = datetime.utcnow().hour
            gas_price = data.get('gas_price', 50)
            network_congestion = data.get('network_congestion', 0.5)
            market_volatility = data.get('market_volatility', 0.5)
            
            # Simple timing score based on various factors
            timing_score = 0.5
            
            # Prefer off-peak hours (2-6 AM UTC)
            if 2 <= current_hour <= 6:
                timing_score += 0.2
            elif 14 <= current_hour <= 18:  # Peak hours
                timing_score -= 0.2
            
            # Consider gas price (lower is better)
            if gas_price < 20:
                timing_score += 0.2
            elif gas_price > 100:
                timing_score -= 0.3
            
            # Consider network congestion
            timing_score -= network_congestion * 0.2
            
            # Consider market volatility
            timing_score -= market_volatility * 0.1
            
            timing_score = max(0, min(1, timing_score))
            
            if timing_score > 0.7:
                recommendation = "EXECUTE_NOW"
                confidence = timing_score
            elif timing_score > 0.4:
                recommendation = "WAIT_FOR_BETTER_CONDITIONS"
                confidence = 0.6
            else:
                recommendation = "DELAY_EXECUTION"
                confidence = 0.8
            
            reasoning = [
                f"Timing score: {timing_score:.2f}",
                f"Current hour: {current_hour}:00 UTC",
                f"Gas price: {gas_price} gwei",
                f"Network congestion: {network_congestion:.2f}"
            ]
            
            decision = Decision(
                DecisionType.TIMING_OPTIMIZATION,
                recommendation,
                confidence,
                reasoning,
                {'timing_score': timing_score, 'optimal_hour': 4}  # 4 AM UTC typically optimal
            )
            
            self.decision_history.append(decision)
            return decision
            
        except Exception as e:
            self.logger.error(f"Error in timing optimization decision: {e}")
            return None
    
    def _calculate_urgency(self, deadline) -> float:
        """Calculate urgency score based on deadline."""
        if not deadline:
            return 0.5
        
        try:
            if isinstance(deadline, str):
                deadline = datetime.fromisoformat(deadline)
            
            time_remaining = (deadline - datetime.utcnow()).total_seconds()
            hours_remaining = time_remaining / 3600
            
            if hours_remaining < 1:
                return 1.0
            elif hours_remaining < 24:
                return 0.8
            elif hours_remaining < 72:
                return 0.6
            elif hours_remaining < 168:  # 1 week
                return 0.4
            else:
                return 0.2
                
        except Exception:
            return 0.5
    
    def _learn_from_outcome(self, decision: Decision, outcome: bool, 
                           feedback: Dict[str, Any] = None):
        """Learn from decision outcome to improve future decisions."""
        try:
            # Simple learning: adjust rule weights based on outcome
            decision_type = decision.decision_type
            
            if decision_type in self.decision_rules:
                rules = self.decision_rules[decision_type]
                
                # Adjust weights based on outcome
                adjustment = self.learning_rate if outcome else -self.learning_rate
                
                # This is a simplified learning mechanism
                # In practice, you'd want more sophisticated ML algorithms
                for rule_name in rules:
                    if rule_name in decision.data:
                        feature_value = decision.data[rule_name]
                        if feature_value > 0.5:  # High feature value
                            rules[rule_name] += adjustment
                        else:  # Low feature value
                            rules[rule_name] -= adjustment
                
                # Normalize weights to prevent drift
                total_weight = sum(abs(w) for w in rules.values())
                if total_weight > 0:
                    for rule_name in rules:
                        rules[rule_name] /= total_weight
            
        except Exception as e:
            self.logger.error(f"Error learning from outcome: {e}")
    
    def _get_type_distribution(self, decisions: List[Decision]) -> Dict[str, int]:
        """Get distribution of decision types."""
        distribution = {}
        for decision in decisions:
            decision_type = decision.decision_type.value
            distribution[decision_type] = distribution.get(decision_type, 0) + 1
        return distribution
