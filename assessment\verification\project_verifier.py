"""
项目真实性验证器

该模块实现了验证项目真实性的功能。
"""

import logging
import requests
import time
import re
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse

from discovery.models.project import Project


class ProjectVerifier:
    """项目真实性验证器，负责验证项目真实性"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目真实性验证器
        
        Args:
            config: 配置字典，包含项目真实性验证器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.timeout = config.get('timeout', 10)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        self.min_website_age = config.get('min_website_age', 30)  # 最小网站年龄（天）
        self.min_content_length = config.get('min_content_length', 1000)  # 最小内容长度
        self.required_pages = config.get('required_pages', ['about', 'team', 'whitepaper', 'roadmap', 'tokenomics'])
        self.suspicious_domains = config.get('suspicious_domains', [
            'free-hosting', 'freehosting', '000webhost', 'blogspot', 'wordpress.com'
        ])
        self.suspicious_tlds = config.get('suspicious_tlds', [
            '.xyz', '.tk', '.ml', '.ga', '.cf', '.gq'
        ])
        
        # 统计信息
        self._stats = {
            'total_verifications': 0,
            'successful_verifications': 0,
            'failed_verifications': 0,
            'high_confidence': 0,
            'medium_confidence': 0,
            'low_confidence': 0
        }
    
    def verify(self, project: Project) -> Dict[str, Any]:
        """
        验证项目真实性
        
        Args:
            project: 要验证的项目
            
        Returns:
            验证结果字典
        """
        self.logger.info(f"开始验证项目 {project.name} (ID: {project.id}) 的真实性")
        
        try:
            # 更新统计信息
            self._stats['total_verifications'] += 1
            
            # 初始化结果
            result = {
                'verified': False,
                'confidence': 0.0,
                'website_available': False,
                'website_age': None,
                'content_quality': 'unknown',
                'has_required_pages': False,
                'has_suspicious_domain': False,
                'has_ssl': False,
                'has_whois_info': False,
                'warnings': [],
                'details': {}
            }
            
            # 检查项目URL
            if not project.url:
                self.logger.warning(f"项目 {project.name} 没有URL")
                result['warnings'].append("没有项目URL")
                result['confidence'] = 0.1
                self._stats['failed_verifications'] += 1
                self._stats['low_confidence'] += 1
                return result
            
            # 验证项目网站
            website_result = self._verify_website(project.url)
            
            # 更新结果
            result.update(website_result)
            
            # 检查域名是否可疑
            domain = self._extract_domain(project.url)
            if domain:
                result['domain'] = domain
                
                # 检查可疑域名
                if any(susp in domain for susp in self.suspicious_domains):
                    result['has_suspicious_domain'] = True
                    result['warnings'].append(f"可疑域名: {domain}")
                
                # 检查可疑顶级域名
                tld = self._extract_tld(domain)
                if tld and any(tld.endswith(susp) for susp in self.suspicious_tlds):
                    result['has_suspicious_domain'] = True
                    result['warnings'].append(f"可疑顶级域名: {tld}")
                
                # 获取WHOIS信息
                whois_info = self._get_whois_info(domain)
                if whois_info:
                    result['has_whois_info'] = True
                    result['details']['whois'] = whois_info
                    
                    # 检查网站年龄
                    if 'creation_date' in whois_info:
                        creation_date = whois_info['creation_date']
                        website_age_days = (time.time() - creation_date) / (24 * 3600)
                        result['website_age'] = website_age_days
                        
                        if website_age_days < self.min_website_age:
                            result['warnings'].append(f"网站创建时间较短 ({website_age_days:.1f} 天 < {self.min_website_age} 天)")
            
            # 计算置信度
            confidence = self._calculate_confidence(result)
            result['confidence'] = confidence
            
            # 确定验证结果
            if result['has_suspicious_domain']:
                result['verified'] = False
                self._stats['failed_verifications'] += 1
            elif confidence >= 0.7:
                result['verified'] = True
                self._stats['successful_verifications'] += 1
            else:
                result['verified'] = False
                self._stats['failed_verifications'] += 1
            
            # 更新置信度统计
            if confidence >= 0.7:
                self._stats['high_confidence'] += 1
            elif confidence >= 0.4:
                self._stats['medium_confidence'] += 1
            else:
                self._stats['low_confidence'] += 1
            
            self.logger.info(f"项目 {project.name} 真实性验证完成，置信度: {confidence:.2f}")
            
            return result
        
        except Exception as e:
            self.logger.error(f"验证项目 {project.name} 真实性时出错: {str(e)}")
            self._stats['failed_verifications'] += 1
            self._stats['low_confidence'] += 1
            
            return {
                'verified': False,
                'confidence': 0.0,
                'error': str(e),
                'warnings': ["验证过程中出错"]
            }
    
    def _verify_website(self, url: str) -> Dict[str, Any]:
        """
        验证项目网站
        
        Args:
            url: 网站URL
            
        Returns:
            网站验证结果
        """
        self.logger.debug(f"验证网站: {url}")
        
        # 初始化结果
        result = {
            'website_available': False,
            'content_quality': 'unknown',
            'has_required_pages': False,
            'has_ssl': False,
            'warnings': [],
            'details': {
                'available_pages': [],
                'missing_pages': [],
                'content_length': 0
            }
        }
        
        try:
            # 检查URL是否使用HTTPS
            if url.startswith('https://'):
                result['has_ssl'] = True
            else:
                result['warnings'].append("网站未使用HTTPS")
            
            # 发送请求
            response = requests.get(url, timeout=self.timeout, allow_redirects=True)
            response.raise_for_status()
            
            # 网站可访问
            result['website_available'] = True
            
            # 解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查内容长度
            content_length = len(soup.get_text())
            result['details']['content_length'] = content_length
            
            if content_length < self.min_content_length:
                result['content_quality'] = 'poor'
                result['warnings'].append(f"网站内容较少 ({content_length} < {self.min_content_length})")
            else:
                # 评估内容质量
                result['content_quality'] = self._evaluate_content_quality(soup)
            
            # 检查必要页面
            available_pages = []
            missing_pages = []
            
            for page in self.required_pages:
                page_available = self._check_page_availability(url, page)
                
                if page_available:
                    available_pages.append(page)
                else:
                    missing_pages.append(page)
            
            result['details']['available_pages'] = available_pages
            result['details']['missing_pages'] = missing_pages
            
            # 检查是否有足够的必要页面
            if len(available_pages) >= len(self.required_pages) * 0.6:  # 至少有60%的必要页面
                result['has_required_pages'] = True
            else:
                result['warnings'].append(f"缺少必要页面: {', '.join(missing_pages)}")
            
            return result
        
        except requests.exceptions.SSLError:
            result['warnings'].append("SSL证书验证失败")
            return result
        
        except requests.exceptions.ConnectionError:
            result['warnings'].append("无法连接到网站")
            return result
        
        except requests.exceptions.Timeout:
            result['warnings'].append("网站响应超时")
            return result
        
        except requests.exceptions.HTTPError as e:
            result['warnings'].append(f"HTTP错误: {str(e)}")
            return result
        
        except Exception as e:
            self.logger.error(f"验证网站 {url} 时出错: {str(e)}")
            result['warnings'].append(f"验证过程中出错: {str(e)}")
            return result
    
    def _check_page_availability(self, base_url: str, page: str) -> bool:
        """
        检查页面是否可用
        
        Args:
            base_url: 基础URL
            page: 页面名称
            
        Returns:
            页面是否可用
        """
        # 构建可能的URL
        possible_urls = [
            f"{base_url.rstrip('/')}/{page}",
            f"{base_url.rstrip('/')}/{page}.html",
            f"{base_url.rstrip('/')}/{page}.php",
            f"{base_url.rstrip('/')}/pages/{page}",
            f"{base_url.rstrip('/')}/docs/{page}"
        ]
        
        # 检查页面链接是否存在于主页
        try:
            response = requests.get(base_url, timeout=self.timeout)
            response.raise_for_status()
            
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            for link in soup.find_all('a'):
                href = link.get('href')
                text = link.get_text(strip=True).lower()
                
                if href and (page in href.lower() or page in text):
                    # 构建完整URL
                    if href.startswith('http'):
                        possible_urls.append(href)
                    elif href.startswith('/'):
                        # 相对路径
                        base_domain = '/'.join(base_url.split('/')[:3])  # 提取域名部分
                        possible_urls.append(f"{base_domain}{href}")
                    else:
                        # 相对路径，不以/开头
                        possible_urls.append(f"{base_url.rstrip('/')}/{href}")
        
        except Exception as e:
            self.logger.error(f"检查主页链接时出错: {str(e)}")
        
        # 检查每个可能的URL
        for url in possible_urls:
            try:
                response = requests.head(url, timeout=self.timeout, allow_redirects=True)
                
                if response.status_code == 200:
                    return True
            
            except Exception:
                continue
        
        return False
    
    def _evaluate_content_quality(self, soup) -> str:
        """
        评估内容质量
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            内容质量评级 ('high', 'medium', 'low', 'poor')
        """
        # 提取文本内容
        text = soup.get_text()
        
        # 计算内容长度
        content_length = len(text)
        
        # 检查图片数量
        image_count = len(soup.find_all('img'))
        
        # 检查标题数量
        heading_count = len(soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']))
        
        # 检查段落数量
        paragraph_count = len(soup.find_all('p'))
        
        # 检查链接数量
        link_count = len(soup.find_all('a'))
        
        # 评估质量
        if content_length > 5000 and image_count >= 5 and heading_count >= 5 and paragraph_count >= 10:
            return 'high'
        elif content_length > 3000 and image_count >= 3 and heading_count >= 3 and paragraph_count >= 5:
            return 'medium'
        elif content_length > 1000 and heading_count >= 2 and paragraph_count >= 3:
            return 'low'
        else:
            return 'poor'
    
    def _extract_domain(self, url: str) -> Optional[str]:
        """
        从URL中提取域名
        
        Args:
            url: URL
            
        Returns:
            域名，如果无法提取则返回None
        """
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            
            # 移除www前缀
            if domain.startswith('www.'):
                domain = domain[4:]
            
            return domain
        
        except Exception as e:
            self.logger.error(f"从URL {url} 提取域名时出错: {str(e)}")
            return None
    
    def _extract_tld(self, domain: str) -> Optional[str]:
        """
        从域名中提取顶级域名
        
        Args:
            domain: 域名
            
        Returns:
            顶级域名，如果无法提取则返回None
        """
        try:
            parts = domain.split('.')
            
            if len(parts) >= 2:
                return '.' + parts[-1]
            
            return None
        
        except Exception as e:
            self.logger.error(f"从域名 {domain} 提取顶级域名时出错: {str(e)}")
            return None
    
    def _get_whois_info(self, domain: str) -> Optional[Dict[str, Any]]:
        """
        获取域名的WHOIS信息
        
        Args:
            domain: 域名
            
        Returns:
            WHOIS信息字典，如果无法获取则返回None
        """
        try:
            import whois
            w = whois.whois(domain)
            
            # 提取关键信息
            whois_info = {
                'domain_name': w.domain_name,
                'registrar': w.registrar,
                'creation_date': None,
                'expiration_date': None,
                'updated_date': None
            }
            
            # 处理日期
            if w.creation_date:
                if isinstance(w.creation_date, list):
                    whois_info['creation_date'] = w.creation_date[0].timestamp()
                else:
                    whois_info['creation_date'] = w.creation_date.timestamp()
            
            if w.expiration_date:
                if isinstance(w.expiration_date, list):
                    whois_info['expiration_date'] = w.expiration_date[0].timestamp()
                else:
                    whois_info['expiration_date'] = w.expiration_date.timestamp()
            
            if w.updated_date:
                if isinstance(w.updated_date, list):
                    whois_info['updated_date'] = w.updated_date[0].timestamp()
                else:
                    whois_info['updated_date'] = w.updated_date.timestamp()
            
            return whois_info
        
        except ImportError:
            self.logger.warning("无法导入whois库，跳过WHOIS信息获取")
            return None
        
        except Exception as e:
            self.logger.error(f"获取域名 {domain} 的WHOIS信息时出错: {str(e)}")
            return None
    
    def _calculate_confidence(self, result: Dict[str, Any]) -> float:
        """
        计算验证结果的置信度
        
        Args:
            result: 验证结果
            
        Returns:
            置信度 (0.0-1.0)
        """
        confidence = 0.0
        
        # 基础置信度
        if result['website_available']:
            confidence += 0.3
        
        # 内容质量
        if result['content_quality'] == 'high':
            confidence += 0.3
        elif result['content_quality'] == 'medium':
            confidence += 0.2
        elif result['content_quality'] == 'low':
            confidence += 0.1
        
        # 必要页面
        if result['has_required_pages']:
            confidence += 0.2
        
        # SSL证书
        if result['has_ssl']:
            confidence += 0.1
        
        # WHOIS信息
        if result['has_whois_info']:
            confidence += 0.1
        
        # 网站年龄
        if result['website_age'] is not None:
            if result['website_age'] >= self.min_website_age:
                confidence += 0.1
        
        # 可疑域名
        if result['has_suspicious_domain']:
            confidence = 0.0
        
        # 警告数量
        warning_penalty = min(0.5, len(result['warnings']) * 0.1)
        confidence = max(0.0, confidence - warning_penalty)
        
        # 限制在0.0-1.0范围内
        return max(0.0, min(1.0, confidence))
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()