"""
Authorization Manager

授权管理器，负责管理用户权限和访问控制。
"""

import logging
import hashlib
import secrets
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta


class AuthorizationManager:
    """
    授权管理器
    
    负责用户身份验证、权限管理和访问控制。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化授权管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 用户和权限存储
        self.users: Dict[str, Dict] = {}
        self.roles: Dict[str, Dict] = {}
        self.permissions: Dict[str, Dict] = {}
        self.active_sessions: Dict[str, Dict] = {}
        
        # 安全配置
        self.security_config = {
            "session_timeout_minutes": config.get("session_timeout_minutes", 30),
            "max_failed_attempts": config.get("max_failed_attempts", 3),
            "lockout_duration_minutes": config.get("lockout_duration_minutes", 15),
            "require_2fa": config.get("require_2fa", True),
            "password_min_length": config.get("password_min_length", 12)
        }
        
        # 授权统计
        self.auth_stats = {
            'total_login_attempts': 0,
            'successful_logins': 0,
            'failed_logins': 0,
            'active_sessions': 0,
            'permission_checks': 0,
            'permission_denials': 0
        }
        
        # 预定义权限
        self.default_permissions = {
            "wallet.create": {"description": "创建钱包", "risk_level": "medium"},
            "wallet.delete": {"description": "删除钱包", "risk_level": "high"},
            "transaction.send": {"description": "发送交易", "risk_level": "high"},
            "transaction.approve": {"description": "批准交易", "risk_level": "high"},
            "balance.view": {"description": "查看余额", "risk_level": "low"},
            "portfolio.manage": {"description": "管理投资组合", "risk_level": "medium"},
            "security.admin": {"description": "安全管理", "risk_level": "critical"}
        }
        
        # 预定义角色
        self.default_roles = {
            "viewer": {
                "description": "只读用户",
                "permissions": ["balance.view"]
            },
            "trader": {
                "description": "交易用户", 
                "permissions": ["balance.view", "transaction.send", "portfolio.manage"]
            },
            "admin": {
                "description": "管理员",
                "permissions": list(self.default_permissions.keys())
            }
        }
    
    async def initialize(self) -> bool:
        """
        初始化授权管理器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 加载默认权限和角色
            await self._load_default_permissions()
            await self._load_default_roles()
            
            self.logger.info("Authorization Manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Authorization Manager: {e}")
            return False
    
    async def create_user(self, username: str, password: str, email: str,
                         roles: List[str] = None) -> bool:
        """
        创建新用户
        
        Args:
            username: 用户名
            password: 密码
            email: 邮箱
            roles: 角色列表
            
        Returns:
            bool: 创建是否成功
        """
        try:
            if username in self.users:
                self.logger.error(f"User {username} already exists")
                return False
            
            # 验证密码强度
            if not self._validate_password(password):
                self.logger.error("Password does not meet security requirements")
                return False
            
            # 创建用户
            user_data = {
                "username": username,
                "email": email,
                "password_hash": self._hash_password(password),
                "roles": roles or ["viewer"],
                "created_at": datetime.utcnow().isoformat(),
                "last_login": None,
                "failed_attempts": 0,
                "locked_until": None,
                "is_active": True,
                "two_factor_enabled": False,
                "two_factor_secret": None
            }
            
            self.users[username] = user_data
            
            self.logger.info(f"Created user {username} with roles {roles}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating user: {e}")
            return False
    
    async def authenticate_user(self, username: str, password: str,
                              two_factor_code: str = None) -> Optional[str]:
        """
        用户身份验证
        
        Args:
            username: 用户名
            password: 密码
            two_factor_code: 双因素认证码
            
        Returns:
            Optional[str]: 会话令牌
        """
        try:
            self.auth_stats['total_login_attempts'] += 1
            
            if username not in self.users:
                self.logger.warning(f"Login attempt for non-existent user: {username}")
                self.auth_stats['failed_logins'] += 1
                return None
            
            user = self.users[username]
            
            # 检查账户是否被锁定
            if await self._is_user_locked(username):
                self.logger.warning(f"Login attempt for locked user: {username}")
                self.auth_stats['failed_logins'] += 1
                return None
            
            # 验证密码
            if not self._verify_password(password, user["password_hash"]):
                await self._handle_failed_login(username)
                self.auth_stats['failed_logins'] += 1
                return None
            
            # 验证双因素认证
            if user["two_factor_enabled"]:
                if not two_factor_code or not self._verify_2fa(user["two_factor_secret"], two_factor_code):
                    self.logger.warning(f"2FA verification failed for user: {username}")
                    self.auth_stats['failed_logins'] += 1
                    return None
            
            # 创建会话
            session_token = await self._create_session(username)
            
            # 更新用户信息
            user["last_login"] = datetime.utcnow().isoformat()
            user["failed_attempts"] = 0
            
            self.auth_stats['successful_logins'] += 1
            self.logger.info(f"User {username} authenticated successfully")
            
            return session_token
            
        except Exception as e:
            self.logger.error(f"Error authenticating user: {e}")
            self.auth_stats['failed_logins'] += 1
            return None

    async def check_permission(self, session_token: str, permission: str) -> bool:
        """
        检查用户权限
        
        Args:
            session_token: 会话令牌
            permission: 权限名称
            
        Returns:
            bool: 是否有权限
        """
        try:
            self.auth_stats['permission_checks'] += 1
            
            # 验证会话
            session = await self._validate_session(session_token)
            if not session:
                self.auth_stats['permission_denials'] += 1
                return False
            
            username = session["username"]
            user = self.users[username]
            
            # 检查用户角色权限
            user_permissions = await self._get_user_permissions(username)
            
            has_permission = permission in user_permissions
            
            if not has_permission:
                self.auth_stats['permission_denials'] += 1
                self.logger.warning(f"Permission denied: {username} -> {permission}")
            
            return has_permission
            
        except Exception as e:
            self.logger.error(f"Error checking permission: {e}")
            self.auth_stats['permission_denials'] += 1
            return False
    
    async def _create_session(self, username: str) -> str:
        """创建用户会话"""
        try:
            session_token = secrets.token_urlsafe(32)
            
            session_data = {
                "token": session_token,
                "username": username,
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow() + timedelta(minutes=self.security_config["session_timeout_minutes"]),
                "last_activity": datetime.utcnow()
            }
            
            self.active_sessions[session_token] = session_data
            self.auth_stats['active_sessions'] = len(self.active_sessions)
            
            return session_token
            
        except Exception as e:
            self.logger.error(f"Error creating session: {e}")
            return ""
    
    async def _validate_session(self, session_token: str) -> Optional[Dict]:
        """验证会话有效性"""
        try:
            if session_token not in self.active_sessions:
                return None
            
            session = self.active_sessions[session_token]
            
            # 检查会话是否过期
            if datetime.utcnow() > session["expires_at"]:
                del self.active_sessions[session_token]
                self.auth_stats['active_sessions'] = len(self.active_sessions)
                return None
            
            # 更新最后活动时间
            session["last_activity"] = datetime.utcnow()
            
            return session
            
        except Exception as e:
            self.logger.error(f"Error validating session: {e}")
            return None
    
    async def _get_user_permissions(self, username: str) -> Set[str]:
        """获取用户所有权限"""
        try:
            user = self.users[username]
            permissions = set()
            
            for role_name in user["roles"]:
                if role_name in self.roles:
                    role_permissions = self.roles[role_name].get("permissions", [])
                    permissions.update(role_permissions)
            
            return permissions
            
        except Exception as e:
            self.logger.error(f"Error getting user permissions: {e}")
            return set()
    
    async def _is_user_locked(self, username: str) -> bool:
        """检查用户是否被锁定"""
        try:
            user = self.users[username]
            
            if user.get("locked_until"):
                locked_until = datetime.fromisoformat(user["locked_until"])
                if datetime.utcnow() < locked_until:
                    return True
                else:
                    # 解锁用户
                    user["locked_until"] = None
                    user["failed_attempts"] = 0
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking user lock status: {e}")
            return False
    
    async def _handle_failed_login(self, username: str):
        """处理登录失败"""
        try:
            user = self.users[username]
            user["failed_attempts"] += 1
            
            if user["failed_attempts"] >= self.security_config["max_failed_attempts"]:
                # 锁定用户
                lockout_duration = timedelta(minutes=self.security_config["lockout_duration_minutes"])
                user["locked_until"] = (datetime.utcnow() + lockout_duration).isoformat()
                
                self.logger.warning(f"User {username} locked due to failed login attempts")
            
        except Exception as e:
            self.logger.error(f"Error handling failed login: {e}")
    
    def _validate_password(self, password: str) -> bool:
        """验证密码强度"""
        try:
            min_length = self.security_config["password_min_length"]
            
            if len(password) < min_length:
                return False
            
            # 检查是否包含大小写字母、数字和特殊字符
            has_upper = any(c.isupper() for c in password)
            has_lower = any(c.islower() for c in password)
            has_digit = any(c.isdigit() for c in password)
            has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
            
            return has_upper and has_lower and has_digit and has_special
            
        except Exception as e:
            self.logger.error(f"Error validating password: {e}")
            return False
    
    def _hash_password(self, password: str) -> str:
        """哈希密码"""
        try:
            salt = secrets.token_hex(16)
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return f"{salt}:{password_hash.hex()}"
        except Exception as e:
            self.logger.error(f"Error hashing password: {e}")
            return ""
    
    def _verify_password(self, password: str, stored_hash: str) -> bool:
        """验证密码"""
        try:
            salt, hash_hex = stored_hash.split(':')
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash.hex() == hash_hex
        except Exception as e:
            self.logger.error(f"Error verifying password: {e}")
            return False
    
    def _verify_2fa(self, secret: str, code: str) -> bool:
        """验证双因素认证码"""
        try:
            # 简化的2FA验证（实际应使用TOTP库）
            return len(code) == 6 and code.isdigit()
        except Exception as e:
            self.logger.error(f"Error verifying 2FA: {e}")
            return False
    
    async def _load_default_permissions(self):
        """加载默认权限"""
        try:
            self.permissions.update(self.default_permissions)
            self.logger.info(f"Loaded {len(self.default_permissions)} default permissions")
        except Exception as e:
            self.logger.error(f"Error loading default permissions: {e}")
    
    async def _load_default_roles(self):
        """加载默认角色"""
        try:
            self.roles.update(self.default_roles)
            self.logger.info(f"Loaded {len(self.default_roles)} default roles")
        except Exception as e:
            self.logger.error(f"Error loading default roles: {e}")
    
    async def logout_user(self, session_token: str) -> bool:
        """用户登出"""
        try:
            if session_token in self.active_sessions:
                del self.active_sessions[session_token]
                self.auth_stats['active_sessions'] = len(self.active_sessions)
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error logging out user: {e}")
            return False
    
    async def get_auth_statistics(self) -> Dict[str, Any]:
        """获取授权统计信息"""
        try:
            success_rate = 0.0
            if self.auth_stats['total_login_attempts'] > 0:
                success_rate = (self.auth_stats['successful_logins'] / 
                              self.auth_stats['total_login_attempts'])
            
            return {
                'auth_stats': self.auth_stats,
                'success_rate': round(success_rate, 3),
                'total_users': len(self.users),
                'total_roles': len(self.roles),
                'total_permissions': len(self.permissions),
                'security_config': self.security_config
            }
        except Exception as e:
            self.logger.error(f"Error getting auth statistics: {e}")
            return {}
