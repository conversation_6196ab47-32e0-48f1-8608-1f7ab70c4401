"""Location Simulator - 位置模拟器"""
import logging
import random

class LocationSimulator:
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 预定义的位置列表
        self.locations = [
            {"country": "US", "city": "New York", "timezone": "America/New_York"},
            {"country": "UK", "city": "London", "timezone": "Europe/London"},
            {"country": "DE", "city": "Berlin", "timezone": "Europe/Berlin"},
            {"country": "JP", "city": "Tokyo", "timezone": "Asia/Tokyo"},
            {"country": "SG", "city": "Singapore", "timezone": "Asia/Singapore"}
        ]
    
    def simulate_location(self, target_country: str = None) -> dict:
        """模拟地理位置"""
        try:
            if target_country:
                # 查找指定国家的位置
                matching_locations = [loc for loc in self.locations if loc["country"] == target_country]
                if matching_locations:
                    return random.choice(matching_locations)
            
            # 随机选择位置
            return random.choice(self.locations)
        except Exception as e:
            self.logger.error(f"Failed to simulate location: {e}")
            return self.locations[0]  # 返回默认位置
    
    def get_location_by_proxy(self, proxy: dict) -> dict:
        """根据代理获取位置信息"""
        # 简化实现，实际应该查询代理的真实位置
        return self.simulate_location()
