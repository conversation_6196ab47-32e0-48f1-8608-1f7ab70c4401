# AirHunter Configuration File
# This file contains the main configuration for the AirHunter system

[general]
app_name = AirHunter
version = 1.0.0
debug = false
log_level = INFO

[database]
type = sqlite
path = data/airhunter.db
backup_interval = 3600
max_connections = 10

[agents]
max_agents = 10
agent_timeout = 300
health_check_interval = 60
restart_on_failure = true

[security]
encrypt_sensitive_data = true
master_password_required = true
session_timeout = 3600

[notifications]
enable_notifications = true
channels = console,file
email_enabled = false
webhook_enabled = false

[proxy]
enable_proxy = false
proxy_host =
proxy_port = 8080
proxy_username =
proxy_password =

[ml]
enable_pattern_recognition = true
enable_anomaly_detection = true
enable_decision_making = true
learning_rate = 0.1

[ui]
theme = dark
auto_refresh_interval = 5
show_system_tray = true
minimize_to_tray = true
