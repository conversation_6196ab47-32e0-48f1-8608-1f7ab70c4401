"""
Rate limiter implementation for API requests

This module provides rate limiting functionality using token bucket algorithm.
"""

import time
import threading
from typing import Dict, Optional
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor

@dataclass
class TokenBucket:
    """Token bucket for rate limiting"""
    rate: float  # Tokens per second
    capacity: int  # Maximum tokens
    tokens: float  # Current tokens
    last_update: float  # Last update timestamp

class RateLimiter:
    """Rate limiter using token bucket algorithm"""
    
    def __init__(self):
        """Initialize rate limiter"""
        self.buckets: Dict[str, TokenBucket] = {}
        self.lock = threading.Lock()
        self._executor = ThreadPoolExecutor()
    
    def add_limit(
        self,
        key: str,
        rate: float,
        capacity: int
    ) -> None:
        """
        Add rate limit for a key
        
        Args:
            key: Identifier for the rate limit
            rate: Tokens per second
            capacity: Maximum tokens
        """
        with self.lock:
            self.buckets[key] = TokenBucket(
                rate=rate,
                capacity=capacity,
                tokens=capacity,
                last_update=time.time()
            )
    
    def remove_limit(self, key: str) -> None:
        """
        Remove rate limit for a key
        
        Args:
            key: Identifier for the rate limit
        """
        with self.lock:
            self.buckets.pop(key, None)
    
    def _update_tokens(self, bucket: TokenBucket) -> None:
        """
        Update tokens in bucket based on elapsed time
        
        Args:
            bucket: Token bucket to update
        """
        now = time.time()
        elapsed = now - bucket.last_update
        
        # Add new tokens based on elapsed time
        new_tokens = elapsed * bucket.rate
        bucket.tokens = min(bucket.capacity, bucket.tokens + new_tokens)
        bucket.last_update = now
    
    def acquire(
        self,
        key: str,
        tokens: int = 1,
        timeout: Optional[float] = None
    ) -> bool:
        """
        Acquire tokens from bucket
        
        Args:
            key: Bucket identifier
            tokens: Number of tokens to acquire
            timeout: Maximum time to wait for tokens
            
        Returns:
            bool: True if tokens were acquired, False otherwise
        """
        start_time = time.time()
        
        while True:
            with self.lock:
                bucket = self.buckets.get(key)
                if not bucket:
                    return True  # No rate limit defined
                
                self._update_tokens(bucket)
                
                if bucket.tokens >= tokens:
                    bucket.tokens -= tokens
                    return True
            
            # Check timeout
            if timeout is not None:
                if time.time() - start_time >= timeout:
                    return False
            
            # Wait before next attempt
            time.sleep(0.1)
    
    def acquire_async(
        self,
        key: str,
        tokens: int = 1,
        timeout: Optional[float] = None
    ):
        """
        Asynchronously acquire tokens
        
        Args:
            key: Bucket identifier
            tokens: Number of tokens to acquire
            timeout: Maximum time to wait for tokens
            
        Returns:
            Future: Future object for the acquisition
        """
        return self._executor.submit(self.acquire, key, tokens, timeout)
    
    def get_token_count(self, key: str) -> Optional[float]:
        """
        Get current token count for a bucket
        
        Args:
            key: Bucket identifier
            
        Returns:
            float: Current token count or None if bucket doesn't exist
        """
        with self.lock:
            bucket = self.buckets.get(key)
            if bucket:
                self._update_tokens(bucket)
                return bucket.tokens
            return None
    
    def reset(self, key: str) -> None:
        """
        Reset bucket to full capacity
        
        Args:
            key: Bucket identifier
        """
        with self.lock:
            bucket = self.buckets.get(key)
            if bucket:
                bucket.tokens = bucket.capacity
                bucket.last_update = time.time()
    
    def update_rate(self, key: str, new_rate: float) -> None:
        """
        Update token generation rate
        
        Args:
            key: Bucket identifier
            new_rate: New tokens per second rate
        """
        with self.lock:
            bucket = self.buckets.get(key)
            if bucket:
                self._update_tokens(bucket)
                bucket.rate = new_rate
    
    def __del__(self):
        """Cleanup executor on deletion"""
        self._executor.shutdown(wait=True)

# Create global rate limiter instance
rate_limiter = RateLimiter()
