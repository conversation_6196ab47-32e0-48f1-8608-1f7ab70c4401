"""
默认配置

该模块定义了代理智能体的默认配置。
"""

# 默认配置
DEFAULT_CONFIG = {
    # 代理获取配置
    'sources': {
        'enabled_sources': ['free_proxy_list', 'proxy_scrape', 'geonode', 'proxy_nova'],
        'free_proxy_list': {
            'url': 'https://free-proxy-list.net/',
            'timeout': 10,
            'weight': 1.0
        },
        'proxy_scrape': {
            'base_url': 'https://api.proxyscrape.com/v2/',
            'timeout': 10,
            'request_types': ['http', 'socks4', 'socks5'],
            'weight': 1.0
        },
        'geonode': {
            'api_url': 'https://proxylist.geonode.com/api/proxy-list',
            'timeout': 10,
            'weight': 1.0
        },
        'proxy_nova': {
            'url': 'https://www.proxynova.com/proxy-server-list/',
            'timeout': 10,
            'weight': 0.8
        }
    },
    
    # 代理验证配置
    'verification': {
        'timeout': 10,
        'max_workers': 10,
        'test_urls': [
            'http://httpbin.org/ip',
            'https://httpbin.org/ip',
            'http://www.google.com',
            'https://www.google.com'
        ],
        'retry_count': 2,
        'anonymity_test_url': 'http://httpbin.org/headers',
        'country_test_url': 'http://ip-api.com/json'
    },
    
    # 代理池配置
    'pool': {
        'max_size': 1000,
        'max_age': 3600,  # 1小时
        'max_fail_count': 3,
        'persistence_file': 'proxies.json',
        'persistence_enabled': True
    },
    
    # 代理分发配置
    'distribution': {
        'rotation_interval': 600,  # 10分钟
        'max_uses': 50,
        'sticky_sessions': True
    },
    
    # 代理智能体配置
    'acquisition_interval': 3600,  # 1小时
    'verification_interval': 300,  # 5分钟
    'cleanup_interval': 1800,  # 30分钟
    'min_pool_size': 20,
    'max_pool_size': 200
}