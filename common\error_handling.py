"""
通用错误处理模块

提供统一的错误处理、重试机制和异常管理功能
"""

import logging
import time
import functools
import traceback
from typing import Any, Callable, Dict, List, Optional, Type, Union
from enum import Enum
from datetime import datetime


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误类别"""
    NETWORK = "network"
    DATABASE = "database"
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    PERMISSION = "permission"
    RESOURCE = "resource"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_API = "external_api"
    SYSTEM = "system"


class AirHunterError(Exception):
    """AirHunter 基础异常类"""
    
    def __init__(self, message: str, category: ErrorCategory = ErrorCategory.SYSTEM,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM, details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.timestamp = datetime.now()
        self.traceback = traceback.format_exc()


class NetworkError(AirHunterError):
    """网络相关错误"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, **kwargs):
        super().__init__(message, ErrorCategory.NETWORK, **kwargs)
        self.status_code = status_code


class DatabaseError(AirHunterError):
    """数据库相关错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.DATABASE, **kwargs)


class ValidationError(AirHunterError):
    """数据验证错误"""
    
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCategory.VALIDATION, ErrorSeverity.LOW, **kwargs)
        self.field = field


class AuthenticationError(AirHunterError):
    """认证错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH, **kwargs)


class PermissionError(AirHunterError):
    """权限错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.PERMISSION, ErrorSeverity.HIGH, **kwargs)


class ResourceError(AirHunterError):
    """资源相关错误"""
    
    def __init__(self, message: str, resource_type: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCategory.RESOURCE, **kwargs)
        self.resource_type = resource_type


class ExternalAPIError(AirHunterError):
    """外部API错误"""
    
    def __init__(self, message: str, api_name: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCategory.EXTERNAL_API, **kwargs)
        self.api_name = api_name


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.error_stats = {
            'total_errors': 0,
            'by_category': {},
            'by_severity': {},
            'recent_errors': []
        }
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> None:
        """
        处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文信息
        """
        context = context or {}
        
        # 更新统计信息
        self._update_stats(error)
        
        # 记录错误
        self._log_error(error, context)
        
        # 发送警报（如果需要）
        if isinstance(error, AirHunterError) and error.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self._send_alert(error, context)
    
    def _update_stats(self, error: Exception) -> None:
        """更新错误统计"""
        self.error_stats['total_errors'] += 1
        
        if isinstance(error, AirHunterError):
            category = error.category.value
            severity = error.severity.value
            
            self.error_stats['by_category'][category] = self.error_stats['by_category'].get(category, 0) + 1
            self.error_stats['by_severity'][severity] = self.error_stats['by_severity'].get(severity, 0) + 1
        
        # 保留最近的错误
        error_info = {
            'type': type(error).__name__,
            'message': str(error),
            'timestamp': datetime.now().isoformat()
        }
        
        self.error_stats['recent_errors'].append(error_info)
        if len(self.error_stats['recent_errors']) > 100:
            self.error_stats['recent_errors'] = self.error_stats['recent_errors'][-100:]
    
    def _log_error(self, error: Exception, context: Dict[str, Any]) -> None:
        """记录错误日志"""
        if isinstance(error, AirHunterError):
            log_level = {
                ErrorSeverity.LOW: logging.WARNING,
                ErrorSeverity.MEDIUM: logging.ERROR,
                ErrorSeverity.HIGH: logging.ERROR,
                ErrorSeverity.CRITICAL: logging.CRITICAL
            }.get(error.severity, logging.ERROR)
            
            self.logger.log(
                log_level,
                f"[{error.category.value.upper()}] {error.message}",
                extra={
                    'error_details': error.details,
                    'context': context,
                    'severity': error.severity.value,
                    'timestamp': error.timestamp.isoformat()
                }
            )
        else:
            self.logger.error(f"Unexpected error: {str(error)}", exc_info=True)
    
    def _send_alert(self, error: AirHunterError, context: Dict[str, Any]) -> None:
        """发送错误警报"""
        # 这里可以集成邮件、Slack、钉钉等警报系统
        alert_message = f"严重错误警报: {error.message}"
        self.logger.critical(f"ALERT: {alert_message}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return self.error_stats.copy()


def retry_on_error(max_retries: int = 3, delay: float = 1.0, 
                  backoff_factor: float = 2.0,
                  exceptions: Union[Type[Exception], tuple] = Exception) -> Callable:
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff_factor: 退避因子
        exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        logging.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {str(e)}")
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        logging.error(f"函数 {func.__name__} 在 {max_retries} 次重试后仍然失败")
            
            raise last_exception
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return: Any = None, 
                error_handler: Optional[ErrorHandler] = None, **kwargs) -> Any:
    """
    安全执行函数
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 出错时的默认返回值
        error_handler: 错误处理器
        **kwargs: 函数关键字参数
    
    Returns:
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if error_handler:
            error_handler.handle_error(e, {'function': func.__name__, 'args': args, 'kwargs': kwargs})
        else:
            logging.error(f"执行函数 {func.__name__} 时出错: {str(e)}", exc_info=True)
        
        return default_return


def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
    """
    验证必需字段
    
    Args:
        data: 要验证的数据
        required_fields: 必需字段列表
    
    Raises:
        ValidationError: 当缺少必需字段时
    """
    missing_fields = []
    
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == "":
            missing_fields.append(field)
    
    if missing_fields:
        raise ValidationError(
            f"缺少必需字段: {', '.join(missing_fields)}",
            details={'missing_fields': missing_fields, 'provided_data': list(data.keys())}
        )


def validate_data_types(data: Dict[str, Any], type_mapping: Dict[str, Type]) -> None:
    """
    验证数据类型
    
    Args:
        data: 要验证的数据
        type_mapping: 字段类型映射
    
    Raises:
        ValidationError: 当数据类型不匹配时
    """
    type_errors = []
    
    for field, expected_type in type_mapping.items():
        if field in data and data[field] is not None:
            if not isinstance(data[field], expected_type):
                type_errors.append(f"{field}: 期望 {expected_type.__name__}, 实际 {type(data[field]).__name__}")
    
    if type_errors:
        raise ValidationError(
            f"数据类型错误: {'; '.join(type_errors)}",
            details={'type_errors': type_errors}
        )


# 全局错误处理器实例
global_error_handler = ErrorHandler()


def setup_global_error_handling(logger: Optional[logging.Logger] = None) -> ErrorHandler:
    """
    设置全局错误处理
    
    Args:
        logger: 日志记录器
    
    Returns:
        全局错误处理器
    """
    global global_error_handler
    global_error_handler = ErrorHandler(logger)
    return global_error_handler


def get_global_error_handler() -> ErrorHandler:
    """获取全局错误处理器"""
    return global_error_handler
