"""
Page analyzer for browser automation

This module provides utilities for analyzing web page content and structure.
"""

import json
import logging
from typing import Any, Dict, List, Optional, Tuple, Union
from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By

class PageAnalyzer:
    """Analyzer for web pages"""
    
    def __init__(self, driver):
        """
        Initialize page analyzer
        
        Args:
            driver: WebDriver instance
        """
        self.driver = driver
        self.logger = logging.getLogger(__name__)
    
    def get_page_source(self) -> str:
        """
        Get page source
        
        Returns:
            str: Page HTML source
        """
        return self.driver.page_source
    
    def get_element_by_xpath(
        self,
        xpath: str,
        multiple: bool = False
    ) -> Union[Any, List[Any]]:
        """
        Find element(s) by XPath
        
        Args:
            xpath: XPath expression
            multiple: Whether to return multiple elements
            
        Returns:
            Union[Any, List[Any]]: Found element(s)
        """
        try:
            if multiple:
                return self.driver.find_elements(By.XPATH, xpath)
            return self.driver.find_element(By.XPATH, xpath)
        except Exception as e:
            self.logger.error(f"XPath query failed: {str(e)}")
            raise
    
    def get_element_by_css(
        self,
        css_selector: str,
        multiple: bool = False
    ) -> Union[Any, List[Any]]:
        """
        Find element(s) by CSS selector
        
        Args:
            css_selector: CSS selector
            multiple: Whether to return multiple elements
            
        Returns:
            Union[Any, List[Any]]: Found element(s)
        """
        try:
            if multiple:
                return self.driver.find_elements(By.CSS_SELECTOR, css_selector)
            return self.driver.find_element(By.CSS_SELECTOR, css_selector)
        except Exception as e:
            self.logger.error(f"CSS selector query failed: {str(e)}")
            raise
    
    def extract_text_content(
        self,
        element_or_selector: Union[str, Any]
    ) -> str:
        """
        Extract text content from element
        
        Args:
            element_or_selector: Element or selector string
            
        Returns:
            str: Extracted text
        """
        try:
            if isinstance(element_or_selector, str):
                element = self.get_element_by_css(element_or_selector)
            else:
                element = element_or_selector
            return element.text.strip()
        except Exception as e:
            self.logger.error(f"Text extraction failed: {str(e)}")
            raise
    
    def get_element_attributes(
        self,
        element: Any
    ) -> Dict[str, str]:
        """
        Get all attributes of element
        
        Args:
            element: Target element
            
        Returns:
            Dict[str, str]: Attribute dictionary
        """
        try:
            return self.driver.execute_script(
                'var items = {}; for (var i = 0; i < arguments[0].attributes.length; i++) { '
                'items[arguments[0].attributes[i].name] = arguments[0].attributes[i].value }; '
                'return items;',
                element
            )
        except Exception as e:
            self.logger.error(f"Attribute extraction failed: {str(e)}")
            raise
    
    def analyze_dom_structure(
        self,
        root_element: Optional[Any] = None
    ) -> Dict:
        """
        Analyze DOM structure
        
        Args:
            root_element: Root element to start analysis
            
        Returns:
            Dict: DOM structure analysis
        """
        try:
            if root_element is None:
                root_element = self.driver.find_element(By.TAG_NAME, 'html')
                
            return {
                'tag': root_element.tag_name,
                'attributes': self.get_element_attributes(root_element),
                'text': root_element.text.strip(),
                'children': [
                    self.analyze_dom_structure(child)
                    for child in root_element.find_elements(By.XPATH, './*')
                ]
            }
        except Exception as e:
            self.logger.error(f"DOM analysis failed: {str(e)}")
            raise
    
    def get_page_metrics(self) -> Dict[str, Any]:
        """
        Get page performance metrics
        
        Returns:
            Dict[str, Any]: Performance metrics
        """
        try:
            metrics = {}
            
            # Navigation timing
            navigation_timing = self.driver.execute_script(
                "return window.performance.timing.toJSON();"
            )
            metrics['navigation_timing'] = navigation_timing
            
            # Resource timing
            resource_timing = self.driver.execute_script(
                "return window.performance.getEntriesByType('resource');"
            )
            metrics['resource_timing'] = resource_timing
            
            # Memory info
            memory_info = self.driver.execute_script(
                "return window.performance.memory;"
            ) if hasattr(self.driver, 'memory') else {}
            metrics['memory'] = memory_info
            
            return metrics
        except Exception as e:
            self.logger.error(f"Performance metrics collection failed: {str(e)}")
            raise
    
    def analyze_resources(self) -> Dict[str, List[str]]:
        """
        Analyze page resources
        
        Returns:
            Dict[str, List[str]]: Resource analysis by type
        """
        try:
            resources = {
                'scripts': [],
                'styles': [],
                'images': [],
                'fonts': [],
                'other': []
            }
            
            # Scripts
            scripts = self.driver.find_elements(By.TAG_NAME, 'script')
            resources['scripts'] = [
                script.get_attribute('src')
                for script in scripts
                if script.get_attribute('src')
            ]
            
            # Styles
            styles = self.driver.find_elements(By.TAG_NAME, 'link')
            resources['styles'] = [
                style.get_attribute('href')
                for style in styles
                if style.get_attribute('rel') == 'stylesheet'
            ]
            
            # Images
            images = self.driver.find_elements(By.TAG_NAME, 'img')
            resources['images'] = [
                img.get_attribute('src')
                for img in images
                if img.get_attribute('src')
            ]
            
            return resources
        except Exception as e:
            self.logger.error(f"Resource analysis failed: {str(e)}")
            raise
    
    def find_forms(self) -> List[Dict]:
        """
        Find and analyze forms
        
        Returns:
            List[Dict]: Form analysis results
        """
        try:
            forms = []
            for form in self.driver.find_elements(By.TAG_NAME, 'form'):
                form_data = {
                    'action': form.get_attribute('action'),
                    'method': form.get_attribute('method'),
                    'fields': []
                }
                
                for field in form.find_elements(By.XPATH, './/input|.//select|.//textarea'):
                    field_data = {
                        'type': field.get_attribute('type'),
                        'name': field.get_attribute('name'),
                        'id': field.get_attribute('id'),
                        'required': field.get_attribute('required') is not None
                    }
                    form_data['fields'].append(field_data)
                    
                forms.append(form_data)
            return forms
        except Exception as e:
            self.logger.error(f"Form analysis failed: {str(e)}")
            raise
    
    def analyze_links(self) -> List[Dict[str, str]]:
        """
        Analyze page links
        
        Returns:
            List[Dict[str, str]]: Link analysis results
        """
        try:
            links = []
            for link in self.driver.find_elements(By.TAG_NAME, 'a'):
                link_data = {
                    'href': link.get_attribute('href'),
                    'text': link.text.strip(),
                    'title': link.get_attribute('title'),
                    'target': link.get_attribute('target')
                }
                links.append(link_data)
            return links
        except Exception as e:
            self.logger.error(f"Link analysis failed: {str(e)}")
            raise

# Create global page analyzer instance
page_analyzer = None  # Will be initialized with WebDriver
