"""
Behavior Designer

行为设计器，负责为每个身份设计独特的行为模式。
"""

import logging
import random
import json
from typing import Dict, Any, Optional, List
from datetime import datetime


class BehaviorDesigner:
    """行为设计器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.behavior_profiles: Dict[str, Dict[str, Any]] = {}
        
        # 行为模板
        self.behavior_templates = {
            "conservative": {
                "mouse_speed": (0.5, 1.0),
                "typing_speed": (40, 70),
                "scroll_pattern": "smooth",
                "click_delay": (0.3, 0.8),
                "browsing_style": "methodical",
                "session_duration": (600, 1800),
                "break_frequency": (0.2, 0.4),
                "error_rate": (0.01, 0.03)
            },
            "normal": {
                "mouse_speed": (0.8, 1.5),
                "typing_speed": (60, 100),
                "scroll_pattern": "mixed",
                "click_delay": (0.1, 0.4),
                "browsing_style": "exploratory",
                "session_duration": (300, 2400),
                "break_frequency": (0.1, 0.3),
                "error_rate": (0.02, 0.05)
            },
            "aggressive": {
                "mouse_speed": (1.2, 2.5),
                "typing_speed": (80, 140),
                "scroll_pattern": "fast",
                "click_delay": (0.05, 0.2),
                "browsing_style": "focused",
                "session_duration": (180, 1200),
                "break_frequency": (0.05, 0.2),
                "error_rate": (0.03, 0.08)
            }
        }
    
    async def initialize(self) -> bool:
        """初始化行为设计器"""
        try:
            self.logger.info("Behavior Designer initialized")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize Behavior Designer: {e}")
            return False
    
    async def design_behavior(self, identity_id: str, persona_type: str = "normal") -> Optional[Dict[str, Any]]:
        """为身份设计行为模式"""
        try:
            # 选择行为模板
            template = self.behavior_templates.get(persona_type, self.behavior_templates["normal"])
            
            # 生成具体的行为参数
            behavior_profile = {
                "identity_id": identity_id,
                "persona_type": persona_type,
                "created_at": datetime.utcnow().isoformat(),
                
                # 鼠标行为
                "mouse_speed": self._random_from_range(template["mouse_speed"]),
                "mouse_acceleration": random.uniform(0.1, 0.5),
                "mouse_jitter": random.uniform(0.0, 0.1),
                
                # 键盘行为
                "typing_speed": self._random_from_range(template["typing_speed"]),
                "typing_rhythm": random.choice(["steady", "burst", "variable"]),
                "backspace_frequency": random.uniform(0.02, 0.08),
                
                # 滚动行为
                "scroll_pattern": template["scroll_pattern"],
                "scroll_speed": random.uniform(0.5, 2.0),
                "scroll_pause_frequency": random.uniform(0.1, 0.3),
                
                # 点击行为
                "click_delay": self._random_from_range(template["click_delay"]),
                "double_click_speed": random.uniform(0.1, 0.3),
                "right_click_frequency": random.uniform(0.05, 0.15),
                
                # 浏览行为
                "browsing_style": template["browsing_style"],
                "page_dwell_time": self._random_from_range((5, 120)),
                "tab_switching_frequency": random.uniform(0.1, 0.4),
                
                # 会话行为
                "session_duration": self._random_from_range(template["session_duration"]),
                "break_frequency": self._random_from_range(template["break_frequency"]),
                "break_duration": self._random_from_range((30, 300)),
                
                # 错误行为
                "error_rate": self._random_from_range(template["error_rate"]),
                "recovery_time": self._random_from_range((1, 5)),
                
                # 特殊行为模式
                "attention_span": random.randint(30, 180),  # 注意力持续时间（秒）
                "multitasking_tendency": random.uniform(0.0, 0.5),
                "impatience_level": random.uniform(0.0, 0.3),
                
                # 时间模式
                "preferred_hours": self._generate_preferred_hours(),
                "timezone_consistency": random.uniform(0.8, 1.0),
                
                # 设备偏好
                "device_familiarity": random.uniform(0.6, 1.0),
                "shortcut_usage": random.uniform(0.1, 0.8),
                
                # 安全意识
                "security_awareness": random.uniform(0.3, 0.9),
                "privacy_concern": random.uniform(0.2, 0.8)
            }
            
            # 添加个性化变异
            behavior_profile = self._add_personality_variations(behavior_profile)
            
            self.behavior_profiles[identity_id] = behavior_profile
            self.logger.info(f"Designed {persona_type} behavior profile for identity {identity_id}")
            
            return behavior_profile
            
        except Exception as e:
            self.logger.error(f"Failed to design behavior: {e}")
            return None
    
    async def get_behavior_profile(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """获取行为配置文件"""
        return self.behavior_profiles.get(identity_id)
    
    async def update_behavior_profile(self, identity_id: str, updates: Dict[str, Any]) -> bool:
        """更新行为配置文件"""
        try:
            if identity_id in self.behavior_profiles:
                self.behavior_profiles[identity_id].update(updates)
                self.behavior_profiles[identity_id]["updated_at"] = datetime.utcnow().isoformat()
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to update behavior profile: {e}")
            return False
    
    async def adapt_behavior(self, identity_id: str, feedback: Dict[str, Any]) -> bool:
        """根据反馈调整行为"""
        try:
            profile = self.behavior_profiles.get(identity_id)
            if not profile:
                return False
            
            # 根据反馈调整行为参数
            if feedback.get("detected_as_bot"):
                # 如果被检测为机器人，调整为更保守的行为
                profile["mouse_speed"] *= 0.8
                profile["typing_speed"] *= 0.9
                profile["click_delay"] *= 1.2
                profile["error_rate"] *= 1.1
                
            elif feedback.get("too_slow"):
                # 如果太慢，稍微加快速度
                profile["mouse_speed"] *= 1.1
                profile["typing_speed"] *= 1.05
                profile["click_delay"] *= 0.9
                
            profile["adapted_at"] = datetime.utcnow().isoformat()
            profile["adaptation_count"] = profile.get("adaptation_count", 0) + 1
            
            self.logger.info(f"Adapted behavior for identity {identity_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to adapt behavior: {e}")
            return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            if not self.behavior_profiles:
                return {"total_profiles": 0}
            
            # 计算平均值
            total_profiles = len(self.behavior_profiles)
            avg_mouse_speed = sum(p.get("mouse_speed", 0) for p in self.behavior_profiles.values()) / total_profiles
            avg_typing_speed = sum(p.get("typing_speed", 0) for p in self.behavior_profiles.values()) / total_profiles
            
            # 统计persona类型分布
            persona_distribution = {}
            for profile in self.behavior_profiles.values():
                persona_type = profile.get("persona_type", "unknown")
                persona_distribution[persona_type] = persona_distribution.get(persona_type, 0) + 1
            
            return {
                "total_profiles": total_profiles,
                "avg_mouse_speed": round(avg_mouse_speed, 2),
                "avg_typing_speed": round(avg_typing_speed, 2),
                "persona_distribution": persona_distribution,
                "total_adaptations": sum(p.get("adaptation_count", 0) for p in self.behavior_profiles.values())
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get statistics: {e}")
            return {}
    
    def _random_from_range(self, range_tuple) -> float:
        """从范围中生成随机值"""
        if isinstance(range_tuple, tuple) and len(range_tuple) == 2:
            return random.uniform(range_tuple[0], range_tuple[1])
        return range_tuple
    
    def _generate_preferred_hours(self) -> List[int]:
        """生成偏好的活动时间"""
        # 生成1-3个活动时间段
        time_blocks = []
        num_blocks = random.randint(1, 3)
        
        for _ in range(num_blocks):
            start_hour = random.randint(6, 22)
            duration = random.randint(2, 8)
            end_hour = min(23, start_hour + duration)
            time_blocks.extend(range(start_hour, end_hour + 1))
        
        return sorted(list(set(time_blocks)))
    
    def _add_personality_variations(self, profile: Dict[str, Any]) -> Dict[str, Any]:
        """添加个性化变异"""
        # 添加一些随机变异使每个配置文件更独特
        variation_factor = random.uniform(0.9, 1.1)
        
        # 对数值参数添加小幅变异
        numeric_keys = ["mouse_speed", "typing_speed", "click_delay", "scroll_speed"]
        for key in numeric_keys:
            if key in profile:
                profile[key] *= variation_factor
        
        # 添加独特的习惯
        habits = []
        if random.random() < 0.3:
            habits.append("frequent_tab_switching")
        if random.random() < 0.2:
            habits.append("uses_keyboard_shortcuts")
        if random.random() < 0.4:
            habits.append("pauses_to_read")
        if random.random() < 0.1:
            habits.append("perfectionist_typing")
        
        profile["unique_habits"] = habits
        
        return profile
