"""
Transaction Recovery

交易恢复工具，负责处理失败交易的重试、替换和恢复操作。
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta


class TransactionRecovery:
    """
    交易恢复工具
    
    负责处理各种交易失败情况，包括Gas不足、nonce错误、网络拥堵等问题的恢复。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化交易恢复工具
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 恢复策略配置
        self.recovery_config = {
            "max_retry_attempts": config.get("max_retry_attempts", 3),
            "gas_increase_factor": config.get("gas_increase_factor", 1.2),
            "gas_price_increase_factor": config.get("gas_price_increase_factor", 1.1),
            "retry_delay_seconds": config.get("retry_delay_seconds", 30),
            "max_gas_price_gwei": config.get("max_gas_price_gwei", 100)
        }
        
        # 恢复记录
        self.recovery_records: Dict[str, List[Dict]] = {}
        
        # 恢复统计
        self.recovery_stats = {
            'total_recovery_attempts': 0,
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'gas_adjustments': 0,
            'nonce_corrections': 0,
            'transaction_replacements': 0
        }
        
        # 支持的恢复策略
        self.recovery_strategies = [
            "retry_with_higher_gas",
            "replace_transaction",
            "cancel_transaction",
            "adjust_nonce",
            "wait_and_retry"
        ]
    
    async def initialize(self) -> bool:
        """
        初始化交易恢复工具
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("Transaction Recovery initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Transaction Recovery: {e}")
            return False
    
    async def recover_failed_transaction(self, original_tx: Dict[str, Any], 
                                       failure_reason: str,
                                       strategy: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        恢复失败的交易
        
        Args:
            original_tx: 原始交易数据
            failure_reason: 失败原因
            strategy: 恢复策略
            
        Returns:
            Optional[Dict[str, Any]]: 恢复后的交易数据
        """
        try:
            self.recovery_stats['total_recovery_attempts'] += 1
            
            # 记录恢复尝试
            tx_hash = original_tx.get("hash", "unknown")
            if tx_hash not in self.recovery_records:
                self.recovery_records[tx_hash] = []
            
            recovery_record = {
                "attempt_time": datetime.utcnow().isoformat(),
                "failure_reason": failure_reason,
                "strategy": strategy,
                "original_tx": original_tx.copy()
            }
            
            # 自动选择恢复策略
            if not strategy:
                strategy = self._select_recovery_strategy(failure_reason)
            
            recovery_record["selected_strategy"] = strategy
            
            # 执行恢复策略
            recovered_tx = await self._execute_recovery_strategy(
                original_tx, failure_reason, strategy
            )
            
            if recovered_tx:
                recovery_record["recovered_tx"] = recovered_tx
                recovery_record["status"] = "success"
                self.recovery_stats['successful_recoveries'] += 1
                
                self.logger.info(f"Successfully recovered transaction using {strategy}")
            else:
                recovery_record["status"] = "failed"
                self.recovery_stats['failed_recoveries'] += 1
                
                self.logger.error(f"Failed to recover transaction using {strategy}")
            
            self.recovery_records[tx_hash].append(recovery_record)
            return recovered_tx
            
        except Exception as e:
            self.logger.error(f"Error in transaction recovery: {e}")
            self.recovery_stats['failed_recoveries'] += 1
            return None
    
    def _select_recovery_strategy(self, failure_reason: str) -> str:
        """
        根据失败原因选择恢复策略
        
        Args:
            failure_reason: 失败原因
            
        Returns:
            str: 选择的恢复策略
        """
        try:
            failure_lower = failure_reason.lower()
            
            if "gas" in failure_lower and ("limit" in failure_lower or "insufficient" in failure_lower):
                return "retry_with_higher_gas"
            elif "nonce" in failure_lower:
                return "adjust_nonce"
            elif "replacement" in failure_lower or "underpriced" in failure_lower:
                return "replace_transaction"
            elif "network" in failure_lower or "timeout" in failure_lower:
                return "wait_and_retry"
            else:
                return "retry_with_higher_gas"  # 默认策略
                
        except Exception as e:
            self.logger.error(f"Error selecting recovery strategy: {e}")
            return "retry_with_higher_gas"
