"""
风险计算器

该模块实现了计算项目风险分数的功能。
"""

import logging
import time
from typing import Dict, List, Any, Optional

from discovery.models.project import Project


class RiskCalculator:
    """风险计算器，负责计算项目风险分数"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化风险计算器
        
        Args:
            config: 配置字典，包含风险计算器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.weights = config.get('weights', {
            'team_verification': 0.2,
            'social_verification': 0.15,
            'project_verification': 0.15,
            'contract_analysis': 0.2,
            'vulnerability_scan': 0.2,
            'permission_analysis': 0.1
        })
        
        # 统计信息
        self._stats = {
            'total_calculations': 0,
            'high_risk': 0,
            'medium_risk': 0,
            'low_risk': 0
        }
    
    def calculate(self, project: Project, team_result: Optional[Dict[str, Any]] = None,
                 social_result: Optional[Dict[str, Any]] = None, project_result: Optional[Dict[str, Any]] = None,
                 contract_result: Optional[Dict[str, Any]] = None, vulnerability_result: Optional[Dict[str, Any]] = None,
                 permission_result: Optional[Dict[str, Any]] = None) -> float:
        """
        计算项目风险分数
        
        Args:
            project: 项目对象
            team_result: 团队验证结果
            social_result: 社交媒体验证结果
            project_result: 项目真实性验证结果
            contract_result: 合约分析结果
            vulnerability_result: 漏洞扫描结果
            permission_result: 权限分析结果
            
        Returns:
            风险分数 (0-100)，分数越高风险越大
        """
        self.logger.info(f"开始计算项目 {project.name} (ID: {project.id}) 的风险分数")
        
        try:
            # 更新统计信息
            self._stats['total_calculations'] += 1
            
            # 计算各部分风险分数
            team_risk = self._calculate_team_risk(team_result)
            social_risk = self._calculate_social_risk(social_result)
            project_risk = self._calculate_project_risk(project_result)
            contract_risk = self._calculate_contract_risk(contract_result)
            vulnerability_risk = self._calculate_vulnerability_risk(vulnerability_result)
            permission_risk = self._calculate_permission_risk(permission_result)
            
            # 计算加权风险分数
            risk_score = (
                team_risk * self.weights.get('team_verification', 0.2) +
                social_risk * self.weights.get('social_verification', 0.15) +
                project_risk * self.weights.get('project_verification', 0.15) +
                contract_risk * self.weights.get('contract_analysis', 0.2) +
                vulnerability_risk * self.weights.get('vulnerability_scan', 0.2) +
                permission_risk * self.weights.get('permission_analysis', 0.1)
            )
            
            # 确保分数在0-100范围内
            risk_score = max(0, min(100, risk_score))
            
            # 更新风险统计
            if risk_score >= 70:
                self._stats['high_risk'] += 1
            elif risk_score >= 40:
                self._stats['medium_risk'] += 1
            else:
                self._stats['low_risk'] += 1
            
            self.logger.info(f"项目 {project.name} 风险分数计算完成: {risk_score:.2f}")
            
            return risk_score
        
        except Exception as e:
            self.logger.error(f"计算项目 {project.name} 风险分数时出错: {str(e)}")
            return 50.0  # 出错时返回中等风险
    
    def _calculate_team_risk(self, team_result: Optional[Dict[str, Any]]) -> float:
        """
        计算团队风险分数
        
        Args:
            team_result: 团队验证结果
            
        Returns:
            风险分数 (0-100)
        """
        if not team_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据验证结果调整风险分数
        if team_result.get('verified', False):
            risk_score -= 30.0
        
        # 根据置信度调整风险分数
        confidence = team_result.get('confidence', 0.0)
        risk_score -= confidence * 20.0
        
        # 根据团队规模调整风险分数
        team_size = team_result.get('team_size', 0)
        if team_size >= 5:
            risk_score -= 10.0
        elif team_size >= 3:
            risk_score -= 5.0
        elif team_size <= 1:
            risk_score += 10.0
        
        # 根据已知成员比例调整风险分数
        if team_size > 0:
            known_ratio = team_result.get('known_members', 0) / team_size
            risk_score -= known_ratio * 10.0
        
        # 根据团队成员角色调整风险分数
        if team_result.get('has_technical_members', False):
            risk_score -= 5.0
        
        if team_result.get('has_business_members', False):
            risk_score -= 5.0
        
        # 根据已知诈骗者调整风险分数
        if team_result.get('has_known_scammers', False):
            risk_score += 50.0
        
        # 根据警告数量调整风险分数
        warnings = team_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def _calculate_social_risk(self, social_result: Optional[Dict[str, Any]]) -> float:
        """
        计算社交媒体风险分数
        
        Args:
            social_result: 社交媒体验证结果
            
        Returns:
            风险分数 (0-100)
        """
        if not social_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据验证结果调整风险分数
        if social_result.get('verified', False):
            risk_score -= 30.0
        
        # 根据置信度调整风险分数
        confidence = social_result.get('confidence', 0.0)
        risk_score -= confidence * 20.0
        
        # 根据渠道数量调整风险分数
        channel_count = social_result.get('channel_count', 0)
        if channel_count >= 3:
            risk_score -= 10.0
        elif channel_count >= 2:
            risk_score -= 5.0
        elif channel_count <= 0:
            risk_score += 20.0
        
        # 根据已验证渠道比例调整风险分数
        if channel_count > 0:
            verified_ratio = social_result.get('verified_channels', 0) / channel_count
            risk_score -= verified_ratio * 10.0
        
        # 根据总粉丝数调整风险分数
        total_followers = social_result.get('total_followers', 0)
        if total_followers >= 10000:
            risk_score -= 15.0
        elif total_followers >= 1000:
            risk_score -= 10.0
        elif total_followers >= 100:
            risk_score -= 5.0
        
        # 根据可疑内容调整风险分数
        if social_result.get('has_suspicious_content', False):
            risk_score += 30.0
        
        # 根据活动级别调整风险分数
        activity_level = social_result.get('activity_level', 'unknown')
        if activity_level == 'high':
            risk_score -= 10.0
        elif activity_level == 'medium':
            risk_score -= 5.0
        elif activity_level == 'very_low':
            risk_score += 10.0
        
        # 根据警告数量调整风险分数
        warnings = social_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def _calculate_project_risk(self, project_result: Optional[Dict[str, Any]]) -> float:
        """
        计算项目真实性风险分数
        
        Args:
            project_result: 项目真实性验证结果
            
        Returns:
            风险分数 (0-100)
        """
        if not project_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据验证结果调整风险分数
        if project_result.get('verified', False):
            risk_score -= 30.0
        
        # 根据置信度调整风险分数
        confidence = project_result.get('confidence', 0.0)
        risk_score -= confidence * 20.0
        
        # 根据网站可用性调整风险分数
        if project_result.get('website_available', False):
            risk_score -= 10.0
        else:
            risk_score += 20.0
        
        # 根据网站年龄调整风险分数
        website_age = project_result.get('website_age')
        if website_age is not None:
            if website_age >= 180:  # 6个月以上
                risk_score -= 15.0
            elif website_age >= 90:  # 3个月以上
                risk_score -= 10.0
            elif website_age >= 30:  # 1个月以上
                risk_score -= 5.0
            elif website_age < 7:  # 不到1周
                risk_score += 15.0
        
        # 根据内容质量调整风险分数
        content_quality = project_result.get('content_quality', 'unknown')
        if content_quality == 'high':
            risk_score -= 15.0
        elif content_quality == 'medium':
            risk_score -= 10.0
        elif content_quality == 'low':
            risk_score -= 5.0
        elif content_quality == 'poor':
            risk_score += 10.0
        
        # 根据必要页面调整风险分数
        if project_result.get('has_required_pages', False):
            risk_score -= 10.0
        
        # 根据可疑域名调整风险分数
        if project_result.get('has_suspicious_domain', False):
            risk_score += 20.0
        
        # 根据SSL证书调整风险分数
        if project_result.get('has_ssl', False):
            risk_score -= 5.0
        else:
            risk_score += 10.0
        
        # 根据WHOIS信息调整风险分数
        if project_result.get('has_whois_info', False):
            risk_score -= 5.0
        
        # 根据警告数量调整风险分数
        warnings = project_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def _calculate_contract_risk(self, contract_result: Optional[Dict[str, Any]]) -> float:
        """
        计算合约风险分数
        
        Args:
            contract_result: 合约分析结果
            
        Returns:
            风险分数 (0-100)
        """
        if not contract_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据分析结果调整风险分数
        if not contract_result.get('analyzed', False):
            return 60.0  # 未分析时返回较高风险
        
        # 根据合约验证状态调整风险分数
        if contract_result.get('contract_verified', False):
            risk_score -= 20.0
        else:
            risk_score += 30.0
            return max(0, min(100, risk_score))  # 未验证合约直接返回高风险
        
        # 根据源代码可用性调整风险分数
        if contract_result.get('has_source_code', False):
            risk_score -= 10.0
        else:
            risk_score += 20.0
        
        # 根据ABI可用性调整风险分数
        if contract_result.get('has_abi', False):
            risk_score -= 5.0
        
        # 根据代理合约调整风险分数
        if contract_result.get('is_proxy', False):
            risk_score += 10.0
        
        # 根据风险级别调整风险分数
        risk_level = contract_result.get('risk_level', 'unknown')
        if risk_level == 'high':
            risk_score += 30.0
        elif risk_level == 'medium':
            risk_score += 15.0
        elif risk_level == 'low':
            risk_score -= 10.0
        
        # 根据警告数量调整风险分数
        warnings = contract_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def _calculate_vulnerability_risk(self, vulnerability_result: Optional[Dict[str, Any]]) -> float:
        """
        计算漏洞风险分数
        
        Args:
            vulnerability_result: 漏洞扫描结果
            
        Returns:
            风险分数 (0-100)
        """
        if not vulnerability_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据扫描结果调整风险分数
        if not vulnerability_result.get('scanned', False):
            return 50.0  # 未扫描时返回中等风险
        
        # 根据漏洞数量调整风险分数
        critical_vulnerabilities = vulnerability_result.get('critical_vulnerabilities', 0)
        high_vulnerabilities = vulnerability_result.get('high_vulnerabilities', 0)
        medium_vulnerabilities = vulnerability_result.get('medium_vulnerabilities', 0)
        low_vulnerabilities = vulnerability_result.get('low_vulnerabilities', 0)
        
        # 严重漏洞
        if critical_vulnerabilities > 0:
            risk_score += 50.0
        
        # 高风险漏洞
        if high_vulnerabilities > 0:
            risk_score += min(30.0, high_vulnerabilities * 10.0)
        
        # 中风险漏洞
        if medium_vulnerabilities > 0:
            risk_score += min(15.0, medium_vulnerabilities * 5.0)
        
        # 低风险漏洞
        if low_vulnerabilities > 0:
            risk_score += min(5.0, low_vulnerabilities * 1.0)
        
        # 根据警告数量调整风险分数
        warnings = vulnerability_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def _calculate_permission_risk(self, permission_result: Optional[Dict[str, Any]]) -> float:
        """
        计算权限风险分数
        
        Args:
            permission_result: 权限分析结果
            
        Returns:
            风险分数 (0-100)
        """
        if not permission_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据分析结果调整风险分数
        if not permission_result.get('analyzed', False):
            return 50.0  # 未分析时返回中等风险
        
        # 根据集中化级别调整风险分数
        centralization_level = permission_result.get('centralization_level', 'unknown')
        if centralization_level == 'high':
            risk_score += 30.0
        elif centralization_level == 'medium':
            risk_score += 15.0
        elif centralization_level == 'low':
            risk_score -= 10.0
        
        # 根据危险函数调整风险分数
        dangerous_functions = permission_result.get('dangerous_functions', [])
        
        for func in dangerous_functions:
            risk_level = func.get('risk_level', 'low')
            has_permission_check = func.get('has_permission_check', False)
            
            if risk_level == 'critical':
                risk_score += 20.0 if not has_permission_check else 10.0
            elif risk_level == 'high':
                risk_score += 10.0 if not has_permission_check else 5.0
            elif risk_level == 'medium':
                risk_score += 5.0 if not has_permission_check else 2.0
        
        # 根据警告数量调整风险分数
        warnings = permission_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()"""
风险计算器

该模块实现了计算项目风险分数的功能。
"""

import logging
import time
from typing import Dict, List, Any, Optional

from discovery.models.project import Project


class RiskCalculator:
    """风险计算器，负责计算项目风险分数"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化风险计算器
        
        Args:
            config: 配置字典，包含风险计算器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.weights = config.get('weights', {
            'team_verification': 0.2,
            'social_verification': 0.15,
            'project_verification': 0.15,
            'contract_analysis': 0.2,
            'vulnerability_scan': 0.2,
            'permission_analysis': 0.1
        })
        
        # 统计信息
        self._stats = {
            'total_calculations': 0,
            'high_risk': 0,
            'medium_risk': 0,
            'low_risk': 0
        }
    
    def calculate(self, project: Project, team_result: Optional[Dict[str, Any]] = None,
                 social_result: Optional[Dict[str, Any]] = None, project_result: Optional[Dict[str, Any]] = None,
                 contract_result: Optional[Dict[str, Any]] = None, vulnerability_result: Optional[Dict[str, Any]] = None,
                 permission_result: Optional[Dict[str, Any]] = None) -> float:
        """
        计算项目风险分数
        
        Args:
            project: 项目对象
            team_result: 团队验证结果
            social_result: 社交媒体验证结果
            project_result: 项目真实性验证结果
            contract_result: 合约分析结果
            vulnerability_result: 漏洞扫描结果
            permission_result: 权限分析结果
            
        Returns:
            风险分数 (0-100)，分数越高风险越大
        """
        self.logger.info(f"开始计算项目 {project.name} (ID: {project.id}) 的风险分数")
        
        try:
            # 更新统计信息
            self._stats['total_calculations'] += 1
            
            # 计算各部分风险分数
            team_risk = self._calculate_team_risk(team_result)
            social_risk = self._calculate_social_risk(social_result)
            project_risk = self._calculate_project_risk(project_result)
            contract_risk = self._calculate_contract_risk(contract_result)
            vulnerability_risk = self._calculate_vulnerability_risk(vulnerability_result)
            permission_risk = self._calculate_permission_risk(permission_result)
            
            # 计算加权风险分数
            risk_score = (
                team_risk * self.weights.get('team_verification', 0.2) +
                social_risk * self.weights.get('social_verification', 0.15) +
                project_risk * self.weights.get('project_verification', 0.15) +
                contract_risk * self.weights.get('contract_analysis', 0.2) +
                vulnerability_risk * self.weights.get('vulnerability_scan', 0.2) +
                permission_risk * self.weights.get('permission_analysis', 0.1)
            )
            
            # 确保分数在0-100范围内
            risk_score = max(0, min(100, risk_score))
            
            # 更新风险统计
            if risk_score >= 70:
                self._stats['high_risk'] += 1
            elif risk_score >= 40:
                self._stats['medium_risk'] += 1
            else:
                self._stats['low_risk'] += 1
            
            self.logger.info(f"项目 {project.name} 风险分数计算完成: {risk_score:.2f}")
            
            return risk_score
        
        except Exception as e:
            self.logger.error(f"计算项目 {project.name} 风险分数时出错: {str(e)}")
            return 50.0  # 出错时返回中等风险
    
    def _calculate_team_risk(self, team_result: Optional[Dict[str, Any]]) -> float:
        """
        计算团队风险分数
        
        Args:
            team_result: 团队验证结果
            
        Returns:
            风险分数 (0-100)
        """
        if not team_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据验证结果调整风险分数
        if team_result.get('verified', False):
            risk_score -= 30.0
        
        # 根据置信度调整风险分数
        confidence = team_result.get('confidence', 0.0)
        risk_score -= confidence * 20.0
        
        # 根据团队规模调整风险分数
        team_size = team_result.get('team_size', 0)
        if team_size >= 5:
            risk_score -= 10.0
        elif team_size >= 3:
            risk_score -= 5.0
        elif team_size <= 1:
            risk_score += 10.0
        
        # 根据已知成员比例调整风险分数
        if team_size > 0:
            known_ratio = team_result.get('known_members', 0) / team_size
            risk_score -= known_ratio * 10.0
        
        # 根据团队成员角色调整风险分数
        if team_result.get('has_technical_members', False):
            risk_score -= 5.0
        
        if team_result.get('has_business_members', False):
            risk_score -= 5.0
        
        # 根据已知诈骗者调整风险分数
        if team_result.get('has_known_scammers', False):
            risk_score += 50.0
        
        # 根据警告数量调整风险分数
        warnings = team_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def _calculate_social_risk(self, social_result: Optional[Dict[str, Any]]) -> float:
        """
        计算社交媒体风险分数
        
        Args:
            social_result: 社交媒体验证结果
            
        Returns:
            风险分数 (0-100)
        """
        if not social_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据验证结果调整风险分数
        if social_result.get('verified', False):
            risk_score -= 30.0
        
        # 根据置信度调整风险分数
        confidence = social_result.get('confidence', 0.0)
        risk_score -= confidence * 20.0
        
        # 根据渠道数量调整风险分数
        channel_count = social_result.get('channel_count', 0)
        if channel_count >= 3:
            risk_score -= 10.0
        elif channel_count >= 2:
            risk_score -= 5.0
        elif channel_count <= 0:
            risk_score += 20.0
        
        # 根据已验证渠道比例调整风险分数
        if channel_count > 0:
            verified_ratio = social_result.get('verified_channels', 0) / channel_count
            risk_score -= verified_ratio * 10.0
        
        # 根据总粉丝数调整风险分数
        total_followers = social_result.get('total_followers', 0)
        if total_followers >= 10000:
            risk_score -= 15.0
        elif total_followers >= 1000:
            risk_score -= 10.0
        elif total_followers >= 100:
            risk_score -= 5.0
        
        # 根据可疑内容调整风险分数
        if social_result.get('has_suspicious_content', False):
            risk_score += 30.0
        
        # 根据活动级别调整风险分数
        activity_level = social_result.get('activity_level', 'unknown')
        if activity_level == 'high':
            risk_score -= 10.0
        elif activity_level == 'medium':
            risk_score -= 5.0
        elif activity_level == 'very_low':
            risk_score += 10.0
        
        # 根据警告数量调整风险分数
        warnings = social_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def _calculate_project_risk(self, project_result: Optional[Dict[str, Any]]) -> float:
        """
        计算项目真实性风险分数
        
        Args:
            project_result: 项目真实性验证结果
            
        Returns:
            风险分数 (0-100)
        """
        if not project_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据验证结果调整风险分数
        if project_result.get('verified', False):
            risk_score -= 30.0
        
        # 根据置信度调整风险分数
        confidence = project_result.get('confidence', 0.0)
        risk_score -= confidence * 20.0
        
        # 根据网站可用性调整风险分数
        if project_result.get('website_available', False):
            risk_score -= 10.0
        else:
            risk_score += 20.0
        
        # 根据网站年龄调整风险分数
        website_age = project_result.get('website_age')
        if website_age is not None:
            if website_age >= 180:  # 6个月以上
                risk_score -= 15.0
            elif website_age >= 90:  # 3个月以上
                risk_score -= 10.0
            elif website_age >= 30:  # 1个月以上
                risk_score -= 5.0
            elif website_age < 7:  # 不到1周
                risk_score += 15.0
        
        # 根据内容质量调整风险分数
        content_quality = project_result.get('content_quality', 'unknown')
        if content_quality == 'high':
            risk_score -= 15.0
        elif content_quality == 'medium':
            risk_score -= 10.0
        elif content_quality == 'low':
            risk_score -= 5.0
        elif content_quality == 'poor':
            risk_score += 10.0
        
        # 根据必要页面调整风险分数
        if project_result.get('has_required_pages', False):
            risk_score -= 10.0
        
        # 根据可疑域名调整风险分数
        if project_result.get('has_suspicious_domain', False):
            risk_score += 20.0
        
        # 根据SSL证书调整风险分数
        if project_result.get('has_ssl', False):
            risk_score -= 5.0
        else:
            risk_score += 10.0
        
        # 根据WHOIS信息调整风险分数
        if project_result.get('has_whois_info', False):
            risk_score -= 5.0
        
        # 根据警告数量调整风险分数
        warnings = project_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def _calculate_contract_risk(self, contract_result: Optional[Dict[str, Any]]) -> float:
        """
        计算合约风险分数
        
        Args:
            contract_result: 合约分析结果
            
        Returns:
            风险分数 (0-100)
        """
        if not contract_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据分析结果调整风险分数
        if not contract_result.get('analyzed', False):
            return 60.0  # 未分析时返回较高风险
        
        # 根据合约验证状态调整风险分数
        if contract_result.get('contract_verified', False):
            risk_score -= 20.0
        else:
            risk_score += 30.0
            return max(0, min(100, risk_score))  # 未验证合约直接返回高风险
        
        # 根据源代码可用性调整风险分数
        if contract_result.get('has_source_code', False):
            risk_score -= 10.0
        else:
            risk_score += 20.0
        
        # 根据ABI可用性调整风险分数
        if contract_result.get('has_abi', False):
            risk_score -= 5.0
        
        # 根据代理合约调整风险分数
        if contract_result.get('is_proxy', False):
            risk_score += 10.0
        
        # 根据风险级别调整风险分数
        risk_level = contract_result.get('risk_level', 'unknown')
        if risk_level == 'high':
            risk_score += 30.0
        elif risk_level == 'medium':
            risk_score += 15.0
        elif risk_level == 'low':
            risk_score -= 10.0
        
        # 根据警告数量调整风险分数
        warnings = contract_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def _calculate_vulnerability_risk(self, vulnerability_result: Optional[Dict[str, Any]]) -> float:
        """
        计算漏洞风险分数
        
        Args:
            vulnerability_result: 漏洞扫描结果
            
        Returns:
            风险分数 (0-100)
        """
        if not vulnerability_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据扫描结果调整风险分数
        if not vulnerability_result.get('scanned', False):
            return 50.0  # 未扫描时返回中等风险
        
        # 根据漏洞数量调整风险分数
        critical_vulnerabilities = vulnerability_result.get('critical_vulnerabilities', 0)
        high_vulnerabilities = vulnerability_result.get('high_vulnerabilities', 0)
        medium_vulnerabilities = vulnerability_result.get('medium_vulnerabilities', 0)
        low_vulnerabilities = vulnerability_result.get('low_vulnerabilities', 0)
        
        # 严重漏洞
        if critical_vulnerabilities > 0:
            risk_score += 50.0
        
        # 高风险漏洞
        if high_vulnerabilities > 0:
            risk_score += min(30.0, high_vulnerabilities * 10.0)
        
        # 中风险漏洞
        if medium_vulnerabilities > 0:
            risk_score += min(15.0, medium_vulnerabilities * 5.0)
        
        # 低风险漏洞
        if low_vulnerabilities > 0:
            risk_score += min(5.0, low_vulnerabilities * 1.0)
        
        # 根据警告数量调整风险分数
        warnings = vulnerability_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def _calculate_permission_risk(self, permission_result: Optional[Dict[str, Any]]) -> float:
        """
        计算权限风险分数
        
        Args:
            permission_result: 权限分析结果
            
        Returns:
            风险分数 (0-100)
        """
        if not permission_result:
            return 50.0  # 没有结果时返回中等风险
        
        # 初始风险分数
        risk_score = 50.0
        
        # 根据分析结果调整风险分数
        if not permission_result.get('analyzed', False):
            return 50.0  # 未分析时返回中等风险
        
        # 根据集中化级别调整风险分数
        centralization_level = permission_result.get('centralization_level', 'unknown')
        if centralization_level == 'high':
            risk_score += 30.0
        elif centralization_level == 'medium':
            risk_score += 15.0
        elif centralization_level == 'low':
            risk_score -= 10.0
        
        # 根据危险函数调整风险分数
        dangerous_functions = permission_result.get('dangerous_functions', [])
        
        for func in dangerous_functions:
            risk_level = func.get('risk_level', 'low')
            has_permission_check = func.get('has_permission_check', False)
            
            if risk_level == 'critical':
                risk_score += 20.0 if not has_permission_check else 10.0
            elif risk_level == 'high':
                risk_score += 10.0 if not has_permission_check else 5.0
            elif risk_level == 'medium':
                risk_score += 5.0 if not has_permission_check else 2.0
        
        # 根据警告数量调整风险分数
        warnings = permission_result.get('warnings', [])
        risk_score += len(warnings) * 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, risk_score))
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()