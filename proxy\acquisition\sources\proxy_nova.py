"""
ProxyNova 代理源

该模块实现了从 ProxyNova 网站获取代理的功能。
"""

import logging
import requests
import time
import random
import re
from typing import Dict, List, Any
from bs4 import BeautifulSoup

from proxy.models.proxy import Proxy
from proxy.acquisition.sources.base_source import BaseProxySource


class ProxyNovaSource(BaseProxySource):
    """ProxyNova 代理源"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 ProxyNova 代理源
        
        Args:
            config: 配置字典，包含代理源的配置信息
        """
        super().__init__(config)
        self.url = config.get('url', 'https://www.proxynova.com/proxy-server-list/')
        self.timeout = config.get('timeout', 10)
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
    
    def get_proxies(self, count: int) -> List[Proxy]:
        """
        从 ProxyNova 获取代理
        
        Args:
            count: 要获取的代理数量
            
        Returns:
            代理列表
        """
        self.logger.info(f"从 ProxyNova 获取 {count} 个代理")
        
        try:
            # 随机选择一个 User-Agent
            headers = {
                'User-Agent': random.choice(self.user_agents)
            }
            
            # 发送请求
            response = requests.get(self.url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找代理表格
            table = soup.find('table', {'id': 'tbl_proxy_list'})
            if not table:
                self.logger.warning("未找到代理表格")
                return []
            
            # 解析表格
            proxies = []
            rows = table.find('tbody').find_all('tr')
            
            for row in rows:
                try:
                    # 跳过广告行
                    if 'class' in row.attrs and 'ad' in row.attrs['class']:
                        continue
                    
                    cols = row.find_all('td')
                    if len(cols) < 2:
                        continue
                    
                    # 获取 IP 地址（需要解析 JavaScript）
                    ip_cell = cols[0]
                    ip_script = ip_cell.find('script')
                    if not ip_script:
                        continue
                    
                    # 尝试从脚本中提取 IP
                    script_text = ip_script.text
                    ip_match = re.search(r'document\.write\(\'([\d\.]+)\'\)', script_text)
                    if not ip_match:
                        continue
                    
                    ip = ip_match.group(1)
                    
                    # 获取端口
                    port_text = cols[1].text.strip()
                    try:
                        port = int(port_text)
                    except ValueError:
                        continue
                    
                    # 获取国家
                    country_cell = cols[5]
                    country = country_cell.text.strip() if country_cell else None
                    
                    # 获取匿名级别
                    anonymity_cell = cols[6]
                    anonymity_text = anonymity_cell.text.strip().lower() if anonymity_cell else ''
                    
                    # 映射匿名级别
                    if 'elite' in anonymity_text or 'high' in anonymity_text:
                        anonymity = 'elite'
                    elif 'anonymous' in anonymity_text:
                        anonymity = 'anonymous'
                    else:
                        anonymity = 'transparent'
                    
                    # 创建代理对象
                    proxy = Proxy(
                        host=ip,
                        port=port,
                        protocol='http',  # ProxyNova 主要提供 HTTP 代理
                        country=country if country else None,
                        anonymity=anonymity,
                        source='proxy_nova'
                    )
                    
                    proxies.append(proxy)
                    
                    # 如果已经获取到足够的代理，就停止
                    if len(proxies) >= count:
                        break
                
                except Exception as e:
                    self.logger.error(f"解析代理行时出错: {str(e)}")
            
            self.logger.info(f"从 ProxyNova 获取到 {len(proxies)} 个代理")
            return proxies
        
        except Exception as e:
            self.logger.error(f"从 ProxyNova 获取代理时出错: {str(e)}")
            return []