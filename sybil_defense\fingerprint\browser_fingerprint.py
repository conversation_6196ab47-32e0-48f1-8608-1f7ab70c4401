"""
Browser Fingerprint Generator

This module handles the generation and management of browser fingerprints
to avoid detection by anti-bot systems.
"""

import logging
import os
import json
import time
import random
import hashlib
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger("BrowserFingerprint")

class BrowserFingerprint:
    """
    Browser fingerprint generator and manager.
    
    This class handles:
    - Generation of realistic browser fingerprints
    - Fingerprint rotation and management
    - Consistency within sessions
    - Variation across identities
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the browser fingerprint generator.
        
        Args:
            config: Configuration for the generator
        """
        self.config = config or {}
        self.fingerprints = {}
        self.fingerprint_history = {}
        
        # Load fingerprint data
        self._load_fingerprint_data()
        
        logger.info("Browser fingerprint generator initialized")
    
    def _load_fingerprint_data(self) -> None:
        """Load fingerprint data from files."""
        # Load user agents
        self.user_agents = self._load_json_file("data/sybil_defense/fingerprint/user_agents.json", [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
        ])
        
        # Load screen resolutions
        self.screen_resolutions = self._load_json_file("data/sybil_defense/fingerprint/screen_resolutions.json", [
            "1920x1080",
            "1366x768",
            "1440x900",
            "1536x864",
            "2560x1440",
            "1280x720"
        ])
        
        # Load time zones
        self.time_zones = self._load_json_file("data/sybil_defense/fingerprint/time_zones.json", [
            "America/New_York",
            "America/Los_Angeles",
            "America/Chicago",
            "Europe/London",
            "Europe/Berlin",
            "Asia/Tokyo",
            "Asia/Singapore",
            "Australia/Sydney"
        ])
        
        # Load languages
        self.languages = self._load_json_file("data/sybil_defense/fingerprint/languages.json", [
            "en-US",
            "en-GB",
            "fr-FR",
            "de-DE",
            "es-ES",
            "it-IT",
            "ja-JP",
            "ko-KR",
            "zh-CN"
        ])
        
        # Load fonts
        self.fonts = self._load_json_file("data/sybil_defense/fingerprint/fonts.json", [
            "Arial",
            "Arial Black",
            "Arial Narrow",
            "Calibri",
            "Cambria",
            "Comic Sans MS",
            "Courier New",
            "Georgia",
            "Impact",
            "Lucida Console",
            "Lucida Sans Unicode",
            "Microsoft Sans Serif",
            "Palatino Linotype",
            "Tahoma",
            "Times New Roman",
            "Trebuchet MS",
            "Verdana",
            "Webdings"
        ])
        
        # Load plugins
        self.plugins = self._load_json_file("data/sybil_defense/fingerprint/plugins.json", [
            "Chrome PDF Plugin",
            "Chrome PDF Viewer",
            "Native Client",
            "Shockwave Flash",
            "Adobe Acrobat",
            "QuickTime Plugin",
            "Java Applet Plug-in",
            "Silverlight Plug-In",
            "Microsoft Office",
            "Windows Media Player Plug-in"
        ])
        
        # Load platforms
        self.platforms = self._load_json_file("data/sybil_defense/fingerprint/platforms.json", [
            "Win32",
            "MacIntel",
            "Linux x86_64",
            "Linux i686",
            "iPhone",
            "iPad",
            "Android"
        ])
    
    def _load_json_file(self, file_path: str, default_data: List[str]) -> List[str]:
        """
        Load data from a JSON file or use default data.
        
        Args:
            file_path: Path to the JSON file
            default_data: Default data to use if file doesn't exist
            
        Returns:
            List of data from the file or default
        """
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    return json.load(f)
            else:
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                
                # Save default data
                with open(file_path, 'w') as f:
                    json.dump(default_data, f, indent=2)
                
                return default_data
        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return default_data
    
    def generate_fingerprint(self, identity_id: str, browser_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a browser fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            browser_type: Optional browser type to use
            
        Returns:
            Dict containing fingerprint data
        """
        # Determine browser type if not specified
        if not browser_type:
            browser_type = random.choice(["chrome", "firefox", "safari", "edge"])
        
        # Generate user agent based on browser type
        user_agent = self._generate_user_agent(browser_type)
        
        # Generate other fingerprint components
        screen_resolution = random.choice(self.screen_resolutions)
        color_depth = random.choice([24, 32])
        time_zone = random.choice(self.time_zones)
        language = random.choice(self.languages)
        platform = self._get_platform_for_browser(browser_type)
        do_not_track = random.choice([None, "1", "0"])
        
        # Generate fonts subset
        available_fonts = random.sample(self.fonts, random.randint(10, min(30, len(self.fonts))))
        
        # Generate plugins subset based on browser
        available_plugins = self._get_plugins_for_browser(browser_type)
        
        # Generate canvas, WebGL, and audio fingerprints
        # These would normally be generated by the browser, but we simulate them here
        canvas_hash = hashlib.md5(f"{identity_id}_canvas_{random.random()}".encode()).hexdigest()
        webgl_hash = hashlib.md5(f"{identity_id}_webgl_{random.random()}".encode()).hexdigest()
        audio_hash = hashlib.md5(f"{identity_id}_audio_{random.random()}".encode()).hexdigest()
        
        # Create fingerprint
        fingerprint = {
            "user_agent": user_agent,
            "browser_type": browser_type,
            "screen_resolution": screen_resolution,
            "color_depth": color_depth,
            "time_zone": time_zone,
            "language": language,
            "platform": platform,
            "do_not_track": do_not_track,
            "fonts": available_fonts,
            "plugins": available_plugins,
            "canvas_hash": canvas_hash,
            "webgl_hash": webgl_hash,
            "audio_hash": audio_hash,
            "created_at": datetime.now().isoformat(),
            "last_rotated": datetime.now().isoformat()
        }
        
        # Store fingerprint
        self.fingerprints[identity_id] = fingerprint
        
        # Add to history
        if identity_id not in self.fingerprint_history:
            self.fingerprint_history[identity_id] = []
        
        self.fingerprint_history[identity_id].append({
            "fingerprint": fingerprint,
            "created_at": datetime.now().isoformat()
        })
        
        logger.info(f"Generated fingerprint for identity {identity_id} using {browser_type}")
        return fingerprint
    
    def _generate_user_agent(self, browser_type: str) -> str:
        """
        Generate a user agent string for a specific browser type.
        
        Args:
            browser_type: Type of browser
            
        Returns:
            str: User agent string
        """
        # Filter user agents by browser type
        if browser_type == "chrome":
            filtered_agents = [ua for ua in self.user_agents if "Chrome" in ua and "Edg" not in ua]
        elif browser_type == "firefox":
            filtered_agents = [ua for ua in self.user_agents if "Firefox" in ua]
        elif browser_type == "safari":
            filtered_agents = [ua for ua in self.user_agents if "Safari" in ua and "Chrome" not in ua]
        elif browser_type == "edge":
            filtered_agents = [ua for ua in self.user_agents if "Edg" in ua]
        else:
            filtered_agents = self.user_agents
        
        # If no matching agents, use all
        if not filtered_agents:
            filtered_agents = self.user_agents
        
        return random.choice(filtered_agents)
    
    def _get_platform_for_browser(self, browser_type: str) -> str:
        """
        Get a compatible platform for a browser type.
        
        Args:
            browser_type: Type of browser
            
        Returns:
            str: Platform string
        """
        if browser_type == "safari":
            return random.choice(["MacIntel", "iPhone", "iPad"])
        elif browser_type == "edge":
            return "Win32"
        else:
            return random.choice(self.platforms)
    
    def _get_plugins_for_browser(self, browser_type: str) -> List[str]:
        """
        Get a list of plugins compatible with a browser type.
        
        Args:
            browser_type: Type of browser
            
        Returns:
            List of plugin names
        """
        # Filter plugins by browser type
        if browser_type == "chrome":
            compatible_plugins = [p for p in self.plugins if "Chrome" in p or p in ["Native Client", "Shockwave Flash"]]
        elif browser_type == "firefox":
            compatible_plugins = [p for p in self.plugins if "Shockwave Flash" in p or "Java" in p or "QuickTime" in p]
        elif browser_type == "safari":
            compatible_plugins = [p for p in self.plugins if "QuickTime" in p or "Java" in p]
        elif browser_type == "edge":
            compatible_plugins = [p for p in self.plugins if "Microsoft" in p or "Silverlight" in p]
        else:
            compatible_plugins = self.plugins
        
        # If no compatible plugins, use all
        if not compatible_plugins:
            compatible_plugins = self.plugins
        
        # Select a random subset
        return random.sample(compatible_plugins, random.randint(1, min(5, len(compatible_plugins))))
    
    def get_fingerprint(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            Dict containing fingerprint data or None if not found
        """
        return self.fingerprints.get(identity_id)
    
    def rotate_fingerprint(self, identity_id: str, browser_type: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Rotate the fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            browser_type: Optional browser type to use
            
        Returns:
            Dict containing new fingerprint data or None if failed
        """
        try:
            # Get current fingerprint
            current = self.get_fingerprint(identity_id)
            
            # Use same browser type if not specified
            if not browser_type and current:
                browser_type = current.get("browser_type")
            
            # Generate new fingerprint
            new_fingerprint = self.generate_fingerprint(identity_id, browser_type)
            
            logger.info(f"Rotated fingerprint for identity {identity_id}")
            return new_fingerprint
        except Exception as e:
            logger.error(f"Error rotating fingerprint for identity {identity_id}: {e}")
            return None
    
    def get_fingerprint_history(self, identity_id: str) -> List[Dict[str, Any]]:
        """
        Get the fingerprint history for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            List of fingerprint history entries
        """
        return self.fingerprint_history.get(identity_id, [])
    
    def get_js_fingerprint_code(self, identity_id: str) -> str:
        """
        Get JavaScript code to set the fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            str: JavaScript code
        """
        fingerprint = self.get_fingerprint(identity_id)
        
        if not fingerprint:
            return ""
        
        # Create JavaScript code to override fingerprinting methods
        js_code = """
        // Fingerprint override script
        (function() {
            // Override navigator properties
            const navigatorProps = {
                userAgent: "%s",
                platform: "%s",
                language: "%s",
                languages: ["%s"],
                doNotTrack: %s
            };
            
            // Apply navigator overrides
            for (const prop in navigatorProps) {
                if (navigatorProps[prop] !== undefined) {
                    Object.defineProperty(navigator, prop, {
                        get: function() { return navigatorProps[prop]; }
                    });
                }
            }
            
            // Override screen properties
            const screenRes = "%s".split("x");
            const screenProps = {
                width: parseInt(screenRes[0]),
                height: parseInt(screenRes[1]),
                colorDepth: %d
            };
            
            // Apply screen overrides
            for (const prop in screenProps) {
                Object.defineProperty(screen, prop, {
                    get: function() { return screenProps[prop]; }
                });
            }
            
            // Override canvas fingerprinting
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function(type) {
                if (this.width > 16 && this.height > 16) {
                    // This is likely a fingerprinting attempt
                    const result = originalToDataURL.apply(this, arguments);
                    // Modify the result slightly to match our fingerprint
                    return result.substring(0, 22) + "%s" + result.substring(30);
                }
                return originalToDataURL.apply(this, arguments);
            };
            
            // Override WebGL fingerprinting
            const getParameterProxied = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                // Modify specific WebGL parameters to match our fingerprint
                const result = getParameterProxied.call(this, parameter);
                return result;
            };
        })();
        """ % (
            fingerprint["user_agent"],
            fingerprint["platform"],
            fingerprint["language"],
            fingerprint["language"],
            "null" if fingerprint["do_not_track"] is None else f"'{fingerprint['do_not_track']}'",
            fingerprint["screen_resolution"],
            fingerprint["color_depth"],
            fingerprint["canvas_hash"][:8]
        )
        
        return js_code
    
    def get_selenium_options(self, identity_id: str) -> Dict[str, Any]:
        """
        Get Selenium options to apply the fingerprint.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            Dict containing Selenium options
        """
        fingerprint = self.get_fingerprint(identity_id)
        
        if not fingerprint:
            return {}
        
        # Extract screen resolution
        width, height = map(int, fingerprint["screen_resolution"].split("x"))
        
        # Create options
        options = {
            "user_agent": fingerprint["user_agent"],
            "window_size": (width, height),
            "arguments": [
                f"--user-agent={fingerprint['user_agent']}",
                f"--lang={fingerprint['language']}",
                f"--window-size={width},{height}"
            ],
            "preferences": {
                "general.useragent.override": fingerprint["user_agent"],
                "intl.accept_languages": fingerprint["language"],
                "privacy.donottrackheader.enabled": fingerprint["do_not_track"] == "1"
            },
            "js_injection": self.get_js_fingerprint_code(identity_id)
        }
        
        return options


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    
    # Initialize generator
    generator = BrowserFingerprint()
    
    # Generate fingerprints for different browsers
    identity_id = "test-identity-123"
    
    chrome_fp = generator.generate_fingerprint(identity_id, "chrome")
    print(f"Chrome fingerprint: {chrome_fp['user_agent']}")
    
    firefox_fp = generator.generate_fingerprint(f"{identity_id}-firefox", "firefox")
    print(f"Firefox fingerprint: {firefox_fp['user_agent']}")
    
    safari_fp = generator.generate_fingerprint(f"{identity_id}-safari", "safari")
    print(f"Safari fingerprint: {safari_fp['user_agent']}")
    
    # Get JavaScript code
    js_code = generator.get_js_fingerprint_code(identity_id)
    print(f"JavaScript fingerprint code length: {len(js_code)} bytes")
    
    # Get Selenium options
    selenium_options = generator.get_selenium_options(identity_id)
    print(f"Selenium options: {selenium_options['arguments']}")

import logging
import os
import json
import time
import random
import hashlib
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger("BrowserFingerprint")

class BrowserFingerprint:
    """
    Browser fingerprint generator and manager.
    
    This class handles:
    - Generation of realistic browser fingerprints
    - Fingerprint rotation and management
    - Consistency within sessions
    - Variation across identities
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the browser fingerprint generator.
        
        Args:
            config: Configuration for the generator
        """
        self.config = config or {}
        self.fingerprints = {}
        self.fingerprint_history = {}
        
        # Load fingerprint data
        self._load_fingerprint_data()
        
        logger.info("Browser fingerprint generator initialized")
    
    def _load_fingerprint_data(self) -> None:
        """Load fingerprint data from files."""
        # Load user agents
        self.user_agents = self._load_json_file("data/sybil_defense/fingerprint/user_agents.json", [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
        ])
        
        # Load screen resolutions
        self.screen_resolutions = self._load_json_file("data/sybil_defense/fingerprint/screen_resolutions.json", [
            "1920x1080",
            "1366x768",
            "1440x900",
            "1536x864",
            "2560x1440",
            "1280x720"
        ])
        
        # Load time zones
        self.time_zones = self._load_json_file("data/sybil_defense/fingerprint/time_zones.json", [
            "America/New_York",
            "America/Los_Angeles",
            "America/Chicago",
            "Europe/London",
            "Europe/Berlin",
            "Asia/Tokyo",
            "Asia/Singapore",
            "Australia/Sydney"
        ])
        
        # Load languages
        self.languages = self._load_json_file("data/sybil_defense/fingerprint/languages.json", [
            "en-US",
            "en-GB",
            "fr-FR",
            "de-DE",
            "es-ES",
            "it-IT",
            "ja-JP",
            "ko-KR",
            "zh-CN"
        ])
        
        # Load fonts
        self.fonts = self._load_json_file("data/sybil_defense/fingerprint/fonts.json", [
            "Arial",
            "Arial Black",
            "Arial Narrow",
            "Calibri",
            "Cambria",
            "Comic Sans MS",
            "Courier New",
            "Georgia",
            "Impact",
            "Lucida Console",
            "Lucida Sans Unicode",
            "Microsoft Sans Serif",
            "Palatino Linotype",
            "Tahoma",
            "Times New Roman",
            "Trebuchet MS",
            "Verdana",
            "Webdings"
        ])
        
        # Load plugins
        self.plugins = self._load_json_file("data/sybil_defense/fingerprint/plugins.json", [
            "Chrome PDF Plugin",
            "Chrome PDF Viewer",
            "Native Client",
            "Shockwave Flash",
            "Adobe Acrobat",
            "QuickTime Plugin",
            "Java Applet Plug-in",
            "Silverlight Plug-In",
            "Microsoft Office",
            "Windows Media Player Plug-in"
        ])
        
        # Load platforms
        self.platforms = self._load_json_file("data/sybil_defense/fingerprint/platforms.json", [
            "Win32",
            "MacIntel",
            "Linux x86_64",
            "Linux i686",
            "iPhone",
            "iPad",
            "Android"
        ])
    
    def _load_json_file(self, file_path: str, default_data: List[str]) -> List[str]:
        """
        Load data from a JSON file or use default data.
        
        Args:
            file_path: Path to the JSON file
            default_data: Default data to use if file doesn't exist
            
        Returns:
            List of data from the file or default
        """
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    return json.load(f)
            else:
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                
                # Save default data
                with open(file_path, 'w') as f:
                    json.dump(default_data, f, indent=2)
                
                return default_data
        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return default_data
    
    def generate_fingerprint(self, identity_id: str, browser_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a browser fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            browser_type: Optional browser type to use
            
        Returns:
            Dict containing fingerprint data
        """
        # Determine browser type if not specified
        if not browser_type:
            browser_type = random.choice(["chrome", "firefox", "safari", "edge"])
        
        # Generate user agent based on browser type
        user_agent = self._generate_user_agent(browser_type)
        
        # Generate other fingerprint components
        screen_resolution = random.choice(self.screen_resolutions)
        color_depth = random.choice([24, 32])
        time_zone = random.choice(self.time_zones)
        language = random.choice(self.languages)
        platform = self._get_platform_for_browser(browser_type)
        do_not_track = random.choice([None, "1", "0"])
        
        # Generate fonts subset
        available_fonts = random.sample(self.fonts, random.randint(10, min(30, len(self.fonts))))
        
        # Generate plugins subset based on browser
        available_plugins = self._get_plugins_for_browser(browser_type)
        
        # Generate canvas, WebGL, and audio fingerprints
        # These would normally be generated by the browser, but we simulate them here
        canvas_hash = hashlib.md5(f"{identity_id}_canvas_{random.random()}".encode()).hexdigest()
        webgl_hash = hashlib.md5(f"{identity_id}_webgl_{random.random()}".encode()).hexdigest()
        audio_hash = hashlib.md5(f"{identity_id}_audio_{random.random()}".encode()).hexdigest()
        
        # Create fingerprint
        fingerprint = {
            "user_agent": user_agent,
            "browser_type": browser_type,
            "screen_resolution": screen_resolution,
            "color_depth": color_depth,
            "time_zone": time_zone,
            "language": language,
            "platform": platform,
            "do_not_track": do_not_track,
            "fonts": available_fonts,
            "plugins": available_plugins,
            "canvas_hash": canvas_hash,
            "webgl_hash": webgl_hash,
            "audio_hash": audio_hash,
            "created_at": datetime.now().isoformat(),
            "last_rotated": datetime.now().isoformat()
        }
        
        # Store fingerprint
        self.fingerprints[identity_id] = fingerprint
        
        # Add to history
        if identity_id not in self.fingerprint_history:
            self.fingerprint_history[identity_id] = []
        
        self.fingerprint_history[identity_id].append({
            "fingerprint": fingerprint,
            "created_at": datetime.now().isoformat()
        })
        
        logger.info(f"Generated fingerprint for identity {identity_id} using {browser_type}")
        return fingerprint
    
    def _generate_user_agent(self, browser_type: str) -> str:
        """
        Generate a user agent string for a specific browser type.
        
        Args:
            browser_type: Type of browser
            
        Returns:
            str: User agent string
        """
        # Filter user agents by browser type
        if browser_type == "chrome":
            filtered_agents = [ua for ua in self.user_agents if "Chrome" in ua and "Edg" not in ua]
        elif browser_type == "firefox":
            filtered_agents = [ua for ua in self.user_agents if "Firefox" in ua]
        elif browser_type == "safari":
            filtered_agents = [ua for ua in self.user_agents if "Safari" in ua and "Chrome" not in ua]
        elif browser_type == "edge":
            filtered_agents = [ua for ua in self.user_agents if "Edg" in ua]
        else:
            filtered_agents = self.user_agents
        
        # If no matching agents, use all
        if not filtered_agents:
            filtered_agents = self.user_agents
        
        return random.choice(filtered_agents)
    
    def _get_platform_for_browser(self, browser_type: str) -> str:
        """
        Get a compatible platform for a browser type.
        
        Args:
            browser_type: Type of browser
            
        Returns:
            str: Platform string
        """
        if browser_type == "safari":
            return random.choice(["MacIntel", "iPhone", "iPad"])
        elif browser_type == "edge":
            return "Win32"
        else:
            return random.choice(self.platforms)
    
    def _get_plugins_for_browser(self, browser_type: str) -> List[str]:
        """
        Get a list of plugins compatible with a browser type.
        
        Args:
            browser_type: Type of browser
            
        Returns:
            List of plugin names
        """
        # Filter plugins by browser type
        if browser_type == "chrome":
            compatible_plugins = [p for p in self.plugins if "Chrome" in p or p in ["Native Client", "Shockwave Flash"]]
        elif browser_type == "firefox":
            compatible_plugins = [p for p in self.plugins if "Shockwave Flash" in p or "Java" in p or "QuickTime" in p]
        elif browser_type == "safari":
            compatible_plugins = [p for p in self.plugins if "QuickTime" in p or "Java" in p]
        elif browser_type == "edge":
            compatible_plugins = [p for p in self.plugins if "Microsoft" in p or "Silverlight" in p]
        else:
            compatible_plugins = self.plugins
        
        # If no compatible plugins, use all
        if not compatible_plugins:
            compatible_plugins = self.plugins
        
        # Select a random subset
        return random.sample(compatible_plugins, random.randint(1, min(5, len(compatible_plugins))))
    
    def get_fingerprint(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            Dict containing fingerprint data or None if not found
        """
        return self.fingerprints.get(identity_id)
    
    def rotate_fingerprint(self, identity_id: str, browser_type: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Rotate the fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            browser_type: Optional browser type to use
            
        Returns:
            Dict containing new fingerprint data or None if failed
        """
        try:
            # Get current fingerprint
            current = self.get_fingerprint(identity_id)
            
            # Use same browser type if not specified
            if not browser_type and current:
                browser_type = current.get("browser_type")
            
            # Generate new fingerprint
            new_fingerprint = self.generate_fingerprint(identity_id, browser_type)
            
            logger.info(f"Rotated fingerprint for identity {identity_id}")
            return new_fingerprint
        except Exception as e:
            logger.error(f"Error rotating fingerprint for identity {identity_id}: {e}")
            return None
    
    def get_fingerprint_history(self, identity_id: str) -> List[Dict[str, Any]]:
        """
        Get the fingerprint history for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            List of fingerprint history entries
        """
        return self.fingerprint_history.get(identity_id, [])
    
    def get_js_fingerprint_code(self, identity_id: str) -> str:
        """
        Get JavaScript code to set the fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            str: JavaScript code
        """
        fingerprint = self.get_fingerprint(identity_id)
        
        if not fingerprint:
            return ""
        
        # Create JavaScript code to override fingerprinting methods
        js_code = """
        // Fingerprint override script
        (function() {
            // Override navigator properties
            const navigatorProps = {
                userAgent: "%s",
                platform: "%s",
                language: "%s",
                languages: ["%s"],
                doNotTrack: %s
            };
            
            // Apply navigator overrides
            for (const prop in navigatorProps) {
                if (navigatorProps[prop] !== undefined) {
                    Object.defineProperty(navigator, prop, {
                        get: function() { return navigatorProps[prop]; }
                    });
                }
            }
            
            // Override screen properties
            const screenRes = "%s".split("x");
            const screenProps = {
                width: parseInt(screenRes[0]),
                height: parseInt(screenRes[1]),
                colorDepth: %d
            };
            
            // Apply screen overrides
            for (const prop in screenProps) {
                Object.defineProperty(screen, prop, {
                    get: function() { return screenProps[prop]; }
                });
            }
            
            // Override canvas fingerprinting
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function(type) {
                if (this.width > 16 && this.height > 16) {
                    // This is likely a fingerprinting attempt
                    const result = originalToDataURL.apply(this, arguments);
                    // Modify the result slightly to match our fingerprint
                    return result.substring(0, 22) + "%s" + result.substring(30);
                }
                return originalToDataURL.apply(this, arguments);
            };
            
            // Override WebGL fingerprinting
            const getParameterProxied = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                // Modify specific WebGL parameters to match our fingerprint
                const result = getParameterProxied.call(this, parameter);
                return result;
            };
        })();
        """ % (
            fingerprint["user_agent"],
            fingerprint["platform"],
            fingerprint["language"],
            fingerprint["language"],
            "null" if fingerprint["do_not_track"] is None else f"'{fingerprint['do_not_track']}'",
            fingerprint["screen_resolution"],
            fingerprint["color_depth"],
            fingerprint["canvas_hash"][:8]
        )
        
        return js_code
    
    def get_selenium_options(self, identity_id: str) -> Dict[str, Any]:
        """
        Get Selenium options to apply the fingerprint.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            Dict containing Selenium options
        """
        fingerprint = self.get_fingerprint(identity_id)
        
        if not fingerprint:
            return {}
        
        # Extract screen resolution
        width, height = map(int, fingerprint["screen_resolution"].split("x"))
        
        # Create options
        options = {
            "user_agent": fingerprint["user_agent"],
            "window_size": (width, height),
            "arguments": [
                f"--user-agent={fingerprint['user_agent']}",
                f"--lang={fingerprint['language']}",
                f"--window-size={width},{height}"
            ],
            "preferences": {
                "general.useragent.override": fingerprint["user_agent"],
                "intl.accept_languages": fingerprint["language"],
                "privacy.donottrackheader.enabled": fingerprint["do_not_track"] == "1"
            },
            "js_injection": self.get_js_fingerprint_code(identity_id)
        }
        
        return options


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    
    # Initialize generator
    generator = BrowserFingerprint()
    
    # Generate fingerprints for different browsers
    identity_id = "test-identity-123"
    
    chrome_fp = generator.generate_fingerprint(identity_id, "chrome")
    print(f"Chrome fingerprint: {chrome_fp['user_agent']}")
    
    firefox_fp = generator.generate_fingerprint(f"{identity_id}-firefox", "firefox")
    print(f"Firefox fingerprint: {firefox_fp['user_agent']}")
    
    safari_fp = generator.generate_fingerprint(f"{identity_id}-safari", "safari")
    print(f"Safari fingerprint: {safari_fp['user_agent']}")
    
    # Get JavaScript code
    js_code = generator.get_js_fingerprint_code(identity_id)
    print(f"JavaScript fingerprint code length: {len(js_code)} bytes")
    
    # Get Selenium options
    selenium_options = generator.get_selenium_options(identity_id)
    print(f"Selenium options: {selenium_options['arguments']}")

import logging
import os
import json
import time
import random
import hashlib
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger("BrowserFingerprint")

class BrowserFingerprint:
    """
    Browser fingerprint generator and manager.
    
    This class handles:
    - Generation of realistic browser fingerprints
    - Fingerprint rotation and management
    - Consistency within sessions
    - Variation across identities
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the browser fingerprint generator.
        
        Args:
            config: Configuration for the generator
        """
        self.config = config or {}
        self.fingerprints = {}
        self.fingerprint_history = {}
        
        # Load fingerprint data
        self._load_fingerprint_data()
        
        logger.info("Browser fingerprint generator initialized")
    
    def _load_fingerprint_data(self) -> None:
        """Load fingerprint data from files."""
        # Load user agents
        self.user_agents = self._load_json_file("data/sybil_defense/fingerprint/user_agents.json", [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
        ])
        
        # Load screen resolutions
        self.screen_resolutions = self._load_json_file("data/sybil_defense/fingerprint/screen_resolutions.json", [
            "1920x1080",
            "1366x768",
            "1440x900",
            "1536x864",
            "2560x1440",
            "1280x720"
        ])
        
        # Load time zones
        self.time_zones = self._load_json_file("data/sybil_defense/fingerprint/time_zones.json", [
            "America/New_York",
            "America/Los_Angeles",
            "America/Chicago",
            "Europe/London",
            "Europe/Berlin",
            "Asia/Tokyo",
            "Asia/Singapore",
            "Australia/Sydney"
        ])
        
        # Load languages
        self.languages = self._load_json_file("data/sybil_defense/fingerprint/languages.json", [
            "en-US",
            "en-GB",
            "fr-FR",
            "de-DE",
            "es-ES",
            "it-IT",
            "ja-JP",
            "ko-KR",
            "zh-CN"
        ])
        
        # Load fonts
        self.fonts = self._load_json_file("data/sybil_defense/fingerprint/fonts.json", [
            "Arial",
            "Arial Black",
            "Arial Narrow",
            "Calibri",
            "Cambria",
            "Comic Sans MS",
            "Courier New",
            "Georgia",
            "Impact",
            "Lucida Console",
            "Lucida Sans Unicode",
            "Microsoft Sans Serif",
            "Palatino Linotype",
            "Tahoma",
            "Times New Roman",
            "Trebuchet MS",
            "Verdana",
            "Webdings"
        ])
        
        # Load plugins
        self.plugins = self._load_json_file("data/sybil_defense/fingerprint/plugins.json", [
            "Chrome PDF Plugin",
            "Chrome PDF Viewer",
            "Native Client",
            "Shockwave Flash",
            "Adobe Acrobat",
            "QuickTime Plugin",
            "Java Applet Plug-in",
            "Silverlight Plug-In",
            "Microsoft Office",
            "Windows Media Player Plug-in"
        ])
        
        # Load platforms
        self.platforms = self._load_json_file("data/sybil_defense/fingerprint/platforms.json", [
            "Win32",
            "MacIntel",
            "Linux x86_64",
            "Linux i686",
            "iPhone",
            "iPad",
            "Android"
        ])
    
    def _load_json_file(self, file_path: str, default_data: List[str]) -> List[str]:
        """
        Load data from a JSON file or use default data.
        
        Args:
            file_path: Path to the JSON file
            default_data: Default data to use if file doesn't exist
            
        Returns:
            List of data from the file or default
        """
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    return json.load(f)
            else:
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                
                # Save default data
                with open(file_path, 'w') as f:
                    json.dump(default_data, f, indent=2)
                
                return default_data
        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return default_data
    
    def generate_fingerprint(self, identity_id: str, browser_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a browser fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            browser_type: Optional browser type to use
            
        Returns:
            Dict containing fingerprint data
        """
        # Determine browser type if not specified
        if not browser_type:
            browser_type = random.choice(["chrome", "firefox", "safari", "edge"])
        
        # Generate user agent based on browser type
        user_agent = self._generate_user_agent(browser_type)
        
        # Generate other fingerprint components
        screen_resolution = random.choice(self.screen_resolutions)
        color_depth = random.choice([24, 32])
        time_zone = random.choice(self.time_zones)
        language = random.choice(self.languages)
        platform = self._get_platform_for_browser(browser_type)
        do_not_track = random.choice([None, "1", "0"])
        
        # Generate fonts subset
        available_fonts = random.sample(self.fonts, random.randint(10, min(30, len(self.fonts))))
        
        # Generate plugins subset based on browser
        available_plugins = self._get_plugins_for_browser(browser_type)
        
        # Generate canvas, WebGL, and audio fingerprints
        # These would normally be generated by the browser, but we simulate them here
        canvas_hash = hashlib.md5(f"{identity_id}_canvas_{random.random()}".encode()).hexdigest()
        webgl_hash = hashlib.md5(f"{identity_id}_webgl_{random.random()}".encode()).hexdigest()
        audio_hash = hashlib.md5(f"{identity_id}_audio_{random.random()}".encode()).hexdigest()
        
        # Create fingerprint
        fingerprint = {
            "user_agent": user_agent,
            "browser_type": browser_type,
            "screen_resolution": screen_resolution,
            "color_depth": color_depth,
            "time_zone": time_zone,
            "language": language,
            "platform": platform,
            "do_not_track": do_not_track,
            "fonts": available_fonts,
            "plugins": available_plugins,
            "canvas_hash": canvas_hash,
            "webgl_hash": webgl_hash,
            "audio_hash": audio_hash,
            "created_at": datetime.now().isoformat(),
            "last_rotated": datetime.now().isoformat()
        }
        
        # Store fingerprint
        self.fingerprints[identity_id] = fingerprint
        
        # Add to history
        if identity_id not in self.fingerprint_history:
            self.fingerprint_history[identity_id] = []
        
        self.fingerprint_history[identity_id].append({
            "fingerprint": fingerprint,
            "created_at": datetime.now().isoformat()
        })
        
        logger.info(f"Generated fingerprint for identity {identity_id} using {browser_type}")
        return fingerprint
    
    def _generate_user_agent(self, browser_type: str) -> str:
        """
        Generate a user agent string for a specific browser type.
        
        Args:
            browser_type: Type of browser
            
        Returns:
            str: User agent string
        """
        # Filter user agents by browser type
        if browser_type == "chrome":
            filtered_agents = [ua for ua in self.user_agents if "Chrome" in ua and "Edg" not in ua]
        elif browser_type == "firefox":
            filtered_agents = [ua for ua in self.user_agents if "Firefox" in ua]
        elif browser_type == "safari":
            filtered_agents = [ua for ua in self.user_agents if "Safari" in ua and "Chrome" not in ua]
        elif browser_type == "edge":
            filtered_agents = [ua for ua in self.user_agents if "Edg" in ua]
        else:
            filtered_agents = self.user_agents
        
        # If no matching agents, use all
        if not filtered_agents:
            filtered_agents = self.user_agents
        
        return random.choice(filtered_agents)
    
    def _get_platform_for_browser(self, browser_type: str) -> str:
        """
        Get a compatible platform for a browser type.
        
        Args:
            browser_type: Type of browser
            
        Returns:
            str: Platform string
        """
        if browser_type == "safari":
            return random.choice(["MacIntel", "iPhone", "iPad"])
        elif browser_type == "edge":
            return "Win32"
        else:
            return random.choice(self.platforms)
    
    def _get_plugins_for_browser(self, browser_type: str) -> List[str]:
        """
        Get a list of plugins compatible with a browser type.
        
        Args:
            browser_type: Type of browser
            
        Returns:
            List of plugin names
        """
        # Filter plugins by browser type
        if browser_type == "chrome":
            compatible_plugins = [p for p in self.plugins if "Chrome" in p or p in ["Native Client", "Shockwave Flash"]]
        elif browser_type == "firefox":
            compatible_plugins = [p for p in self.plugins if "Shockwave Flash" in p or "Java" in p or "QuickTime" in p]
        elif browser_type == "safari":
            compatible_plugins = [p for p in self.plugins if "QuickTime" in p or "Java" in p]
        elif browser_type == "edge":
            compatible_plugins = [p for p in self.plugins if "Microsoft" in p or "Silverlight" in p]
        else:
            compatible_plugins = self.plugins
        
        # If no compatible plugins, use all
        if not compatible_plugins:
            compatible_plugins = self.plugins
        
        # Select a random subset
        return random.sample(compatible_plugins, random.randint(1, min(5, len(compatible_plugins))))
    
    def get_fingerprint(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            Dict containing fingerprint data or None if not found
        """
        return self.fingerprints.get(identity_id)
    
    def rotate_fingerprint(self, identity_id: str, browser_type: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Rotate the fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            browser_type: Optional browser type to use
            
        Returns:
            Dict containing new fingerprint data or None if failed
        """
        try:
            # Get current fingerprint
            current = self.get_fingerprint(identity_id)
            
            # Use same browser type if not specified
            if not browser_type and current:
                browser_type = current.get("browser_type")
            
            # Generate new fingerprint
            new_fingerprint = self.generate_fingerprint(identity_id, browser_type)
            
            logger.info(f"Rotated fingerprint for identity {identity_id}")
            return new_fingerprint
        except Exception as e:
            logger.error(f"Error rotating fingerprint for identity {identity_id}: {e}")
            return None
    
    def get_fingerprint_history(self, identity_id: str) -> List[Dict[str, Any]]:
        """
        Get the fingerprint history for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            List of fingerprint history entries
        """
        return self.fingerprint_history.get(identity_id, [])
    
    def get_js_fingerprint_code(self, identity_id: str) -> str:
        """
        Get JavaScript code to set the fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            str: JavaScript code
        """
        fingerprint = self.get_fingerprint(identity_id)
        
        if not fingerprint:
            return ""
        
        # Create JavaScript code to override fingerprinting methods
        js_code = """
        // Fingerprint override script
        (function() {
            // Override navigator properties
            const navigatorProps = {
                userAgent: "%s",
                platform: "%s",
                language: "%s",
                languages: ["%s"],
                doNotTrack: %s
            };
            
            // Apply navigator overrides
            for (const prop in navigatorProps) {
                if (navigatorProps[prop] !== undefined) {
                    Object.defineProperty(navigator, prop, {
                        get: function() { return navigatorProps[prop]; }
                    });
                }
            }
            
            // Override screen properties
            const screenRes = "%s".split("x");
            const screenProps = {
                width: parseInt(screenRes[0]),
                height: parseInt(screenRes[1]),
                colorDepth: %d
            };
            
            // Apply screen overrides
            for (const prop in screenProps) {
                Object.defineProperty(screen, prop, {
                    get: function() { return screenProps[prop]; }
                });
            }
            
            // Override canvas fingerprinting
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function(type) {
                if (this.width > 16 && this.height > 16) {
                    // This is likely a fingerprinting attempt
                    const result = originalToDataURL.apply(this, arguments);
                    // Modify the result slightly to match our fingerprint
                    return result.substring(0, 22) + "%s" + result.substring(30);
                }
                return originalToDataURL.apply(this, arguments);
            };
            
            // Override WebGL fingerprinting
            const getParameterProxied = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                // Modify specific WebGL parameters to match our fingerprint
                const result = getParameterProxied.call(this, parameter);
                return result;
            };
        })();
        """ % (
            fingerprint["user_agent"],
            fingerprint["platform"],
            fingerprint["language"],
            fingerprint["language"],
            "null" if fingerprint["do_not_track"] is None else f"'{fingerprint['do_not_track']}'",
            fingerprint["screen_resolution"],
            fingerprint["color_depth"],
            fingerprint["canvas_hash"][:8]
        )
        
        return js_code
    
    def get_selenium_options(self, identity_id: str) -> Dict[str, Any]:
        """
        Get Selenium options to apply the fingerprint.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            Dict containing Selenium options
        """
        fingerprint = self.get_fingerprint(identity_id)
        
        if not fingerprint:
            return {}
        
        # Extract screen resolution
        width, height = map(int, fingerprint["screen_resolution"].split("x"))
        
        # Create options
        options = {
            "user_agent": fingerprint["user_agent"],
            "window_size": (width, height),
            "arguments": [
                f"--user-agent={fingerprint['user_agent']}",
                f"--lang={fingerprint['language']}",
                f"--window-size={width},{height}"
            ],
            "preferences": {
                "general.useragent.override": fingerprint["user_agent"],
                "intl.accept_languages": fingerprint["language"],
                "privacy.donottrackheader.enabled": fingerprint["do_not_track"] == "1"
            },
            "js_injection": self.get_js_fingerprint_code(identity_id)
        }
        
        return options


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    
    # Initialize generator
    generator = BrowserFingerprint()
    
    # Generate fingerprints for different browsers
    identity_id = "test-identity-123"
    
    chrome_fp = generator.generate_fingerprint(identity_id, "chrome")
    print(f"Chrome fingerprint: {chrome_fp['user_agent']}")
    
    firefox_fp = generator.generate_fingerprint(f"{identity_id}-firefox", "firefox")
    print(f"Firefox fingerprint: {firefox_fp['user_agent']}")
    
    safari_fp = generator.generate_fingerprint(f"{identity_id}-safari", "safari")
    print(f"Safari fingerprint: {safari_fp['user_agent']}")
    
    # Get JavaScript code
    js_code = generator.get_js_fingerprint_code(identity_id)
    print(f"JavaScript fingerprint code length: {len(js_code)} bytes")
    
    # Get Selenium options
    selenium_options = generator.get_selenium_options(identity_id)
    print(f"Selenium options: {selenium_options['arguments']}")