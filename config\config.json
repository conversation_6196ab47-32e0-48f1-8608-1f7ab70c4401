{"system": {"debug": false, "log_level": "INFO", "data_dir": "data", "max_concurrent_tasks": 5, "auto_restart": true, "health_check_interval": 300}, "discovery": {"config_path": "config/discovery_config.json", "sources": {"twitter": {"enabled": true, "keywords": ["airdrop", "free tokens", "claim tokens", "crypto giveaway"], "accounts_to_follow": ["binance", "coinbase", "crypto_twitter_list"], "check_interval": 3600}, "discord": {"enabled": true, "servers": ["crypto_server_1", "crypto_server_2"], "keywords": ["airdrop", "whitelist", "free mint"], "check_interval": 3600}, "telegram": {"enabled": true, "channels": ["airdrop_channel_1", "crypto_news"], "keywords": ["airdrop", "free tokens"], "check_interval": 3600}, "github": {"enabled": true, "repositories": ["ethereum", "solana"], "check_interval": 86400}, "medium": {"enabled": true, "tags": ["cryptocurrency", "airdrop", "blockchain"], "check_interval": 86400}}, "filters": {"min_followers": 1000, "min_engagement": 50, "exclude_keywords": ["scam", "fake"], "min_project_age_days": 30}}, "assessment": {"config_path": "config/assessment_config.json", "risk_thresholds": {"high_risk": 0.7, "medium_risk": 0.4, "low_risk": 0.2}, "verification": {"team_verification": true, "contract_verification": true, "social_verification": true}, "reward_estimation": {"min_expected_value": 50, "time_value_ratio": 10}}, "monitoring": {"config_path": "config/monitoring_config.json", "check_interval": 3600, "alert_thresholds": {"high_priority": ["token listing", "airdrop live", "claim open"], "medium_priority": ["announcement", "update"], "low_priority": ["community post"]}, "notification_channels": {"email": {"enabled": false, "address": "<EMAIL>"}, "telegram": {"enabled": false, "chat_id": "your-telegram-chat-id"}}}, "fund_management": {"config_path": "config/fund_management_config.json", "wallet_storage_path": "data/wallets", "supported_chains": ["ethereum", "solana", "polygon", "binance-smart-chain"], "auto_backup": true, "backup_frequency_hours": 24, "gas_optimization": true, "max_wallets_per_chain": 5, "min_balance_alert": 0.01}, "task_planning": {"config_path": "config/task_planning_config.json", "max_concurrent_tasks": 3, "priority_levels": {"high": 1, "medium": 2, "low": 3}, "retry_attempts": 3, "retry_delay": 300}, "task_execution": {"config_path": "config/task_execution_config.json", "browser_profiles_path": "data/browser_profiles", "extensions_path": "data/extensions", "timeout": 300, "execution_delay": {"min": 5, "max": 30}}, "sybil_defense": {"config_path": "config/sybil_defense_config.json", "data_storage_path": "data/sybil_defense", "max_identities_per_project": 5, "min_activity_interval_minutes": 30, "max_activity_interval_minutes": 240, "activity_randomization_factor": 0.3, "high_risk_threshold": 0.7, "medium_risk_threshold": 0.4, "fingerprint_rotation_days": 7, "ip_rotation_enabled": true, "browser_fingerprint_rotation_enabled": true, "behavior_randomization_enabled": true}, "profit_optimization": {"config_path": "config/profit_optimization_config.json", "data_storage_path": "data/profit_optimization", "min_roi_threshold": 1.5, "max_concurrent_projects": 10, "resource_allocation": {"time_weight": 0.3, "capital_weight": 0.4, "risk_weight": 0.3}, "risk_tolerance": 0.7, "rebalance_interval_hours": 24, "profit_taking_thresholds": {"low": 1.5, "medium": 3.0, "high": 5.0}}, "proxy": {"config_path": "config/proxy_config.json", "enabled": true, "proxy_storage_path": "data/proxies", "rotation_interval": 3600, "max_consecutive_failures": 3, "providers": [{"name": "provider1", "type": "http", "url": "http://proxy-provider-api.example.com", "api_key": "your-api-key", "countries": ["US", "UK", "CA", "DE", "JP"]}]}}