"""
项目发现智能体主模块

该模块是项目发现智能体的入口点。
"""

import os
import sys
import logging
import argparse
import json
import time
from typing import Dict, Any

from discovery.discovery_agent import DiscoveryAgent


def setup_logging(log_level: str, log_file: str = None) -> None:
    """
    设置日志
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径
    """
    # 转换日志级别
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"无效的日志级别: {log_level}")
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置日志处理器
    handlers = []
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(log_format))
    handlers.append(console_handler)
    
    # 添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(log_format))
        handlers.append(file_handler)
    
    # 配置日志
    logging.basicConfig(
        level=numeric_level,
        format=log_format,
        handlers=handlers
    )


def load_config(config_file: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        配置字典
    """
    try:
        with open(config_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"加载配置文件时出错: {str(e)}")
        return {}


def main() -> None:
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='项目发现智能体')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--log-level', type=str, default='INFO', help='日志级别')
    parser.add_argument('--log-file', type=str, help='日志文件路径')
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level, args.log_file)
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建项目发现智能体
    agent = DiscoveryAgent(config)
    
    try:
        # 启动项目发现智能体
        agent.start()
        
        # 等待终止信号
        logging.info("项目发现智能体已启动，按 Ctrl+C 终止")
        while True:
            time.sleep(1)
    
    except KeyboardInterrupt:
        logging.info("接收到终止信号")
    
    finally:
        # 停止项目发现智能体
        agent.stop()
        logging.info("项目发现智能体已停止")


if __name__ == '__main__':
    main()