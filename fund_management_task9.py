#!/usr/bin/env python3
"""
Fund Management Agent - Task 9

完成Token Manager并创建Token Detector
"""

from pathlib import Path

def create_file(path: str, content: str):
    """Create a file with the given content."""
    file_path = Path(path)
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Created: {path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create {path}: {e}")
        return False

def complete_token_manager():
    """完成Token Manager的剩余方法"""
    
    additional_content = '''
    async def _load_default_tokens(self):
        """加载默认代币列表"""
        try:
            for address, info in self.default_tokens.items():
                await self.add_token(address, info)
            
            self.logger.info(f"Loaded {len(self.default_tokens)} default tokens")
            
        except Exception as e:
            self.logger.error(f"Error loading default tokens: {e}")
    
    async def _fetch_token_metadata(self, token_address: str) -> Dict[str, Any]:
        """
        从区块链获取代币元数据
        
        Args:
            token_address: 代币合约地址
            
        Returns:
            Dict[str, Any]: 代币元数据
        """
        try:
            # 模拟从区块链获取代币信息
            # 实际实现需要调用合约的name(), symbol(), decimals()方法
            
            import random
            
            metadata = {
                "symbol": f"TOKEN{random.randint(1, 999)}",
                "name": f"Test Token {random.randint(1, 999)}",
                "decimals": random.choice([6, 8, 18]),
                "total_supply": random.randint(1000000, 1000000000),
                "is_verified": random.choice([True, False])
            }
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"Error fetching token metadata: {e}")
            return {
                "symbol": "UNKNOWN",
                "name": "Unknown Token",
                "decimals": 18
            }
    
    async def remove_token(self, token_address: str) -> bool:
        """
        移除代币
        
        Args:
            token_address: 代币合约地址
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if token_address in self.tokens:
                token_info = self.tokens[token_address]
                del self.tokens[token_address]
                
                # 清理价格缓存
                self.price_cache.pop(token_address, None)
                
                self.manager_stats['total_tokens'] -= 1
                
                self.logger.info(f"Removed token {token_info.get('symbol', 'UNKNOWN')}")
                return True
            else:
                self.logger.warning(f"Token {token_address} not found")
                return False
                
        except Exception as e:
            self.logger.error(f"Error removing token: {e}")
            return False
    
    async def get_all_tokens(self) -> List[Dict[str, Any]]:
        """
        获取所有代币信息
        
        Returns:
            List[Dict[str, Any]]: 所有代币信息列表
        """
        try:
            return list(self.tokens.values())
        except Exception as e:
            self.logger.error(f"Error getting all tokens: {e}")
            return []
    
    async def search_tokens(self, query: str) -> List[Dict[str, Any]]:
        """
        搜索代币
        
        Args:
            query: 搜索查询（符号或名称）
            
        Returns:
            List[Dict[str, Any]]: 匹配的代币列表
        """
        try:
            query_lower = query.lower()
            matching_tokens = []
            
            for token_info in self.tokens.values():
                symbol = token_info.get("symbol", "").lower()
                name = token_info.get("name", "").lower()
                
                if query_lower in symbol or query_lower in name:
                    matching_tokens.append(token_info)
            
            return matching_tokens
            
        except Exception as e:
            self.logger.error(f"Error searching tokens: {e}")
            return []
    
    async def get_token_price(self, token_address: str) -> Optional[Dict[str, Any]]:
        """
        获取代币价格信息
        
        Args:
            token_address: 代币合约地址
            
        Returns:
            Optional[Dict[str, Any]]: 价格信息
        """
        try:
            if token_address in self.tokens:
                return self.tokens[token_address].get("price_info", {})
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting token price: {e}")
            return None
    
    async def update_token_metadata(self, token_address: str, metadata: Dict[str, Any]) -> bool:
        """
        更新代币元数据
        
        Args:
            token_address: 代币合约地址
            metadata: 新的元数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if token_address not in self.tokens:
                self.logger.error(f"Token {token_address} not found")
                return False
            
            # 更新元数据
            current_metadata = self.tokens[token_address].get("metadata", {})
            current_metadata.update(metadata)
            
            self.tokens[token_address]["metadata"] = current_metadata
            self.tokens[token_address]["last_updated"] = datetime.utcnow().isoformat()
            
            self.manager_stats['metadata_updates'] += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating token metadata: {e}")
            return False
    
    def _is_valid_address(self, address: str) -> bool:
        """验证以太坊地址格式"""
        if not isinstance(address, str):
            return False
        if not address.startswith("0x"):
            return False
        if len(address) != 42:
            return False
        try:
            int(address[2:], 16)
            return True
        except ValueError:
            return False
    
    async def get_manager_statistics(self) -> Dict[str, Any]:
        """
        获取管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 统计代币类型分布
            type_distribution = {}
            verified_count = 0
            
            for token_info in self.tokens.values():
                token_type = token_info.get("type", "unknown")
                type_distribution[token_type] = type_distribution.get(token_type, 0) + 1
                
                if token_info.get("is_verified", False):
                    verified_count += 1
            
            return {
                'manager_stats': self.manager_stats,
                'total_tokens': len(self.tokens),
                'verified_tokens': verified_count,
                'type_distribution': type_distribution,
                'cached_prices': len(self.price_cache)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting manager statistics: {e}")
            return {}
    
    async def sync_token_data(self) -> bool:
        """
        同步所有代币数据
        
        Returns:
            bool: 同步是否成功
        """
        try:
            sync_count = 0
            
            for token_address in list(self.tokens.keys()):
                try:
                    # 更新元数据
                    updated_metadata = await self._fetch_token_metadata(token_address)
                    await self.update_token_metadata(token_address, updated_metadata)
                    
                    sync_count += 1
                    
                except Exception as e:
                    self.logger.error(f"Error syncing token {token_address}: {e}")
            
            self.manager_stats['last_sync_time'] = datetime.utcnow().isoformat()
            
            self.logger.info(f"Synced {sync_count} tokens")
            return True
            
        except Exception as e:
            self.logger.error(f"Error in token data sync: {e}")
            return False
'''
    
    return additional_content

def create_token_detector():
    """创建Token Detector"""
    
    detector_content = '''"""
Token Detector

代币检测器，负责自动检测新的代币和空投代币。
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta


class TokenDetector:
    """
    代币检测器
    
    负责监控钱包地址，自动检测新出现的代币，特别是空投代币。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化代币检测器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 监控的钱包地址
        self.monitored_addresses: Set[str] = set()
        
        # 已知代币列表（用于检测新代币）
        self.known_tokens: Dict[str, Set[str]] = {}  # address -> set of token addresses
        
        # 检测到的新代币
        self.detected_tokens: List[Dict] = []
        
        # 检测配置
        self.detection_config = {
            "scan_interval": config.get("scan_interval", 60),  # 秒
            "min_token_balance": config.get("min_token_balance", 0.001),
            "max_detection_history": config.get("max_detection_history", 1000),
            "ignore_dust_tokens": config.get("ignore_dust_tokens", True)
        }
        
        # 检测统计
        self.detection_stats = {
            'total_scans': 0,
            'tokens_detected': 0,
            'addresses_monitored': 0,
            'last_scan_time': None
        }
        
        # 检测任务
        self.detection_task: Optional[asyncio.Task] = None
        self.is_detecting = False
        
        # 代币过滤规则
        self.filter_rules = {
            "min_balance": 0.001,
            "blacklisted_tokens": set(),
            "whitelist_only": False,
            "whitelisted_tokens": set()
        }
    
    async def initialize(self) -> bool:
        """
        初始化代币检测器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 启动检测任务
            await self.start_detection()
            
            self.logger.info("Token Detector initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Token Detector: {e}")
            return False
    
    async def add_address_to_monitor(self, address: str) -> bool:
        """
        添加地址到监控列表
        
        Args:
            address: 钱包地址
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if not self._is_valid_address(address):
                self.logger.error(f"Invalid address format: {address}")
                return False
            
            self.monitored_addresses.add(address)
            
            # 初始化已知代币列表
            if address not in self.known_tokens:
                self.known_tokens[address] = await self._get_current_tokens(address)
            
            self.detection_stats['addresses_monitored'] = len(self.monitored_addresses)
            
            self.logger.info(f"Added address {address} to monitoring")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add address to monitoring: {e}")
            return False
    
    async def remove_address_from_monitor(self, address: str) -> bool:
        """
        从监控列表移除地址
        
        Args:
            address: 钱包地址
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if address in self.monitored_addresses:
                self.monitored_addresses.remove(address)
                self.known_tokens.pop(address, None)
                
                self.detection_stats['addresses_monitored'] = len(self.monitored_addresses)
                
                self.logger.info(f"Removed address {address} from monitoring")
                return True
            else:
                self.logger.warning(f"Address {address} not in monitoring list")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to remove address from monitoring: {e}")
            return False
    
    async def start_detection(self):
        """启动代币检测"""
        try:
            if not self.is_detecting:
                self.is_detecting = True
                self.detection_task = asyncio.create_task(self._detection_loop())
                self.logger.info("Token detection started")
                
        except Exception as e:
            self.logger.error(f"Failed to start detection: {e}")
    
    async def stop_detection(self):
        """停止代币检测"""
        try:
            self.is_detecting = False
            if self.detection_task:
                self.detection_task.cancel()
                try:
                    await self.detection_task
                except asyncio.CancelledError:
                    pass
                self.detection_task = None
            
            self.logger.info("Token detection stopped")
            
        except Exception as e:
            self.logger.error(f"Failed to stop detection: {e}")
    
    async def _detection_loop(self):
        """检测循环"""
        while self.is_detecting:
            try:
                await self._scan_all_addresses()
                await asyncio.sleep(self.detection_config["scan_interval"])
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in detection loop: {e}")
                await asyncio.sleep(30)  # 错误后等待
'''
    
    return detector_content

def main():
    """主函数"""
    print("🚀 Fund Management Agent - Task 9")
    print("=" * 50)
    
    # 完成Token Manager
    additional_content = complete_token_manager()
    
    try:
        with open("fund_management/assets/token_manager.py", 'a', encoding='utf-8') as f:
            f.write(additional_content)
        print("✅ Completed Token Manager methods")
    except Exception as e:
        print(f"❌ Failed to complete Token Manager: {e}")
    
    # 创建Token Detector
    detector_content = create_token_detector()
    success = create_file("fund_management/assets/token_detector.py", detector_content)
    
    if success:
        print("✅ Task 9 completed: Token Detector created!")
    else:
        print("❌ Task 9 failed")

if __name__ == "__main__":
    main()
