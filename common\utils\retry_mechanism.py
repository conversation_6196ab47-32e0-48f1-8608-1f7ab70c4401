"""
Retry mechanism utilities for AirHunter

This module provides retry functionality for handling transient failures.
"""

import time
import random
import logging
from functools import wraps
from typing import Callable, Type, Union, Tuple, List, Optional

class RetryMechanism:
    """Implements retry mechanisms for handling failures"""
    
    @staticmethod
    def retry(
        max_attempts: int = 3,
        delay: float = 1.0,
        backoff: float = 2.0,
        exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
        jitter: bool = True,
        on_retry: Callable = None
    ) -> Callable:
        """
        Retry decorator with exponential backoff
        
        Args:
            max_attempts: Maximum number of retry attempts
            delay: Initial delay between retries in seconds
            backoff: Backoff multiplier
            exceptions: Exception(s) to catch and retry on
            jitter: Whether to add random jitter to delay
            on_retry: Callback function to execute before each retry
            
        Returns:
            Callable: Decorated function
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                attempt = 1
                current_delay = delay
                
                while attempt <= max_attempts:
                    try:
                        return func(*args, **kwargs)
                    except exceptions as e:
                        if attempt == max_attempts:
                            raise
                            
                        if on_retry:
                            on_retry(attempt, e, current_delay)
                        else:
                            logging.warning(
                                f"Retry {attempt}/{max_attempts} for {func.__name__} "
                                f"failed with {type(e).__name__}: {str(e)}"
                            )
                        
                        # Calculate sleep time with optional jitter
                        sleep_time = current_delay
                        if jitter:
                            sleep_time *= (1 + random.random())
                            
                        time.sleep(sleep_time)
                        
                        # Increase delay for next attempt
                        current_delay *= backoff
                        attempt += 1
                        
            return wrapper
        return decorator
    
    @staticmethod
    def retry_with_context(
        context: dict,
        max_attempts: int = 3,
        exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
    ) -> Callable:
        """
        Retry decorator that updates context on each attempt
        
        Args:
            context: Dictionary to store retry context
            max_attempts: Maximum number of retry attempts
            exceptions: Exception(s) to catch and retry on
            
        Returns:
            Callable: Decorated function
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                attempt = 1
                last_error = None
                
                while attempt <= max_attempts:
                    try:
                        context.update({
                            'current_attempt': attempt,
                            'max_attempts': max_attempts,
                            'last_error': str(last_error) if last_error else None
                        })
                        return func(*args, **kwargs)
                    except exceptions as e:
                        last_error = e
                        if attempt == max_attempts:
                            raise
                        attempt += 1
                        
            return wrapper
        return decorator
    
    @staticmethod
    def retry_with_fallback(
        fallback_function: Callable,
        max_attempts: int = 3,
        exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
    ) -> Callable:
        """
        Retry decorator with fallback function
        
        Args:
            fallback_function: Function to call if all retries fail
            max_attempts: Maximum number of retry attempts
            exceptions: Exception(s) to catch and retry on
            
        Returns:
            Callable: Decorated function
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                attempt = 1
                last_error = None
                
                while attempt <= max_attempts:
                    try:
                        return func(*args, **kwargs)
                    except exceptions as e:
                        last_error = e
                        if attempt == max_attempts:
                            logging.warning(
                                f"All {max_attempts} attempts failed for {func.__name__}, "
                                f"using fallback function"
                            )
                            return fallback_function(*args, **kwargs)
                        attempt += 1
                        
            return wrapper
        return decorator

# Create global retry mechanism instance
retry_mechanism = RetryMechanism()
