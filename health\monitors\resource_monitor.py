"""
Resource Monitor

Monitors system resource usage including CPU, memory, disk, and network.
"""

import psutil
import logging
import threading
import time
from typing import Dict, Any, List
from datetime import datetime, timedelta
from collections import deque


class ResourceMonitor:
    """System resource monitoring."""

    def __init__(self, check_interval: float = 5.0, history_size: int = 100):
        self.check_interval = check_interval
        self.history_size = history_size
        self.logger = logging.getLogger(__name__)

        # Resource history
        self._cpu_history = deque(maxlen=history_size)
        self._memory_history = deque(maxlen=history_size)
        self._disk_history = deque(maxlen=history_size)
        self._network_history = deque(maxlen=history_size)

        # Monitoring control
        self._monitoring = False
        self._monitor_thread = None
        self._lock = threading.RLock()

        # Alert thresholds
        self.cpu_warning = 70.0
        self.cpu_critical = 90.0
        self.memory_warning = 70.0
        self.memory_critical = 90.0
        self.disk_warning = 80.0
        self.disk_critical = 95.0

        # Statistics
        self._stats = {
            'monitoring_duration': 0,
            'alerts_triggered': 0,
            'last_check': None
        }

    def start_monitoring(self):
        """Start resource monitoring."""
        with self._lock:
            if self._monitoring:
                return

            self._monitoring = True
            self._monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                name="ResourceMonitor",
                daemon=True
            )
            self._monitor_thread.start()

            self.logger.info("Resource monitoring started")

    def stop_monitoring(self):
        """Stop resource monitoring."""
        with self._lock:
            if not self._monitoring:
                return

            self._monitoring = False

            if self._monitor_thread:
                self._monitor_thread.join(timeout=5.0)

            self.logger.info("Resource monitoring stopped")

    def get_current_usage(self) -> Dict[str, Any]:
        """Get current resource usage."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()

            return {
                'cpu': {
                    'percent': cpu_percent,
                    'count': psutil.cpu_count(),
                    'freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used,
                    'free': memory.free
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'timestamp': datetime.utcnow()
            }
        except Exception as e:
            self.logger.error(f"Error getting resource usage: {e}")
            return {}

    def get_resource_history(self, resource_type: str = None) -> Dict[str, List]:
        """Get resource usage history."""
        with self._lock:
            if resource_type == 'cpu':
                return {'cpu': list(self._cpu_history)}
            elif resource_type == 'memory':
                return {'memory': list(self._memory_history)}
            elif resource_type == 'disk':
                return {'disk': list(self._disk_history)}
            elif resource_type == 'network':
                return {'network': list(self._network_history)}
            else:
                return {
                    'cpu': list(self._cpu_history),
                    'memory': list(self._memory_history),
                    'disk': list(self._disk_history),
                    'network': list(self._network_history)
                }

    def get_statistics(self) -> Dict[str, Any]:
        """Get monitoring statistics."""
        with self._lock:
            stats = self._stats.copy()
            stats['monitoring'] = self._monitoring
            stats['history_size'] = {
                'cpu': len(self._cpu_history),
                'memory': len(self._memory_history),
                'disk': len(self._disk_history),
                'network': len(self._network_history)
            }
            return stats

    def _monitoring_loop(self):
        """Main monitoring loop."""
        start_time = datetime.utcnow()

        while self._monitoring:
            try:
                usage = self.get_current_usage()
                if usage:
                    self._record_usage(usage)
                    self._check_alerts(usage)

                with self._lock:
                    self._stats['last_check'] = datetime.utcnow()
                    self._stats['monitoring_duration'] = (
                        datetime.utcnow() - start_time
                    ).total_seconds()

                time.sleep(self.check_interval)

            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                time.sleep(self.check_interval)

    def _record_usage(self, usage: Dict[str, Any]):
        """Record usage data in history."""
        with self._lock:
            self._cpu_history.append({
                'timestamp': usage['timestamp'],
                'percent': usage['cpu']['percent']
            })

            self._memory_history.append({
                'timestamp': usage['timestamp'],
                'percent': usage['memory']['percent'],
                'used': usage['memory']['used'],
                'available': usage['memory']['available']
            })

            self._disk_history.append({
                'timestamp': usage['timestamp'],
                'percent': usage['disk']['percent'],
                'used': usage['disk']['used'],
                'free': usage['disk']['free']
            })

            self._network_history.append({
                'timestamp': usage['timestamp'],
                'bytes_sent': usage['network']['bytes_sent'],
                'bytes_recv': usage['network']['bytes_recv']
            })

    def _check_alerts(self, usage: Dict[str, Any]):
        """Check for resource usage alerts."""
        try:
            alerts = []

            # CPU alerts
            cpu_percent = usage['cpu']['percent']
            if cpu_percent >= self.cpu_critical:
                alerts.append(f"CRITICAL: CPU usage at {cpu_percent:.1f}%")
            elif cpu_percent >= self.cpu_warning:
                alerts.append(f"WARNING: CPU usage at {cpu_percent:.1f}%")

            # Memory alerts
            memory_percent = usage['memory']['percent']
            if memory_percent >= self.memory_critical:
                alerts.append(f"CRITICAL: Memory usage at {memory_percent:.1f}%")
            elif memory_percent >= self.memory_warning:
                alerts.append(f"WARNING: Memory usage at {memory_percent:.1f}%")

            # Disk alerts
            disk_percent = usage['disk']['percent']
            if disk_percent >= self.disk_critical:
                alerts.append(f"CRITICAL: Disk usage at {disk_percent:.1f}%")
            elif disk_percent >= self.disk_warning:
                alerts.append(f"WARNING: Disk usage at {disk_percent:.1f}%")

            # Log alerts
            for alert in alerts:
                self.logger.warning(alert)
                with self._lock:
                    self._stats['alerts_triggered'] += 1

        except Exception as e:
            self.logger.error(f"Error checking alerts: {e}")
