"""
AirHunter User Interface

This module provides the graphical user interface for the AirHunter system
including dashboard, settings, and monitoring components.
"""

from .main_window import MainWindow
from .dashboard import (
    OverviewPanel,
    ProjectPanel,
    WalletPanel,
    TaskPanel,
    ProxyPanel,
    AnalyticsPanel
)
from .settings import (
    GeneralSettings,
    AgentSettings,
    ProxySettings,
    SecuritySettings,
    AdvancedSettings
)
from .components import (
    ProjectCard,
    TaskList,
    WalletCard,
    ProxyStatus,
    NotificationCenter,
    ProgressTracker
)
from .themes import (
    DarkTheme,
    LightTheme,
    ThemeManager
)

__version__ = "1.0.0"
__all__ = [
    "MainWindow",
    "OverviewPanel",
    "ProjectPanel",
    "WalletPanel",
    "TaskPanel",
    "ProxyPanel",
    "AnalyticsPanel",
    "GeneralSettings",
    "AgentSettings",
    "ProxySettings",
    "SecuritySettings",
    "AdvancedSettings",
    "ProjectCard",
    "TaskList",
    "WalletCard",
    "ProxyStatus",
    "NotificationCenter",
    "ProgressTracker",
    "DarkTheme",
    "LightTheme",
    "ThemeManager"
]
