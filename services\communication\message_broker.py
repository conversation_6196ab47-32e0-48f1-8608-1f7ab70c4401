"""
Message Broker

Provides message passing and communication between different agents
and system components with support for different message patterns.
"""

import asyncio
import logging
import threading
import json
from typing import Dict, List, Any, Optional, Callable, Set
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import uuid


class MessageType(Enum):
    """Message types for different communication patterns."""
    COMMAND = "command"
    EVENT = "event"
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    BROADCAST = "broadcast"


class MessagePriority(Enum):
    """Message priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Message:
    """Message data structure."""
    id: str
    type: MessageType
    sender: str
    recipient: str
    topic: str
    payload: Dict[str, Any]
    priority: MessagePriority = MessagePriority.NORMAL
    timestamp: datetime = None
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    expires_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.id is None:
            self.id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        data = asdict(self)
        data['type'] = self.type.value
        data['priority'] = self.priority.value
        data['timestamp'] = self.timestamp.isoformat()
        if self.expires_at:
            data['expires_at'] = self.expires_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from dictionary."""
        data = data.copy()
        data['type'] = MessageType(data['type'])
        data['priority'] = MessagePriority(data['priority'])
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        if data.get('expires_at'):
            data['expires_at'] = datetime.fromisoformat(data['expires_at'])
        return cls(**data)


class MessageBroker:
    """
    Message broker for inter-agent communication.
    
    Provides publish-subscribe messaging, request-response patterns,
    and message routing between different system components.
    """
    
    def __init__(self, max_queue_size: int = 10000):
        """
        Initialize message broker.
        
        Args:
            max_queue_size: Maximum size of message queues
        """
        self.max_queue_size = max_queue_size
        self.logger = logging.getLogger(__name__)
        
        # Message queues for different agents/components
        self._queues: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_queue_size))
        
        # Topic subscriptions
        self._subscriptions: Dict[str, Set[str]] = defaultdict(set)
        
        # Message handlers
        self._handlers: Dict[str, Dict[str, Callable]] = defaultdict(dict)
        
        # Request-response tracking
        self._pending_requests: Dict[str, asyncio.Future] = {}
        
        # Statistics
        self._stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'messages_dropped': 0,
            'active_subscriptions': 0,
            'pending_requests': 0
        }
        
        # Threading
        self._lock = threading.RLock()
        self._running = False
        self._worker_thread = None
    
    def start(self):
        """Start the message broker."""
        with self._lock:
            if self._running:
                return
            
            self._running = True
            self._worker_thread = threading.Thread(
                target=self._message_processor,
                name="MessageBroker",
                daemon=True
            )
            self._worker_thread.start()
            
            self.logger.info("Message broker started")
    
    def stop(self):
        """Stop the message broker."""
        with self._lock:
            if not self._running:
                return
            
            self._running = False
            
            # Cancel pending requests
            for future in self._pending_requests.values():
                if not future.done():
                    future.cancel()
            self._pending_requests.clear()
            
            if self._worker_thread:
                self._worker_thread.join(timeout=5.0)
            
            self.logger.info("Message broker stopped")
    
    def register_agent(self, agent_id: str) -> bool:
        """
        Register an agent with the broker.
        
        Args:
            agent_id: Unique agent identifier
            
        Returns:
            bool: True if registration successful
        """
        with self._lock:
            if agent_id not in self._queues:
                self._queues[agent_id] = deque(maxlen=self.max_queue_size)
                self.logger.info(f"Registered agent: {agent_id}")
                return True
            return False
    
    def unregister_agent(self, agent_id: str) -> bool:
        """
        Unregister an agent from the broker.
        
        Args:
            agent_id: Agent identifier to unregister
            
        Returns:
            bool: True if unregistration successful
        """
        with self._lock:
            if agent_id in self._queues:
                del self._queues[agent_id]
                
                # Remove from subscriptions
                for topic, subscribers in self._subscriptions.items():
                    subscribers.discard(agent_id)
                
                # Remove handlers
                if agent_id in self._handlers:
                    del self._handlers[agent_id]
                
                self.logger.info(f"Unregistered agent: {agent_id}")
                return True
            return False
    
    def subscribe(self, agent_id: str, topic: str, handler: Callable[[Message], None] = None) -> bool:
        """
        Subscribe agent to a topic.
        
        Args:
            agent_id: Agent identifier
            topic: Topic to subscribe to
            handler: Optional message handler function
            
        Returns:
            bool: True if subscription successful
        """
        with self._lock:
            self._subscriptions[topic].add(agent_id)
            
            if handler:
                self._handlers[agent_id][topic] = handler
            
            self._stats['active_subscriptions'] = sum(
                len(subscribers) for subscribers in self._subscriptions.values()
            )
            
            self.logger.debug(f"Agent {agent_id} subscribed to topic: {topic}")
            return True
    
    def unsubscribe(self, agent_id: str, topic: str) -> bool:
        """
        Unsubscribe agent from a topic.
        
        Args:
            agent_id: Agent identifier
            topic: Topic to unsubscribe from
            
        Returns:
            bool: True if unsubscription successful
        """
        with self._lock:
            if topic in self._subscriptions:
                self._subscriptions[topic].discard(agent_id)
                
                if agent_id in self._handlers and topic in self._handlers[agent_id]:
                    del self._handlers[agent_id][topic]
                
                self._stats['active_subscriptions'] = sum(
                    len(subscribers) for subscribers in self._subscriptions.values()
                )
                
                self.logger.debug(f"Agent {agent_id} unsubscribed from topic: {topic}")
                return True
            return False
    
    def send_message(self, message: Message) -> bool:
        """
        Send a message to a specific recipient.
        
        Args:
            message: Message to send
            
        Returns:
            bool: True if message was queued successfully
        """
        try:
            with self._lock:
                # Check if message has expired
                if message.expires_at and datetime.utcnow() > message.expires_at:
                    self.logger.warning(f"Message {message.id} expired, dropping")
                    self._stats['messages_dropped'] += 1
                    return False
                
                # Queue message for recipient
                if message.recipient in self._queues:
                    self._queues[message.recipient].append(message)
                    self._stats['messages_sent'] += 1
                    self.logger.debug(f"Queued message {message.id} for {message.recipient}")
                    return True
                else:
                    self.logger.warning(f"Recipient {message.recipient} not found")
                    self._stats['messages_dropped'] += 1
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            self._stats['messages_dropped'] += 1
            return False
    
    def publish(self, topic: str, payload: Dict[str, Any], sender: str,
                message_type: MessageType = MessageType.EVENT,
                priority: MessagePriority = MessagePriority.NORMAL) -> int:
        """
        Publish a message to all subscribers of a topic.
        
        Args:
            topic: Topic to publish to
            payload: Message payload
            sender: Message sender
            message_type: Type of message
            priority: Message priority
            
        Returns:
            int: Number of subscribers the message was sent to
        """
        try:
            with self._lock:
                subscribers = self._subscriptions.get(topic, set())
                sent_count = 0
                
                for subscriber in subscribers:
                    message = Message(
                        id=str(uuid.uuid4()),
                        type=message_type,
                        sender=sender,
                        recipient=subscriber,
                        topic=topic,
                        payload=payload,
                        priority=priority
                    )
                    
                    if self.send_message(message):
                        sent_count += 1
                
                self.logger.debug(f"Published message to topic {topic}, sent to {sent_count} subscribers")
                return sent_count
                
        except Exception as e:
            self.logger.error(f"Error publishing message: {e}")
            return 0
    
    def receive_message(self, agent_id: str, timeout: float = 1.0) -> Optional[Message]:
        """
        Receive a message for an agent.
        
        Args:
            agent_id: Agent identifier
            timeout: Timeout in seconds
            
        Returns:
            Optional[Message]: Received message or None if timeout
        """
        try:
            start_time = datetime.utcnow()
            
            while (datetime.utcnow() - start_time).total_seconds() < timeout:
                with self._lock:
                    if agent_id in self._queues and self._queues[agent_id]:
                        message = self._queues[agent_id].popleft()
                        self._stats['messages_received'] += 1
                        return message
                
                # Short sleep to avoid busy waiting
                threading.Event().wait(0.01)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error receiving message: {e}")
            return None
    
    def send_request(self, recipient: str, topic: str, payload: Dict[str, Any],
                    sender: str, timeout: float = 30.0) -> Optional[Message]:
        """
        Send a request and wait for response.
        
        Args:
            recipient: Request recipient
            topic: Request topic
            payload: Request payload
            sender: Request sender
            timeout: Response timeout
            
        Returns:
            Optional[Message]: Response message or None if timeout
        """
        try:
            correlation_id = str(uuid.uuid4())
            
            # Create request message
            request = Message(
                id=str(uuid.uuid4()),
                type=MessageType.REQUEST,
                sender=sender,
                recipient=recipient,
                topic=topic,
                payload=payload,
                correlation_id=correlation_id,
                reply_to=sender
            )
            
            # Set up response tracking
            future = asyncio.Future()
            with self._lock:
                self._pending_requests[correlation_id] = future
                self._stats['pending_requests'] = len(self._pending_requests)
            
            # Send request
            if not self.send_message(request):
                with self._lock:
                    self._pending_requests.pop(correlation_id, None)
                    self._stats['pending_requests'] = len(self._pending_requests)
                return None
            
            # Wait for response
            try:
                loop = asyncio.get_event_loop()
                response = loop.run_until_complete(
                    asyncio.wait_for(future, timeout=timeout)
                )
                return response
            except asyncio.TimeoutError:
                self.logger.warning(f"Request {correlation_id} timed out")
                return None
            finally:
                with self._lock:
                    self._pending_requests.pop(correlation_id, None)
                    self._stats['pending_requests'] = len(self._pending_requests)
                    
        except Exception as e:
            self.logger.error(f"Error sending request: {e}")
            return None
    
    def send_response(self, original_request: Message, payload: Dict[str, Any],
                     sender: str) -> bool:
        """
        Send a response to a request.
        
        Args:
            original_request: Original request message
            payload: Response payload
            sender: Response sender
            
        Returns:
            bool: True if response was sent successfully
        """
        try:
            if not original_request.reply_to or not original_request.correlation_id:
                self.logger.warning("Cannot send response: missing reply_to or correlation_id")
                return False
            
            response = Message(
                id=str(uuid.uuid4()),
                type=MessageType.RESPONSE,
                sender=sender,
                recipient=original_request.reply_to,
                topic=original_request.topic,
                payload=payload,
                correlation_id=original_request.correlation_id
            )
            
            return self.send_message(response)
            
        except Exception as e:
            self.logger.error(f"Error sending response: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get broker statistics.
        
        Returns:
            Dict[str, Any]: Broker statistics
        """
        with self._lock:
            stats = self._stats.copy()
            stats.update({
                'registered_agents': len(self._queues),
                'total_topics': len(self._subscriptions),
                'queue_sizes': {
                    agent_id: len(queue) 
                    for agent_id, queue in self._queues.items()
                },
                'running': self._running
            })
            return stats
    
    def _message_processor(self):
        """Background message processor for handling responses."""
        while self._running:
            try:
                # Process responses for pending requests
                with self._lock:
                    agents_to_check = list(self._queues.keys())
                
                for agent_id in agents_to_check:
                    message = self.receive_message(agent_id, timeout=0.1)
                    if message and message.type == MessageType.RESPONSE:
                        self._handle_response(message)
                    elif message:
                        # Put message back if it's not a response
                        with self._lock:
                            self._queues[agent_id].appendleft(message)
                
                threading.Event().wait(0.1)
                
            except Exception as e:
                self.logger.error(f"Error in message processor: {e}")
                threading.Event().wait(1.0)
    
    def _handle_response(self, response: Message):
        """Handle response message for pending request."""
        try:
            correlation_id = response.correlation_id
            if correlation_id and correlation_id in self._pending_requests:
                future = self._pending_requests[correlation_id]
                if not future.done():
                    future.set_result(response)
                    
        except Exception as e:
            self.logger.error(f"Error handling response: {e}")
