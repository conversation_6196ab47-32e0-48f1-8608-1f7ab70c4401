"""
Database Connection Pool

Manages a pool of database connections for efficient resource utilization
and improved performance in multi-threaded environments.
"""

import sqlite3
import threading
import time
import logging
from queue import Queue, Empty
from typing import Optional
from contextlib import contextmanager


class ConnectionPool:
    """
    Database connection pool for SQLite connections.
    
    Manages a pool of database connections to improve performance and
    handle concurrent access efficiently.
    """
    
    def __init__(self, db_path: str, pool_size: int = 10, timeout: float = 30.0):
        """
        Initialize connection pool.
        
        Args:
            db_path: Path to SQLite database file
            pool_size: Maximum number of connections in pool
            timeout: Timeout for getting connection from pool
        """
        self.db_path = db_path
        self.pool_size = pool_size
        self.timeout = timeout
        self.logger = logging.getLogger(__name__)
        
        # Connection pool and tracking
        self._pool = Queue(maxsize=pool_size)
        self._active_connections = set()
        self._lock = threading.Lock()
        self._closed = False
        
        # Initialize pool with connections
        self._initialize_pool()
    
    def _initialize_pool(self):
        """Initialize the connection pool with connections."""
        try:
            for _ in range(self.pool_size):
                conn = self._create_connection()
                self._pool.put(conn)
            
            self.logger.info(f"Connection pool initialized with {self.pool_size} connections")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize connection pool: {e}")
            raise
    
    def _create_connection(self) -> sqlite3.Connection:
        """
        Create a new database connection.
        
        Returns:
            sqlite3.Connection: New database connection
        """
        conn = sqlite3.connect(
            self.db_path,
            check_same_thread=False,
            timeout=self.timeout
        )
        
        # Configure connection
        conn.execute("PRAGMA foreign_keys = ON")
        conn.execute("PRAGMA journal_mode = WAL")
        conn.execute("PRAGMA synchronous = NORMAL")
        conn.execute("PRAGMA cache_size = -64000")  # 64MB cache
        conn.execute("PRAGMA temp_store = MEMORY")
        
        return conn
    
    def get_connection(self) -> sqlite3.Connection:
        """
        Get a connection from the pool.
        
        Returns:
            sqlite3.Connection: Database connection
            
        Raises:
            RuntimeError: If pool is closed or timeout exceeded
        """
        if self._closed:
            raise RuntimeError("Connection pool is closed")
        
        try:
            # Try to get connection from pool
            conn = self._pool.get(timeout=self.timeout)
            
            # Verify connection is still valid
            if not self._is_connection_valid(conn):
                conn.close()
                conn = self._create_connection()
            
            # Track active connection
            with self._lock:
                self._active_connections.add(conn)
            
            return conn
            
        except Empty:
            raise RuntimeError(f"Timeout getting connection from pool after {self.timeout}s")
        except Exception as e:
            self.logger.error(f"Error getting connection from pool: {e}")
            raise
    
    def return_connection(self, conn: sqlite3.Connection):
        """
        Return a connection to the pool.
        
        Args:
            conn: Database connection to return
        """
        if self._closed:
            conn.close()
            return
        
        try:
            # Remove from active connections
            with self._lock:
                self._active_connections.discard(conn)
            
            # Verify connection is still valid
            if self._is_connection_valid(conn):
                # Rollback any uncommitted transactions
                conn.rollback()
                
                # Return to pool
                self._pool.put_nowait(conn)
            else:
                # Connection is invalid, close it and create new one
                conn.close()
                new_conn = self._create_connection()
                self._pool.put_nowait(new_conn)
                
        except Exception as e:
            self.logger.error(f"Error returning connection to pool: {e}")
            # Close the connection if we can't return it
            try:
                conn.close()
            except:
                pass
    
    def _is_connection_valid(self, conn: sqlite3.Connection) -> bool:
        """
        Check if a connection is still valid.
        
        Args:
            conn: Database connection to check
            
        Returns:
            bool: True if connection is valid
        """
        try:
            conn.execute("SELECT 1")
            return True
        except Exception:
            return False
    
    @contextmanager
    def get_connection_context(self):
        """
        Context manager for getting and returning connections.
        
        Yields:
            sqlite3.Connection: Database connection
        """
        conn = None
        try:
            conn = self.get_connection()
            yield conn
        finally:
            if conn:
                self.return_connection(conn)
    
    def get_pool_stats(self) -> dict:
        """
        Get connection pool statistics.
        
        Returns:
            dict: Pool statistics
        """
        with self._lock:
            return {
                "pool_size": self.pool_size,
                "available_connections": self._pool.qsize(),
                "active_connections": len(self._active_connections),
                "total_connections": self._pool.qsize() + len(self._active_connections),
                "closed": self._closed
            }
    
    def close(self):
        """Close all connections in the pool."""
        if self._closed:
            return
        
        self._closed = True
        
        try:
            # Close active connections
            with self._lock:
                for conn in list(self._active_connections):
                    try:
                        conn.close()
                    except Exception as e:
                        self.logger.error(f"Error closing active connection: {e}")
                self._active_connections.clear()
            
            # Close pooled connections
            while not self._pool.empty():
                try:
                    conn = self._pool.get_nowait()
                    conn.close()
                except Empty:
                    break
                except Exception as e:
                    self.logger.error(f"Error closing pooled connection: {e}")
            
            self.logger.info("Connection pool closed")
            
        except Exception as e:
            self.logger.error(f"Error closing connection pool: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
