"""
Task Scheduler

Central task scheduling service that manages task execution timing,
priorities, and resource allocation across the AirHunter system.
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
from queue import PriorityQueue as StdPriorityQueue
import heapq


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    SCHEDULED = "scheduled"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class TaskPriority(Enum):
    """Task priority levels."""
    CRITICAL = 1
    HIGH = 2
    NORMAL = 3
    LOW = 4
    BACKGROUND = 5


@dataclass
class ScheduledTask:
    """
    Scheduled task data structure.
    """
    id: str
    name: str
    func: Callable
    args: tuple = ()
    kwargs: dict = None
    priority: TaskPriority = TaskPriority.NORMAL
    scheduled_time: datetime = None
    max_retries: int = 3
    retry_delay: float = 60.0
    timeout: float = 300.0
    
    # Runtime tracking
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_count: int = 0
    error_message: str = ""
    result: Any = None
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.kwargs is None:
            self.kwargs = {}
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.scheduled_time is None:
            self.scheduled_time = datetime.utcnow()
    
    def __lt__(self, other):
        """Comparison for priority queue ordering."""
        if self.scheduled_time != other.scheduled_time:
            return self.scheduled_time < other.scheduled_time
        return self.priority.value < other.priority.value
    
    def should_retry(self) -> bool:
        """Check if task should be retried."""
        return (self.status == TaskStatus.FAILED and 
                self.retry_count < self.max_retries)
    
    def get_next_retry_time(self) -> datetime:
        """Get next retry time with exponential backoff."""
        delay = self.retry_delay * (2 ** self.retry_count)
        return datetime.utcnow() + timedelta(seconds=delay)


class TaskScheduler:
    """
    Central task scheduler for AirHunter system.
    
    Manages task scheduling, execution, and monitoring with support for
    priorities, retries, and resource management.
    """
    
    def __init__(self, max_workers: int = 10, check_interval: float = 1.0):
        """
        Initialize task scheduler.
        
        Args:
            max_workers: Maximum number of concurrent workers
            check_interval: Interval between scheduler checks (seconds)
        """
        self.max_workers = max_workers
        self.check_interval = check_interval
        self.logger = logging.getLogger(__name__)
        
        # Task storage and tracking
        self._tasks: Dict[str, ScheduledTask] = {}
        self._task_queue: List[ScheduledTask] = []
        self._running_tasks: Dict[str, asyncio.Task] = {}
        
        # Synchronization
        self._lock = threading.Lock()
        self._shutdown_event = threading.Event()
        
        # Worker management
        self._worker_threads: List[threading.Thread] = []
        self._running = False
        
        # Statistics
        self._stats = {
            "total_scheduled": 0,
            "total_completed": 0,
            "total_failed": 0,
            "total_retried": 0
        }
    
    def start(self):
        """Start the task scheduler."""
        if self._running:
            return
        
        self._running = True
        self._shutdown_event.clear()
        
        # Start scheduler thread
        scheduler_thread = threading.Thread(
            target=self._scheduler_loop,
            name="TaskScheduler",
            daemon=True
        )
        scheduler_thread.start()
        
        # Start worker threads
        for i in range(self.max_workers):
            worker_thread = threading.Thread(
                target=self._worker_loop,
                name=f"TaskWorker-{i}",
                daemon=True
            )
            worker_thread.start()
            self._worker_threads.append(worker_thread)
        
        self.logger.info(f"Task scheduler started with {self.max_workers} workers")
    
    def stop(self, timeout: float = 30.0):
        """
        Stop the task scheduler.
        
        Args:
            timeout: Timeout for graceful shutdown
        """
        if not self._running:
            return
        
        self.logger.info("Stopping task scheduler...")
        self._running = False
        self._shutdown_event.set()
        
        # Cancel running tasks
        with self._lock:
            for task_id, task in self._running_tasks.items():
                if not task.done():
                    task.cancel()
                    self.logger.debug(f"Cancelled running task: {task_id}")
        
        # Wait for threads to finish
        start_time = time.time()
        for thread in self._worker_threads:
            remaining_time = timeout - (time.time() - start_time)
            if remaining_time > 0:
                thread.join(timeout=remaining_time)
        
        self.logger.info("Task scheduler stopped")
    
    def schedule_task(self, task: ScheduledTask) -> str:
        """
        Schedule a task for execution.
        
        Args:
            task: Task to schedule
            
        Returns:
            str: Task ID
        """
        with self._lock:
            self._tasks[task.id] = task
            heapq.heappush(self._task_queue, task)
            self._stats["total_scheduled"] += 1
        
        self.logger.debug(f"Scheduled task: {task.name} ({task.id})")
        return task.id
    
    def schedule_function(self, func: Callable, name: str = None, 
                         delay: float = 0, priority: TaskPriority = TaskPriority.NORMAL,
                         *args, **kwargs) -> str:
        """
        Schedule a function for execution.
        
        Args:
            func: Function to execute
            name: Task name
            delay: Delay before execution (seconds)
            priority: Task priority
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            str: Task ID
        """
        if name is None:
            name = func.__name__
        
        task_id = f"{name}_{int(time.time() * 1000)}"
        scheduled_time = datetime.utcnow() + timedelta(seconds=delay)
        
        task = ScheduledTask(
            id=task_id,
            name=name,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            scheduled_time=scheduled_time
        )
        
        return self.schedule_task(task)
    
    def schedule_recurring(self, func: Callable, interval: float, 
                          name: str = None, priority: TaskPriority = TaskPriority.NORMAL,
                          *args, **kwargs) -> str:
        """
        Schedule a recurring task.
        
        Args:
            func: Function to execute
            interval: Interval between executions (seconds)
            name: Task name
            priority: Task priority
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            str: Task ID
        """
        def recurring_wrapper():
            try:
                result = func(*args, **kwargs)
                # Reschedule for next execution
                self.schedule_function(
                    func, name, interval, priority, *args, **kwargs
                )
                return result
            except Exception as e:
                self.logger.error(f"Recurring task {name} failed: {e}")
                # Still reschedule even if failed
                self.schedule_function(
                    func, name, interval, priority, *args, **kwargs
                )
                raise
        
        return self.schedule_function(
            recurring_wrapper, name or f"recurring_{func.__name__}", 
            0, priority
        )
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a scheduled task.
        
        Args:
            task_id: Task ID to cancel
            
        Returns:
            bool: True if task was cancelled
        """
        with self._lock:
            if task_id in self._tasks:
                task = self._tasks[task_id]
                if task.status in [TaskStatus.PENDING, TaskStatus.SCHEDULED]:
                    task.status = TaskStatus.CANCELLED
                    self.logger.debug(f"Cancelled task: {task_id}")
                    return True
                elif task_id in self._running_tasks:
                    running_task = self._running_tasks[task_id]
                    running_task.cancel()
                    task.status = TaskStatus.CANCELLED
                    self.logger.debug(f"Cancelled running task: {task_id}")
                    return True
        
        return False
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """
        Get task status.
        
        Args:
            task_id: Task ID
            
        Returns:
            Optional[TaskStatus]: Task status or None if not found
        """
        with self._lock:
            task = self._tasks.get(task_id)
            return task.status if task else None
    
    def get_task_result(self, task_id: str) -> Any:
        """
        Get task result.
        
        Args:
            task_id: Task ID
            
        Returns:
            Task result or None if not available
        """
        with self._lock:
            task = self._tasks.get(task_id)
            return task.result if task else None
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get scheduler statistics.
        
        Returns:
            Dict[str, Any]: Scheduler statistics
        """
        with self._lock:
            stats = self._stats.copy()
            stats.update({
                "pending_tasks": len([t for t in self._tasks.values() 
                                    if t.status in [TaskStatus.PENDING, TaskStatus.SCHEDULED]]),
                "running_tasks": len(self._running_tasks),
                "total_tasks": len(self._tasks),
                "workers": self.max_workers,
                "running": self._running
            })
        
        return stats
    
    def _scheduler_loop(self):
        """Main scheduler loop."""
        while self._running and not self._shutdown_event.is_set():
            try:
                self._process_scheduled_tasks()
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"Scheduler loop error: {e}")
    
    def _process_scheduled_tasks(self):
        """Process tasks that are ready to run."""
        now = datetime.utcnow()
        ready_tasks = []
        
        with self._lock:
            # Find tasks ready to run
            while (self._task_queue and 
                   self._task_queue[0].scheduled_time <= now and
                   len(self._running_tasks) < self.max_workers):
                
                task = heapq.heappop(self._task_queue)
                if task.status == TaskStatus.CANCELLED:
                    continue
                
                if task.status in [TaskStatus.PENDING, TaskStatus.SCHEDULED]:
                    ready_tasks.append(task)
        
        # Start ready tasks
        for task in ready_tasks:
            self._start_task(task)
    
    def _start_task(self, task: ScheduledTask):
        """Start executing a task."""
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.utcnow()
        
        # Create async task for execution
        async_task = asyncio.create_task(self._execute_task(task))
        
        with self._lock:
            self._running_tasks[task.id] = async_task
        
        self.logger.debug(f"Started task: {task.name} ({task.id})")
    
    async def _execute_task(self, task: ScheduledTask):
        """Execute a task asynchronously."""
        try:
            # Execute the task function
            if asyncio.iscoroutinefunction(task.func):
                result = await asyncio.wait_for(
                    task.func(*task.args, **task.kwargs),
                    timeout=task.timeout
                )
            else:
                # Run sync function in thread pool
                loop = asyncio.get_event_loop()
                result = await asyncio.wait_for(
                    loop.run_in_executor(
                        None, lambda: task.func(*task.args, **task.kwargs)
                    ),
                    timeout=task.timeout
                )
            
            # Task completed successfully
            task.result = result
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()
            
            with self._lock:
                self._stats["total_completed"] += 1
            
            self.logger.debug(f"Completed task: {task.name} ({task.id})")
            
        except asyncio.CancelledError:
            task.status = TaskStatus.CANCELLED
            self.logger.debug(f"Task cancelled: {task.name} ({task.id})")
            
        except Exception as e:
            # Task failed
            task.error_message = str(e)
            task.status = TaskStatus.FAILED
            
            with self._lock:
                self._stats["total_failed"] += 1
            
            self.logger.error(f"Task failed: {task.name} ({task.id}) - {e}")
            
            # Schedule retry if applicable
            if task.should_retry():
                task.retry_count += 1
                task.status = TaskStatus.RETRYING
                task.scheduled_time = task.get_next_retry_time()
                
                with self._lock:
                    heapq.heappush(self._task_queue, task)
                    self._stats["total_retried"] += 1
                
                self.logger.info(f"Scheduled retry {task.retry_count}/{task.max_retries} "
                               f"for task: {task.name} ({task.id})")
        
        finally:
            # Remove from running tasks
            with self._lock:
                self._running_tasks.pop(task.id, None)
    
    def _worker_loop(self):
        """Worker thread loop."""
        # Set up asyncio event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            while self._running and not self._shutdown_event.is_set():
                # Process any pending async tasks
                try:
                    loop.run_until_complete(asyncio.sleep(0.1))
                except Exception as e:
                    self.logger.error(f"Worker loop error: {e}")
        finally:
            loop.close()
