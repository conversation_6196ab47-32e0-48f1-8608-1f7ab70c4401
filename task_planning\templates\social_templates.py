"""
Social Media Task Templates

This module defines task templates for social media interactions.
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger("SocialTemplates")

class SocialTemplates:
    """
    Provides task templates for social media interactions.
    
    Templates include:
    - Social media registration
    - Daily engagement
    - Content creation
    - Community management
    """
    
    @staticmethod
    def get_registration_template() -> Dict[str, Any]:
        """
        Get a template for social media registration.
        
        Returns:
            Dict containing template data
        """
        return {
            "id": "social_registration",
            "name": "Social Media Registration",
            "description": "Register for social media accounts related to the project",
            "steps": [
                {
                    "name": "Twitter Registration",
                    "agent": "task_execution",
                    "params": {
                        "platform": "twitter",
                        "action": "register"
                    }
                },
                {
                    "name": "Discord Registration",
                    "agent": "task_execution",
                    "params": {
                        "platform": "discord",
                        "action": "register"
                    }
                },
                {
                    "name": "Telegram Registration",
                    "agent": "task_execution",
                    "params": {
                        "platform": "telegram",
                        "action": "register"
                    }
                }
            ],
            "estimated_duration_minutes": 30,
            "priority": 8
        }
    
    @staticmethod
    def get_daily_engagement_template() -> Dict[str, Any]:
        """
        Get a template for daily social media engagement.
        
        Returns:
            Dict containing template data
        """
        return {
            "id": "daily_engagement",
            "name": "Daily Social Engagement",
            "description": "Perform daily social engagement tasks",
            "steps": [
                {
                    "name": "Twitter Engagement",
                    "agent": "task_execution",
                    "params": {
                        "platform": "twitter",
                        "action": "engage",
                        "engagement_type": "like_retweet",
                        "min_actions": 3
                    }
                },
                {
                    "name": "Discord Engagement",
                    "agent": "task_execution",
                    "params": {
                        "platform": "discord",
                        "action": "engage",
                        "engagement_type": "chat",
                        "min_messages": 2
                    }
                },
                {
                    "name": "Telegram Engagement",
                    "agent": "task_execution",
                    "params": {
                        "platform": "telegram",
                        "action": "engage",
                        "engagement_type": "react",
                        "min_actions": 3
                    }
                }
            ],
            "estimated_duration_minutes": 20,
            "priority": 5,
            "recurring": True,
            "recurrence_hours": 24
        }
    
    @staticmethod
    def get_content_creation_template() -> Dict[str, Any]:
        """
        Get a template for social media content creation.
        
        Returns:
            Dict containing template data
        """
        return {
            "id": "content_creation",
            "name": "Social Media Content Creation",
            "description": "Create and post content on social media",
            "steps": [
                {
                    "name": "Generate Content Ideas",
                    "agent": "task_execution",
                    "params": {
                        "action": "generate_content",
                        "content_type": "ideas",
                        "count": 3
                    }
                },
                {
                    "name": "Create Twitter Post",
                    "agent": "task_execution",
                    "params": {
                        "platform": "twitter",
                        "action": "post",
                        "content_type": "text_image"
                    }
                },
                {
                    "name": "Share in Discord",
                    "agent": "task_execution",
                    "params": {
                        "platform": "discord",
                        "action": "post",
                        "content_type": "text_link"
                    }
                }
            ],
            "estimated_duration_minutes": 45,
            "priority": 6,
            "recurring": True,
            "recurrence_hours": 72  # Every 3 days
        }
    
    @staticmethod
    def get_community_management_template() -> Dict[str, Any]:
        """
        Get a template for community management.
        
        Returns:
            Dict containing template data
        """
        return {
            "id": "community_management",
            "name": "Community Management",
            "description": "Manage community interactions and respond to messages",
            "steps": [
                {
                    "name": "Check Direct Messages",
                    "agent": "task_execution",
                    "params": {
                        "action": "check_messages",
                        "platforms": ["twitter", "discord", "telegram"]
                    }
                },
                {
                    "name": "Respond to Messages",
                    "agent": "task_execution",
                    "params": {
                        "action": "respond",
                        "max_responses": 10
                    }
                },
                {
                    "name": "Monitor Mentions",
                    "agent": "task_execution",
                    "params": {
                        "platform": "twitter",
                        "action": "check_mentions"
                    }
                }
            ],
            "estimated_duration_minutes": 30,
            "priority": 7,
            "recurring": True,
            "recurrence_hours": 12  # Twice daily
        }
    
    @staticmethod
    def get_all_templates() -> Dict[str, Dict[str, Any]]:
        """
        Get all social media templates.
        
        Returns:
            Dict mapping template IDs to template data
        """
        templates = {
            "social_registration": SocialTemplates.get_registration_template(),
            "daily_engagement": SocialTemplates.get_daily_engagement_template(),
            "content_creation": SocialTemplates.get_content_creation_template(),
            "community_management": SocialTemplates.get_community_management_template()
        }
        
        return templates


if __name__ == "__main__":
    # Simple test
    import json
    
    templates = SocialTemplates.get_all_templates()
    
    print("Available social media templates:")
    for template_id, template in templates.items():
        print(f"- {template_id}: {template['name']}")
        print(f"  Description: {template['description']}")
        print(f"  Steps: {len(template['steps'])}")
        print(f"  Duration: {template['estimated_duration_minutes']} minutes")
        print(f"  Priority: {template['priority']}")
        print(f"  Recurring: {template.get('recurring', False)}")
        if template.get('recurring', False):
            print(f"  Recurrence: Every {template['recurrence_hours']} hours")
        print()