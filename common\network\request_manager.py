"""
Request manager for network operations

This module provides utilities for managing and controlling HTTP requests.
"""

import time
import queue
import threading
import logging
from typing import Any, Dict, List, Optional, Tuple, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import requests
from requests.exceptions import RequestException

@dataclass
class Request:
    """Request data class"""
    url: str
    method: str
    priority: int
    params: Dict = None
    data: Dict = None
    headers: Dict = None
    proxy: Dict = None
    callback: Callable = None
    retry_count: int = 0
    max_retries: int = 3
    timeout: int = 30

class RequestManager:
    """Manager for HTTP requests"""
    
    def __init__(
        self,
        max_concurrent: int = 10,
        max_queue_size: int = 1000,
        rate_limit: Optional[int] = None
    ):
        """
        Initialize request manager
        
        Args:
            max_concurrent: Maximum concurrent requests
            max_queue_size: Maximum queue size
            rate_limit: Requests per second limit
        """
        self.request_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent)
        self.rate_limit = rate_limit
        self.last_request_time = 0
        self.session = requests.Session()
        self.proxies = []
        self.proxy_index = 0
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        self._running = True
        self._worker_thread = threading.Thread(target=self._process_queue)
        self._worker_thread.daemon = True
        self._worker_thread.start()
    
    def add_request(
        self,
        url: str,
        method: str = "GET",
        priority: int = 0,
        **kwargs
    ) -> None:
        """
        Add request to queue
        
        Args:
            url: Request URL
            method: HTTP method
            priority: Request priority (lower = higher priority)
            **kwargs: Additional request parameters
        """
        try:
            request = Request(
                url=url,
                method=method,
                priority=priority,
                **kwargs
            )
            self.request_queue.put((priority, request))
            self.logger.debug(f"Added request: {url}")
        except queue.Full:
            self.logger.error("Request queue is full")
            raise
    
    def _process_queue(self) -> None:
        """Process request queue"""
        while self._running:
            try:
                _, request = self.request_queue.get(timeout=1)
                self._handle_rate_limit()
                future = self.executor.submit(self._execute_request, request)
                future.add_done_callback(self._handle_response)
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Error processing queue: {str(e)}")
    
    def _handle_rate_limit(self) -> None:
        """Handle request rate limiting"""
        if self.rate_limit:
            with self.lock:
                current_time = time.time()
                time_diff = current_time - self.last_request_time
                if time_diff < 1.0 / self.rate_limit:
                    time.sleep(1.0 / self.rate_limit - time_diff)
                self.last_request_time = time.time()
    
    def _get_next_proxy(self) -> Optional[Dict[str, str]]:
        """
        Get next proxy from pool
        
        Returns:
            Optional[Dict[str, str]]: Proxy configuration
        """
        if not self.proxies:
            return None
            
        with self.lock:
            proxy = self.proxies[self.proxy_index]
            self.proxy_index = (self.proxy_index + 1) % len(self.proxies)
            return proxy
    
    def _execute_request(self, request: Request) -> Tuple[Request, Any]:
        """
        Execute HTTP request
        
        Args:
            request: Request object
            
        Returns:
            Tuple[Request, Any]: Request and response
        """
        try:
            # Use request proxy or get next from pool
            proxy = request.proxy or self._get_next_proxy()
            
            response = self.session.request(
                method=request.method,
                url=request.url,
                params=request.params,
                data=request.data,
                headers=request.headers,
                proxies=proxy,
                timeout=request.timeout
            )
            response.raise_for_status()
            return request, response
            
        except RequestException as e:
            if request.retry_count < request.max_retries:
                self.logger.warning(
                    f"Request failed, retrying: {request.url} "
                    f"(attempt {request.retry_count + 1}/{request.max_retries})"
                )
                request.retry_count += 1
                self.request_queue.put((request.priority, request))
            else:
                self.logger.error(f"Request failed after {request.max_retries} retries: {str(e)}")
            return request, None
    
    def _handle_response(self, future) -> None:
        """
        Handle request response
        
        Args:
            future: Future object
        """
        try:
            request, response = future.result()
            if response and request.callback:
                request.callback(response)
        except Exception as e:
            self.logger.error(f"Error handling response: {str(e)}")
    
    def add_proxy(self, proxy: Dict[str, str]) -> None:
        """
        Add proxy to pool
        
        Args:
            proxy: Proxy configuration dictionary
        """
        self.proxies.append(proxy)
    
    def remove_proxy(self, proxy: Dict[str, str]) -> None:
        """
        Remove proxy from pool
        
        Args:
            proxy: Proxy configuration to remove
        """
        try:
            self.proxies.remove(proxy)
        except ValueError:
            pass
    
    def clear_proxies(self) -> None:
        """Clear proxy pool"""
        self.proxies = []
        self.proxy_index = 0
    
    def set_rate_limit(self, requests_per_second: int) -> None:
        """
        Set request rate limit
        
        Args:
            requests_per_second: Maximum requests per second
        """
        self.rate_limit = requests_per_second
    
    def get_queue_size(self) -> int:
        """
        Get current queue size
        
        Returns:
            int: Queue size
        """
        return self.request_queue.qsize()
    
    def clear_queue(self) -> None:
        """Clear request queue"""
        while not self.request_queue.empty():
            try:
                self.request_queue.get_nowait()
            except queue.Empty:
                break
    
    def shutdown(self) -> None:
        """Shutdown request manager"""
        self._running = False
        self.clear_queue()
        self.executor.shutdown(wait=True)
        self.session.close()
    
    def __enter__(self):
        """Context manager enter"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.shutdown()

# Create global request manager instance
request_manager = RequestManager()
