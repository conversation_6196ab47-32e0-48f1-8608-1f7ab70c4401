"""
初始化数据目录脚本

该脚本创建AirHunter系统所需的所有数据目录。
"""

import os
import sys
import logging
from typing import List, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("InitializeDirectories")

def create_directories() -> List[Tuple[str, bool]]:
    """
    创建系统所需的所有目录
    
    Returns:
        包含目录路径和创建结果的列表
    """
    directories = [
        # 主数据目录
        "data",
        
        # 日志目录
        "data/logs",
        
        # 钱包相关目录
        "data/wallets",
        "data/wallets/ethereum",
        "data/wallets/solana",
        "data/wallets/polygon",
        "data/wallets/binance-smart-chain",
        "data/wallets/keys",
        "data/wallets/backups",
        
        # 代理相关目录
        "data/proxies",
        "data/proxies/configs",
        "data/proxies/logs",
        
        # 项目相关目录
        "data/projects",
        "data/projects/discovered",
        "data/projects/approved",
        "data/projects/completed",
        
        # 浏览器相关目录
        "data/browser_profiles",
        "data/extensions",
        
        # 防女巫相关目录
        "data/sybil_defense",
        "data/sybil_defense/fingerprint",
        "data/sybil_defense/identities",
        
        # 收益优化相关目录
        "data/profit_optimization",
        
        # 监控相关目录
        "data/monitoring",
        "data/monitoring/snapshots",
        
        # 任务相关目录
        "data/tasks",
        "data/tasks/pending",
        "data/tasks/completed",
        "data/tasks/failed",
        
        # 缓存目录
        "data/cache",
        
        # 临时目录
        "data/temp"
    ]
    
    results = []
    
    for directory in directories:
        try:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"创建目录: {directory}")
                results.append((directory, True))
            else:
                logger.info(f"目录已存在: {directory}")
                results.append((directory, True))
        except Exception as e:
            logger.error(f"创建目录失败 {directory}: {e}")
            results.append((directory, False))
    
    return results

def main():
    """主函数"""
    print("=" * 50)
    print("AirHunter 数据目录初始化")
    print("=" * 50)
    
    results = create_directories()
    
    # 统计结果
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    print(f"\n成功创建 {success_count}/{total_count} 个目录")
    
    if success_count == total_count:
        print("\n✅ 所有目录创建成功！")
        return 0
    else:
        print("\n❌ 部分目录创建失败，请检查日志获取详细信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())