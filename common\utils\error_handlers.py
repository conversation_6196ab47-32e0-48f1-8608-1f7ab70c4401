"""
Error handling utilities for AirHunter

This module provides error handling and logging functionality.
"""

import sys
import logging
import traceback
from functools import wraps
from typing import Callable, Type, Union, Tuple, Optional, Dict, Any

class AirHunterError(Exception):
    """Base exception class for AirHunter"""
    pass

class ConfigurationError(AirHunterError):
    """Raised when there's a configuration error"""
    pass

class NetworkError(AirHunterError):
    """Raised when there's a network-related error"""
    pass

class SecurityError(AirHunterError):
    """Raised when there's a security-related error"""
    pass

class ValidationError(AirHunterError):
    """Raised when validation fails"""
    pass

class ErrorHandler:
    """Handles error logging and recovery"""
    
    def __init__(self):
        """Initialize error handler"""
        self.error_callbacks = {}
        self.logger = logging.getLogger(__name__)
    
    def register_callback(
        self,
        error_type: Type[Exception],
        callback: Callable[[Exception], None]
    ) -> None:
        """
        Register callback for specific error type
        
        Args:
            error_type: Type of error to handle
            callback: Callback function to execute
        """
        self.error_callbacks[error_type] = callback
    
    def handle_error(
        self,
        error: Exception,
        context: Dict[str, Any] = None
    ) -> None:
        """
        Handle error with registered callback
        
        Args:
            error: Exception to handle
            context: Additional context information
        """
        error_type = type(error)
        
        # Log error with context
        self.logger.error(
            f"Error occurred: {str(error)}",
            extra={"error_type": error_type.__name__, "context": context}
        )
        
        # Execute callback if registered
        if error_type in self.error_callbacks:
            try:
                self.error_callbacks[error_type](error)
            except Exception as callback_error:
                self.logger.error(
                    f"Error in error callback: {str(callback_error)}"
                )
    
    @staticmethod
    def catch_errors(
        exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
        reraise: bool = False,
        log_level: int = logging.ERROR
    ) -> Callable:
        """
        Decorator to catch and handle errors
        
        Args:
            exceptions: Exception(s) to catch
            reraise: Whether to re-raise caught exceptions
            log_level: Logging level for errors
            
        Returns:
            Callable: Decorated function
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    logger = logging.getLogger(func.__module__)
                    logger.log(
                        log_level,
                        f"Error in {func.__name__}: {str(e)}",
                        exc_info=True
                    )
                    if reraise:
                        raise
                    return None
            return wrapper
        return decorator
    
    @staticmethod
    def with_error_context(
        context: Dict[str, Any]
    ) -> Callable:
        """
        Decorator to add context to error logging
        
        Args:
            context: Context information to add
            
        Returns:
            Callable: Decorated function
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger = logging.getLogger(func.__module__)
                    logger.error(
                        f"Error in {func.__name__}",
                        extra={"context": context, "error": str(e)},
                        exc_info=True
                    )
                    raise
            return wrapper
        return decorator
    
    @staticmethod
    def format_exception(e: Exception) -> str:
        """
        Format exception with traceback
        
        Args:
            e: Exception to format
            
        Returns:
            str: Formatted exception string
        """
        return "".join(traceback.format_exception(
            type(e),
            e,
            e.__traceback__
        ))
    
    def setup_error_logging(
        self,
        log_file: str = None,
        log_level: int = logging.ERROR
    ) -> None:
        """
        Setup error logging configuration
        
        Args:
            log_file: Path to error log file
            log_level: Logging level for errors
        """
        logger = logging.getLogger()
        logger.setLevel(log_level)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Add file handler if log_file specified
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        # Add stream handler for console output
        stream_handler = logging.StreamHandler(sys.stdout)
        stream_handler.setFormatter(formatter)
        logger.addHandler(stream_handler)

# Create global error handler instance
error_handler = ErrorHandler()
