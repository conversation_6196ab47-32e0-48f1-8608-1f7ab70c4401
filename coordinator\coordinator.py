"""
协调控制智能体的主类

该模块包含协调控制智能体的主类，负责初始化和管理所有其他智能体。
"""

import logging
from typing import Dict, List, Any, Optional

# 导入核心组件
from coordinator.core.agent_registry import AgentRegistry
from coordinator.core.lifecycle_manager import LifecycleManager
from coordinator.core.system_state import SystemState

# 导入工作流组件
from coordinator.workflow.workflow_manager import WorkflowManager

# 导入资源组件
from coordinator.resources.resource_allocator import ResourceAllocator

# 导入通信组件
from coordinator.communication.message_broker import MessageBroker
from coordinator.communication.event_system import EventSystem

# 导入监控组件
from coordinator.monitoring.health_monitor import HealthMonitor

# 导入恢复组件
from coordinator.recovery.error_handler import ErrorHandler

# 导入接口组件
from coordinator.interface.logging_service import LoggingService


class Coordinator:
    """协调控制智能体的主类，负责管理所有其他智能体和系统资源"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化协调控制智能体
        
        Args:
            config: 配置字典，包含所有智能体的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化日志服务
        self.logging_service = LoggingService(config.get('logging', {}))
        self.logging_service.setup()
        
        self.logger.info("初始化协调控制智能体...")
        
        # 初始化核心组件
        self.agent_registry = AgentRegistry()
        self.lifecycle_manager = LifecycleManager(self.agent_registry)
        self.system_state = SystemState()
        
        # 初始化通信组件
        self.message_broker = MessageBroker()
        self.event_system = EventSystem(self.message_broker)
        
        # 初始化资源组件
        self.resource_allocator = ResourceAllocator(config.get('resources', {}))
        
        # 初始化工作流组件
        self.workflow_manager = WorkflowManager(
            self.agent_registry, 
            self.message_broker,
            self.resource_allocator
        )
        
        # 初始化监控组件
        self.health_monitor = HealthMonitor(
            self.system_state,
            self.event_system
        )
        
        # 初始化恢复组件
        self.error_handler = ErrorHandler(
            self.system_state,
            self.event_system
        )
        
        self.logger.info("协调控制智能体初始化完成")
        
    def start(self) -> None:
        """启动协调控制智能体及所有注册的智能体"""
        self.logger.info("启动协调控制智能体...")
        
        # 启动核心服务
        self.message_broker.start()
        self.event_system.start()
        self.health_monitor.start()
        
        # 启动所有注册的智能体
        self.lifecycle_manager.start_all_agents()
        
        self.logger.info("协调控制智能体启动完成")
        
    def stop(self) -> None:
        """停止协调控制智能体及所有注册的智能体"""
        self.logger.info("停止协调控制智能体...")
        
        # 停止所有注册的智能体
        self.lifecycle_manager.stop_all_agents()
        
        # 停止核心服务
        self.health_monitor.stop()
        self.event_system.stop()
        self.message_broker.stop()
        
        self.logger.info("协调控制智能体停止完成")
        
    def register_agent(self, agent_name: str, agent_instance: Any) -> None:
        """
        注册智能体
        
        Args:
            agent_name: 智能体名称
            agent_instance: 智能体实例
        """
        self.agent_registry.register(agent_name, agent_instance)
        
    def get_agent(self, agent_name: str) -> Optional[Any]:
        """
        获取智能体实例
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            智能体实例，如果不存在则返回None
        """
        return self.agent_registry.get(agent_name)
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            包含系统状态信息的字典
        """
        return self.system_state.get_status()
    
    def execute_workflow(self, workflow_name: str, params: Dict[str, Any] = None) -> Any:
        """
        执行工作流
        
        Args:
            workflow_name: 工作流名称
            params: 工作流参数
            
        Returns:
            工作流执行结果
        """
        return self.workflow_manager.execute(workflow_name, params or {})