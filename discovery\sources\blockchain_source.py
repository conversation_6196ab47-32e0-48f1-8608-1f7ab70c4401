"""
区块链活动源

该模块实现了从区块链活动获取项目信息的功能。
"""

import logging
import requests
import time
import random
import re
from typing import Dict, List, Any, Optional

from discovery.sources.base_source import BaseProjectSource


class BlockchainSource(BaseProjectSource):
    """区块链活动源"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化区块链活动源
        
        Args:
            config: 配置字典，包含项目源的配置信息
        """
        super().__init__(config)
        self.timeout = config.get('timeout', 10)
        self.platforms = config.get('platforms', [
            'ethereum', 'binance', 'solana', 'polygon',
            'avalanche', 'arbitrum', 'optimism', 'base'
        ])
        self.etherscan_api_key = config.get('etherscan_api_key', '')
        self.bscscan_api_key = config.get('bscscan_api_key', '')
        self.polygonscan_api_key = config.get('polygonscan_api_key', '')
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
    
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        从区块链活动获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        self.logger.info(f"从区块链活动获取 {count} 个项目")
        
        try:
            projects = []
            
            # 计算每个平台需要获取的项目数量
            platforms_count = len(self.platforms)
            projects_per_platform = max(1, count // platforms_count)
            
            # 从每个平台获取项目
            for platform in self.platforms:
                try:
                    platform_projects = []
                    
                    if platform == 'ethereum':
                        platform_projects = self._get_ethereum_projects(projects_per_platform)
                    elif platform == 'binance':
                        platform_projects = self._get_binance_projects(projects_per_platform)
                    elif platform == 'solana':
                        platform_projects = self._get_solana_projects(projects_per_platform)
                    elif platform == 'polygon':
                        platform_projects = self._get_polygon_projects(projects_per_platform)
                    elif platform == 'avalanche':
                        platform_projects = self._get_avalanche_projects(projects_per_platform)
                    elif platform == 'arbitrum':
                        platform_projects = self._get_arbitrum_projects(projects_per_platform)
                    elif platform == 'optimism':
                        platform_projects = self._get_optimism_projects(projects_per_platform)
                    elif platform == 'base':
                        platform_projects = self._get_base_projects(projects_per_platform)
                    
                    projects.extend(platform_projects)
                    
                    # 如果已经获取到足够的项目，就停止
                    if len(projects) >= count:
                        break
                    
                    # 避免请求过于频繁
                    time.sleep(1)
                
                except Exception as e:
                    self.logger.error(f"从平台 '{platform}' 获取项目时出错: {str(e)}")
            
            self.logger.info(f"从区块链活动获取到 {len(projects)} 个项目")
            return projects[:count]
        
        except Exception as e:
            self.logger.error(f"获取区块链活动项目时出错: {str(e)}")
            return []
    
    def _get_ethereum_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取以太坊项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            projects = []
            
            # 使用 Etherscan API 获取最新的代币合约
            if self.etherscan_api_key:
                try:
                    # 构建 API URL
                    api_url = f"https://api.etherscan.io/api?module=account&action=tokentx&page=1&offset={count}&sort=desc&apikey={self.etherscan_api_key}"
                    
                    # 发送请求
                    response = requests.get(api_url, timeout=self.timeout)
                    response.raise_for_status()
                    
                    # 解析响应
                    data = response.json()
                    
                    if data.get('status') == '1' and 'result' in data:
                        # 提取唯一的代币合约
                        token_contracts = {}
                        
                        for tx in data['result']:
                            contract_address = tx.get('contractAddress')
                            token_name = tx.get('tokenName')
                            token_symbol = tx.get('tokenSymbol')
                            
                            if contract_address and token_name and token_symbol:
                                if contract_address not in token_contracts:
                                    token_contracts[contract_address] = {
                                        'name': token_name,
                                        'symbol': token_symbol
                                    }
                        
                        # 获取每个代币的详细信息
                        for contract_address, token_info in token_contracts.items():
                            try:
                                # 获取代币详细信息
                                token_api_url = f"https://api.etherscan.io/api?module=token&action=tokeninfo&contractaddress={contract_address}&apikey={self.etherscan_api_key}"
                                token_response = requests.get(token_api_url, timeout=self.timeout)
                                token_response.raise_for_status()
                                
                                token_data = token_response.json()
                                
                                if token_data.get('status') == '1' and 'result' in token_data:
                                    token_result = token_data['result'][0] if isinstance(token_data['result'], list) else token_data['result']
                                    
                                    # 创建项目信息
                                    project_info = {
                                        'name': token_info['name'],
                                        'description': f"{token_info['name']} ({token_info['symbol']}) token on Ethereum",
                                        'url': f"https://etherscan.io/token/{contract_address}",
                                        'project_type': 'airdrop',
                                        'blockchain': 'ethereum',
                                        'source_url': f"https://etherscan.io/token/{contract_address}",
                                        'discovery_source': 'blockchain',
                                        'social_channels': []
                                    }
                                    
                                    # 添加社交媒体链接
                                    if 'website' in token_result and token_result['website']:
                                        project_info['url'] = token_result['website']
                                    
                                    if 'twitter' in token_result and token_result['twitter']:
                                        project_info['social_channels'].append({
                                            'platform': 'twitter',
                                            'url': token_result['twitter'],
                                            'followers': None
                                        })
                                    
                                    if 'telegram' in token_result and token_result['telegram']:
                                        project_info['social_channels'].append({
                                            'platform': 'telegram',
                                            'url': token_result['telegram'],
                                            'followers': None
                                        })
                                    
                                    if 'discord' in token_result and token_result['discord']:
                                        project_info['social_channels'].append({
                                            'platform': 'discord',
                                            'url': token_result['discord'],
                                            'followers': None
                                        })
                                    
                                    # 添加代币信息
                                    project_info['token_info'] = {
                                        'name': token_info['name'],
                                        'symbol': token_info['symbol'],
                                        'contract_address': contract_address,
                                        'blockchain': 'ethereum'
                                    }
                                    
                                    projects.append(project_info)
                                    
                                    # 如果已经获取到足够的项目，就停止
                                    if len(projects) >= count:
                                        break
                            
                            except Exception as e:
                                self.logger.error(f"获取代币 {contract_address} 详细信息时出错: {str(e)}")
                            
                            # 避免请求过于频繁
                            time.sleep(0.2)
                
                except Exception as e:
                    self.logger.error(f"使用 Etherscan API 获取代币时出错: {str(e)}")
            
            # 如果没有足够的项目，使用 DappRadar 获取
            if len(projects) < count:
                try:
                    # 随机选择一个 User-Agent
                    headers = {
                        'User-Agent': random.choice(self.user_agents)
                    }
                    
                    # 获取最新的 DApp
                    dapp_url = "https://dappradar.com/rankings/protocol/ethereum"
                    dapp_response = requests.get(dapp_url, headers=headers, timeout=self.timeout)
                    dapp_response.raise_for_status()
                    
                    # 使用正则表达式提取 DApp 信息
                    dapp_pattern = r'<a[^>]*?href="(/ethereum/[^"]+)"[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*<img[^>]*?>\s*</div>\s*<div[^>]*?>\s*<div[^>]*?>\s*<span[^>]*?>\s*([^<]+)\s*</span>'
                    dapp_matches = re.findall(dapp_pattern, dapp_response.text)
                    
                    for dapp_path, dapp_name in dapp_matches[:count - len(projects)]:
                        try:
                            # 获取 DApp 详情
                            dapp_detail_url = f"https://dappradar.com{dapp_path}"
                            dapp_detail_response = requests.get(dapp_detail_url, headers=headers, timeout=self.timeout)
                            dapp_detail_response.raise_for_status()
                            
                            # 提取 DApp 描述
                            desc_pattern = r'<div[^>]*?class="[^"]*?Description[^"]*?"[^>]*?>\s*<div[^>]*?>\s*<p[^>]*?>\s*(.*?)\s*</p>'
                            desc_match = re.search(desc_pattern, dapp_detail_response.text)
                            description = desc_match.group(1).strip() if desc_match else f"{dapp_name} on Ethereum"
                            
                            # 提取 DApp 网站
                            website_pattern = r'<a[^>]*?href="([^"]+)"[^>]*?target="_blank"[^>]*?rel="noopener noreferrer"[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*Website\s*</div>'
                            website_match = re.search(website_pattern, dapp_detail_response.text)
                            website = website_match.group(1) if website_match else dapp_detail_url
                            
                            # 创建项目信息
                            project_info = {
                                'name': dapp_name,
                                'description': description[:200] + ('...' if len(description) > 200 else ''),
                                'url': website,
                                'project_type': 'airdrop',
                                'blockchain': 'ethereum',
                                'source_url': dapp_detail_url,
                                'discovery_source': 'blockchain',
                                'social_channels': []
                            }
                            
                            # 提取社交媒体链接
                            social_patterns = {
                                'twitter': r'<a[^>]*?href="(https?://(?:www\.)?twitter\.com/[^"]+)"[^>]*?>',
                                'telegram': r'<a[^>]*?href="(https?://(?:www\.)?t\.me/[^"]+)"[^>]*?>',
                                'discord': r'<a[^>]*?href="(https?://(?:www\.)?discord\.(?:gg|com)/[^"]+)"[^>]*?>'
                            }
                            
                            for platform, pattern in social_patterns.items():
                                social_match = re.search(pattern, dapp_detail_response.text)
                                if social_match:
                                    project_info['social_channels'].append({
                                        'platform': platform,
                                        'url': social_match.group(1),
                                        'followers': None
                                    })
                            
                            projects.append(project_info)
                        
                        except Exception as e:
                            self.logger.error(f"获取 DApp {dapp_name} 详细信息时出错: {str(e)}")
                        
                        # 避免请求过于频繁
                        time.sleep(0.5)
                
                except Exception as e:
                    self.logger.error(f"使用 DappRadar 获取以太坊 DApp 时出错: {str(e)}")
            
            return projects
        
        except Exception as e:
            self.logger.error(f"获取以太坊项目时出错: {str(e)}")
            return []
    
    def _get_binance_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取币安智能链项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            projects = []
            
            # 使用 BscScan API 获取最新的代币合约
            if self.bscscan_api_key:
                try:
                    # 构建 API URL
                    api_url = f"https://api.bscscan.com/api?module=account&action=tokentx&page=1&offset={count}&sort=desc&apikey={self.bscscan_api_key}"
                    
                    # 发送请求
                    response = requests.get(api_url, timeout=self.timeout)
                    response.raise_for_status()
                    
                    # 解析响应
                    data = response.json()
                    
                    if data.get('status') == '1' and 'result' in data:
                        # 提取唯一的代币合约
                        token_contracts = {}
                        
                        for tx in data['result']:
                            contract_address = tx.get('contractAddress')
                            token_name = tx.get('tokenName')
                            token_symbol = tx.get('tokenSymbol')
                            
                            if contract_address and token_name and token_symbol:
                                if contract_address not in token_contracts:
                                    token_contracts[contract_address] = {
                                        'name': token_name,
                                        'symbol': token_symbol
                                    }
                        
                        # 获取每个代币的详细信息
                        for contract_address, token_info in token_contracts.items():
                            try:
                                # 创建项目信息
                                project_info = {
                                    'name': token_info['name'],
                                    'description': f"{token_info['name']} ({token_info['symbol']}) token on Binance Smart Chain",
                                    'url': f"https://bscscan.com/token/{contract_address}",
                                    'project_type': 'airdrop',
                                    'blockchain': 'binance',
                                    'source_url': f"https://bscscan.com/token/{contract_address}",
                                    'discovery_source': 'blockchain',
                                    'social_channels': [],
                                    'token_info': {
                                        'name': token_info['name'],
                                        'symbol': token_info['symbol'],
                                        'contract_address': contract_address,
                                        'blockchain': 'binance'
                                    }
                                }
                                
                                projects.append(project_info)
                                
                                # 如果已经获取到足够的项目，就停止
                                if len(projects) >= count:
                                    break
                            
                            except Exception as e:
                                self.logger.error(f"处理代币 {contract_address} 时出错: {str(e)}")
                
                except Exception as e:
                    self.logger.error(f"使用 BscScan API 获取代币时出错: {str(e)}")
            
            # 如果没有足够的项目，使用 DappRadar 获取
            if len(projects) < count:
                try:
                    # 随机选择一个 User-Agent
                    headers = {
                        'User-Agent': random.choice(self.user_agents)
                    }
                    
                    # 获取最新的 DApp
                    dapp_url = "https://dappradar.com/rankings/protocol/binance-smart-chain"
                    dapp_response = requests.get(dapp_url, headers=headers, timeout=self.timeout)
                    dapp_response.raise_for_status()
                    
                    # 使用正则表达式提取 DApp 信息
                    dapp_pattern = r'<a[^>]*?href="(/binance-smart-chain/[^"]+)"[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*<img[^>]*?>\s*</div>\s*<div[^>]*?>\s*<div[^>]*?>\s*<span[^>]*?>\s*([^<]+)\s*</span>'
                    dapp_matches = re.findall(dapp_pattern, dapp_response.text)
                    
                    for dapp_path, dapp_name in dapp_matches[:count - len(projects)]:
                        try:
                            # 获取 DApp 详情
                            dapp_detail_url = f"https://dappradar.com{dapp_path}"
                            dapp_detail_response = requests.get(dapp_detail_url, headers=headers, timeout=self.timeout)
                            dapp_detail_response.raise_for_status()
                            
                            # 提取 DApp 描述
                            desc_pattern = r'<div[^>]*?class="[^"]*?Description[^"]*?"[^>]*?>\s*<div[^>]*?>\s*<p[^>]*?>\s*(.*?)\s*</p>'
                            desc_match = re.search(desc_pattern, dapp_detail_response.text)
                            description = desc_match.group(1).strip() if desc_match else f"{dapp_name} on Binance Smart Chain"
                            
                            # 提取 DApp 网站
                            website_pattern = r'<a[^>]*?href="([^"]+)"[^>]*?target="_blank"[^>]*?rel="noopener noreferrer"[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*Website\s*</div>'
                            website_match = re.search(website_pattern, dapp_detail_response.text)
                            website = website_match.group(1) if website_match else dapp_detail_url
                            
                            # 创建项目信息
                            project_info = {
                                'name': dapp_name,
                                'description': description[:200] + ('...' if len(description) > 200 else ''),
                                'url': website,
                                'project_type': 'airdrop',
                                'blockchain': 'binance',
                                'source_url': dapp_detail_url,
                                'discovery_source': 'blockchain',
                                'social_channels': []
                            }
                            
                            # 提取社交媒体链接
                            social_patterns = {
                                'twitter': r'<a[^>]*?href="(https?://(?:www\.)?twitter\.com/[^"]+)"[^>]*?>',
                                'telegram': r'<a[^>]*?href="(https?://(?:www\.)?t\.me/[^"]+)"[^>]*?>',
                                'discord': r'<a[^>]*?href="(https?://(?:www\.)?discord\.(?:gg|com)/[^"]+)"[^>]*?>'
                            }
                            
                            for platform, pattern in social_patterns.items():
                                social_match = re.search(pattern, dapp_detail_response.text)
                                if social_match:
                                    project_info['social_channels'].append({
                                        'platform': platform,
                                        'url': social_match.group(1),
                                        'followers': None
                                    })
                            
                            projects.append(project_info)
                        
                        except Exception as e:
                            self.logger.error(f"获取 DApp {dapp_name} 详细信息时出错: {str(e)}")
                        
                        # 避免请求过于频繁
                        time.sleep(0.5)
                
                except Exception as e:
                    self.logger.error(f"使用 DappRadar 获取币安智能链 DApp 时出错: {str(e)}")
            
            return projects
        
        except Exception as e:
            self.logger.error(f"获取币安智能链项目时出错: {str(e)}")
            return []
    
    def _get_solana_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取 Solana 项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            projects = []
            
            # 使用 DappRadar 获取 Solana DApp
            try:
                # 随机选择一个 User-Agent
                headers = {
                    'User-Agent': random.choice(self.user_agents)
                }
                
                # 获取最新的 DApp
                dapp_url = "https://dappradar.com/rankings/protocol/solana"
                dapp_response = requests.get(dapp_url, headers=headers, timeout=self.timeout)
                dapp_response.raise_for_status()
                
                # 使用正则表达式提取 DApp 信息
                dapp_pattern = r'<a[^>]*?href="(/solana/[^"]+)"[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*<img[^>]*?>\s*</div>\s*<div[^>]*?>\s*<div[^>]*?>\s*<span[^>]*?>\s*([^<]+)\s*</span>'
                dapp_matches = re.findall(dapp_pattern, dapp_response.text)
                
                for dapp_path, dapp_name in dapp_matches[:count]:
                    try:
                        # 获取 DApp 详情
                        dapp_detail_url = f"https://dappradar.com{dapp_path}"
                        dapp_detail_response = requests.get(dapp_detail_url, headers=headers, timeout=self.timeout)
                        dapp_detail_response.raise_for_status()
                        
                        # 提取 DApp 描述
                        desc_pattern = r'<div[^>]*?class="[^"]*?Description[^"]*?"[^>]*?>\s*<div[^>]*?>\s*<p[^>]*?>\s*(.*?)\s*</p>'
                        desc_match = re.search(desc_pattern, dapp_detail_response.text)
                        description = desc_match.group(1).strip() if desc_match else f"{dapp_name} on Solana"
                        
                        # 提取 DApp 网站
                        website_pattern = r'<a[^>]*?href="([^"]+)"[^>]*?target="_blank"[^>]*?rel="noopener noreferrer"[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*Website\s*</div>'
                        website_match = re.search(website_pattern, dapp_detail_response.text)
                        website = website_match.group(1) if website_match else dapp_detail_url
                        
                        # 创建项目信息
                        project_info = {
                            'name': dapp_name,
                            'description': description[:200] + ('...' if len(description) > 200 else ''),
                            'url': website,
                            'project_type': 'airdrop',
                            'blockchain': 'solana',
                            'source_url': dapp_detail_url,
                            'discovery_source': 'blockchain',
                            'social_channels': []
                        }
                        
                        # 提取社交媒体链接
                        social_patterns = {
                            'twitter': r'<a[^>]*?href="(https?://(?:www\.)?twitter\.com/[^"]+)"[^>]*?>',
                            'telegram': r'<a[^>]*?href="(https?://(?:www\.)?t\.me/[^"]+)"[^>]*?>',
                            'discord': r'<a[^>]*?href="(https?://(?:www\.)?discord\.(?:gg|com)/[^"]+)"[^>]*?>'
                        }
                        
                        for platform, pattern in social_patterns.items():
                            social_match = re.search(pattern, dapp_detail_response.text)
                            if social_match:
                                project_info['social_channels'].append({
                                    'platform': platform,
                                    'url': social_match.group(1),
                                    'followers': None
                                })
                        
                        projects.append(project_info)
                    
                    except Exception as e:
                        self.logger.error(f"获取 DApp {dapp_name} 详细信息时出错: {str(e)}")
                    
                    # 避免请求过于频繁
                    time.sleep(0.5)
            
            except Exception as e:
                self.logger.error(f"使用 DappRadar 获取 Solana DApp 时出错: {str(e)}")
            
            return projects
        
        except Exception as e:
            self.logger.error(f"获取 Solana 项目时出错: {str(e)}")
            return []
    
    def _get_polygon_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取 Polygon 项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            projects = []
            
            # 使用 PolygonScan API 获取最新的代币合约
            if self.polygonscan_api_key:
                try:
                    # 构建 API URL
                    api_url = f"https://api.polygonscan.com/api?module=account&action=tokentx&page=1&offset={count}&sort=desc&apikey={self.polygonscan_api_key}"
                    
                    # 发送请求
                    response = requests.get(api_url, timeout=self.timeout)
                    response.raise_for_status()
                    
                    # 解析响应
                    data = response.json()
                    
                    if data.get('status') == '1' and 'result' in data:
                        # 提取唯一的代币合约
                        token_contracts = {}
                        
                        for tx in data['result']:
                            contract_address = tx.get('contractAddress')
                            token_name = tx.get('tokenName')
                            token_symbol = tx.get('tokenSymbol')
                            
                            if contract_address and token_name and token_symbol:
                                if contract_address not in token_contracts:
                                    token_contracts[contract_address] = {
                                        'name': token_name,
                                        'symbol': token_symbol
                                    }
                        
                        # 获取每个代币的详细信息
                        for contract_address, token_info in token_contracts.items():
                            try:
                                # 创建项目信息
                                project_info = {
                                    'name': token_info['name'],
                                    'description': f"{token_info['name']} ({token_info['symbol']}) token on Polygon",
                                    'url': f"https://polygonscan.com/token/{contract_address}",
                                    'project_type': 'airdrop',
                                    'blockchain': 'polygon',
                                    'source_url': f"https://polygonscan.com/token/{contract_address}",
                                    'discovery_source': 'blockchain',
                                    'social_channels': [],
                                    'token_info': {
                                        'name': token_info['name'],
                                        'symbol': token_info['symbol'],
                                        'contract_address': contract_address,
                                        'blockchain': 'polygon'
                                    }
                                }
                                
                                projects.append(project_info)
                                
                                # 如果已经获取到足够的项目，就停止
                                if len(projects) >= count:
                                    break
                            
                            except Exception as e:
                                self.logger.error(f"处理代币 {contract_address} 时出错: {str(e)}")
                
                except Exception as e:
                    self.logger.error(f"使用 PolygonScan API 获取代币时出错: {str(e)}")
            
            # 如果没有足够的项目，使用 DappRadar 获取
            if len(projects) < count:
                try:
                    # 随机选择一个 User-Agent
                    headers = {
                        'User-Agent': random.choice(self.user_agents)
                    }
                    
                    # 获取最新的 DApp
                    dapp_url = "https://dappradar.com/rankings/protocol/polygon"
                    dapp_response = requests.get(dapp_url, headers=headers, timeout=self.timeout)
                    dapp_response.raise_for_status()
                    
                    # 使用正则表达式提取 DApp 信息
                    dapp_pattern = r'<a[^>]*?href="(/polygon/[^"]+)"[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*<img[^>]*?>\s*</div>\s*<div[^>]*?>\s*<div[^>]*?>\s*<span[^>]*?>\s*([^<]+)\s*</span>'
                    dapp_matches = re.findall(dapp_pattern, dapp_response.text)
                    
                    for dapp_path, dapp_name in dapp_matches[:count - len(projects)]:
                        try:
                            # 获取 DApp 详情
                            dapp_detail_url = f"https://dappradar.com{dapp_path}"
                            dapp_detail_response = requests.get(dapp_detail_url, headers=headers, timeout=self.timeout)
                            dapp_detail_response.raise_for_status()
                            
                            # 提取 DApp 描述
                            desc_pattern = r'<div[^>]*?class="[^"]*?Description[^"]*?"[^>]*?>\s*<div[^>]*?>\s*<p[^>]*?>\s*(.*?)\s*</p>'
                            desc_match = re.search(desc_pattern, dapp_detail_response.text)
                            description = desc_match.group(1).strip() if desc_match else f"{dapp_name} on Polygon"
                            
                            # 提取 DApp 网站
                            website_pattern = r'<a[^>]*?href="([^"]+)"[^>]*?target="_blank"[^>]*?rel="noopener noreferrer"[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*Website\s*</div>'
                            website_match = re.search(website_pattern, dapp_detail_response.text)
                            website = website_match.group(1) if website_match else dapp_detail_url
                            
                            # 创建项目信息
                            project_info = {
                                'name': dapp_name,
                                'description': description[:200] + ('...' if len(description) > 200 else ''),
                                'url': website,
                                'project_type': 'airdrop',
                                'blockchain': 'polygon',
                                'source_url': dapp_detail_url,
                                'discovery_source': 'blockchain',
                                'social_channels': []
                            }
                            
                            # 提取社交媒体链接
                            social_patterns = {
                                'twitter': r'<a[^>]*?href="(https?://(?:www\.)?twitter\.com/[^"]+)"[^>]*?>',
                                'telegram': r'<a[^>]*?href="(https?://(?:www\.)?t\.me/[^"]+)"[^>]*?>',
                                'discord': r'<a[^>]*?href="(https?://(?:www\.)?discord\.(?:gg|com)/[^"]+)"[^>]*?>'
                            }
                            
                            for platform, pattern in social_patterns.items():
                                social_match = re.search(pattern, dapp_detail_response.text)
                                if social_match:
                                    project_info['social_channels'].append({
                                        'platform': platform,
                                        'url': social_match.group(1),
                                        'followers': None
                                    })
                            
                            projects.append(project_info)
                        
                        except Exception as e:
                            self.logger.error(f"获取 DApp {dapp_name} 详细信息时出错: {str(e)}")
                        
                        # 避免请求过于频繁
                        time.sleep(0.5)
                
                except Exception as e:
                    self.logger.error(f"使用 DappRadar 获取 Polygon DApp 时出错: {str(e)}")
            
            return projects
        
        except Exception as e:
            self.logger.error(f"获取 Polygon 项目时出错: {str(e)}")
            return []
    
    def _get_avalanche_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取 Avalanche 项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        # 简化实现，使用 DappRadar 获取
        return self._get_dappradar_projects('avalanche', count)
    
    def _get_arbitrum_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取 Arbitrum 项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        # 简化实现，使用 DappRadar 获取
        return self._get_dappradar_projects('arbitrum', count)
    
    def _get_optimism_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取 Optimism 项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        # 简化实现，使用 DappRadar 获取
        return self._get_dappradar_projects('optimism', count)
    
    def _get_base_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取 Base 项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        # 简化实现，使用 DappRadar 获取
        return self._get_dappradar_projects('base', count)
    
    def _get_dappradar_projects(self, platform: str, count: int) -> List[Dict[str, Any]]:
        """
        使用 DappRadar 获取项目
        
        Args:
            platform: 区块链平台
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            projects = []
            
            # 随机选择一个 User-Agent
            headers = {
                'User-Agent': random.choice(self.user_agents)
            }
            
            # 获取最新的 DApp
            dapp_url = f"https://dappradar.com/rankings/protocol/{platform}"
            dapp_response = requests.get(dapp_url, headers=headers, timeout=self.timeout)
            dapp_response.raise_for_status()
            
            # 使用正则表达式提取 DApp 信息
            dapp_pattern = r'<a[^>]*?href="(/[^/]+/[^"]+)"[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*<img[^>]*?>\s*</div>\s*<div[^>]*?>\s*<div[^>]*?>\s*<span[^>]*?>\s*([^<]+)\s*</span>'
            dapp_matches = re.findall(dapp_pattern, dapp_response.text)
            
            for dapp_path, dapp_name in dapp_matches[:count]:
                try:
                    # 获取 DApp 详情
                    dapp_detail_url = f"https://dappradar.com{dapp_path}"
                    dapp_detail_response = requests.get(dapp_detail_url, headers=headers, timeout=self.timeout)
                    dapp_detail_response.raise_for_status()
                    
                    # 提取 DApp 描述
                    desc_pattern = r'<div[^>]*?class="[^"]*?Description[^"]*?"[^>]*?>\s*<div[^>]*?>\s*<p[^>]*?>\s*(.*?)\s*</p>'
                    desc_match = re.search(desc_pattern, dapp_detail_response.text)
                    description = desc_match.group(1).strip() if desc_match else f"{dapp_name} on {platform.capitalize()}"
                    
                    # 提取 DApp 网站
                    website_pattern = r'<a[^>]*?href="([^"]+)"[^>]*?target="_blank"[^>]*?rel="noopener noreferrer"[^>]*?>\s*<div[^>]*?>\s*<div[^>]*?>\s*Website\s*</div>'
                    website_match = re.search(website_pattern, dapp_detail_response.text)
                    website = website_match.group(1) if website_match else dapp_detail_url
                    
                    # 创建项目信息
                    project_info = {
                        'name': dapp_name,
                        'description': description[:200] + ('...' if len(description) > 200 else ''),
                        'url': website,
                        'project_type': 'airdrop',
                        'blockchain': platform,
                        'source_url': dapp_detail_url,
                        'discovery_source': 'blockchain',
                        'social_channels': []
                    }
                    
                    # 提取社交媒体链接
                    social_patterns = {
                        'twitter': r'<a[^>]*?href="(https?://(?:www\.)?twitter\.com/[^"]+)"[^>]*?>',
                        'telegram': r'<a[^>]*?href="(https?://(?:www\.)?t\.me/[^"]+)"[^>]*?>',
                        'discord': r'<a[^>]*?href="(https?://(?:www\.)?discord\.(?:gg|com)/[^"]+)"[^>]*?>'
                    }
                    
                    for social_platform, pattern in social_patterns.items():
                        social_match = re.search(pattern, dapp_detail_response.text)
                        if social_match:
                            project_info['social_channels'].append({
                                'platform': social_platform,
                                'url': social_match.group(1),
                                'followers': None
                            })
                    
                    projects.append(project_info)
                
                except Exception as e:
                    self.logger.error(f"获取 DApp {dapp_name} 详细信息时出错: {str(e)}")
                
                # 避免请求过于频繁
                time.sleep(0.5)
            
            return projects
        
        except Exception as e:
            self.logger.error(f"使用 DappRadar 获取 {platform} DApp 时出错: {str(e)}")
            return []