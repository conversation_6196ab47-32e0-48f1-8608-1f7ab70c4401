"""
团队验证器

该模块实现了验证项目团队背景的功能。
"""

import logging
import requests
import time
import re
from typing import Dict, List, Any, Optional

from discovery.models.project import Project


class TeamVerifier:
    """团队验证器，负责验证项目团队背景"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化团队验证器
        
        Args:
            config: 配置字典，包含团队验证器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.timeout = config.get('timeout', 10)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        self.min_team_size = config.get('min_team_size', 2)
        self.min_experience_years = config.get('min_experience_years', 1)
        self.known_scammers_list = config.get('known_scammers_list', [])
        self.trusted_sources = config.get('trusted_sources', [
            'linkedin.com', 'github.com', 'crunchbase.com', 'angel.co'
        ])
        
        # 统计信息
        self._stats = {
            'total_verifications': 0,
            'successful_verifications': 0,
            'failed_verifications': 0,
            'high_confidence': 0,
            'medium_confidence': 0,
            'low_confidence': 0
        }
    
    def verify(self, project: Project) -> Dict[str, Any]:
        """
        验证项目团队背景
        
        Args:
            project: 要验证的项目
            
        Returns:
            验证结果字典
        """
        self.logger.info(f"开始验证项目 {project.name} (ID: {project.id}) 的团队背景")
        
        try:
            # 更新统计信息
            self._stats['total_verifications'] += 1
            
            # 初始化结果
            result = {
                'verified': False,
                'confidence': 0.0,
                'team_size': 0,
                'known_members': 0,
                'average_experience': 0.0,
                'has_technical_members': False,
                'has_business_members': False,
                'has_known_scammers': False,
                'team_links': [],
                'warnings': [],
                'details': {}
            }
            
            # 从项目描述和网站中提取团队信息
            team_info = self._extract_team_info(project)
            
            if not team_info:
                self.logger.warning(f"无法从项目 {project.name} 中提取团队信息")
                result['warnings'].append("无法提取团队信息")
                result['confidence'] = 0.2
                self._stats['failed_verifications'] += 1
                self._stats['low_confidence'] += 1
                return result
            
            # 验证团队成员
            team_members = team_info.get('members', [])
            result['team_size'] = len(team_members)
            
            if result['team_size'] < self.min_team_size:
                result['warnings'].append(f"团队规模较小 ({result['team_size']} < {self.min_team_size})")
            
            # 验证团队成员背景
            total_experience = 0
            for member in team_members:
                # 检查是否为已知诈骗者
                if self._is_known_scammer(member):
                    result['has_known_scammers'] = True
                    result['warnings'].append(f"团队成员 {member.get('name')} 在已知诈骗者列表中")
                
                # 检查成员背景
                member_verified = self._verify_member(member)
                
                if member_verified:
                    result['known_members'] += 1
                    
                    # 更新经验年限
                    experience_years = member.get('experience_years', 0)
                    total_experience += experience_years
                    
                    # 检查成员角色
                    role = member.get('role', '').lower()
                    if any(tech_role in role for tech_role in ['developer', 'engineer', 'cto', 'architect', 'tech']):
                        result['has_technical_members'] = True
                    
                    if any(biz_role in role for biz_role in ['ceo', 'founder', 'business', 'marketing', 'operations']):
                        result['has_business_members'] = True
                
                # 添加团队成员链接
                for link in member.get('links', []):
                    if link not in result['team_links']:
                        result['team_links'].append(link)
            
            # 计算平均经验年限
            if result['team_size'] > 0:
                result['average_experience'] = total_experience / result['team_size']
            
            if result['average_experience'] < self.min_experience_years:
                result['warnings'].append(f"团队平均经验较少 ({result['average_experience']:.1f} < {self.min_experience_years})")
            
            # 计算置信度
            confidence = self._calculate_confidence(result)
            result['confidence'] = confidence
            
            # 确定验证结果
            if result['has_known_scammers']:
                result['verified'] = False
                self._stats['failed_verifications'] += 1
            elif confidence >= 0.7:
                result['verified'] = True
                self._stats['successful_verifications'] += 1
            else:
                result['verified'] = False
                self._stats['failed_verifications'] += 1
            
            # 更新置信度统计
            if confidence >= 0.7:
                self._stats['high_confidence'] += 1
            elif confidence >= 0.4:
                self._stats['medium_confidence'] += 1
            else:
                self._stats['low_confidence'] += 1
            
            # 添加详细信息
            result['details'] = team_info
            
            self.logger.info(f"项目 {project.name} 团队验证完成，置信度: {confidence:.2f}")
            
            return result
        
        except Exception as e:
            self.logger.error(f"验证项目 {project.name} 团队时出错: {str(e)}")
            self._stats['failed_verifications'] += 1
            self._stats['low_confidence'] += 1
            
            return {
                'verified': False,
                'confidence': 0.0,
                'error': str(e),
                'warnings': ["验证过程中出错"]
            }
    
    def _extract_team_info(self, project: Project) -> Dict[str, Any]:
        """
        从项目中提取团队信息
        
        Args:
            project: 项目对象
            
        Returns:
            团队信息字典
        """
        # 初始化团队信息
        team_info = {
            'members': [],
            'has_team_page': False,
            'team_page_url': None
        }
        
        # 从项目描述中提取团队信息
        if project.description:
            team_members = self._extract_team_from_text(project.description)
            team_info['members'].extend(team_members)
        
        # 从项目网站中提取团队信息
        if project.url:
            # 检查是否有团队页面
            team_page_url = self._find_team_page(project.url)
            
            if team_page_url:
                team_info['has_team_page'] = True
                team_info['team_page_url'] = team_page_url
                
                # 从团队页面提取团队成员
                team_members = self._extract_team_from_page(team_page_url)
                team_info['members'].extend(team_members)
        
        # 去重
        unique_members = {}
        for member in team_info['members']:
            name = member.get('name', '').lower()
            if name and name not in unique_members:
                unique_members[name] = member
        
        team_info['members'] = list(unique_members.values())
        
        return team_info
    
    def _extract_team_from_text(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取团队信息
        
        Args:
            text: 文本内容
            
        Returns:
            团队成员列表
        """
        team_members = []
        
        # 查找团队相关段落
        team_paragraphs = []
        paragraphs = text.split('\n')
        
        for i, para in enumerate(paragraphs):
            if re.search(r'\b(team|founder|co-founder|ceo|cto|developer)\b', para, re.IGNORECASE):
                team_paragraphs.append(para)
                
                # 添加相邻段落
                if i > 0:
                    team_paragraphs.append(paragraphs[i-1])
                if i < len(paragraphs) - 1:
                    team_paragraphs.append(paragraphs[i+1])
        
        # 从段落中提取人名和角色
        for para in team_paragraphs:
            # 使用简单的模式匹配提取人名和角色
            # 实际中可能需要更复杂的NLP技术
            name_role_matches = re.finditer(r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,2})\s+(?:is|as|our|the)?\s+(?:a\s+)?([A-Za-z]+(?:\s+[A-Za-z]+){0,2})', para)
            
            for match in name_role_matches:
                name = match.group(1)
                role = match.group(2)
                
                # 检查角色是否有效
                if re.search(r'\b(ceo|cto|founder|co-founder|developer|engineer|designer|marketing|operations|advisor)\b', role, re.IGNORECASE):
                    team_members.append({
                        'name': name,
                        'role': role,
                        'source': 'description',
                        'links': [],
                        'experience_years': 0  # 默认经验年限
                    })
        
        return team_members
    
    def _find_team_page(self, website_url: str) -> Optional[str]:
        """
        查找网站的团队页面
        
        Args:
            website_url: 网站URL
            
        Returns:
            团队页面URL，如果没有找到则返回None
        """
        try:
            # 发送请求
            response = requests.get(website_url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找团队页面链接
            team_links = []
            
            for link in soup.find_all('a'):
                href = link.get('href')
                text = link.get_text(strip=True).lower()
                
                if href and text:
                    if re.search(r'\b(team|about us|about|who we are|our team|people)\b', text, re.IGNORECASE):
                        # 构建完整URL
                        if href.startswith('http'):
                            team_links.append(href)
                        elif href.startswith('/'):
                            # 相对路径
                            base_url = '/'.join(website_url.split('/')[:3])  # 提取域名部分
                            team_links.append(f"{base_url}{href}")
                        else:
                            # 相对路径，不以/开头
                            team_links.append(f"{website_url.rstrip('/')}/{href}")
            
            # 返回第一个找到的团队页面链接
            if team_links:
                return team_links[0]
            
            return None
        
        except Exception as e:
            self.logger.error(f"查找团队页面时出错: {str(e)}")
            return None
    
    def _extract_team_from_page(self, page_url: str) -> List[Dict[str, Any]]:
        """
        从网页中提取团队信息
        
        Args:
            page_url: 网页URL
            
        Returns:
            团队成员列表
        """
        team_members = []
        
        try:
            # 发送请求
            response = requests.get(page_url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找团队成员信息
            # 这里使用一些常见的团队页面结构模式
            
            # 模式1: 团队成员卡片
            team_cards = soup.select('.team-member, .team-card, .member, .person, .staff, .employee')
            
            for card in team_cards:
                name_elem = card.select_one('.name, h3, h4, .title')
                role_elem = card.select_one('.role, .position, .job-title, .designation')
                bio_elem = card.select_one('.bio, .description, .about, p')
                
                if name_elem:
                    name = name_elem.get_text(strip=True)
                    role = role_elem.get_text(strip=True) if role_elem else ''
                    bio = bio_elem.get_text(strip=True) if bio_elem else ''
                    
                    # 提取链接
                    links = []
                    for link in card.select('a'):
                        href = link.get('href')
                        if href and any(source in href for source in self.trusted_sources):
                            links.append(href)
                    
                    # 估算经验年限
                    experience_years = self._estimate_experience(bio)
                    
                    team_members.append({
                        'name': name,
                        'role': role,
                        'bio': bio,
                        'source': 'team_page',
                        'links': links,
                        'experience_years': experience_years
                    })
            
            # 如果没有找到卡片，尝试其他模式
            if not team_members:
                # 模式2: 查找包含人名和角色的段落
                for section in soup.select('section, div, article'):
                    text = section.get_text()
                    
                    # 检查是否包含团队相关词汇
                    if re.search(r'\b(team|founder|co-founder|ceo|cto|developer)\b', text, re.IGNORECASE):
                        # 提取人名和角色
                        name_role_matches = re.finditer(r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,2})\s+(?:is|as|our|the)?\s+(?:a\s+)?([A-Za-z]+(?:\s+[A-Za-z]+){0,2})', text)
                        
                        for match in name_role_matches:
                            name = match.group(1)
                            role = match.group(2)
                            
                            # 检查角色是否有效
                            if re.search(r'\b(ceo|cto|founder|co-founder|developer|engineer|designer|marketing|operations|advisor)\b', role, re.IGNORECASE):
                                # 提取链接
                                links = []
                                for link in section.select('a'):
                                    href = link.get('href')
                                    if href and any(source in href for source in self.trusted_sources):
                                        links.append(href)
                                
                                team_members.append({
                                    'name': name,
                                    'role': role,
                                    'source': 'team_page',
                                    'links': links,
                                    'experience_years': 0  # 默认经验年限
                                })
            
            return team_members
        
        except Exception as e:
            self.logger.error(f"从页面 {page_url} 提取团队信息时出错: {str(e)}")
            return []
    
    def _is_known_scammer(self, member: Dict[str, Any]) -> bool:
        """
        检查团队成员是否为已知诈骗者
        
        Args:
            member: 团队成员信息
            
        Returns:
            是否为已知诈骗者
        """
        name = member.get('name', '').lower()
        
        if not name:
            return False
        
        # 检查是否在已知诈骗者列表中
        for scammer in self.known_scammers_list:
            if name == scammer.lower():
                return True
        
        return False
    
    def _verify_member(self, member: Dict[str, Any]) -> bool:
        """
        验证团队成员背景
        
        Args:
            member: 团队成员信息
            
        Returns:
            是否验证通过
        """
        # 检查是否有可信来源的链接
        links = member.get('links', [])
        
        for link in links:
            if any(source in link for source in self.trusted_sources):
                return True
        
        # 如果没有可信链接，但有详细角色和经验，也可以认为是有效的
        if member.get('role') and member.get('experience_years', 0) > 0:
            return True
        
        return False
    
    def _estimate_experience(self, bio: str) -> int:
        """
        从简介中估算经验年限
        
        Args:
            bio: 个人简介
            
        Returns:
            估算的经验年限
        """
        if not bio:
            return 0
        
        # 查找经验年限相关的表述
        experience_matches = re.search(r'(\d+)\+?\s+years?\s+(?:of\s+)?experience', bio, re.IGNORECASE)
        
        if experience_matches:
            return int(experience_matches.group(1))
        
        # 查找工作年份范围
        year_ranges = re.findall(r'(?:from|since|between)?\s+(\d{4})\s*(?:-|to|–|until)\s*(\d{4}|\w+)', bio, re.IGNORECASE)
        
        if year_ranges:
            current_year = time.localtime().tm_year
            total_years = 0
            
            for start_year, end_year in year_ranges:
                start = int(start_year)
                
                # 处理结束年份
                if end_year.isdigit():
                    end = int(end_year)
                elif end_year.lower() in ['present', 'now', 'current']:
                    end = current_year
                else:
                    end = current_year
                
                # 计算年限
                if start <= end and start >= 1970 and end <= current_year:
                    total_years += (end - start)
            
            return total_years
        
        return 0
    
    def _calculate_confidence(self, result: Dict[str, Any]) -> float:
        """
        计算验证结果的置信度
        
        Args:
            result: 验证结果
            
        Returns:
            置信度 (0.0-1.0)
        """
        confidence = 0.0
        
        # 基础置信度
        if result['team_size'] >= self.min_team_size:
            confidence += 0.2
        else:
            confidence += 0.1 * (result['team_size'] / self.min_team_size)
        
        # 已知成员比例
        if result['team_size'] > 0:
            known_ratio = result['known_members'] / result['team_size']
            confidence += 0.3 * known_ratio
        
        # 经验年限
        if result['average_experience'] >= self.min_experience_years:
            confidence += 0.2
        else:
            confidence += 0.1 * (result['average_experience'] / self.min_experience_years)
        
        # 团队组成
        if result['has_technical_members'] and result['has_business_members']:
            confidence += 0.2
        elif result['has_technical_members'] or result['has_business_members']:
            confidence += 0.1
        
        # 团队链接
        if result['team_links']:
            confidence += 0.1 * min(len(result['team_links']), 3) / 3
        
        # 已知诈骗者
        if result['has_known_scammers']:
            confidence = 0.0
        
        # 限制在0.0-1.0范围内
        return max(0.0, min(1.0, confidence))
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()