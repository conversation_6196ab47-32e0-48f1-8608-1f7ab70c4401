#!/usr/bin/env python3
"""
测试修复效果的脚本

验证所有关键组件是否可以正常导入和初始化
"""

import os
import sys
import logging
import traceback
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_imports():
    """测试关键模块导入"""
    logger.info("🧪 测试模块导入...")
    
    tests = [
        # 配置管理
        ("common.config_manager", "ConfigManager"),
        ("common.error_handling", "ErrorHandler"),
        
        # 协调器组件
        ("coordinator.core.agent_registry", "AgentRegistry"),
        ("coordinator.communication.message_broker", "MessageBroker"),
        ("coordinator.workflow.workflow_manager", "WorkflowManager"),
        
        # 发现组件
        ("discovery.models.project", "Project"),
        ("discovery.sources.source_manager", "SourceManager"),
        
        # 占位符模块
        ("coordinator.resources.resource_allocator", "ResourceAllocator"),
        ("coordinator.monitoring.health_monitor", "HealthMonitor"),
        ("discovery.sources.twitter_monitor", "TwitterMonitor"),
        ("discovery.filters.project_filter", "ProjectFilter"),
        ("discovery.storage.project_storage", "ProjectStorage"),
    ]
    
    passed = 0
    failed = 0
    
    for module_name, class_name in tests:
        try:
            module = __import__(module_name, fromlist=[class_name])
            cls = getattr(module, class_name)
            logger.info(f"  ✅ {module_name}.{class_name}")
            passed += 1
        except Exception as e:
            logger.error(f"  ❌ {module_name}.{class_name}: {e}")
            failed += 1
    
    logger.info(f"导入测试完成: {passed} 通过, {failed} 失败")
    return failed == 0


def test_coordinator_agent():
    """测试协调器智能体"""
    logger.info("🤖 测试协调器智能体...")
    
    try:
        from coordinator.coordinator_agent import CoordinatorAgent
        
        # 创建配置
        config = {
            'logging': {'level': 'INFO'},
            'resources': {'max_memory': 1024}
        }
        
        # 创建实例
        agent = CoordinatorAgent(config)
        logger.info(f"  ✅ 协调器创建成功，状态: {agent.status}")
        
        # 测试基本方法
        info = agent.get_agent_info()
        logger.info(f"  ✅ 智能体信息获取成功: {info['name']}")
        
        status = agent.get_system_status()
        logger.info(f"  ✅ 系统状态获取成功")
        
        return True
        
    except Exception as e:
        logger.error(f"  ❌ 协调器测试失败: {e}")
        traceback.print_exc()
        return False


def test_discovery_agent():
    """测试发现智能体"""
    logger.info("🔍 测试发现智能体...")
    
    try:
        from discovery.discovery_agent import DiscoveryAgent
        
        # 创建配置
        config = {
            'sources': {'enabled_sources': []},
            'filters': {},
            'collectors': {}
        }
        
        # 创建实例
        agent = DiscoveryAgent(config)
        logger.info(f"  ✅ 发现智能体创建成功，状态: {agent.status}")
        
        # 测试基本方法
        info = agent.get_agent_info()
        logger.info(f"  ✅ 智能体信息获取成功: {info['name']}")
        
        return True
        
    except Exception as e:
        logger.error(f"  ❌ 发现智能体测试失败: {e}")
        traceback.print_exc()
        return False


def test_config_manager():
    """测试配置管理器"""
    logger.info("⚙️ 测试配置管理器...")
    
    try:
        from common.config_manager import ConfigManager, get_config_manager
        
        # 测试配置管理器
        config_manager = ConfigManager()
        logger.info("  ✅ 配置管理器创建成功")
        
        # 测试获取配置
        system_config = config_manager.get_system_config()
        logger.info(f"  ✅ 系统配置获取成功: {system_config.get('name', 'Unknown')}")
        
        # 测试智能体配置
        discovery_config = config_manager.get_agent_config('discovery')
        logger.info("  ✅ 智能体配置获取成功")
        
        # 测试全局实例
        global_manager = get_config_manager()
        logger.info("  ✅ 全局配置管理器获取成功")
        
        return True
        
    except Exception as e:
        logger.error(f"  ❌ 配置管理器测试失败: {e}")
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理"""
    logger.info("🛡️ 测试错误处理...")
    
    try:
        from common.error_handling import ErrorHandler, safe_execute, retry_on_error
        
        # 测试错误处理器
        error_handler = ErrorHandler()
        logger.info("  ✅ 错误处理器创建成功")
        
        # 测试安全执行
        def test_func():
            return "success"
        
        result = safe_execute(test_func, default_return="failed")
        logger.info(f"  ✅ 安全执行测试成功: {result}")
        
        # 测试重试装饰器
        @retry_on_error(max_retries=2)
        def test_retry():
            return "retry_success"
        
        result = test_retry()
        logger.info(f"  ✅ 重试机制测试成功: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"  ❌ 错误处理测试失败: {e}")
        traceback.print_exc()
        return False


def test_project_structure():
    """测试项目结构"""
    logger.info("🏗️ 测试项目结构...")
    
    required_files = [
        "config_unified.json",
        "common/config_manager.py",
        "common/error_handling.py",
        "coordinator/coordinator_agent.py",
        "discovery/discovery_agent.py",
        "coordinator/workflow/workflow_manager.py",
        "coordinator/resources/resource_allocator.py",
        "discovery/sources/twitter_monitor.py",
        "discovery/filters/project_filter.py",
        "discovery/storage/project_storage.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            logger.info(f"  ✅ {file_path}")
    
    if missing_files:
        logger.error(f"  ❌ 缺失文件: {missing_files}")
        return False
    else:
        logger.info("  ✅ 所有必需文件都存在")
        return True


def main():
    """主函数"""
    logger.info("🚀 开始测试 AirHunter 修复效果...")
    logger.info(f"📁 工作目录: {os.getcwd()}")
    logger.info(f"🕒 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行所有测试
    tests = [
        ("项目结构", test_project_structure),
        ("模块导入", test_imports),
        ("配置管理器", test_config_manager),
        ("错误处理", test_error_handling),
        ("协调器智能体", test_coordinator_agent),
        ("发现智能体", test_discovery_agent),
    ]
    
    results = {}
    total_score = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            if result:
                total_score += 1
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 输出总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 测试结果总结")
    logger.info(f"{'='*60}")
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
    
    success_rate = (total_score / len(tests)) * 100
    logger.info(f"\n🎯 总体成功率: {total_score}/{len(tests)} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        status = "🟢 优秀"
        message = "修复效果很好！"
    elif success_rate >= 80:
        status = "🟡 良好"
        message = "修复基本成功，还有少量问题。"
    elif success_rate >= 60:
        status = "🟠 需要改进"
        message = "修复部分成功，需要进一步改进。"
    else:
        status = "🔴 需要重点关注"
        message = "修复效果不佳，需要重点关注。"
    
    logger.info(f"   状态: {status}")
    logger.info(f"   评价: {message}")
    
    return success_rate >= 80


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
