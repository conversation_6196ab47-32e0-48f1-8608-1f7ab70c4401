"""
Persona Generator

角色生成器，负责生成不同类型的数字角色和身份特征。
"""

import logging
import random
from typing import Dict, Any, List
from datetime import datetime


class PersonaGenerator:
    """角色生成器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 角色模板
        self.persona_templates = {
            "conservative": {
                "risk_tolerance": "low",
                "activity_level": "moderate",
                "tech_savviness": "basic",
                "social_engagement": "minimal"
            },
            "normal": {
                "risk_tolerance": "medium",
                "activity_level": "active",
                "tech_savviness": "intermediate",
                "social_engagement": "moderate"
            },
            "aggressive": {
                "risk_tolerance": "high",
                "activity_level": "very_active",
                "tech_savviness": "advanced",
                "social_engagement": "high"
            }
        }
    
    def generate_persona(self, persona_type: str = "normal") -> Dict[str, Any]:
        """
        生成角色配置文件
        
        Args:
            persona_type: 角色类型
            
        Returns:
            Dict[str, Any]: 角色配置文件
        """
        try:
            template = self.persona_templates.get(persona_type, self.persona_templates["normal"])
            
            persona = {
                "type": persona_type,
                "created_at": datetime.utcnow().isoformat(),
                
                # 基础特征
                "risk_tolerance": template["risk_tolerance"],
                "activity_level": template["activity_level"],
                "tech_savviness": template["tech_savviness"],
                "social_engagement": template["social_engagement"],
                
                # 行为特征
                "browsing_habits": self._generate_browsing_habits(),
                "time_patterns": self._generate_time_patterns(),
                "device_preferences": self._generate_device_preferences(),
                "security_awareness": self._generate_security_awareness(),
                
                # 社交特征
                "communication_style": self._generate_communication_style(),
                "content_preferences": self._generate_content_preferences(),
                "interaction_frequency": self._generate_interaction_frequency()
            }
            
            self.logger.info(f"Generated {persona_type} persona")
            return persona
            
        except Exception as e:
            self.logger.error(f"Failed to generate persona: {e}")
            return {}
    
    def _generate_browsing_habits(self) -> Dict[str, Any]:
        """生成浏览习惯"""
        return {
            "session_duration": random.randint(300, 3600),  # 5分钟到1小时
            "pages_per_session": random.randint(5, 50),
            "scroll_behavior": random.choice(["fast", "moderate", "slow"]),
            "tab_usage": random.choice(["single", "multiple", "heavy_multiple"]),
            "bookmark_usage": random.choice(["never", "rarely", "frequently"])
        }
    
    def _generate_time_patterns(self) -> Dict[str, Any]:
        """生成时间模式"""
        # 生成偏好的活动时间
        preferred_hours = []
        for _ in range(random.randint(1, 3)):
            start_hour = random.randint(6, 22)
            duration = random.randint(2, 6)
            preferred_hours.extend(range(start_hour, min(24, start_hour + duration)))
        
        return {
            "preferred_hours": sorted(list(set(preferred_hours))),
            "timezone_consistency": random.uniform(0.7, 1.0),
            "weekend_behavior": random.choice(["same", "different", "more_active"]),
            "break_patterns": random.choice(["regular", "irregular", "none"])
        }
    
    def _generate_device_preferences(self) -> Dict[str, Any]:
        """生成设备偏好"""
        return {
            "primary_device": random.choice(["desktop", "laptop", "mobile"]),
            "screen_size_preference": random.choice(["small", "medium", "large"]),
            "browser_preference": random.choice(["chrome", "firefox", "safari", "edge"]),
            "os_familiarity": random.uniform(0.5, 1.0),
            "shortcut_usage": random.uniform(0.1, 0.8)
        }
    
    def _generate_security_awareness(self) -> Dict[str, Any]:
        """生成安全意识"""
        return {
            "password_strength": random.choice(["weak", "medium", "strong"]),
            "two_factor_usage": random.choice([True, False]),
            "privacy_concern": random.uniform(0.2, 0.9),
            "suspicious_link_caution": random.uniform(0.3, 0.9),
            "software_update_frequency": random.choice(["never", "rarely", "regularly"])
        }
    
    def _generate_communication_style(self) -> Dict[str, Any]:
        """生成沟通风格"""
        return {
            "formality_level": random.choice(["casual", "semi_formal", "formal"]),
            "emoji_usage": random.choice(["never", "rarely", "frequently"]),
            "response_speed": random.choice(["immediate", "quick", "delayed"]),
            "message_length": random.choice(["short", "medium", "long"]),
            "typo_frequency": random.uniform(0.01, 0.1)
        }
    
    def _generate_content_preferences(self) -> Dict[str, Any]:
        """生成内容偏好"""
        interests = ["technology", "finance", "gaming", "social", "news", "entertainment", 
                    "education", "sports", "travel", "food", "art", "music"]
        
        return {
            "primary_interests": random.sample(interests, random.randint(2, 5)),
            "content_consumption": random.choice(["passive", "active", "creator"]),
            "platform_preferences": random.sample(
                ["twitter", "discord", "telegram", "reddit", "youtube"], 
                random.randint(2, 4)
            ),
            "engagement_style": random.choice(["lurker", "commenter", "sharer"])
        }
    
    def _generate_interaction_frequency(self) -> Dict[str, Any]:
        """生成交互频率"""
        return {
            "daily_interactions": random.randint(5, 100),
            "peak_activity_hours": random.randint(2, 6),
            "social_media_frequency": random.choice(["low", "medium", "high"]),
            "community_participation": random.choice(["none", "observer", "participant", "leader"])
        }
    
    def get_persona_templates(self) -> List[str]:
        """获取可用的角色模板"""
        return list(self.persona_templates.keys())
    
    def validate_persona(self, persona: Dict[str, Any]) -> bool:
        """验证角色配置文件的完整性"""
        required_fields = ["type", "risk_tolerance", "activity_level", "tech_savviness"]
        
        for field in required_fields:
            if field not in persona:
                self.logger.warning(f"Missing required field: {field}")
                return False
        
        return True
