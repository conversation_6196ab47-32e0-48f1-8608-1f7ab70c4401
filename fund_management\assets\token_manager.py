"""
Token Manager

代币管理器，负责管理代币信息、元数据和交互操作。
"""

import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime


class TokenManager:
    """
    代币管理器
    
    负责管理代币的基本信息、元数据、价格信息和交互操作。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化代币管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 代币信息存储
        self.tokens: Dict[str, Dict] = {}
        
        # 代币价格缓存
        self.price_cache: Dict[str, Dict] = {}
        
        # 管理统计
        self.manager_stats = {
            'total_tokens': 0,
            'price_updates': 0,
            'metadata_updates': 0,
            'last_sync_time': None
        }
        
        # 默认代币列表
        self.default_tokens = {
            "******************************************": {
                "symbol": "USDC",
                "name": "USD Coin",
                "decimals": 6,
                "type": "stablecoin"
            },
            "******************************************": {
                "symbol": "USDT",
                "name": "Tether USD",
                "decimals": 6,
                "type": "stablecoin"
            }
        }
    
    async def initialize(self) -> bool:
        """
        初始化代币管理器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 加载默认代币
            await self._load_default_tokens()
            
            self.logger.info("Token Manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Token Manager: {e}")
            return False
    
    async def add_token(self, token_address: str, token_info: Dict[str, Any] = None) -> bool:
        """
        添加代币到管理器
        
        Args:
            token_address: 代币合约地址
            token_info: 代币信息
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if not self._is_valid_address(token_address):
                self.logger.error(f"Invalid token address: {token_address}")
                return False
            
            # 如果没有提供信息，尝试从链上获取
            if not token_info:
                token_info = await self._fetch_token_metadata(token_address)
            
            # 构建完整的代币信息
            complete_token_info = {
                "address": token_address,
                "symbol": token_info.get("symbol", "UNKNOWN"),
                "name": token_info.get("name", "Unknown Token"),
                "decimals": token_info.get("decimals", 18),
                "type": token_info.get("type", "erc20"),
                "total_supply": token_info.get("total_supply"),
                "added_at": datetime.utcnow().isoformat(),
                "last_updated": datetime.utcnow().isoformat(),
                "metadata": token_info.get("metadata", {}),
                "price_info": {},
                "is_verified": token_info.get("is_verified", False)
            }
            
            self.tokens[token_address] = complete_token_info
            self.manager_stats['total_tokens'] += 1
            
            self.logger.info(f"Added token {token_info.get('symbol', 'UNKNOWN')} at {token_address}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add token: {e}")
            return False
    
    async def get_token_info(self, token_address: str) -> Optional[Dict[str, Any]]:
        """
        获取代币信息
        
        Args:
            token_address: 代币合约地址
            
        Returns:
            Optional[Dict[str, Any]]: 代币信息
        """
        try:
            return self.tokens.get(token_address)
        except Exception as e:
            self.logger.error(f"Error getting token info: {e}")
            return None
    
    async def update_token_price(self, token_address: str, price_data: Dict[str, Any]) -> bool:
        """
        更新代币价格信息
        
        Args:
            token_address: 代币合约地址
            price_data: 价格数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if token_address not in self.tokens:
                self.logger.error(f"Token {token_address} not found")
                return False
            
            # 更新价格信息
            self.tokens[token_address]["price_info"] = {
                "price_usd": price_data.get("price_usd", 0.0),
                "price_eth": price_data.get("price_eth", 0.0),
                "market_cap": price_data.get("market_cap", 0.0),
                "volume_24h": price_data.get("volume_24h", 0.0),
                "change_24h": price_data.get("change_24h", 0.0),
                "last_updated": datetime.utcnow().isoformat()
            }
            
            # 更新价格缓存
            self.price_cache[token_address] = self.tokens[token_address]["price_info"].copy()
            
            self.tokens[token_address]["last_updated"] = datetime.utcnow().isoformat()
            self.manager_stats['price_updates'] += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update token price: {e}")
            return False

    async def _load_default_tokens(self):
        """加载默认代币列表"""
        try:
            for address, info in self.default_tokens.items():
                await self.add_token(address, info)
            
            self.logger.info(f"Loaded {len(self.default_tokens)} default tokens")
            
        except Exception as e:
            self.logger.error(f"Error loading default tokens: {e}")
    
    async def _fetch_token_metadata(self, token_address: str) -> Dict[str, Any]:
        """
        从区块链获取代币元数据
        
        Args:
            token_address: 代币合约地址
            
        Returns:
            Dict[str, Any]: 代币元数据
        """
        try:
            # 模拟从区块链获取代币信息
            # 实际实现需要调用合约的name(), symbol(), decimals()方法
            
            import random
            
            metadata = {
                "symbol": f"TOKEN{random.randint(1, 999)}",
                "name": f"Test Token {random.randint(1, 999)}",
                "decimals": random.choice([6, 8, 18]),
                "total_supply": random.randint(1000000, 1000000000),
                "is_verified": random.choice([True, False])
            }
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"Error fetching token metadata: {e}")
            return {
                "symbol": "UNKNOWN",
                "name": "Unknown Token",
                "decimals": 18
            }
    
    async def remove_token(self, token_address: str) -> bool:
        """
        移除代币
        
        Args:
            token_address: 代币合约地址
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if token_address in self.tokens:
                token_info = self.tokens[token_address]
                del self.tokens[token_address]
                
                # 清理价格缓存
                self.price_cache.pop(token_address, None)
                
                self.manager_stats['total_tokens'] -= 1
                
                self.logger.info(f"Removed token {token_info.get('symbol', 'UNKNOWN')}")
                return True
            else:
                self.logger.warning(f"Token {token_address} not found")
                return False
                
        except Exception as e:
            self.logger.error(f"Error removing token: {e}")
            return False
    
    async def get_all_tokens(self) -> List[Dict[str, Any]]:
        """
        获取所有代币信息
        
        Returns:
            List[Dict[str, Any]]: 所有代币信息列表
        """
        try:
            return list(self.tokens.values())
        except Exception as e:
            self.logger.error(f"Error getting all tokens: {e}")
            return []
    
    async def search_tokens(self, query: str) -> List[Dict[str, Any]]:
        """
        搜索代币
        
        Args:
            query: 搜索查询（符号或名称）
            
        Returns:
            List[Dict[str, Any]]: 匹配的代币列表
        """
        try:
            query_lower = query.lower()
            matching_tokens = []
            
            for token_info in self.tokens.values():
                symbol = token_info.get("symbol", "").lower()
                name = token_info.get("name", "").lower()
                
                if query_lower in symbol or query_lower in name:
                    matching_tokens.append(token_info)
            
            return matching_tokens
            
        except Exception as e:
            self.logger.error(f"Error searching tokens: {e}")
            return []
    
    async def get_token_price(self, token_address: str) -> Optional[Dict[str, Any]]:
        """
        获取代币价格信息
        
        Args:
            token_address: 代币合约地址
            
        Returns:
            Optional[Dict[str, Any]]: 价格信息
        """
        try:
            if token_address in self.tokens:
                return self.tokens[token_address].get("price_info", {})
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting token price: {e}")
            return None
    
    async def update_token_metadata(self, token_address: str, metadata: Dict[str, Any]) -> bool:
        """
        更新代币元数据
        
        Args:
            token_address: 代币合约地址
            metadata: 新的元数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if token_address not in self.tokens:
                self.logger.error(f"Token {token_address} not found")
                return False
            
            # 更新元数据
            current_metadata = self.tokens[token_address].get("metadata", {})
            current_metadata.update(metadata)
            
            self.tokens[token_address]["metadata"] = current_metadata
            self.tokens[token_address]["last_updated"] = datetime.utcnow().isoformat()
            
            self.manager_stats['metadata_updates'] += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating token metadata: {e}")
            return False
    
    def _is_valid_address(self, address: str) -> bool:
        """验证以太坊地址格式"""
        if not isinstance(address, str):
            return False
        if not address.startswith("0x"):
            return False
        if len(address) != 42:
            return False
        try:
            int(address[2:], 16)
            return True
        except ValueError:
            return False
    
    async def get_manager_statistics(self) -> Dict[str, Any]:
        """
        获取管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 统计代币类型分布
            type_distribution = {}
            verified_count = 0
            
            for token_info in self.tokens.values():
                token_type = token_info.get("type", "unknown")
                type_distribution[token_type] = type_distribution.get(token_type, 0) + 1
                
                if token_info.get("is_verified", False):
                    verified_count += 1
            
            return {
                'manager_stats': self.manager_stats,
                'total_tokens': len(self.tokens),
                'verified_tokens': verified_count,
                'type_distribution': type_distribution,
                'cached_prices': len(self.price_cache)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting manager statistics: {e}")
            return {}
    
    async def sync_token_data(self) -> bool:
        """
        同步所有代币数据
        
        Returns:
            bool: 同步是否成功
        """
        try:
            sync_count = 0
            
            for token_address in list(self.tokens.keys()):
                try:
                    # 更新元数据
                    updated_metadata = await self._fetch_token_metadata(token_address)
                    await self.update_token_metadata(token_address, updated_metadata)
                    
                    sync_count += 1
                    
                except Exception as e:
                    self.logger.error(f"Error syncing token {token_address}: {e}")
            
            self.manager_stats['last_sync_time'] = datetime.utcnow().isoformat()
            
            self.logger.info(f"Synced {sync_count} tokens")
            return True
            
        except Exception as e:
            self.logger.error(f"Error in token data sync: {e}")
            return False
