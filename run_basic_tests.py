#!/usr/bin/env python3
"""
AirHunter 项目基础测试运行器

运行基础的语法检查和导入测试
"""

import os
import sys
import importlib.util
import traceback
from pathlib import Path


def test_python_syntax(file_path):
    """测试Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 编译检查语法
        compile(content, file_path, 'exec')
        return True, None
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"其他错误: {e}"


def test_module_import(module_path, module_name):
    """测试模块导入"""
    try:
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        if spec is None:
            return False, "无法创建模块规范"
        
        module = importlib.util.module_from_spec(spec)
        # 不执行模块，只检查是否可以加载
        return True, None
    except Exception as e:
        return False, f"导入错误: {e}"


def run_basic_tests():
    """运行基础测试"""
    print("🧪 开始运行 AirHunter 项目基础测试...")
    print("=" * 60)
    
    # 测试结果统计
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    # 要测试的主要智能体文件
    agent_files = [
        ("coordinator/coordinator_agent.py", "coordinator_agent"),
        ("discovery/discovery_agent.py", "discovery_agent"),
        ("assessment/assessment_agent.py", "assessment_agent"),
        ("monitoring/monitoring_agent.py", "monitoring_agent"),
        ("fund_management/fund_management_agent.py", "fund_management_agent"),
        ("task_planning/task_planning_agent.py", "task_planning_agent"),
        ("task_execution/task_execution_agent.py", "task_execution_agent"),
        ("proxy/proxy_agent.py", "proxy_agent"),
        ("anti_sybil/anti_sybil_agent.py", "anti_sybil_agent"),
        ("profit_optimization/profit_optimization_agent.py", "profit_optimization_agent")
    ]
    
    # 测试语法错误修复
    print("\n📝 测试语法错误修复...")
    syntax_test_files = [
        "assessment/main.py",
        "assessment/risk/reward_estimator.py", 
        "assessment/risk/risk_calculator.py"
    ]
    
    for file_path in syntax_test_files:
        total_tests += 1
        if os.path.exists(file_path):
            success, error = test_python_syntax(file_path)
            if success:
                print(f"  ✅ {file_path} - 语法正确")
                passed_tests += 1
            else:
                print(f"  ❌ {file_path} - {error}")
                failed_tests += 1
        else:
            print(f"  ⚠️  {file_path} - 文件不存在")
            failed_tests += 1
    
    # 测试主智能体文件
    print("\n🤖 测试主智能体文件...")
    for file_path, module_name in agent_files:
        total_tests += 1
        if os.path.exists(file_path):
            # 先测试语法
            syntax_ok, syntax_error = test_python_syntax(file_path)
            if syntax_ok:
                print(f"  ✅ {file_path} - 语法正确")
                passed_tests += 1
            else:
                print(f"  ❌ {file_path} - 语法错误: {syntax_error}")
                failed_tests += 1
        else:
            print(f"  ❌ {file_path} - 文件不存在")
            failed_tests += 1
    
    # 测试关键模块文件
    print("\n📦 测试关键模块文件...")
    key_modules = [
        "discovery/models/project.py",
        "discovery/sources/twitter_monitor.py",
        "assessment/risk/risk_calculator.py",
        "monitoring/trackers/project_tracker.py",
        "fund_management/core/wallet_manager.py",
        "task_planning/core/task_planner.py",
        "task_execution/core/task_executor.py",
        "proxy/core/proxy_manager.py",
        "anti_sybil/detection/behavior_analyzer.py",
        "profit_optimization/core/optimizer.py"
    ]
    
    for file_path in key_modules:
        total_tests += 1
        if os.path.exists(file_path):
            success, error = test_python_syntax(file_path)
            if success:
                print(f"  ✅ {file_path} - 语法正确")
                passed_tests += 1
            else:
                print(f"  ❌ {file_path} - {error}")
                failed_tests += 1
        else:
            print(f"  ⚠️  {file_path} - 文件不存在")
    
    # 测试项目结构
    print("\n🏗️  测试项目结构...")
    required_dirs = [
        "coordinator", "discovery", "assessment", "monitoring",
        "fund_management", "task_planning", "task_execution", 
        "proxy", "anti_sybil", "profit_optimization"
    ]
    
    structure_tests = 0
    structure_passed = 0
    
    for dir_name in required_dirs:
        structure_tests += 1
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            # 检查是否有主文件
            main_file = f"{dir_name}/{dir_name}_agent.py"
            if os.path.exists(main_file):
                print(f"  ✅ {dir_name} - 目录和主文件存在")
                structure_passed += 1
            else:
                print(f"  ⚠️  {dir_name} - 目录存在但缺少主文件")
        else:
            print(f"  ❌ {dir_name} - 目录不存在")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    print(f"🧪 语法和文件测试:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed_tests}")
    print(f"   失败: {failed_tests}")
    print(f"   成功率: {(passed_tests/total_tests*100):.1f}%")
    
    print(f"\n🏗️  结构测试:")
    print(f"   总目录数: {structure_tests}")
    print(f"   完整目录: {structure_passed}")
    print(f"   完整率: {(structure_passed/structure_tests*100):.1f}%")
    
    # 总体评估
    overall_success_rate = ((passed_tests + structure_passed) / (total_tests + structure_tests)) * 100
    
    print(f"\n🎯 总体评估:")
    print(f"   整体成功率: {overall_success_rate:.1f}%")
    
    if overall_success_rate >= 90:
        print("   状态: 🟢 优秀")
    elif overall_success_rate >= 80:
        print("   状态: 🟡 良好")
    elif overall_success_rate >= 70:
        print("   状态: 🟠 需要改进")
    else:
        print("   状态: 🔴 需要重点关注")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    if failed_tests > 0:
        print("   - 修复语法错误和缺失文件")
    if structure_passed < structure_tests:
        print("   - 补充缺失的主智能体文件")
    if overall_success_rate < 95:
        print("   - 增加单元测试覆盖")
        print("   - 完善错误处理机制")
    
    return overall_success_rate >= 80


def main():
    """主函数"""
    print("🚀 AirHunter 项目基础测试运行器")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"🐍 Python版本: {sys.version}")
    
    success = run_basic_tests()
    
    if success:
        print("\n🎉 基础测试通过！项目状态良好。")
        return 0
    else:
        print("\n⚠️  基础测试发现问题，请查看上述建议进行改进。")
        return 1


if __name__ == "__main__":
    exit(main())
