"""
Risk Analyzer

风险分析器，负责分析和评估系统安全风险。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta


class RiskAnalyzer:
    """
    风险分析器

    负责分析交易风险、用户行为风险和系统安全风险。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化风险分析器

        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 风险评估历史
        self.risk_assessments: List[Dict] = []

        # 风险阈值配置
        self.risk_thresholds = {
            "transaction_amount_usd": config.get("max_transaction_amount", 10000),
            "daily_transaction_limit": config.get("daily_limit", 50000),
            "suspicious_pattern_score": config.get("suspicious_threshold", 0.7),
            "velocity_threshold": config.get("velocity_threshold", 5)  # transactions per minute
        }

        # 风险统计
        self.risk_stats = {
            'total_assessments': 0,
            'high_risk_transactions': 0,
            'blocked_transactions': 0,
            'false_positives': 0
        }

    async def initialize(self) -> bool:
        """
        初始化风险分析器

        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("Risk Analyzer initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Risk Analyzer: {e}")
            return False

    async def analyze_transaction_risk(self, transaction_data: Dict[str, Any],
                                     user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        分析交易风险

        Args:
            transaction_data: 交易数据
            user_context: 用户上下文信息

        Returns:
            Dict[str, Any]: 风险分析结果
        """
        try:
            self.risk_stats['total_assessments'] += 1

            risk_factors = []
            risk_score = 0.0

            # 分析交易金额风险
            amount_risk = await self._analyze_amount_risk(transaction_data)
            risk_factors.append(amount_risk)
            risk_score += amount_risk["score"]

            # 分析用户行为风险
            if user_context:
                behavior_risk = await self._analyze_behavior_risk(transaction_data, user_context)
                risk_factors.append(behavior_risk)
                risk_score += behavior_risk["score"]

            # 分析时间模式风险
            timing_risk = await self._analyze_timing_risk(transaction_data)
            risk_factors.append(timing_risk)
            risk_score += timing_risk["score"]

            # 分析目标地址风险
            address_risk = await self._analyze_address_risk(transaction_data)
            risk_factors.append(address_risk)
            risk_score += address_risk["score"]

            # 标准化风险评分
            normalized_score = min(1.0, risk_score / len(risk_factors))

            # 确定风险等级
            risk_level = self._determine_risk_level(normalized_score)

            # 生成建议
            recommendations = self._generate_risk_recommendations(risk_factors, risk_level)

            assessment = {
                "assessment_id": f"risk_{len(self.risk_assessments) + 1}",
                "transaction_id": transaction_data.get("id", "unknown"),
                "risk_score": round(normalized_score, 3),
                "risk_level": risk_level,
                "risk_factors": risk_factors,
                "recommendations": recommendations,
                "should_block": normalized_score > self.risk_thresholds["suspicious_pattern_score"],
                "assessed_at": datetime.utcnow().isoformat()
            }

            self.risk_assessments.append(assessment)

            # 更新统计
            if risk_level == "high":
                self.risk_stats['high_risk_transactions'] += 1

            if assessment["should_block"]:
                self.risk_stats['blocked_transactions'] += 1

            self.logger.info(f"Transaction risk analysis completed: {risk_level} risk ({normalized_score:.3f})")
            return assessment

        except Exception as e:
            self.logger.error(f"Error analyzing transaction risk: {e}")
            return {"risk_score": 1.0, "risk_level": "high", "error": str(e)}

    async def _analyze_amount_risk(self, transaction_data: Dict) -> Dict[str, Any]:
        """分析交易金额风险"""
        try:
            amount = transaction_data.get("value_usd", 0.0)
            threshold = self.risk_thresholds["transaction_amount_usd"]

            if amount > threshold:
                score = min(1.0, amount / (threshold * 2))
                return {
                    "type": "amount_risk",
                    "score": score,
                    "description": f"交易金额 ${amount:,.2f} 超过阈值 ${threshold:,.2f}",
                    "severity": "high" if score > 0.7 else "medium"
                }
            else:
                return {
                    "type": "amount_risk",
                    "score": 0.0,
                    "description": "交易金额在正常范围内",
                    "severity": "low"
                }

        except Exception as e:
            self.logger.error(f"Error analyzing amount risk: {e}")
            return {"type": "amount_risk", "score": 0.5, "description": "金额分析失败"}

    async def _analyze_behavior_risk(self, transaction_data: Dict, user_context: Dict) -> Dict[str, Any]:
        """分析用户行为风险"""
        try:
            # 分析交易频率
            recent_transactions = user_context.get("recent_transactions", [])
            transaction_velocity = len(recent_transactions) / 60  # per minute

            velocity_score = min(1.0, transaction_velocity / self.risk_thresholds["velocity_threshold"])

            # 分析交易模式变化
            avg_amount = user_context.get("average_transaction_amount", 0)
            current_amount = transaction_data.get("value_usd", 0)

            if avg_amount > 0:
                amount_deviation = abs(current_amount - avg_amount) / avg_amount
                pattern_score = min(1.0, amount_deviation / 2)  # 2倍偏差为满分
            else:
                pattern_score = 0.0

            combined_score = (velocity_score + pattern_score) / 2

            return {
                "type": "behavior_risk",
                "score": combined_score,
                "description": f"交易频率: {transaction_velocity:.2f}/min, 金额偏差: {amount_deviation:.2f}" if avg_amount > 0 else f"交易频率: {transaction_velocity:.2f}/min",
                "severity": "high" if combined_score > 0.7 else "medium" if combined_score > 0.3 else "low",
                "velocity_score": velocity_score,
                "pattern_score": pattern_score
            }

        except Exception as e:
            self.logger.error(f"Error analyzing behavior risk: {e}")
            return {"type": "behavior_risk", "score": 0.0, "description": "行为分析失败"}

    async def _analyze_timing_risk(self, transaction_data: Dict) -> Dict[str, Any]:
        """分析时间模式风险"""
        try:
            current_time = datetime.utcnow()
            hour = current_time.hour

            # 深夜交易风险较高
            if 2 <= hour <= 5:
                score = 0.6
                description = "深夜时段交易"
                severity = "medium"
            elif 22 <= hour or hour <= 1:
                score = 0.3
                description = "夜间时段交易"
                severity = "low"
            else:
                score = 0.0
                description = "正常时段交易"
                severity = "low"

            return {
                "type": "timing_risk",
                "score": score,
                "description": description,
                "severity": severity,
                "transaction_hour": hour
            }

        except Exception as e:
            self.logger.error(f"Error analyzing timing risk: {e}")
            return {"type": "timing_risk", "score": 0.0, "description": "时间分析失败"}

    async def _analyze_address_risk(self, transaction_data: Dict) -> Dict[str, Any]:
        """分析目标地址风险"""
        try:
            to_address = transaction_data.get("to", "")

            # 简化的地址风险分析
            # 实际实现应该检查黑名单、已知恶意地址等

            # 检查是否是新地址（简化检查）
            if len(to_address) == 42 and to_address.startswith("0x"):
                # 模拟地址风险检查
                import random
                risk_score = random.uniform(0.0, 0.3)  # 大多数地址风险较低

                if risk_score > 0.2:
                    return {
                        "type": "address_risk",
                        "score": risk_score,
                        "description": "目标地址存在潜在风险",
                        "severity": "medium",
                        "address": to_address
                    }
                else:
                    return {
                        "type": "address_risk",
                        "score": 0.0,
                        "description": "目标地址风险较低",
                        "severity": "low",
                        "address": to_address
                    }
            else:
                return {
                    "type": "address_risk",
                    "score": 0.8,
                    "description": "无效的目标地址格式",
                    "severity": "high",
                    "address": to_address
                }

        except Exception as e:
            self.logger.error(f"Error analyzing address risk: {e}")
            return {"type": "address_risk", "score": 0.5, "description": "地址分析失败"}

    def _determine_risk_level(self, risk_score: float) -> str:
        """确定风险等级"""
        if risk_score >= 0.8:
            return "critical"
        elif risk_score >= 0.6:
            return "high"
        elif risk_score >= 0.3:
            return "medium"
        else:
            return "low"

    def _generate_risk_recommendations(self, risk_factors: List[Dict], risk_level: str) -> List[str]:
        """生成风险建议"""
        recommendations = []

        if risk_level in ["high", "critical"]:
            recommendations.append("建议人工审核此交易")
            recommendations.append("考虑要求额外的身份验证")

        for factor in risk_factors:
            if factor["severity"] == "high":
                if factor["type"] == "amount_risk":
                    recommendations.append("大额交易建议分批执行")
                elif factor["type"] == "behavior_risk":
                    recommendations.append("异常行为模式，建议暂停交易并联系用户")
                elif factor["type"] == "address_risk":
                    recommendations.append("目标地址风险较高，建议验证地址有效性")

        return list(set(recommendations))  # 去重
