"""
Encryption Service

Provides encryption and decryption services for sensitive data
including wallet private keys, API keys, and configuration data.
"""

import os
import base64
import hashlib
import logging
from typing import Dict, Any, Optional, Union
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class EncryptionService:
    """
    Encryption service for protecting sensitive data.
    
    Provides symmetric encryption using Fernet (AES 128) with
    password-based key derivation for secure data protection.
    """
    
    def __init__(self, master_password: str = None):
        """
        Initialize encryption service.
        
        Args:
            master_password: Master password for key derivation
        """
        self.logger = logging.getLogger(__name__)
        self._master_password = master_password
        self._fernet = None
        
        if master_password:
            self._initialize_encryption(master_password)
    
    def _initialize_encryption(self, password: str, salt: bytes = None) -> bool:
        """
        Initialize encryption with password.
        
        Args:
            password: Master password
            salt: Optional salt (generated if not provided)
            
        Returns:
            bool: True if initialization successful
        """
        try:
            if salt is None:
                salt = os.urandom(16)
            
            # Derive key from password
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            self._fernet = Fernet(key)
            self._salt = salt
            
            self.logger.info("Encryption service initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize encryption: {e}")
            return False
    
    def set_master_password(self, password: str) -> bool:
        """
        Set master password for encryption.
        
        Args:
            password: Master password
            
        Returns:
            bool: True if password set successfully
        """
        self._master_password = password
        return self._initialize_encryption(password)
    
    def encrypt(self, data: Union[str, bytes]) -> Optional[str]:
        """
        Encrypt data.
        
        Args:
            data: Data to encrypt (string or bytes)
            
        Returns:
            Optional[str]: Base64 encoded encrypted data or None if failed
        """
        try:
            if self._fernet is None:
                self.logger.error("Encryption not initialized")
                return None
            
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted_data = self._fernet.encrypt(data)
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Encryption failed: {e}")
            return None
    
    def decrypt(self, encrypted_data: str) -> Optional[str]:
        """
        Decrypt data.
        
        Args:
            encrypted_data: Base64 encoded encrypted data
            
        Returns:
            Optional[str]: Decrypted data or None if failed
        """
        try:
            if self._fernet is None:
                self.logger.error("Encryption not initialized")
                return None
            
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self._fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Decryption failed: {e}")
            return None
    
    def encrypt_dict(self, data: Dict[str, Any]) -> Optional[str]:
        """
        Encrypt dictionary data.
        
        Args:
            data: Dictionary to encrypt
            
        Returns:
            Optional[str]: Encrypted JSON string or None if failed
        """
        try:
            import json
            json_data = json.dumps(data)
            return self.encrypt(json_data)
            
        except Exception as e:
            self.logger.error(f"Dictionary encryption failed: {e}")
            return None
    
    def decrypt_dict(self, encrypted_data: str) -> Optional[Dict[str, Any]]:
        """
        Decrypt dictionary data.
        
        Args:
            encrypted_data: Encrypted JSON string
            
        Returns:
            Optional[Dict[str, Any]]: Decrypted dictionary or None if failed
        """
        try:
            import json
            json_data = self.decrypt(encrypted_data)
            if json_data:
                return json.loads(json_data)
            return None
            
        except Exception as e:
            self.logger.error(f"Dictionary decryption failed: {e}")
            return None
    
    def hash_password(self, password: str, salt: bytes = None) -> tuple[str, str]:
        """
        Hash password with salt.
        
        Args:
            password: Password to hash
            salt: Optional salt (generated if not provided)
            
        Returns:
            tuple[str, str]: (hashed_password, salt) both base64 encoded
        """
        try:
            if salt is None:
                salt = os.urandom(32)
            
            # Hash password with salt
            pwdhash = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt,
                100000
            )
            
            hashed_password = base64.urlsafe_b64encode(pwdhash).decode('utf-8')
            salt_b64 = base64.urlsafe_b64encode(salt).decode('utf-8')
            
            return hashed_password, salt_b64
            
        except Exception as e:
            self.logger.error(f"Password hashing failed: {e}")
            return "", ""
    
    def verify_password(self, password: str, hashed_password: str, salt: str) -> bool:
        """
        Verify password against hash.
        
        Args:
            password: Password to verify
            hashed_password: Base64 encoded hashed password
            salt: Base64 encoded salt
            
        Returns:
            bool: True if password matches
        """
        try:
            salt_bytes = base64.urlsafe_b64decode(salt.encode('utf-8'))
            
            # Hash the provided password with the same salt
            pwdhash = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt_bytes,
                100000
            )
            
            new_hash = base64.urlsafe_b64encode(pwdhash).decode('utf-8')
            return new_hash == hashed_password
            
        except Exception as e:
            self.logger.error(f"Password verification failed: {e}")
            return False
    
    def generate_key(self) -> str:
        """
        Generate a new encryption key.
        
        Returns:
            str: Base64 encoded encryption key
        """
        try:
            key = Fernet.generate_key()
            return base64.urlsafe_b64encode(key).decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Key generation failed: {e}")
            return ""
    
    def encrypt_file(self, file_path: str, output_path: str = None) -> bool:
        """
        Encrypt a file.
        
        Args:
            file_path: Path to file to encrypt
            output_path: Output path (defaults to file_path + '.enc')
            
        Returns:
            bool: True if encryption successful
        """
        try:
            if self._fernet is None:
                self.logger.error("Encryption not initialized")
                return False
            
            if output_path is None:
                output_path = file_path + '.enc'
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            encrypted_data = self._fernet.encrypt(file_data)
            
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)
            
            self.logger.info(f"File encrypted: {file_path} -> {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"File encryption failed: {e}")
            return False
    
    def decrypt_file(self, encrypted_file_path: str, output_path: str = None) -> bool:
        """
        Decrypt a file.
        
        Args:
            encrypted_file_path: Path to encrypted file
            output_path: Output path (defaults to removing '.enc' extension)
            
        Returns:
            bool: True if decryption successful
        """
        try:
            if self._fernet is None:
                self.logger.error("Encryption not initialized")
                return False
            
            if output_path is None:
                if encrypted_file_path.endswith('.enc'):
                    output_path = encrypted_file_path[:-4]
                else:
                    output_path = encrypted_file_path + '.dec'
            
            with open(encrypted_file_path, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self._fernet.decrypt(encrypted_data)
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            self.logger.info(f"File decrypted: {encrypted_file_path} -> {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"File decryption failed: {e}")
            return False
    
    def is_initialized(self) -> bool:
        """
        Check if encryption is initialized.
        
        Returns:
            bool: True if encryption is ready
        """
        return self._fernet is not None
    
    def get_salt(self) -> Optional[str]:
        """
        Get the current salt.
        
        Returns:
            Optional[str]: Base64 encoded salt or None if not initialized
        """
        if hasattr(self, '_salt'):
            return base64.urlsafe_b64encode(self._salt).decode('utf-8')
        return None
