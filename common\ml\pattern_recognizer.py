"""
Pattern Recognizer

Machine learning component for recognizing patterns in airdrop projects,
user behaviors, and system operations to improve decision making.
"""

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    # Fallback implementations
    class np:
        @staticmethod
        def mean(values):
            return sum(values) / len(values) if values else 0

        @staticmethod
        def std(values):
            if not values:
                return 0
            mean_val = sum(values) / len(values)
            variance = sum((x - mean_val) ** 2 for x in values) / len(values)
            return variance ** 0.5

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json
import pickle


@dataclass
class Pattern:
    """Represents a recognized pattern."""
    id: str
    name: str
    pattern_type: str
    features: Dict[str, Any]
    confidence: float
    frequency: int
    last_seen: datetime
    created_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert pattern to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'pattern_type': self.pattern_type,
            'features': self.features,
            'confidence': self.confidence,
            'frequency': self.frequency,
            'last_seen': self.last_seen.isoformat(),
            'created_at': self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Pattern':
        """Create pattern from dictionary."""
        return cls(
            id=data['id'],
            name=data['name'],
            pattern_type=data['pattern_type'],
            features=data['features'],
            confidence=data['confidence'],
            frequency=data['frequency'],
            last_seen=datetime.fromisoformat(data['last_seen']),
            created_at=datetime.fromisoformat(data['created_at'])
        )


class PatternRecognizer:
    """
    Pattern recognition system for AirHunter.
    
    Analyzes data streams to identify recurring patterns in:
    - Project characteristics and success rates
    - User behavior and interaction patterns
    - System performance and resource usage
    - Market trends and timing patterns
    """
    
    def __init__(self, min_confidence: float = 0.7, max_patterns: int = 1000):
        """
        Initialize pattern recognizer.
        
        Args:
            min_confidence: Minimum confidence threshold for pattern recognition
            max_patterns: Maximum number of patterns to store
        """
        self.min_confidence = min_confidence
        self.max_patterns = max_patterns
        self.logger = logging.getLogger(__name__)
        
        # Pattern storage
        self.patterns: Dict[str, Pattern] = {}
        self.pattern_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Feature extractors
        self.feature_extractors = {
            'project': self._extract_project_features,
            'behavior': self._extract_behavior_features,
            'performance': self._extract_performance_features,
            'market': self._extract_market_features
        }
        
        # Pattern templates
        self.pattern_templates = {
            'successful_project': {
                'features': ['team_size', 'social_followers', 'github_activity', 'whitepaper_quality'],
                'weights': [0.2, 0.3, 0.3, 0.2]
            },
            'optimal_timing': {
                'features': ['hour_of_day', 'day_of_week', 'market_volatility', 'gas_price'],
                'weights': [0.3, 0.2, 0.3, 0.2]
            },
            'user_engagement': {
                'features': ['session_duration', 'click_rate', 'scroll_depth', 'return_frequency'],
                'weights': [0.25, 0.25, 0.25, 0.25]
            }
        }
        
        # Statistics
        self.stats = {
            'patterns_recognized': 0,
            'patterns_applied': 0,
            'accuracy_rate': 0.0,
            'last_analysis': None
        }
    
    def analyze_data(self, data: Dict[str, Any], data_type: str) -> List[Pattern]:
        """
        Analyze data to recognize patterns.
        
        Args:
            data: Data to analyze
            data_type: Type of data (project, behavior, performance, market)
            
        Returns:
            List[Pattern]: Recognized patterns
        """
        try:
            # Extract features
            features = self._extract_features(data, data_type)
            if not features:
                return []
            
            # Find matching patterns
            recognized_patterns = []
            
            for template_name, template in self.pattern_templates.items():
                if self._matches_template_type(data_type, template_name):
                    pattern = self._match_pattern(features, template_name, template)
                    if pattern and pattern.confidence >= self.min_confidence:
                        recognized_patterns.append(pattern)
            
            # Update pattern history
            self._update_pattern_history(recognized_patterns, data_type)
            
            # Update statistics
            self.stats['patterns_recognized'] += len(recognized_patterns)
            self.stats['last_analysis'] = datetime.utcnow()
            
            return recognized_patterns
            
        except Exception as e:
            self.logger.error(f"Pattern analysis error: {e}")
            return []
    
    def learn_pattern(self, data: Dict[str, Any], outcome: bool, pattern_type: str) -> bool:
        """
        Learn from data and outcome to improve pattern recognition.
        
        Args:
            data: Input data
            outcome: Whether the outcome was successful
            pattern_type: Type of pattern to learn
            
        Returns:
            bool: True if learning was successful
        """
        try:
            features = self._extract_features(data, pattern_type)
            if not features:
                return False
            
            # Create or update pattern
            pattern_id = self._generate_pattern_id(features, pattern_type)
            
            if pattern_id in self.patterns:
                # Update existing pattern
                pattern = self.patterns[pattern_id]
                pattern.frequency += 1
                pattern.last_seen = datetime.utcnow()
                
                # Update confidence based on outcome
                if outcome:
                    pattern.confidence = min(1.0, pattern.confidence + 0.1)
                else:
                    pattern.confidence = max(0.0, pattern.confidence - 0.05)
            else:
                # Create new pattern
                pattern = Pattern(
                    id=pattern_id,
                    name=f"{pattern_type}_pattern_{len(self.patterns)}",
                    pattern_type=pattern_type,
                    features=features,
                    confidence=0.8 if outcome else 0.3,
                    frequency=1,
                    last_seen=datetime.utcnow(),
                    created_at=datetime.utcnow()
                )
                
                self.patterns[pattern_id] = pattern
            
            # Cleanup old patterns if needed
            self._cleanup_patterns()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Pattern learning error: {e}")
            return False
    
    def predict_outcome(self, data: Dict[str, Any], data_type: str) -> Tuple[float, List[str]]:
        """
        Predict outcome based on recognized patterns.
        
        Args:
            data: Data to analyze
            data_type: Type of data
            
        Returns:
            Tuple[float, List[str]]: (confidence, reasons)
        """
        try:
            patterns = self.analyze_data(data, data_type)
            if not patterns:
                return 0.5, ["No patterns recognized"]
            
            # Calculate weighted confidence
            total_weight = 0
            weighted_confidence = 0
            reasons = []
            
            for pattern in patterns:
                weight = pattern.frequency * pattern.confidence
                weighted_confidence += weight * pattern.confidence
                total_weight += weight
                
                reasons.append(f"Pattern '{pattern.name}' (confidence: {pattern.confidence:.2f})")
            
            if total_weight > 0:
                final_confidence = weighted_confidence / total_weight
            else:
                final_confidence = 0.5
            
            return final_confidence, reasons
            
        except Exception as e:
            self.logger.error(f"Prediction error: {e}")
            return 0.5, ["Prediction error"]
    
    def get_pattern_insights(self, pattern_type: str = None) -> Dict[str, Any]:
        """
        Get insights about recognized patterns.
        
        Args:
            pattern_type: Filter by pattern type
            
        Returns:
            Dict[str, Any]: Pattern insights
        """
        patterns = self.patterns.values()
        if pattern_type:
            patterns = [p for p in patterns if p.pattern_type == pattern_type]
        
        if not patterns:
            return {"message": "No patterns found"}
        
        # Calculate statistics
        total_patterns = len(patterns)
        avg_confidence = sum(p.confidence for p in patterns) / total_patterns
        most_frequent = max(patterns, key=lambda p: p.frequency)
        highest_confidence = max(patterns, key=lambda p: p.confidence)
        
        # Pattern distribution by type
        type_distribution = defaultdict(int)
        for pattern in patterns:
            type_distribution[pattern.pattern_type] += 1
        
        return {
            "total_patterns": total_patterns,
            "average_confidence": avg_confidence,
            "most_frequent_pattern": {
                "name": most_frequent.name,
                "frequency": most_frequent.frequency,
                "confidence": most_frequent.confidence
            },
            "highest_confidence_pattern": {
                "name": highest_confidence.name,
                "confidence": highest_confidence.confidence,
                "frequency": highest_confidence.frequency
            },
            "pattern_distribution": dict(type_distribution),
            "statistics": self.stats
        }
    
    def save_patterns(self, filepath: str) -> bool:
        """
        Save patterns to file.
        
        Args:
            filepath: Path to save file
            
        Returns:
            bool: True if successful
        """
        try:
            data = {
                'patterns': {pid: pattern.to_dict() for pid, pattern in self.patterns.items()},
                'stats': self.stats,
                'saved_at': datetime.utcnow().isoformat()
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.logger.info(f"Patterns saved to {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save patterns: {e}")
            return False
    
    def load_patterns(self, filepath: str) -> bool:
        """
        Load patterns from file.
        
        Args:
            filepath: Path to load file
            
        Returns:
            bool: True if successful
        """
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            # Load patterns
            self.patterns = {}
            for pid, pattern_data in data.get('patterns', {}).items():
                self.patterns[pid] = Pattern.from_dict(pattern_data)
            
            # Load statistics
            self.stats.update(data.get('stats', {}))
            
            self.logger.info(f"Patterns loaded from {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load patterns: {e}")
            return False
    
    def _extract_features(self, data: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """Extract features from data based on type."""
        extractor = self.feature_extractors.get(data_type)
        if extractor:
            return extractor(data)
        return {}
    
    def _extract_project_features(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features from project data."""
        features = {}
        
        # Basic project metrics
        features['team_size'] = data.get('team_size', 0)
        features['social_followers'] = data.get('social_followers', 0)
        features['github_activity'] = data.get('github_commits', 0)
        features['whitepaper_quality'] = data.get('whitepaper_score', 0)
        
        # Derived features
        features['social_engagement'] = data.get('social_followers', 0) * data.get('engagement_rate', 0)
        features['development_activity'] = data.get('github_commits', 0) / max(1, data.get('project_age_days', 1))
        
        return features
    
    def _extract_behavior_features(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features from behavior data."""
        features = {}
        
        features['session_duration'] = data.get('session_duration', 0)
        features['click_rate'] = data.get('clicks', 0) / max(1, data.get('page_views', 1))
        features['scroll_depth'] = data.get('max_scroll_depth', 0)
        features['return_frequency'] = data.get('return_visits', 0)
        
        return features
    
    def _extract_performance_features(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features from performance data."""
        features = {}
        
        features['cpu_usage'] = data.get('cpu_usage', 0)
        features['memory_usage'] = data.get('memory_usage', 0)
        features['response_time'] = data.get('response_time', 0)
        features['error_rate'] = data.get('errors', 0) / max(1, data.get('total_requests', 1))
        
        return features
    
    def _extract_market_features(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features from market data."""
        features = {}
        
        features['price_volatility'] = data.get('price_volatility', 0)
        features['trading_volume'] = data.get('trading_volume', 0)
        features['market_cap'] = data.get('market_cap', 0)
        features['sentiment_score'] = data.get('sentiment_score', 0)
        
        return features
    
    def _matches_template_type(self, data_type: str, template_name: str) -> bool:
        """Check if data type matches template."""
        type_mappings = {
            'project': ['successful_project'],
            'behavior': ['user_engagement'],
            'performance': ['optimal_timing'],
            'market': ['optimal_timing']
        }
        
        return template_name in type_mappings.get(data_type, [])
    
    def _match_pattern(self, features: Dict[str, Any], template_name: str, 
                      template: Dict[str, Any]) -> Optional[Pattern]:
        """Match features against pattern template."""
        try:
            # Calculate similarity score
            template_features = template['features']
            weights = template['weights']
            
            score = 0.0
            total_weight = 0.0
            
            for i, feature_name in enumerate(template_features):
                if feature_name in features:
                    # Normalize feature value (simple min-max normalization)
                    feature_value = self._normalize_feature(features[feature_name], feature_name)
                    weight = weights[i] if i < len(weights) else 1.0
                    
                    score += feature_value * weight
                    total_weight += weight
            
            if total_weight > 0:
                confidence = score / total_weight
                
                if confidence >= self.min_confidence:
                    pattern_id = self._generate_pattern_id(features, template_name)
                    
                    return Pattern(
                        id=pattern_id,
                        name=template_name,
                        pattern_type=template_name,
                        features=features,
                        confidence=confidence,
                        frequency=1,
                        last_seen=datetime.utcnow(),
                        created_at=datetime.utcnow()
                    )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Pattern matching error: {e}")
            return None
    
    def _normalize_feature(self, value: float, feature_name: str) -> float:
        """Normalize feature value to 0-1 range."""
        # Simple normalization - in practice, this would use learned min/max values
        normalization_ranges = {
            'team_size': (1, 50),
            'social_followers': (100, 1000000),
            'github_activity': (1, 1000),
            'whitepaper_quality': (0, 100),
            'session_duration': (10, 3600),
            'click_rate': (0, 1),
            'scroll_depth': (0, 100),
            'cpu_usage': (0, 100),
            'memory_usage': (0, 100),
            'response_time': (0, 5000)
        }
        
        min_val, max_val = normalization_ranges.get(feature_name, (0, 100))
        return max(0, min(1, (value - min_val) / (max_val - min_val)))
    
    def _generate_pattern_id(self, features: Dict[str, Any], pattern_type: str) -> str:
        """Generate unique pattern ID."""
        feature_hash = hash(str(sorted(features.items())))
        return f"{pattern_type}_{abs(feature_hash)}"
    
    def _update_pattern_history(self, patterns: List[Pattern], data_type: str):
        """Update pattern recognition history."""
        for pattern in patterns:
            self.pattern_history[data_type].append({
                'pattern_id': pattern.id,
                'confidence': pattern.confidence,
                'timestamp': datetime.utcnow()
            })
    
    def _cleanup_patterns(self):
        """Remove old or low-confidence patterns."""
        if len(self.patterns) <= self.max_patterns:
            return
        
        # Sort patterns by score (frequency * confidence)
        sorted_patterns = sorted(
            self.patterns.items(),
            key=lambda x: x[1].frequency * x[1].confidence,
            reverse=True
        )
        
        # Keep only top patterns
        self.patterns = dict(sorted_patterns[:self.max_patterns])
        
        self.logger.info(f"Cleaned up patterns, kept {len(self.patterns)} patterns")
