"""
Response parser for API responses

This module provides utilities for parsing and validating API responses.
"""

import json
from typing import Any, Dict, List, Optional, Union, Callable
from xml.etree import ElementTree
from ..utils.error_handlers import error_handler

class ResponseParser:
    """Parser for API responses"""
    
    def __init__(self):
        """Initialize response parser"""
        self._custom_parsers = {}
        self._validators = {}
    
    def register_parser(
        self,
        content_type: str,
        parser: Callable[[str], Any]
    ) -> None:
        """
        Register custom parser for content type
        
        Args:
            content_type: Content type to handle
            parser: Parser function
        """
        self._custom_parsers[content_type] = parser
    
    def register_validator(
        self,
        content_type: str,
        validator: Callable[[Any], bool]
    ) -> None:
        """
        Register validator for content type
        
        Args:
            content_type: Content type to validate
            validator: Validator function
        """
        self._validators[content_type] = validator
    
    def parse_json(self, content: str) -> Any:
        """
        Parse JSON content
        
        Args:
            content: JSON string
            
        Returns:
            Any: Parsed JSON data
            
        Raises:
            ValueError: If content is invalid JSON
        """
        try:
            return json.loads(content)
        except json.JSONDecodeError as e:
            error_handler.handle_error(e, {'content': content[:100]})
            raise ValueError(f"Invalid JSON content: {str(e)}")
    
    def parse_xml(self, content: str) -> Dict:
        """
        Parse XML content
        
        Args:
            content: XML string
            
        Returns:
            Dict: Parsed XML data
            
        Raises:
            ValueError: If content is invalid XML
        """
        try:
            root = ElementTree.fromstring(content)
            return self._xml_to_dict(root)
        except ElementTree.ParseError as e:
            error_handler.handle_error(e, {'content': content[:100]})
            raise ValueError(f"Invalid XML content: {str(e)}")
    
    def _xml_to_dict(self, element: ElementTree.Element) -> Union[Dict, str]:
        """
        Convert XML element to dictionary
        
        Args:
            element: XML element
            
        Returns:
            Union[Dict, str]: Converted data
        """
        result = {}
        
        # Handle attributes
        for key, value in element.attrib.items():
            result[f"@{key}"] = value
        
        # Handle child elements
        for child in element:
            child_data = self._xml_to_dict(child)
            if child.tag in result:
                if isinstance(result[child.tag], list):
                    result[child.tag].append(child_data)
                else:
                    result[child.tag] = [result[child.tag], child_data]
            else:
                result[child.tag] = child_data
        
        # Handle text content
        text = element.text.strip() if element.text else ""
        if text:
            if result:
                result["#text"] = text
            else:
                return text
        
        return result
    
    def parse_form_data(self, content: str) -> Dict[str, str]:
        """
        Parse form data content
        
        Args:
            content: Form data string
            
        Returns:
            Dict[str, str]: Parsed form data
        """
        result = {}
        pairs = content.split('&')
        for pair in pairs:
            if '=' in pair:
                key, value = pair.split('=', 1)
                result[key] = value
        return result
    
    def extract_field(
        self,
        data: Any,
        field_path: str,
        default: Any = None
    ) -> Any:
        """
        Extract field from parsed data using dot notation
        
        Args:
            data: Parsed data
            field_path: Path to field using dot notation
            default: Default value if field not found
            
        Returns:
            Any: Field value or default
        """
        try:
            current = data
            for key in field_path.split('.'):
                if isinstance(current, dict):
                    current = current.get(key, default)
                elif isinstance(current, list) and key.isdigit():
                    current = current[int(key)]
                else:
                    return default
            return current
        except (KeyError, IndexError, TypeError):
            return default
    
    def validate_response(
        self,
        content: Any,
        content_type: str,
        schema: Optional[Dict] = None
    ) -> bool:
        """
        Validate response content
        
        Args:
            content: Response content
            content_type: Content type
            schema: Optional validation schema
            
        Returns:
            bool: True if valid, False otherwise
        """
        # Use registered validator if available
        if content_type in self._validators:
            return self._validators[content_type](content)
        
        # Basic schema validation
        if schema:
            return self._validate_schema(content, schema)
        
        return True
    
    def _validate_schema(self, data: Any, schema: Dict) -> bool:
        """
        Validate data against schema
        
        Args:
            data: Data to validate
            schema: Validation schema
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(data, dict):
            return False
            
        for key, value_type in schema.items():
            if key not in data:
                return False
            
            if isinstance(value_type, dict):
                if not self._validate_schema(data[key], value_type):
                    return False
            elif not isinstance(data[key], value_type):
                return False
        
        return True
    
    def parse(
        self,
        content: str,
        content_type: str,
        schema: Optional[Dict] = None
    ) -> Any:
        """
        Parse response content based on content type
        
        Args:
            content: Response content
            content_type: Content type
            schema: Optional validation schema
            
        Returns:
            Any: Parsed content
            
        Raises:
            ValueError: If content is invalid or parsing fails
        """
        # Use custom parser if registered
        if content_type in self._custom_parsers:
            parsed = self._custom_parsers[content_type](content)
        else:
            # Use built-in parsers
            if 'json' in content_type:
                parsed = self.parse_json(content)
            elif 'xml' in content_type:
                parsed = self.parse_xml(content)
            elif 'x-www-form-urlencoded' in content_type:
                parsed = self.parse_form_data(content)
            else:
                parsed = content
        
        # Validate if schema provided
        if schema and not self.validate_response(parsed, content_type, schema):
            raise ValueError(f"Response validation failed for {content_type}")
        
        return parsed

# Create global response parser instance
response_parser = ResponseParser()
