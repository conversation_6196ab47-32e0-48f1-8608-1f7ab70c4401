# AirHunter - 代理智能体

代理智能体是 AirHunter 系统的核心组件之一，负责获取、验证、管理和分发代理，确保系统有足够的高质量代理资源。

## 功能特点

- **多源获取** - 从多个来源获取代理
- **自动验证** - 验证代理的可用性和性能
- **智能管理** - 管理代理池和代理的生命周期
- **高效分发** - 根据需求分发代理
- **状态报告** - 报告代理的使用状态
- **API 接口** - 提供 API 接口与其他组件交互

## 组件结构

```
proxy/
├── __init__.py                # 包初始化
├── proxy_agent.py             # 代理智能体主类
├── main.py                    # 主程序入口
├── models/                    # 数据模型
│   ├── __init__.py
│   └── proxy.py               # 代理模型
├── acquisition/               # 代理获取
│   ├── __init__.py
│   ├── proxy_source_manager.py # 代理源管理器
│   └── sources/               # 代理源
│       ├── __init__.py
│       ├── base_source.py     # 基础代理源
│       ├── free_proxy_list.py # Free Proxy List 源
│       ├── proxy_scrape.py    # ProxyScrape 源
│       ├── geonode.py         # Geonode 源
│       └── proxy_nova.py      # ProxyNova 源
├── verification/              # 代理验证
│   ├── __init__.py
│   └── proxy_verifier.py      # 代理验证器
├── management/                # 代理管理
│   ├── __init__.py
│   └── proxy_pool.py          # 代理池
├── distribution/              # 代理分发
│   ├── __init__.py
│   └── proxy_distributor.py   # 代理分发器
├── api/                       # API 接口
│   ├── __init__.py
│   └── proxy_api.py           # 代理 API
├── config/                    # 配置
│   ├── __init__.py
│   └── default_config.py      # 默认配置
└── utils/                     # 工具
    ├── __init__.py
    └── proxy_utils.py         # 代理工具函数
```

## 使用方法

### 启动代理智能体

```bash
python -m proxy.main --config config.json --log-level INFO
```

### 配置文件示例

```json
{
  "sources": {
    "enabled_sources": ["free_proxy_list", "proxy_scrape", "geonode"],
    "free_proxy_list": {
      "weight": 1.0
    },
    "proxy_scrape": {
      "weight": 1.0
    },
    "geonode": {
      "weight": 1.0
    }
  },
  "verification": {
    "timeout": 10,
    "max_workers": 10
  },
  "pool": {
    "max_size": 500,
    "max_age": 3600
  },
  "distribution": {
    "rotation_interval": 600,
    "max_uses": 50
  },
  "acquisition_interval": 3600,
  "verification_interval": 300,
  "cleanup_interval": 1800,
  "min_pool_size": 20,
  "max_pool_size": 200
}
```

### API 使用示例

```python
import requests
import json

# 获取代理
response = requests.post('http://localhost:8000/api/proxy', json={
    'action': 'get_proxy',
    'protocol': 'http',
    'country': 'US',
    'anonymity': 'elite'
})

proxy = response.json()['proxy']
print(f"获取到代理: {proxy['host']}:{proxy['port']}")

# 报告代理状态
requests.post('http://localhost:8000/api/proxy', json={
    'action': 'report_proxy_status',
    'proxy': proxy,
    'success': True,
    'response_time': 0.5
})
```

## 依赖项

- Python 3.8+
- requests
- beautifulsoup4
- psutil

## 开发指南

### 添加新的代理源

1. 在 `proxy/acquisition/sources/` 目录下创建新的源文件
2. 继承 `BaseProxySource` 类并实现 `get_proxies` 方法
3. 在 `proxy_source_manager.py` 中注册新的源

### 自定义验证逻辑

修改 `proxy/verification/proxy_verifier.py` 文件中的验证逻辑。