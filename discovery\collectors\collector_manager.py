"""
项目收集器管理器

该模块负责管理多个项目收集器，并收集项目详细信息。
"""

import logging
import threading
from typing import Dict, List, Any, Optional

from discovery.collectors.base_collector import BaseCollector
from discovery.collectors.web_collector import WebCollector
from discovery.collectors.social_collector import SocialCollector
from discovery.collectors.blockchain_collector import BlockchainCollector


class CollectorManager:
    """项目收集器管理器，负责管理多个项目收集器，并收集项目详细信息"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目收集器管理器
        
        Args:
            config: 配置字典，包含项目收集器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        
        # 初始化收集器
        self.web_collector = WebCollector(config.get('web_collector', {}))
        self.social_collector = SocialCollector(config.get('social_collector', {}))
        self.blockchain_collector = BlockchainCollector(config.get('blockchain_collector', {}))
        
        # 收集器启用状态
        self.web_collector_enabled = config.get('web_collector_enabled', True)
        self.social_collector_enabled = config.get('social_collector_enabled', True)
        self.blockchain_collector_enabled = config.get('blockchain_collector_enabled', True)
    
    def collect_project_info(self, project: Dict[str, Any]) -> Dict[str, Any]:
        """
        收集项目详细信息
        
        Args:
            project: 项目字典
            
        Returns:
            包含详细信息的项目字典
        """
        with self._lock:
            # 创建项目副本
            project_info = project.copy()
            
            try:
                # 收集网站信息
                if self.web_collector_enabled:
                    web_info = self.web_collector.collect(project_info)
                    if web_info:
                        project_info.update(web_info)
                
                # 收集社交媒体信息
                if self.social_collector_enabled:
                    social_info = self.social_collector.collect(project_info)
                    if social_info:
                        project_info.update(social_info)
                
                # 收集区块链信息
                if self.blockchain_collector_enabled:
                    blockchain_info = self.blockchain_collector.collect(project_info)
                    if blockchain_info:
                        project_info.update(blockchain_info)
                
                return project_info
            
            except Exception as e:
                self.logger.error(f"收集项目信息时出错: {str(e)}")
                return project_info
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            config: 新的配置字典
        """
        with self._lock:
            self.config = config
            
            # 更新收集器配置
            self.web_collector.update_config(config.get('web_collector', {}))
            self.social_collector.update_config(config.get('social_collector', {}))
            self.blockchain_collector.update_config(config.get('blockchain_collector', {}))
            
            # 更新收集器启用状态
            self.web_collector_enabled = config.get('web_collector_enabled', True)
            self.social_collector_enabled = config.get('social_collector_enabled', True)
            self.blockchain_collector_enabled = config.get('blockchain_collector_enabled', True)
            
            self.logger.info("已更新收集器配置")