"""
代理源管理器

该模块负责管理多个代理源，并从中获取代理。
"""

import logging
import threading
import time
import random
from typing import Dict, List, Any, Optional, Set, Tuple

from proxy.models.proxy import Proxy
from proxy.acquisition.sources.base_source import BaseProxySource
from proxy.acquisition.sources.free_proxy_list import FreeProxyListSource
from proxy.acquisition.sources.proxy_scrape import ProxyScrapeSource
from proxy.acquisition.sources.geonode import GeonodeSource
from proxy.acquisition.sources.proxy_nova import ProxyNovaSource


class ProxySourceManager:
    """代理源管理器，负责管理多个代理源，并从中获取代理"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化代理源管理器
        
        Args:
            config: 配置字典，包含代理源的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        self._sources: Dict[str, BaseProxySource] = {}
        self._source_stats: Dict[str, Dict[str, Any]] = {}
        
        # 初始化代理源
        self._init_sources()
    
    def _init_sources(self) -> None:
        """初始化代理源"""
        # 获取启用的源
        enabled_sources = self.config.get('enabled_sources', [])
        
        # 如果没有指定，默认启用所有源
        if not enabled_sources:
            enabled_sources = ['free_proxy_list', 'proxy_scrape', 'geonode', 'proxy_nova']
        
        # 初始化源
        for source_name in enabled_sources:
            source_config = self.config.get(source_name, {})
            
            if source_name == 'free_proxy_list':
                self._sources[source_name] = FreeProxyListSource(source_config)
            elif source_name == 'proxy_scrape':
                self._sources[source_name] = ProxyScrapeSource(source_config)
            elif source_name == 'geonode':
                self._sources[source_name] = GeonodeSource(source_config)
            elif source_name == 'proxy_nova':
                self._sources[source_name] = ProxyNovaSource(source_config)
            else:
                self.logger.warning(f"未知的代理源: {source_name}")
                continue
            
            # 初始化源统计信息
            self._source_stats[source_name] = {
                'last_fetch_time': 0,
                'last_fetch_count': 0,
                'total_fetch_count': 0,
                'success_count': 0,
                'fail_count': 0
            }
            
            self.logger.info(f"已初始化代理源: {source_name}")
    
    def get_proxies(self, count: int) -> List[Proxy]:
        """
        获取代理
        
        Args:
            count: 要获取的代理数量
            
        Returns:
            代理列表
        """
        with self._lock:
            if not self._sources:
                self.logger.warning("没有可用的代理源")
                return []
            
            # 计算每个源需要获取的代理数量
            source_counts = self._calculate_source_counts(count)
            
            # 从每个源获取代理
            all_proxies = []
            for source_name, source_count in source_counts.items():
                if source_count <= 0:
                    continue
                
                source = self._sources.get(source_name)
                if not source:
                    continue
                
                try:
                    self.logger.info(f"从源 '{source_name}' 获取 {source_count} 个代理")
                    
                    # 记录开始时间
                    start_time = time.time()
                    
                    # 获取代理
                    proxies = source.get_proxies(source_count)
                    
                    # 更新统计信息
                    self._source_stats[source_name]['last_fetch_time'] = start_time
                    self._source_stats[source_name]['last_fetch_count'] = len(proxies)
                    self._source_stats[source_name]['total_fetch_count'] += len(proxies)
                    self._source_stats[source_name]['success_count'] += 1
                    
                    # 设置代理源
                    for proxy in proxies:
                        proxy.source = source_name
                    
                    all_proxies.extend(proxies)
                    
                    self.logger.info(f"从源 '{source_name}' 获取到 {len(proxies)} 个代理")
                
                except Exception as e:
                    self.logger.error(f"从源 '{source_name}' 获取代理时出错: {str(e)}")
                    self._source_stats[source_name]['fail_count'] += 1
            
            # 去重
            unique_proxies = []
            seen = set()
            for proxy in all_proxies:
                proxy_key = (proxy.host, proxy.port, proxy.protocol)
                if proxy_key not in seen:
                    seen.add(proxy_key)
                    unique_proxies.append(proxy)
            
            self.logger.info(f"总共获取到 {len(unique_proxies)} 个唯一代理")
            
            return unique_proxies
    
    def _calculate_source_counts(self, total_count: int) -> Dict[str, int]:
        """
        计算每个源需要获取的代理数量
        
        Args:
            total_count: 总共需要获取的代理数量
            
        Returns:
            每个源需要获取的代理数量字典
        """
        # 获取可用的源
        available_sources = list(self._sources.keys())
        
        if not available_sources:
            return {}
        
        # 获取源权重
        weights = {}
        for source_name in available_sources:
            # 默认权重为1
            weight = self.config.get(source_name, {}).get('weight', 1.0)
            
            # 如果源最近失败，降低权重
            stats = self._source_stats.get(source_name, {})
            if stats.get('fail_count', 0) > 0:
                last_fail_ratio = stats.get('fail_count', 0) / (stats.get('success_count', 0) + stats.get('fail_count', 0))
                weight *= (1.0 - last_fail_ratio)
            
            weights[source_name] = max(0.1, weight)  # 最小权重为0.1
        
        # 计算总权重
        total_weight = sum(weights.values())
        
        # 计算每个源的代理数量
        source_counts = {}
        remaining = total_count
        
        for source_name in available_sources:
            if total_weight <= 0:
                source_counts[source_name] = remaining // len(available_sources)
            else:
                source_counts[source_name] = int(total_count * weights[source_name] / total_weight)
            
            remaining -= source_counts[source_name]
        
        # 分配剩余的代理
        if remaining > 0:
            # 按权重排序
            sorted_sources = sorted(available_sources, key=lambda s: weights[s], reverse=True)
            
            for source_name in sorted_sources:
                if remaining <= 0:
                    break
                
                source_counts[source_name] += 1
                remaining -= 1
        
        return source_counts
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取代理源状态
        
        Returns:
            代理源状态字典
        """
        with self._lock:
            return {
                'sources': list(self._sources.keys()),
                'stats': self._source_stats.copy()
            }
    
    def get_source_names(self) -> List[str]:
        """
        获取所有代理源名称
        
        Returns:
            代理源名称列表
        """
        with self._lock:
            return list(self._sources.keys())