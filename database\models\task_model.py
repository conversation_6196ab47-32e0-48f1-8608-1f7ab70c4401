"""
Task Model

Database model for task management including task scheduling,
execution status, and result tracking.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum
import json


class TaskStatus(Enum):
    """Task status enumeration."""
    PENDING = "pending"
    SCHEDULED = "scheduled"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(Enum):
    """Task type enumeration."""
    SOCIAL_FOLLOW = "social_follow"
    SOCIAL_LIKE = "social_like"
    SOCIAL_RETWEET = "social_retweet"
    BLOCKCHAIN_TRANSACTION = "blockchain_transaction"
    FORM_SUBMISSION = "form_submission"
    VERIFICATION = "verification"


@dataclass
class TaskModel:
    """Task data model."""
    
    id: Optional[int] = None
    project_id: int = 0
    wallet_id: Optional[int] = None
    task_type: TaskType = TaskType.SOCIAL_FOLLOW
    status: TaskStatus = TaskStatus.PENDING
    priority: int = 5
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    task_data: Dict[str, Any] = None
    result_data: Dict[str, Any] = None
    error_message: str = ""
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        if self.task_data is None:
            self.task_data = {}
        if self.result_data is None:
            self.result_data = {}
    
    @classmethod
    def create_table_sql(cls) -> str:
        """Get SQL for creating tasks table."""
        return """
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            wallet_id INTEGER,
            task_type TEXT NOT NULL,
            status TEXT NOT NULL,
            priority INTEGER DEFAULT 5,
            scheduled_at TIMESTAMP,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            retry_count INTEGER DEFAULT 0,
            max_retries INTEGER DEFAULT 3,
            task_data TEXT,
            result_data TEXT,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects (id),
            FOREIGN KEY (wallet_id) REFERENCES wallets (id)
        )
        """
