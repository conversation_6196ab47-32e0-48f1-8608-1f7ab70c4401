"""
测试 CoinGecko 扫描器

该脚本用于测试 CoinGecko 扫描器的功能。
"""

import logging
import sys
from discovery.sources.coingecko_scanner import CoinGeckoScanner

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# 创建简化的配置
scanner_config = {
    "enabled": True,
    "timeout": 30,
    "max_retries": 3
}

# 创建 CoinGecko 扫描器
scanner = CoinGeckoScanner(scanner_config)

# 获取项目
projects = scanner.get_projects(10)

# 打印项目信息
print(f"\n找到 {len(projects)} 个项目:")
for i, project in enumerate(projects):
    if i < 5:  # 只打印前5个项目，避免输出过多
        print(f"\n项目 {i+1}:")
        print(f"名称: {project.get('name')}")
        print(f"描述: {project.get('description')[:100] if project.get('description') else '无描述'}...")
        print(f"URL: {project.get('url')}")
        print(f"类型: {project.get('project_type')}")
        print(f"区块链: {project.get('blockchain')}")
        print(f"来源: {project.get('discovery_source')}")
    elif i == 5:
        print(f"\n... 还有 {len(projects) - 5} 个项目 ...")