"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/ConstructorContracts.sol:SimpleConstructorContract  # noqa: E501
SIMPLE_CONSTRUCTOR_CONTRACT_BYTECODE = "0x6080604052348015600e575f5ffd5b50603e80601a5f395ff3fe60806040525f5ffdfea26469706673582212209e191fa382f12b19b6452b5f4a2aa0219b128737733ec839fa9a00044a51a5b264736f6c634300081e0033"  # noqa: E501
SIMPLE_CONSTRUCTOR_CONTRACT_RUNTIME = "0x60806040525f5ffdfea26469706673582212209e191fa382f12b19b6452b5f4a2aa0219b128737733ec839fa9a00044a51a5b264736f6c634300081e0033"  # noqa: E501
SIMPLE_CONSTRUCTOR_CONTRACT_ABI = [
    {"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}
]
SIMPLE_CONSTRUCTOR_CONTRACT_DATA = {
    "bytecode": SIMPLE_CONSTRUCTOR_CONTRACT_BYTECODE,
    "bytecode_runtime": SIMPLE_CONSTRUCTOR_CONTRACT_RUNTIME,
    "abi": SIMPLE_CONSTRUCTOR_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/ConstructorContracts.sol:ConstructorWithArgumentsContract  # noqa: E501
CONSTRUCTOR_WITH_ARGUMENTS_CONTRACT_BYTECODE = "0x6080604052348015600e575f5ffd5b506040516101e83803806101e88339818101604052810190602e919060a1565b815f8190555080600181905550505060d8565b5f5ffd5b5f819050919050565b6055816045565b8114605e575f5ffd5b50565b5f81519050606d81604e565b92915050565b5f819050919050565b6083816073565b8114608c575f5ffd5b50565b5f81519050609b81607c565b92915050565b5f5f6040838503121560b45760b36041565b5b5f60bf858286016061565b925050602060ce85828601608f565b9150509250929050565b610103806100e55f395ff3fe6080604052348015600e575f5ffd5b50600436106030575f3560e01c806388ec1346146034578063d4c46c7614604e575b5f5ffd5b603a6068565b604051604591906089565b60405180910390f35b6054606d565b604051605f919060b6565b60405180910390f35b5f5481565b60015481565b5f819050919050565b6083816073565b82525050565b5f602082019050609a5f830184607c565b92915050565b5f819050919050565b60b08160a0565b82525050565b5f60208201905060c75f83018460a9565b9291505056fea26469706673582212207d403d1908a2cf17f2230e67186cf271a4264a5fa23cebf6bb111ac4ff68fb4964736f6c634300081e0033"  # noqa: E501
CONSTRUCTOR_WITH_ARGUMENTS_CONTRACT_RUNTIME = "0x6080604052348015600e575f5ffd5b50600436106030575f3560e01c806388ec1346146034578063d4c46c7614604e575b5f5ffd5b603a6068565b604051604591906089565b60405180910390f35b6054606d565b604051605f919060b6565b60405180910390f35b5f5481565b60015481565b5f819050919050565b6083816073565b82525050565b5f602082019050609a5f830184607c565b92915050565b5f819050919050565b60b08160a0565b82525050565b5f60208201905060c75f83018460a9565b9291505056fea26469706673582212207d403d1908a2cf17f2230e67186cf271a4264a5fa23cebf6bb111ac4ff68fb4964736f6c634300081e0033"  # noqa: E501
CONSTRUCTOR_WITH_ARGUMENTS_CONTRACT_ABI = [
    {
        "inputs": [
            {"internalType": "uint256", "name": "a", "type": "uint256"},
            {"internalType": "bytes32", "name": "b", "type": "bytes32"},
        ],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [],
        "name": "data_a",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "data_b",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
]
CONSTRUCTOR_WITH_ARGUMENTS_CONTRACT_DATA = {
    "bytecode": CONSTRUCTOR_WITH_ARGUMENTS_CONTRACT_BYTECODE,
    "bytecode_runtime": CONSTRUCTOR_WITH_ARGUMENTS_CONTRACT_RUNTIME,
    "abi": CONSTRUCTOR_WITH_ARGUMENTS_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/ConstructorContracts.sol:ConstructorWithAddressArgumentContract  # noqa: E501
CONSTRUCTOR_WITH_ADDRESS_ARGUMENT_CONTRACT_BYTECODE = "0x608060405234801561000f575f5ffd5b506040516101fb3803806101fb833981810160405281019061003191906100d4565b805f5f6101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550506100ff565b5f5ffd5b5f73ffffffffffffffffffffffffffffffffffffffff82169050919050565b5f6100a38261007a565b9050919050565b6100b381610099565b81146100bd575f5ffd5b50565b5f815190506100ce816100aa565b92915050565b5f602082840312156100e9576100e8610076565b5b5f6100f6848285016100c0565b91505092915050565b60f08061010b5f395ff3fe6080604052348015600e575f5ffd5b50600436106026575f3560e01c806334664e3a14602a575b5f5ffd5b60306044565b604051603b919060a3565b60405180910390f35b5f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b5f73ffffffffffffffffffffffffffffffffffffffff82169050919050565b5f608f826068565b9050919050565b609d816087565b82525050565b5f60208201905060b45f8301846096565b9291505056fea2646970667358221220d29806ab73246c53e77ade19f47279948e66b8efb71c14084c98c3d059ea35ee64736f6c634300081e0033"  # noqa: E501
CONSTRUCTOR_WITH_ADDRESS_ARGUMENT_CONTRACT_RUNTIME = "0x6080604052348015600e575f5ffd5b50600436106026575f3560e01c806334664e3a14602a575b5f5ffd5b60306044565b604051603b919060a3565b60405180910390f35b5f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b5f73ffffffffffffffffffffffffffffffffffffffff82169050919050565b5f608f826068565b9050919050565b609d816087565b82525050565b5f60208201905060b45f8301846096565b9291505056fea2646970667358221220d29806ab73246c53e77ade19f47279948e66b8efb71c14084c98c3d059ea35ee64736f6c634300081e0033"  # noqa: E501
CONSTRUCTOR_WITH_ADDRESS_ARGUMENT_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "address", "name": "_testAddr", "type": "address"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [],
        "name": "testAddr",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "view",
        "type": "function",
    },
]
CONSTRUCTOR_WITH_ADDRESS_ARGUMENT_CONTRACT_DATA = {
    "bytecode": CONSTRUCTOR_WITH_ADDRESS_ARGUMENT_CONTRACT_BYTECODE,
    "bytecode_runtime": CONSTRUCTOR_WITH_ADDRESS_ARGUMENT_CONTRACT_RUNTIME,
    "abi": CONSTRUCTOR_WITH_ADDRESS_ARGUMENT_CONTRACT_ABI,
}
