"""
Monitoring Agent

This agent is responsible for continuously tracking official channels of joined projects,
capturing the latest announcements and updates.
"""

import logging
import os
import json
import time
from typing import Dict, List, Optional, Any, Set
from datetime import datetime

# 导入错误处理模块
try:
    from common.error_handling import ErrorHand<PERSON>, safe_execute
except ImportError:
    # 如果错误处理模块不存在，创建简单的替代实现
    class ErrorHandler:
        def __init__(self, logger=None):
            self.logger = logger or logging.getLogger(__name__)
        def handle_error(self, error, context=None):
            self.logger.error(f"Error: {error}", exc_info=True)

    def safe_execute(func, *args, default_return=None, error_handler=None, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if error_handler:
                error_handler.handle_error(e)
            return default_return

# 导入持续运行和多账号支持
try:
    from common.mixins.continuous_operation import ContinuousOperationMixin, MultiAccountMixin
except ImportError:
    # 如果混入类不存在，创建简单的替代实现
    class ContinuousOperationMixin:
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self._running = False
            self._stats = {'total_cycles': 0, 'successful_cycles': 0, 'failed_cycles': 0}

        async def start_continuous_operation(self):
            self._running = True
            return True

        async def stop_continuous_operation(self):
            self._running = False
            return True

        def get_operation_stats(self):
            return self._stats

    class MultiAccountMixin:
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self._accounts = {}
            self._current_account = None

        def add_account(self, account_id, account_data):
            self._accounts[account_id] = account_data
            return True

        def switch_account(self, account_id):
            if account_id in self._accounts:
                self._current_account = account_id
                return True
            return False

        def get_current_account(self):
            return self._accounts.get(self._current_account) if self._current_account else None


# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("MonitoringAgent")

class MonitoringAgent(ContinuousOperationMixin, MultiAccountMixin):
    """
    Enhanced Monitoring Agent for tracking project updates and announcements.

    This agent handles:
    - Monitoring social media channels
    - Tracking project websites
    - Analyzing announcements
    - Detecting important updates
    - Notifying about significant changes
    - Multi-account management for different platforms
    - Continuous operation with error recovery
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Enhanced Monitoring Agent.

        Args:
            config_path: Path to the configuration file
        """
        # 初始化混入类
        super().__init__()

        self.config = self._load_config(config_path)
        self.monitored_projects = {}
        self.active = False
        self.last_check_times = {}
        self.update_cache = {}

        # 设置持续运行参数
        self.loop_interval = self.config.get("check_interval_minutes", 15) * 60  # 转换为秒
        self.max_errors = self.config.get("max_errors", 5)

        # 设置logger属性以兼容混入类
        self.logger = logger

        # 初始化错误处理器
        self.error_handler = ErrorHandler(logger)

        # 添加恢复策略
        self.add_recovery_strategy(self._restart_monitoring_services)
        self.add_recovery_strategy(self._clear_error_state)

        logger.info("Enhanced Monitoring Agent initialized")
    
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "check_interval_minutes": 15,
            "data_storage_path": "data/monitoring",
            "max_history_days": 30,
            "notification_channels": ["console"],
            "priority_keywords": ["airdrop", "token", "launch", "announcement", "update"],
            "monitored_channels": {
                "twitter": True,
                "discord": True,
                "telegram": True,
                "website": True
            }
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    # Merge user config with defaults
                    for key, value in user_config.items():
                        default_config[key] = value
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    async def start(self) -> bool:
        """
        Start the Enhanced Monitoring Agent with continuous operation.

        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            # Initialize data storage
            os.makedirs(self.config["data_storage_path"], exist_ok=True)

            # Load monitored projects
            self._load_monitored_projects()

            # 启动持续运行
            success = await self.start_continuous_operation()
            if success:
                self.active = True
                logger.info("Enhanced Monitoring Agent started successfully with continuous operation")
                return True
            else:
                logger.error("Failed to start continuous operation")
                return False

        except Exception as e:
            self.error_handler.handle_error(e, {'context': 'start_monitoring_agent'})
            return False
    
    async def stop(self) -> bool:
        """
        Stop the Enhanced Monitoring Agent.

        Returns:
            bool: True if stopped successfully, False otherwise
        """
        try:
            # 停止持续运行
            await self.stop_continuous_operation()

            # Save current state
            self._save_monitored_projects()

            self.active = False
            logger.info("Enhanced Monitoring Agent stopped successfully")
            return True
        except Exception as e:
            self.error_handler.handle_error(e, {'context': 'stop_monitoring_agent'})
            return False

    async def _execute_operation_cycle(self) -> bool:
        """
        执行一个监控周期 - 这是持续运行的核心方法

        Returns:
            bool: 执行是否成功
        """
        try:
            logger.info("开始执行监控周期")

            # 轮换账号（如果启用）
            if hasattr(self, '_account_rotation_enabled') and self._account_rotation_enabled:
                self.rotate_account()

            # 检查所有监控项目的更新
            updates = await self._check_all_projects_async()

            # 处理发现的更新
            if updates:
                self._process_updates(updates)
                logger.info(f"监控周期完成，发现 {len(updates)} 个更新")
            else:
                logger.info("监控周期完成，无新更新")

            return True

        except Exception as e:
            self.error_handler.handle_error(e, {'context': 'execute_monitoring_cycle'})
            return False

    async def _check_all_projects_async(self) -> List[Dict[str, Any]]:
        """
        异步检查所有项目的更新

        Returns:
            List[Dict[str, Any]]: 发现的更新列表
        """
        all_updates = []

        try:
            # 获取当前账号信息
            current_account = self.get_current_account()
            account_info = f" (账号: {self._current_account})" if current_account else ""

            logger.info(f"开始检查 {len(self.monitored_projects)} 个项目的更新{account_info}")

            for project_id, project in self.monitored_projects.items():
                try:
                    # 使用安全执行检查单个项目
                    project_updates = safe_execute(
                        self._check_single_project,
                        project_id, project, current_account,
                        default_return=[],
                        error_handler=self.error_handler
                    )

                    if project_updates:
                        all_updates.extend(project_updates)

                    # 更新账号统计
                    if self._current_account:
                        self.update_account_stats(self._current_account, True)

                except Exception as e:
                    logger.error(f"检查项目 {project_id} 时出错: {e}")
                    if self._current_account:
                        self.update_account_stats(self._current_account, False)

            return all_updates

        except Exception as e:
            self.error_handler.handle_error(e, {'context': 'check_all_projects_async'})
            return []
    
    def _load_monitored_projects(self) -> None:
        """Load the list of projects to monitor."""
        projects_file = os.path.join(self.config["data_storage_path"], "monitored_projects.json")
        
        if os.path.exists(projects_file):
            try:
                with open(projects_file, 'r') as f:
                    self.monitored_projects = json.load(f)
                logger.info(f"Loaded {len(self.monitored_projects)} monitored projects")
            except Exception as e:
                logger.error(f"Error loading monitored projects: {e}")
                self.monitored_projects = {}
    
    def _save_monitored_projects(self) -> None:
        """Save the list of monitored projects."""
        projects_file = os.path.join(self.config["data_storage_path"], "monitored_projects.json")
        
        try:
            with open(projects_file, 'w') as f:
                json.dump(self.monitored_projects, f, indent=2)
            logger.info(f"Saved {len(self.monitored_projects)} monitored projects")
        except Exception as e:
            logger.error(f"Error saving monitored projects: {e}")

    def _check_single_project(self, project_id: str, project: Dict[str, Any],
                             account: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        检查单个项目的更新

        Args:
            project_id: 项目ID
            project: 项目数据
            account: 当前使用的账号信息

        Returns:
            List[Dict[str, Any]]: 该项目的更新列表
        """
        updates = []

        try:
            logger.info(f"检查项目 {project_id} 的更新")

            # 检查各个渠道（使用当前账号）
            if self.config["monitored_channels"]["twitter"] and "twitter" in project:
                twitter_updates = self._check_twitter_with_account(project, account)
                updates.extend(twitter_updates)

            if self.config["monitored_channels"]["discord"] and "discord" in project:
                discord_updates = self._check_discord_with_account(project, account)
                updates.extend(discord_updates)

            if self.config["monitored_channels"]["telegram"] and "telegram" in project:
                telegram_updates = self._check_telegram_with_account(project, account)
                updates.extend(telegram_updates)

            if self.config["monitored_channels"]["website"] and "website" in project:
                website_updates = self._check_website_with_account(project, account)
                updates.extend(website_updates)

            # 更新最后检查时间
            self.monitored_projects[project_id]["last_check"] = datetime.now().isoformat()

            return updates

        except Exception as e:
            logger.error(f"检查项目 {project_id} 时出错: {e}")
            return []

    def _check_twitter_with_account(self, project: Dict[str, Any],
                                   account: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        使用指定账号检查Twitter更新

        Args:
            project: 项目数据
            account: 账号信息

        Returns:
            List[Dict[str, Any]]: Twitter更新列表
        """
        try:
            # 这里会使用账号信息进行Twitter API调用
            account_info = f" (账号: {account.get('username', 'unknown')})" if account else ""
            logger.info(f"检查项目 {project['id']} 的Twitter更新{account_info}")

            # 实际实现会调用Twitter API
            # 这里返回模拟数据
            return []

        except Exception as e:
            logger.error(f"检查Twitter更新失败: {e}")
            return []

    def _check_discord_with_account(self, project: Dict[str, Any],
                                   account: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        使用指定账号检查Discord更新

        Args:
            project: 项目数据
            account: 账号信息

        Returns:
            List[Dict[str, Any]]: Discord更新列表
        """
        try:
            account_info = f" (账号: {account.get('username', 'unknown')})" if account else ""
            logger.info(f"检查项目 {project['id']} 的Discord更新{account_info}")

            # 实际实现会调用Discord API
            return []

        except Exception as e:
            logger.error(f"检查Discord更新失败: {e}")
            return []

    def _check_telegram_with_account(self, project: Dict[str, Any],
                                    account: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        使用指定账号检查Telegram更新

        Args:
            project: 项目数据
            account: 账号信息

        Returns:
            List[Dict[str, Any]]: Telegram更新列表
        """
        try:
            account_info = f" (账号: {account.get('username', 'unknown')})" if account else ""
            logger.info(f"检查项目 {project['id']} 的Telegram更新{account_info}")

            # 实际实现会调用Telegram API
            return []

        except Exception as e:
            logger.error(f"检查Telegram更新失败: {e}")
            return []

    def _check_website_with_account(self, project: Dict[str, Any],
                                   account: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        使用指定账号检查网站更新

        Args:
            project: 项目数据
            account: 账号信息

        Returns:
            List[Dict[str, Any]]: 网站更新列表
        """
        try:
            account_info = f" (代理: {account.get('proxy', 'none')})" if account else ""
            logger.info(f"检查项目 {project['id']} 的网站更新{account_info}")

            # 实际实现会使用账号的代理信息访问网站
            return []

        except Exception as e:
            logger.error(f"检查网站更新失败: {e}")
            return []

    async def _restart_monitoring_services(self) -> bool:
        """
        重启监控服务 - 恢复策略

        Returns:
            bool: 重启是否成功
        """
        try:
            logger.info("重启监控服务...")

            # 重新加载配置
            self.config = self._load_config()

            # 重新加载监控项目
            self._load_monitored_projects()

            # 清理缓存
            self.update_cache.clear()
            self.last_check_times.clear()

            logger.info("监控服务重启成功")
            return True

        except Exception as e:
            logger.error(f"重启监控服务失败: {e}")
            return False

    async def _clear_error_state(self) -> bool:
        """
        清理错误状态 - 恢复策略

        Returns:
            bool: 清理是否成功
        """
        try:
            logger.info("清理错误状态...")

            # 重置错误计数器
            if hasattr(self, '_stats'):
                self._stats['consecutive_errors'] = 0

            # 清理错误缓存
            self.update_cache.clear()

            logger.info("错误状态清理成功")
            return True

        except Exception as e:
            logger.error(f"清理错误状态失败: {e}")
            return False
    
    def add_project(self, project_data: Dict[str, Any]) -> bool:
        """
        Add a project to monitor.
        
        Args:
            project_data: Project information including channels to monitor
            
        Returns:
            bool: True if added successfully, False otherwise
        """
        try:
            project_id = project_data.get("id")
            if not project_id:
                logger.error("Project data missing required 'id' field")
                return False
            
            # Add timestamp
            project_data["added_at"] = datetime.now().isoformat()
            project_data["last_check"] = None
            project_data["updates"] = []
            
            # Store project
            self.monitored_projects[project_id] = project_data
            self._save_monitored_projects()
            
            logger.info(f"Added project to monitor: {project_id}")
            return True
        except Exception as e:
            logger.error(f"Error adding project: {e}")
            return False
    
    def remove_project(self, project_id: str) -> bool:
        """
        Remove a project from monitoring.
        
        Args:
            project_id: ID of the project to remove
            
        Returns:
            bool: True if removed successfully, False otherwise
        """
        try:
            if project_id in self.monitored_projects:
                del self.monitored_projects[project_id]
                self._save_monitored_projects()
                logger.info(f"Removed project from monitoring: {project_id}")
                return True
            else:
                logger.warning(f"Project not found for removal: {project_id}")
                return False
        except Exception as e:
            logger.error(f"Error removing project: {e}")
            return False
    
    def check_for_updates(self, project_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Check for updates on monitored projects.
        
        Args:
            project_id: Optional specific project ID to check
            
        Returns:
            List of update objects
        """
        updates = []
        
        try:
            projects_to_check = [project_id] if project_id else self.monitored_projects.keys()
            
            for pid in projects_to_check:
                if pid not in self.monitored_projects:
                    logger.warning(f"Project not found: {pid}")
                    continue
                
                project = self.monitored_projects[pid]
                logger.info(f"Checking for updates on project: {pid}")
                
                # Check each channel
                if self.config["monitored_channels"]["twitter"] and "twitter" in project:
                    twitter_updates = self._check_twitter(project)
                    updates.extend(twitter_updates)
                
                if self.config["monitored_channels"]["discord"] and "discord" in project:
                    discord_updates = self._check_discord(project)
                    updates.extend(discord_updates)
                
                if self.config["monitored_channels"]["telegram"] and "telegram" in project:
                    telegram_updates = self._check_telegram(project)
                    updates.extend(telegram_updates)
                
                if self.config["monitored_channels"]["website"] and "website" in project:
                    website_updates = self._check_website(project)
                    updates.extend(website_updates)
                
                # Update last check time
                self.monitored_projects[pid]["last_check"] = datetime.now().isoformat()
            
            # Save updated projects
            self._save_monitored_projects()
            
            # Process and notify about updates
            if updates:
                self._process_updates(updates)
            
            return updates
        except Exception as e:
            logger.error(f"Error checking for updates: {e}")
            return []
    
    def _check_twitter(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check Twitter for updates.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        # This would be implemented in twitter_monitor.py
        logger.info(f"Checking Twitter for project {project['id']}")
        return []
    
    def _check_discord(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check Discord for updates.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        # This would be implemented in discord_monitor.py
        logger.info(f"Checking Discord for project {project['id']}")
        return []
    
    def _check_telegram(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check Telegram for updates.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        # This would be implemented in telegram_monitor.py
        logger.info(f"Checking Telegram for project {project['id']}")
        return []
    
    def _check_website(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check website for updates.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        # This would be implemented in website_monitor.py
        logger.info(f"Checking website for project {project['id']}")
        return []
    
    def _process_updates(self, updates: List[Dict[str, Any]]) -> None:
        """
        Process and store updates.
        
        Args:
            updates: List of update objects
        """
        try:
            for update in updates:
                project_id = update.get("project_id")
                if not project_id or project_id not in self.monitored_projects:
                    logger.warning(f"Update for unknown project: {project_id}")
                    continue
                
                # Add update to project history
                self.monitored_projects[project_id]["updates"].append(update)
                
                # Check if this is a high-priority update
                is_priority = self._is_priority_update(update)
                
                # Send notification
                self._send_notification(update, is_priority)
            
            # Save updated projects
            self._save_monitored_projects()
        except Exception as e:
            logger.error(f"Error processing updates: {e}")
    
    def _is_priority_update(self, update: Dict[str, Any]) -> bool:
        """
        Check if an update is high priority.
        
        Args:
            update: Update object
            
        Returns:
            bool: True if high priority, False otherwise
        """
        # Check for priority keywords in the update content
        if "content" in update:
            content = update["content"].lower()
            for keyword in self.config["priority_keywords"]:
                if keyword.lower() in content:
                    return True
        
        return False
    
    def _send_notification(self, update: Dict[str, Any], is_priority: bool) -> None:
        """
        Send notification about an update.
        
        Args:
            update: Update object
            is_priority: Whether this is a high-priority update
        """
        # This would be implemented in notification_service.py
        notification_type = "PRIORITY" if is_priority else "STANDARD"
        logger.info(f"{notification_type} UPDATE: {update.get('title', 'Untitled')} for project {update.get('project_id')}")
    
    def get_project_updates(self, project_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent updates for a specific project.
        
        Args:
            project_id: Project ID
            limit: Maximum number of updates to return
            
        Returns:
            List of update objects
        """
        try:
            if project_id not in self.monitored_projects:
                logger.warning(f"Project not found: {project_id}")
                return []
            
            updates = self.monitored_projects[project_id].get("updates", [])
            
            # Sort by timestamp (newest first) and limit
            sorted_updates = sorted(updates, key=lambda x: x.get("timestamp", ""), reverse=True)
            return sorted_updates[:limit]
        except Exception as e:
            logger.error(f"Error getting project updates: {e}")
            return []
    
    def get_all_updates(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent updates across all projects.
        
        Args:
            limit: Maximum number of updates to return
            
        Returns:
            List of update objects
        """
        try:
            all_updates = []
            
            for project_id, project in self.monitored_projects.items():
                updates = project.get("updates", [])
                for update in updates:
                    # Ensure update has project_id
                    if "project_id" not in update:
                        update["project_id"] = project_id
                    all_updates.append(update)
            
            # Sort by timestamp (newest first) and limit
            sorted_updates = sorted(all_updates, key=lambda x: x.get("timestamp", ""), reverse=True)
            return sorted_updates[:limit]
        except Exception as e:
            logger.error(f"Error getting all updates: {e}")
            return []
    
    def run_monitoring_cycle(self) -> None:
        """Run a complete monitoring cycle for all projects."""
        if not self.active:
            logger.warning("Monitoring Agent is not active")
            return
        
        logger.info("Starting monitoring cycle")
        updates = self.check_for_updates()
        logger.info(f"Monitoring cycle complete, found {len(updates)} updates")
    
    def run_continuous_monitoring(self, stop_event=None) -> None:
        """
        Run continuous monitoring until stopped.
        
        Args:
            stop_event: Optional event to signal stopping
        """
        self.active = True
        interval_seconds = self.config["check_interval_minutes"] * 60
        
        logger.info(f"Starting continuous monitoring (interval: {self.config['check_interval_minutes']} minutes)")
        
        try:
            while self.active:
                if stop_event and stop_event.is_set():
                    logger.info("Stop event received, ending continuous monitoring")
                    break
                
                self.run_monitoring_cycle()
                
                # Sleep until next check
                logger.info(f"Sleeping for {interval_seconds} seconds until next check")
                time.sleep(interval_seconds)
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received, ending continuous monitoring")
        except Exception as e:
            logger.error(f"Error in continuous monitoring: {e}")
        finally:
            self.active = False
    
    def status(self) -> Dict[str, Any]:
        """
        Get the current status of the Monitoring Agent.
        
        Returns:
            Dict containing status information
        """
        return {
            "active": self.active,
            "projects_count": len(self.monitored_projects),
            "last_check": max([p.get("last_check") for p in self.monitored_projects.values() if p.get("last_check")], default=None),
            "check_interval_minutes": self.config["check_interval_minutes"],
            "monitored_channels": self.config["monitored_channels"]
        }


if __name__ == "__main__":
    # Simple test
    agent = MonitoringAgent()
    agent.start()
    
    # Add a test project
    test_project = {
        "id": "test-project-123",
        "name": "Test Project",
        "twitter": "testproject",
        "discord": "https://discord.gg/testproject",
        "telegram": "testprojectofficial",
        "website": "https://testproject.io"
    }
    agent.add_project(test_project)
    
    # Run a monitoring cycle
    agent.run_monitoring_cycle()
    
    # Print status
    print(agent.status())
    
    agent.stop()