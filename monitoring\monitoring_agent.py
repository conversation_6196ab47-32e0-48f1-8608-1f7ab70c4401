"""
Monitoring Agent

This agent is responsible for continuously tracking official channels of joined projects,
capturing the latest announcements and updates.
"""

import logging
import os
import json
import time
from typing import Dict, List, Optional, Any, Set
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("MonitoringAgent")

class MonitoringAgent:
    """
    Monitoring Agent for tracking project updates and announcements.
    
    This agent handles:
    - Monitoring social media channels
    - Tracking project websites
    - Analyzing announcements
    - Detecting important updates
    - Notifying about significant changes
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Monitoring Agent.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config = self._load_config(config_path)
        self.monitored_projects = {}
        self.active = False
        self.last_check_times = {}
        self.update_cache = {}
        logger.info("Monitoring Agent initialized")
    
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "check_interval_minutes": 15,
            "data_storage_path": "data/monitoring",
            "max_history_days": 30,
            "notification_channels": ["console"],
            "priority_keywords": ["airdrop", "token", "launch", "announcement", "update"],
            "monitored_channels": {
                "twitter": True,
                "discord": True,
                "telegram": True,
                "website": True
            }
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    # Merge user config with defaults
                    for key, value in user_config.items():
                        default_config[key] = value
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def start(self) -> bool:
        """
        Start the Monitoring Agent.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            # Initialize data storage
            os.makedirs(self.config["data_storage_path"], exist_ok=True)
            
            # Load monitored projects
            self._load_monitored_projects()
            
            self.active = True
            logger.info("Monitoring Agent started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start Monitoring Agent: {e}")
            return False
    
    def stop(self) -> bool:
        """
        Stop the Monitoring Agent.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        try:
            # Save current state
            self._save_monitored_projects()
            
            self.active = False
            logger.info("Monitoring Agent stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Error stopping Monitoring Agent: {e}")
            return False
    
    def _load_monitored_projects(self) -> None:
        """Load the list of projects to monitor."""
        projects_file = os.path.join(self.config["data_storage_path"], "monitored_projects.json")
        
        if os.path.exists(projects_file):
            try:
                with open(projects_file, 'r') as f:
                    self.monitored_projects = json.load(f)
                logger.info(f"Loaded {len(self.monitored_projects)} monitored projects")
            except Exception as e:
                logger.error(f"Error loading monitored projects: {e}")
                self.monitored_projects = {}
    
    def _save_monitored_projects(self) -> None:
        """Save the list of monitored projects."""
        projects_file = os.path.join(self.config["data_storage_path"], "monitored_projects.json")
        
        try:
            with open(projects_file, 'w') as f:
                json.dump(self.monitored_projects, f, indent=2)
            logger.info(f"Saved {len(self.monitored_projects)} monitored projects")
        except Exception as e:
            logger.error(f"Error saving monitored projects: {e}")
    
    def add_project(self, project_data: Dict[str, Any]) -> bool:
        """
        Add a project to monitor.
        
        Args:
            project_data: Project information including channels to monitor
            
        Returns:
            bool: True if added successfully, False otherwise
        """
        try:
            project_id = project_data.get("id")
            if not project_id:
                logger.error("Project data missing required 'id' field")
                return False
            
            # Add timestamp
            project_data["added_at"] = datetime.now().isoformat()
            project_data["last_check"] = None
            project_data["updates"] = []
            
            # Store project
            self.monitored_projects[project_id] = project_data
            self._save_monitored_projects()
            
            logger.info(f"Added project to monitor: {project_id}")
            return True
        except Exception as e:
            logger.error(f"Error adding project: {e}")
            return False
    
    def remove_project(self, project_id: str) -> bool:
        """
        Remove a project from monitoring.
        
        Args:
            project_id: ID of the project to remove
            
        Returns:
            bool: True if removed successfully, False otherwise
        """
        try:
            if project_id in self.monitored_projects:
                del self.monitored_projects[project_id]
                self._save_monitored_projects()
                logger.info(f"Removed project from monitoring: {project_id}")
                return True
            else:
                logger.warning(f"Project not found for removal: {project_id}")
                return False
        except Exception as e:
            logger.error(f"Error removing project: {e}")
            return False
    
    def check_for_updates(self, project_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Check for updates on monitored projects.
        
        Args:
            project_id: Optional specific project ID to check
            
        Returns:
            List of update objects
        """
        updates = []
        
        try:
            projects_to_check = [project_id] if project_id else self.monitored_projects.keys()
            
            for pid in projects_to_check:
                if pid not in self.monitored_projects:
                    logger.warning(f"Project not found: {pid}")
                    continue
                
                project = self.monitored_projects[pid]
                logger.info(f"Checking for updates on project: {pid}")
                
                # Check each channel
                if self.config["monitored_channels"]["twitter"] and "twitter" in project:
                    twitter_updates = self._check_twitter(project)
                    updates.extend(twitter_updates)
                
                if self.config["monitored_channels"]["discord"] and "discord" in project:
                    discord_updates = self._check_discord(project)
                    updates.extend(discord_updates)
                
                if self.config["monitored_channels"]["telegram"] and "telegram" in project:
                    telegram_updates = self._check_telegram(project)
                    updates.extend(telegram_updates)
                
                if self.config["monitored_channels"]["website"] and "website" in project:
                    website_updates = self._check_website(project)
                    updates.extend(website_updates)
                
                # Update last check time
                self.monitored_projects[pid]["last_check"] = datetime.now().isoformat()
            
            # Save updated projects
            self._save_monitored_projects()
            
            # Process and notify about updates
            if updates:
                self._process_updates(updates)
            
            return updates
        except Exception as e:
            logger.error(f"Error checking for updates: {e}")
            return []
    
    def _check_twitter(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check Twitter for updates.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        # This would be implemented in twitter_monitor.py
        logger.info(f"Checking Twitter for project {project['id']}")
        return []
    
    def _check_discord(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check Discord for updates.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        # This would be implemented in discord_monitor.py
        logger.info(f"Checking Discord for project {project['id']}")
        return []
    
    def _check_telegram(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check Telegram for updates.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        # This would be implemented in telegram_monitor.py
        logger.info(f"Checking Telegram for project {project['id']}")
        return []
    
    def _check_website(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check website for updates.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        # This would be implemented in website_monitor.py
        logger.info(f"Checking website for project {project['id']}")
        return []
    
    def _process_updates(self, updates: List[Dict[str, Any]]) -> None:
        """
        Process and store updates.
        
        Args:
            updates: List of update objects
        """
        try:
            for update in updates:
                project_id = update.get("project_id")
                if not project_id or project_id not in self.monitored_projects:
                    logger.warning(f"Update for unknown project: {project_id}")
                    continue
                
                # Add update to project history
                self.monitored_projects[project_id]["updates"].append(update)
                
                # Check if this is a high-priority update
                is_priority = self._is_priority_update(update)
                
                # Send notification
                self._send_notification(update, is_priority)
            
            # Save updated projects
            self._save_monitored_projects()
        except Exception as e:
            logger.error(f"Error processing updates: {e}")
    
    def _is_priority_update(self, update: Dict[str, Any]) -> bool:
        """
        Check if an update is high priority.
        
        Args:
            update: Update object
            
        Returns:
            bool: True if high priority, False otherwise
        """
        # Check for priority keywords in the update content
        if "content" in update:
            content = update["content"].lower()
            for keyword in self.config["priority_keywords"]:
                if keyword.lower() in content:
                    return True
        
        return False
    
    def _send_notification(self, update: Dict[str, Any], is_priority: bool) -> None:
        """
        Send notification about an update.
        
        Args:
            update: Update object
            is_priority: Whether this is a high-priority update
        """
        # This would be implemented in notification_service.py
        notification_type = "PRIORITY" if is_priority else "STANDARD"
        logger.info(f"{notification_type} UPDATE: {update.get('title', 'Untitled')} for project {update.get('project_id')}")
    
    def get_project_updates(self, project_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent updates for a specific project.
        
        Args:
            project_id: Project ID
            limit: Maximum number of updates to return
            
        Returns:
            List of update objects
        """
        try:
            if project_id not in self.monitored_projects:
                logger.warning(f"Project not found: {project_id}")
                return []
            
            updates = self.monitored_projects[project_id].get("updates", [])
            
            # Sort by timestamp (newest first) and limit
            sorted_updates = sorted(updates, key=lambda x: x.get("timestamp", ""), reverse=True)
            return sorted_updates[:limit]
        except Exception as e:
            logger.error(f"Error getting project updates: {e}")
            return []
    
    def get_all_updates(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent updates across all projects.
        
        Args:
            limit: Maximum number of updates to return
            
        Returns:
            List of update objects
        """
        try:
            all_updates = []
            
            for project_id, project in self.monitored_projects.items():
                updates = project.get("updates", [])
                for update in updates:
                    # Ensure update has project_id
                    if "project_id" not in update:
                        update["project_id"] = project_id
                    all_updates.append(update)
            
            # Sort by timestamp (newest first) and limit
            sorted_updates = sorted(all_updates, key=lambda x: x.get("timestamp", ""), reverse=True)
            return sorted_updates[:limit]
        except Exception as e:
            logger.error(f"Error getting all updates: {e}")
            return []
    
    def run_monitoring_cycle(self) -> None:
        """Run a complete monitoring cycle for all projects."""
        if not self.active:
            logger.warning("Monitoring Agent is not active")
            return
        
        logger.info("Starting monitoring cycle")
        updates = self.check_for_updates()
        logger.info(f"Monitoring cycle complete, found {len(updates)} updates")
    
    def run_continuous_monitoring(self, stop_event=None) -> None:
        """
        Run continuous monitoring until stopped.
        
        Args:
            stop_event: Optional event to signal stopping
        """
        self.active = True
        interval_seconds = self.config["check_interval_minutes"] * 60
        
        logger.info(f"Starting continuous monitoring (interval: {self.config['check_interval_minutes']} minutes)")
        
        try:
            while self.active:
                if stop_event and stop_event.is_set():
                    logger.info("Stop event received, ending continuous monitoring")
                    break
                
                self.run_monitoring_cycle()
                
                # Sleep until next check
                logger.info(f"Sleeping for {interval_seconds} seconds until next check")
                time.sleep(interval_seconds)
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received, ending continuous monitoring")
        except Exception as e:
            logger.error(f"Error in continuous monitoring: {e}")
        finally:
            self.active = False
    
    def status(self) -> Dict[str, Any]:
        """
        Get the current status of the Monitoring Agent.
        
        Returns:
            Dict containing status information
        """
        return {
            "active": self.active,
            "projects_count": len(self.monitored_projects),
            "last_check": max([p.get("last_check") for p in self.monitored_projects.values() if p.get("last_check")], default=None),
            "check_interval_minutes": self.config["check_interval_minutes"],
            "monitored_channels": self.config["monitored_channels"]
        }


if __name__ == "__main__":
    # Simple test
    agent = MonitoringAgent()
    agent.start()
    
    # Add a test project
    test_project = {
        "id": "test-project-123",
        "name": "Test Project",
        "twitter": "testproject",
        "discord": "https://discord.gg/testproject",
        "telegram": "testprojectofficial",
        "website": "https://testproject.io"
    }
    agent.add_project(test_project)
    
    # Run a monitoring cycle
    agent.run_monitoring_cycle()
    
    # Print status
    print(agent.status())
    
    agent.stop()