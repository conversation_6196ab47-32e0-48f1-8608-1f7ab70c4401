"""
Validation utilities for AirHunter

This module provides various validation functions used throughout the system.
"""

import re
import json
from typing import Any, Dict, List, Union
from web3 import Web3

class Validators:
    """Collection of validation methods"""
    
    @staticmethod
    def is_valid_eth_address(address: str) -> bool:
        """
        Validate Ethereum address format
        
        Args:
            address: Ethereum address to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        return Web3.isAddress(address)
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """
        Validate URL format
        
        Args:
            url: URL to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return url_pattern.match(url) is not None
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """
        Validate email format
        
        Args:
            email: Email address to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        return email_pattern.match(email) is not None
    
    @staticmethod
    def is_valid_json(data: str) -> bool:
        """
        Validate JSON string
        
        Args:
            data: JSON string to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        try:
            json.loads(data)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_type(value: Any, expected_type: Union[type, tuple]) -> bool:
        """
        Validate value type
        
        Args:
            value: Value to validate
            expected_type: Expected type or tuple of types
            
        Returns:
            bool: True if valid, False otherwise
        """
        return isinstance(value, expected_type)
    
    @staticmethod
    def validate_dict_schema(data: Dict, schema: Dict) -> bool:
        """
        Validate dictionary against schema
        
        Args:
            data: Dictionary to validate
            schema: Schema to validate against
            
        Returns:
            bool: True if valid, False otherwise
        """
        try:
            for key, type_info in schema.items():
                if key not in data:
                    return False
                if not isinstance(data[key], type_info):
                    return False
            return True
        except Exception:
            return False
    
    @staticmethod
    def is_valid_string_length(string: str, min_length: int = 0, max_length: int = None) -> bool:
        """
        Validate string length
        
        Args:
            string: String to validate
            min_length: Minimum allowed length
            max_length: Maximum allowed length
            
        Returns:
            bool: True if valid, False otherwise
        """
        length = len(string)
        if length < min_length:
            return False
        if max_length is not None and length > max_length:
            return False
        return True

# Create global validator instance
validator = Validators()
