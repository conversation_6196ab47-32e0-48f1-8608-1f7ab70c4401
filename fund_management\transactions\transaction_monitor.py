"""
Transaction Monitor

交易监控器，负责监控交易状态、确认数和执行结果。
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta


class TransactionMonitor:
    """
    交易监控器
    
    负责监控区块链交易的状态变化，包括pending、confirmed、failed等状态。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化交易监控器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 监控的交易
        self.monitored_transactions: Dict[str, Dict] = {}
        
        # 状态回调函数
        self.status_callbacks: Dict[str, List[Callable]] = {}
        
        # 监控配置
        self.monitoring_config = {
            "check_interval": config.get("check_interval", 10),  # 秒
            "max_confirmations": config.get("max_confirmations", 12),
            "timeout_minutes": config.get("timeout_minutes", 30),
            "retry_attempts": config.get("retry_attempts", 3)
        }
        
        # 监控统计
        self.monitor_stats = {
            'total_monitored': 0,
            'confirmed_transactions': 0,
            'failed_transactions': 0,
            'timeout_transactions': 0
        }
        
        # 监控任务
        self.monitoring_task: Optional[asyncio.Task] = None
        self.is_monitoring = False
    
    async def initialize(self) -> bool:
        """
        初始化交易监控器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 启动监控任务
            await self.start_monitoring()
            
            self.logger.info("Transaction Monitor initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Transaction Monitor: {e}")
            return False
    
    async def start_monitoring(self):
        """启动交易监控"""
        try:
            if not self.is_monitoring:
                self.is_monitoring = True
                self.monitoring_task = asyncio.create_task(self._monitoring_loop())
                self.logger.info("Transaction monitoring started")
                
        except Exception as e:
            self.logger.error(f"Failed to start monitoring: {e}")
    
    async def stop_monitoring(self):
        """停止交易监控"""
        try:
            self.is_monitoring = False
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
                self.monitoring_task = None
            
            self.logger.info("Transaction monitoring stopped")
            
        except Exception as e:
            self.logger.error(f"Failed to stop monitoring: {e}")
    
    async def add_transaction(self, tx_hash: str, transaction_data: Dict[str, Any],
                            callback: Optional[Callable] = None) -> bool:
        """
        添加交易到监控列表
        
        Args:
            tx_hash: 交易哈希
            transaction_data: 交易数据
            callback: 状态变化回调函数
            
        Returns:
            bool: 添加是否成功
        """
        try:
            monitor_data = {
                "tx_hash": tx_hash,
                "transaction_data": transaction_data,
                "status": "pending",
                "confirmations": 0,
                "added_at": datetime.utcnow(),
                "last_checked": None,
                "retry_count": 0,
                "block_number": None,
                "gas_used": None,
                "effective_gas_price": None
            }
            
            self.monitored_transactions[tx_hash] = monitor_data
            self.monitor_stats['total_monitored'] += 1
            
            # 添加回调函数
            if callback:
                if tx_hash not in self.status_callbacks:
                    self.status_callbacks[tx_hash] = []
                self.status_callbacks[tx_hash].append(callback)
            
            self.logger.info(f"Added transaction {tx_hash} to monitoring")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add transaction to monitoring: {e}")
            return False

    async def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                await self._check_all_transactions()
                await asyncio.sleep(self.monitoring_config["check_interval"])
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5)  # 错误后短暂等待
    
    async def _check_all_transactions(self):
        """检查所有监控中的交易"""
        try:
            current_time = datetime.utcnow()
            transactions_to_remove = []
            
            for tx_hash, monitor_data in self.monitored_transactions.items():
                try:
                    # 检查超时
                    if self._is_transaction_timeout(monitor_data, current_time):
                        await self._handle_timeout_transaction(tx_hash, monitor_data)
                        transactions_to_remove.append(tx_hash)
                        continue
                    
                    # 检查交易状态
                    await self._check_transaction_status(tx_hash, monitor_data)
                    
                    # 如果交易已确认，从监控列表移除
                    if monitor_data["status"] in ["confirmed", "failed"]:
                        transactions_to_remove.append(tx_hash)
                    
                except Exception as e:
                    self.logger.error(f"Error checking transaction {tx_hash}: {e}")
                    monitor_data["retry_count"] += 1
                    
                    if monitor_data["retry_count"] >= self.monitoring_config["retry_attempts"]:
                        transactions_to_remove.append(tx_hash)
            
            # 移除已完成或超时的交易
            for tx_hash in transactions_to_remove:
                self.monitored_transactions.pop(tx_hash, None)
                self.status_callbacks.pop(tx_hash, None)
                
        except Exception as e:
            self.logger.error(f"Error in check all transactions: {e}")
    
    async def _check_transaction_status(self, tx_hash: str, monitor_data: Dict):
        """检查单个交易状态"""
        try:
            # 模拟区块链状态查询
            # 实际实现需要调用区块链RPC接口
            
            # 模拟状态变化
            import random
            
            current_status = monitor_data["status"]
            
            if current_status == "pending":
                # 30%概率变为confirmed，5%概率变为failed
                rand = random.random()
                if rand < 0.3:
                    monitor_data["status"] = "confirmed"
                    monitor_data["confirmations"] = 1
                    monitor_data["block_number"] = 18000000 + random.randint(1, 1000)
                    monitor_data["gas_used"] = 21000
                    monitor_data["effective_gas_price"] = 20000000000
                    self.monitor_stats['confirmed_transactions'] += 1
                elif rand < 0.35:
                    monitor_data["status"] = "failed"
                    monitor_data["failure_reason"] = "Gas limit exceeded"
                    self.monitor_stats['failed_transactions'] += 1
            
            elif current_status == "confirmed":
                # 增加确认数
                if monitor_data["confirmations"] < self.monitoring_config["max_confirmations"]:
                    monitor_data["confirmations"] += 1
            
            monitor_data["last_checked"] = datetime.utcnow()
            
            # 调用状态变化回调
            await self._call_status_callbacks(tx_hash, monitor_data)
            
        except Exception as e:
            self.logger.error(f"Error checking transaction status for {tx_hash}: {e}")
    
    async def _call_status_callbacks(self, tx_hash: str, monitor_data: Dict):
        """调用状态变化回调函数"""
        try:
            if tx_hash in self.status_callbacks:
                for callback in self.status_callbacks[tx_hash]:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(tx_hash, monitor_data)
                        else:
                            callback(tx_hash, monitor_data)
                    except Exception as e:
                        self.logger.error(f"Error in status callback: {e}")
                        
        except Exception as e:
            self.logger.error(f"Error calling status callbacks: {e}")
    
    def _is_transaction_timeout(self, monitor_data: Dict, current_time: datetime) -> bool:
        """检查交易是否超时"""
        try:
            timeout_delta = timedelta(minutes=self.monitoring_config["timeout_minutes"])
            return current_time - monitor_data["added_at"] > timeout_delta
        except Exception:
            return False
    
    async def _handle_timeout_transaction(self, tx_hash: str, monitor_data: Dict):
        """处理超时交易"""
        try:
            monitor_data["status"] = "timeout"
            self.monitor_stats['timeout_transactions'] += 1
            
            self.logger.warning(f"Transaction {tx_hash} timed out")
            
            # 调用回调函数
            await self._call_status_callbacks(tx_hash, monitor_data)
            
        except Exception as e:
            self.logger.error(f"Error handling timeout transaction: {e}")
    
    async def get_transaction_info(self, tx_hash: str) -> Optional[Dict[str, Any]]:
        """
        获取交易监控信息
        
        Args:
            tx_hash: 交易哈希
            
        Returns:
            Optional[Dict[str, Any]]: 交易信息
        """
        try:
            return self.monitored_transactions.get(tx_hash)
        except Exception as e:
            self.logger.error(f"Error getting transaction info: {e}")
            return None
    
    async def remove_transaction(self, tx_hash: str) -> bool:
        """
        从监控列表移除交易
        
        Args:
            tx_hash: 交易哈希
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if tx_hash in self.monitored_transactions:
                del self.monitored_transactions[tx_hash]
                self.status_callbacks.pop(tx_hash, None)
                self.logger.info(f"Removed transaction {tx_hash} from monitoring")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error removing transaction: {e}")
            return False
    
    async def get_monitoring_statistics(self) -> Dict[str, Any]:
        """
        获取监控统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            active_count = len(self.monitored_transactions)
            
            status_distribution = {}
            for monitor_data in self.monitored_transactions.values():
                status = monitor_data["status"]
                status_distribution[status] = status_distribution.get(status, 0) + 1
            
            return {
                'monitor_stats': self.monitor_stats,
                'active_transactions': active_count,
                'status_distribution': status_distribution,
                'monitoring_config': self.monitoring_config,
                'is_monitoring': self.is_monitoring
            }
            
        except Exception as e:
            self.logger.error(f"Error getting monitoring statistics: {e}")
            return {}
