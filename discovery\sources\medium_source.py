"""
Medium 项目源

该模块实现了从 Medium 博客获取项目信息的功能。
"""

import logging
import requests
import time
import random
import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup

from discovery.sources.base_source import BaseProjectSource


class MediumSource(BaseProjectSource):
    """Medium 项目源"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 Medium 项目源
        
        Args:
            config: 配置字典，包含项目源的配置信息
        """
        super().__init__(config)
        self.timeout = config.get('timeout', 10)
        self.search_terms = config.get('search_terms', [
            'airdrop', 'crypto airdrop', 'token airdrop', 'free tokens',
            'blockchain airdrop', 'defi airdrop', 'nft airdrop'
        ])
        self.publications = config.get('publications', [
            'coinmonks', 'coinsbench', 'coinbyte', 'cryptolinks', 'levelup-gitconnected'
        ])
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
    
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        从 Medium 获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        self.logger.info(f"从 Medium 获取 {count} 个项目")
        
        try:
            projects = []
            
            # 计算每个搜索词需要获取的文章数量
            terms_count = len(self.search_terms)
            articles_per_term = max(1, count // terms_count)
            
            # 搜索文章
            for term in self.search_terms:
                try:
                    # 构建搜索URL
                    search_url = f"https://medium.com/search?q={term}"
                    
                    # 随机选择一个 User-Agent
                    headers = {
                        'User-Agent': random.choice(self.user_agents)
                    }
                    
                    # 发送请求
                    response = requests.get(search_url, headers=headers, timeout=self.timeout)
                    response.raise_for_status()
                    
                    # 解析 HTML
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 查找文章
                    article_elements = soup.select('article')
                    
                    for article_element in article_elements[:articles_per_term]:
                        try:
                            # 获取文章标题
                            title_element = article_element.select_one('h2')
                            title = title_element.get_text(strip=True) if title_element else "Unknown Title"
                            
                            # 获取文章链接
                            link_element = article_element.select_one('a[href^="https://medium.com"]')
                            article_url = link_element['href'] if link_element else None
                            
                            if not article_url:
                                continue
                            
                            # 获取文章作者
                            author_element = article_element.select_one('a[href*="/"]')
                            author = author_element.get_text(strip=True) if author_element else "Unknown Author"
                            
                            # 获取文章摘要
                            summary_element = article_element.select_one('p')
                            summary = summary_element.get_text(strip=True) if summary_element else ""
                            
                            # 获取文章内容
                            article_content = self._get_article_content(article_url, headers)
                            
                            # 提取项目信息
                            project_info = self._extract_project_info_from_article(title, summary, article_content, article_url)
                            
                            if project_info:
                                projects.append(project_info)
                                
                                # 如果已经获取到足够的项目，就停止
                                if len(projects) >= count:
                                    break
                        
                        except Exception as e:
                            self.logger.error(f"解析文章时出错: {str(e)}")
                
                except Exception as e:
                    self.logger.error(f"搜索文章 '{term}' 时出错: {str(e)}")
                
                # 如果已经获取到足够的项目，就停止
                if len(projects) >= count:
                    break
                
                # 避免请求过于频繁
                time.sleep(1)
            
            # 如果还没有足够的项目，尝试从特定出版物获取
            if len(projects) < count:
                for publication in self.publications:
                    try:
                        # 构建出版物URL
                        publication_url = f"https://medium.com/{publication}"
                        
                        # 随机选择一个 User-Agent
                        headers = {
                            'User-Agent': random.choice(self.user_agents)
                        }
                        
                        # 发送请求
                        response = requests.get(publication_url, headers=headers, timeout=self.timeout)
                        response.raise_for_status()
                        
                        # 解析 HTML
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # 查找文章
                        article_elements = soup.select('article')
                        
                        for article_element in article_elements[:articles_per_term]:
                            try:
                                # 获取文章标题
                                title_element = article_element.select_one('h2, h3')
                                title = title_element.get_text(strip=True) if title_element else "Unknown Title"
                                
                                # 获取文章链接
                                link_element = article_element.select_one('a[href^="https://medium.com"]')
                                article_url = link_element['href'] if link_element else None
                                
                                if not article_url:
                                    continue
                                
                                # 获取文章作者
                                author_element = article_element.select_one('a[href*="/"]')
                                author = author_element.get_text(strip=True) if author_element else "Unknown Author"
                                
                                # 获取文章摘要
                                summary_element = article_element.select_one('p')
                                summary = summary_element.get_text(strip=True) if summary_element else ""
                                
                                # 获取文章内容
                                article_content = self._get_article_content(article_url, headers)
                                
                                # 提取项目信息
                                project_info = self._extract_project_info_from_article(title, summary, article_content, article_url)
                                
                                if project_info:
                                    projects.append(project_info)
                                    
                                    # 如果已经获取到足够的项目，就停止
                                    if len(projects) >= count:
                                        break
                            
                            except Exception as e:
                                self.logger.error(f"解析文章时出错: {str(e)}")
                        
                        # 如果已经获取到足够的项目，就停止
                        if len(projects) >= count:
                            break
                        
                        # 避免请求过于频繁
                        time.sleep(1)
                    
                    except Exception as e:
                        self.logger.error(f"获取出版物 '{publication}' 的文章时出错: {str(e)}")
                    
                    # 如果已经获取到足够的项目，就停止
                    if len(projects) >= count:
                        break
            
            self.logger.info(f"从 Medium 获取到 {len(projects)} 个项目")
            return projects[:count]
        
        except Exception as e:
            self.logger.error(f"获取 Medium 项目时出错: {str(e)}")
            return []
    
    def _get_article_content(self, article_url: str, headers: Dict[str, str]) -> str:
        """
        获取文章内容
        
        Args:
            article_url: 文章URL
            headers: 请求头
            
        Returns:
            文章内容
        """
        try:
            # 发送请求
            response = requests.get(article_url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 获取文章内容
            content_elements = soup.select('article p')
            content = ' '.join([element.get_text(strip=True) for element in content_elements])
            
            return content
        
        except Exception as e:
            self.logger.error(f"获取文章内容时出错: {str(e)}")
            return ""
    
    def _extract_project_info_from_article(self, title: str, summary: str, content: str, article_url: str) -> Optional[Dict[str, Any]]:
        """
        从文章中提取项目信息
        
        Args:
            title: 文章标题
            summary: 文章摘要
            content: 文章内容
            article_url: 文章URL
            
        Returns:
            项目信息字典，如果无法提取则返回None
        """
        # 检查文章是否包含关键词
        keywords = ['airdrop', 'token', 'crypto', 'blockchain', 'defi', 'nft']
        combined_text = f"{title} {summary} {content}".lower()
        
        if not any(keyword in combined_text for keyword in keywords):
            return None
        
        # 提取项目名称
        name_match = re.search(r'(?i)(?:project|token|airdrop)[\s:]+([A-Za-z0-9\s]+)', combined_text)
        name = name_match.group(1).strip() if name_match else "Unknown Project"
        
        # 提取项目URL
        url_match = re.search(r'(?i)(?:website|site|join|register|claim|visit)[\s:]+\s*(https?://[^\s]+)', content)
        if not url_match:
            url_match = re.search(r'(https?://[^\s]+)', content)
        
        project_url = None
        if url_match:
            project_url = url_match.group(1)
        
        # 如果没有找到项目URL，尝试从文章中提取所有URL
        if not project_url:
            all_urls = re.findall(r'(https?://[^\s]+)', content)
            for url in all_urls:
                # 排除常见的非项目URL
                if not any(domain in url.lower() for domain in ['medium.com', 'twitter.com', 'facebook.com', 'instagram.com', 'linkedin.com', 'youtube.com']):
                    project_url = url
                    break
        
        if not project_url:
            return None
        
        # 创建项目信息
        project_info = {
            'name': name,
            'description': summary if summary else (content[:200] + ('...' if len(content) > 200 else '')),
            'url': project_url,
            'project_type': 'airdrop',
            'blockchain': 'other',
            'source_url': article_url,
            'discovery_source': 'medium',
            'social_channels': []
        }
        
        # 尝试识别区块链平台
        blockchain_keywords = {
            'ethereum': ['ethereum', 'eth', 'erc20', 'erc721'],
            'binance': ['binance', 'bsc', 'bnb', 'bep20'],
            'solana': ['solana', 'sol'],
            'polygon': ['polygon', 'matic'],
            'avalanche': ['avalanche', 'avax'],
            'arbitrum': ['arbitrum', 'arb'],
            'optimism': ['optimism', 'op'],
            'base': ['base'],
            'cosmos': ['cosmos', 'atom'],
            'polkadot': ['polkadot', 'dot'],
            'near': ['near'],
            'aptos': ['aptos', 'apt'],
            'sui': ['sui']
        }
        
        for blockchain, keywords in blockchain_keywords.items():
            if any(keyword in combined_text for keyword in keywords):
                project_info['blockchain'] = blockchain
                break
        
        # 提取社交媒体链接
        social_media_patterns = {
            'twitter': r'(?:twitter\.com|x\.com)/([^/\s]+)',
            'telegram': r'(?:t\.me|telegram\.me)/([^/\s]+)',
            'discord': r'(?:discord\.gg|discord\.com/invite)/([^/\s]+)',
            'github': r'github\.com/([^/\s]+)'
        }
        
        for platform, pattern in social_media_patterns.items():
            matches = re.findall(pattern, content)
            if matches:
                project_info['social_channels'].append({
                    'platform': platform,
                    'url': f"https://{platform}.com/{matches[0]}" if platform != 'telegram' else f"https://t.me/{matches[0]}",
                    'followers': None
                })
        
        # 提取项目类型
        if 'testnet' in combined_text:
            project_info['project_type'] = 'testnet'
        elif 'presale' in combined_text or 'ico' in combined_text or 'ido' in combined_text:
            project_info['project_type'] = 'presale'
        elif 'farming' in combined_text or 'yield' in combined_text:
            project_info['project_type'] = 'farming'
        elif 'staking' in combined_text:
            project_info['project_type'] = 'staking'
        
        return project_info