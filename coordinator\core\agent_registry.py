"""
智能体注册表

该模块提供智能体注册和管理功能，允许协调控制智能体跟踪所有已注册的智能体。
"""

import logging
from typing import Dict, Any, Optional, List


class AgentRegistry:
    """智能体注册表，用于管理所有已注册的智能体"""
    
    def __init__(self):
        """初始化智能体注册表"""
        self.agents: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)
        
    def register(self, agent_name: str, agent_instance: Any) -> None:
        """
        注册智能体
        
        Args:
            agent_name: 智能体名称
            agent_instance: 智能体实例
        """
        if agent_name in self.agents:
            self.logger.warning(f"智能体 '{agent_name}' 已存在，将被覆盖")
            
        self.agents[agent_name] = agent_instance
        self.logger.info(f"智能体 '{agent_name}' 已注册")
        
    def unregister(self, agent_name: str) -> bool:
        """
        注销智能体
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果智能体存在并被成功注销则返回True，否则返回False
        """
        if agent_name in self.agents:
            del self.agents[agent_name]
            self.logger.info(f"智能体 '{agent_name}' 已注销")
            return True
        
        self.logger.warning(f"尝试注销不存在的智能体 '{agent_name}'")
        return False
    
    def get(self, agent_name: str) -> Optional[Any]:
        """
        获取智能体实例
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            智能体实例，如果不存在则返回None
        """
        if agent_name not in self.agents:
            self.logger.warning(f"尝试获取不存在的智能体 '{agent_name}'")
            return None
        
        return self.agents[agent_name]
    
    def get_all(self) -> Dict[str, Any]:
        """
        获取所有已注册的智能体
        
        Returns:
            包含所有已注册智能体的字典，键为智能体名称，值为智能体实例
        """
        return self.agents.copy()
    
    def get_names(self) -> List[str]:
        """
        获取所有已注册的智能体名称
        
        Returns:
            包含所有已注册智能体名称的列表
        """
        return list(self.agents.keys())
    
    def exists(self, agent_name: str) -> bool:
        """
        检查智能体是否存在
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果智能体存在则返回True，否则返回False
        """
        return agent_name in self.agents
    
    def count(self) -> int:
        """
        获取已注册的智能体数量
        
        Returns:
            已注册的智能体数量
        """
        return len(self.agents)