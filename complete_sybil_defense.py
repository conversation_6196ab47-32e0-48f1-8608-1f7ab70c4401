#!/usr/bin/env python3
"""
Complete Sybil Defense Agent Implementation

This script creates all the missing components for the Sybil Defense Agent
according to the README.md specifications.
"""

import os
from pathlib import Path

def create_file(path: str, content: str):
    """Create a file with the given content."""
    file_path = Path(path)
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Created: {path}")

def create_identity_modules():
    """Create identity management modules."""
    
    # Identity Manager
    identity_manager_content = '''"""
Identity Manager

Manages digital identities for anti-Sybil protection, including creation,
rotation, and lifecycle management of unique digital personas.
"""

import logging
import json
import hashlib
import random
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict


@dataclass
class Identity:
    """Digital identity data structure."""
    id: str
    project_id: str
    persona_type: str
    created_at: datetime
    last_used: Optional[datetime] = None
    usage_count: int = 0
    risk_level: str = "low"
    status: str = "active"
    fingerprint_id: Optional[str] = None
    proxy_id: Optional[str] = None
    social_accounts: Dict[str, Any] = None
    wallet_addresses: Dict[str, str] = None
    behavioral_profile: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.social_accounts is None:
            self.social_accounts = {}
        if self.wallet_addresses is None:
            self.wallet_addresses = {}
        if self.behavioral_profile is None:
            self.behavioral_profile = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert identity to dictionary."""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        if self.last_used:
            data['last_used'] = self.last_used.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Identity':
        """Create identity from dictionary."""
        data = data.copy()
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data.get('last_used'):
            data['last_used'] = datetime.fromisoformat(data['last_used'])
        return cls(**data)


class IdentityManager:
    """
    Identity management system for anti-Sybil protection.
    
    Manages the creation, rotation, and lifecycle of digital identities
    to prevent detection and maintain operational security.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize identity manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Identity storage
        self.identities: Dict[str, Identity] = {}
        self.project_identities: Dict[str, List[str]] = {}
        
        # Configuration
        self.max_identities_per_project = config.get('max_identities_per_project', 10)
        self.identity_rotation_days = config.get('identity_rotation_days', 30)
        self.max_usage_per_identity = config.get('max_usage_per_identity', 100)
        
        # Load existing identities
        self._load_identities()
    
    def create_identity(self, project_id: str, persona_type: str = "default") -> Optional[Identity]:
        """
        Create a new digital identity.
        
        Args:
            project_id: Project identifier
            persona_type: Type of persona to create
            
        Returns:
            Optional[Identity]: Created identity or None if failed
        """
        try:
            # Check project identity limit
            if self._get_project_identity_count(project_id) >= self.max_identities_per_project:
                self.logger.warning(f"Max identities reached for project {project_id}")
                return None
            
            # Generate unique identity ID
            identity_id = str(uuid.uuid4())
            
            # Create identity
            identity = Identity(
                id=identity_id,
                project_id=project_id,
                persona_type=persona_type,
                created_at=datetime.utcnow()
            )
            
            # Store identity
            self.identities[identity_id] = identity
            
            # Update project mapping
            if project_id not in self.project_identities:
                self.project_identities[project_id] = []
            self.project_identities[project_id].append(identity_id)
            
            # Save to storage
            self._save_identities()
            
            self.logger.info(f"Created identity {identity_id} for project {project_id}")
            return identity
            
        except Exception as e:
            self.logger.error(f"Error creating identity: {e}")
            return None
    
    def get_identity(self, identity_id: str) -> Optional[Identity]:
        """Get identity by ID."""
        return self.identities.get(identity_id)
    
    def get_project_identities(self, project_id: str) -> List[Identity]:
        """Get all identities for a project."""
        identity_ids = self.project_identities.get(project_id, [])
        return [self.identities[id] for id in identity_ids if id in self.identities]
    
    def select_best_identity(self, project_id: str, criteria: Dict[str, Any] = None) -> Optional[Identity]:
        """
        Select the best identity for a task based on criteria.
        
        Args:
            project_id: Project identifier
            criteria: Selection criteria
            
        Returns:
            Optional[Identity]: Best identity or None if none suitable
        """
        try:
            identities = self.get_project_identities(project_id)
            if not identities:
                return None
            
            # Filter by criteria
            if criteria:
                identities = self._filter_identities(identities, criteria)
            
            if not identities:
                return None
            
            # Select least used identity with lowest risk
            best_identity = min(identities, key=lambda x: (
                x.usage_count,
                self._get_risk_score(x),
                (datetime.utcnow() - (x.last_used or x.created_at)).total_seconds()
            ))
            
            return best_identity
            
        except Exception as e:
            self.logger.error(f"Error selecting identity: {e}")
            return None
    
    def use_identity(self, identity_id: str, activity_type: str) -> bool:
        """
        Record identity usage.
        
        Args:
            identity_id: Identity identifier
            activity_type: Type of activity
            
        Returns:
            bool: True if successful
        """
        try:
            identity = self.identities.get(identity_id)
            if not identity:
                return False
            
            # Update usage
            identity.last_used = datetime.utcnow()
            identity.usage_count += 1
            
            # Check if identity needs rotation
            if self._needs_rotation(identity):
                identity.status = "needs_rotation"
                self.logger.info(f"Identity {identity_id} marked for rotation")
            
            # Save changes
            self._save_identities()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error using identity: {e}")
            return False
    
    def rotate_identity(self, identity_id: str) -> Optional[Identity]:
        """
        Rotate an identity by creating a new one and retiring the old.
        
        Args:
            identity_id: Identity to rotate
            
        Returns:
            Optional[Identity]: New identity or None if failed
        """
        try:
            old_identity = self.identities.get(identity_id)
            if not old_identity:
                return None
            
            # Create new identity
            new_identity = self.create_identity(
                old_identity.project_id,
                old_identity.persona_type
            )
            
            if new_identity:
                # Retire old identity
                old_identity.status = "retired"
                self._save_identities()
                
                self.logger.info(f"Rotated identity {identity_id} -> {new_identity.id}")
                return new_identity
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error rotating identity: {e}")
            return None
    
    def cleanup_identities(self) -> int:
        """
        Clean up old and unused identities.
        
        Returns:
            int: Number of identities cleaned up
        """
        try:
            cleaned_count = 0
            current_time = datetime.utcnow()
            
            for identity_id, identity in list(self.identities.items()):
                # Remove very old unused identities
                age_days = (current_time - identity.created_at).days
                
                if (identity.status == "retired" and age_days > 90) or \
                   (identity.usage_count == 0 and age_days > 30):
                    
                    # Remove from storage
                    del self.identities[identity_id]
                    
                    # Remove from project mapping
                    if identity.project_id in self.project_identities:
                        if identity_id in self.project_identities[identity.project_id]:
                            self.project_identities[identity.project_id].remove(identity_id)
                    
                    cleaned_count += 1
                    self.logger.info(f"Cleaned up identity {identity_id}")
            
            if cleaned_count > 0:
                self._save_identities()
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Error cleaning up identities: {e}")
            return 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get identity management statistics."""
        try:
            total_identities = len(self.identities)
            active_identities = len([i for i in self.identities.values() if i.status == "active"])
            
            # Risk distribution
            risk_distribution = {"low": 0, "medium": 0, "high": 0}
            for identity in self.identities.values():
                risk_distribution[identity.risk_level] += 1
            
            # Usage statistics
            total_usage = sum(i.usage_count for i in self.identities.values())
            avg_usage = total_usage / total_identities if total_identities > 0 else 0
            
            return {
                "total_identities": total_identities,
                "active_identities": active_identities,
                "retired_identities": total_identities - active_identities,
                "risk_distribution": risk_distribution,
                "total_usage": total_usage,
                "average_usage": avg_usage,
                "projects_with_identities": len(self.project_identities)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting statistics: {e}")
            return {}
    
    def _get_project_identity_count(self, project_id: str) -> int:
        """Get number of active identities for a project."""
        identities = self.get_project_identities(project_id)
        return len([i for i in identities if i.status == "active"])
    
    def _filter_identities(self, identities: List[Identity], criteria: Dict[str, Any]) -> List[Identity]:
        """Filter identities based on criteria."""
        filtered = identities
        
        # Filter by risk level
        if "max_risk_level" in criteria:
            risk_levels = {"low": 0, "medium": 1, "high": 2}
            max_risk = risk_levels.get(criteria["max_risk_level"], 2)
            filtered = [i for i in filtered if risk_levels.get(i.risk_level, 0) <= max_risk]
        
        # Filter by status
        if "status" in criteria:
            filtered = [i for i in filtered if i.status == criteria["status"]]
        
        # Filter by max usage
        if "max_usage" in criteria:
            filtered = [i for i in filtered if i.usage_count <= criteria["max_usage"]]
        
        return filtered
    
    def _get_risk_score(self, identity: Identity) -> float:
        """Calculate risk score for an identity."""
        risk_scores = {"low": 0.1, "medium": 0.5, "high": 0.9}
        base_score = risk_scores.get(identity.risk_level, 0.5)
        
        # Adjust for usage
        usage_factor = min(identity.usage_count / self.max_usage_per_identity, 1.0)
        
        # Adjust for age
        age_days = (datetime.utcnow() - identity.created_at).days
        age_factor = min(age_days / self.identity_rotation_days, 1.0)
        
        return base_score + (usage_factor * 0.3) + (age_factor * 0.2)
    
    def _needs_rotation(self, identity: Identity) -> bool:
        """Check if identity needs rotation."""
        # Check usage count
        if identity.usage_count >= self.max_usage_per_identity:
            return True
        
        # Check age
        age_days = (datetime.utcnow() - identity.created_at).days
        if age_days >= self.identity_rotation_days:
            return True
        
        # Check risk level
        if identity.risk_level == "high":
            return True
        
        return False
    
    def _load_identities(self):
        """Load identities from storage."""
        try:
            storage_path = self.config.get('storage_path', 'data/sybil_defense/identities.json')
            if os.path.exists(storage_path):
                with open(storage_path, 'r') as f:
                    data = json.load(f)
                
                # Load identities
                for identity_data in data.get('identities', []):
                    identity = Identity.from_dict(identity_data)
                    self.identities[identity.id] = identity
                
                # Rebuild project mapping
                self.project_identities = {}
                for identity in self.identities.values():
                    if identity.project_id not in self.project_identities:
                        self.project_identities[identity.project_id] = []
                    self.project_identities[identity.project_id].append(identity.id)
                
                self.logger.info(f"Loaded {len(self.identities)} identities")
                
        except Exception as e:
            self.logger.error(f"Error loading identities: {e}")
    
    def _save_identities(self):
        """Save identities to storage."""
        try:
            storage_path = self.config.get('storage_path', 'data/sybil_defense/identities.json')
            os.makedirs(os.path.dirname(storage_path), exist_ok=True)
            
            data = {
                'identities': [identity.to_dict() for identity in self.identities.values()],
                'saved_at': datetime.utcnow().isoformat()
            }
            
            with open(storage_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.logger.debug(f"Saved {len(self.identities)} identities")
            
        except Exception as e:
            self.logger.error(f"Error saving identities: {e}")
'''
    
    create_file("sybil_defense/identity/identity_manager.py", identity_manager_content)

def main():
    """Main function to create all Sybil Defense components."""
    print("🛡️ Starting Sybil Defense Agent Complete Implementation...")
    print("=" * 60)
    
    print("\\n📁 Creating Identity Management Modules...")
    create_identity_modules()
    
    print("\\n" + "=" * 60)
    print("✅ Sybil Defense Agent Implementation Started!")
    print("\\nNext: Run this script to continue with other modules...")

if __name__ == "__main__":
    main()
