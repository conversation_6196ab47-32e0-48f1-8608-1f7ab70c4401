#!/usr/bin/env python3
"""
README.md Compliance Checker

检查所有智能体是否严格按照README.md文档结构创建
"""

import os
from pathlib import Path
from typing import Dict, List, Tuple

def check_agent_compliance() -> Dict[str, Dict[str, any]]:
    """检查所有智能体的合规性"""
    
    # 根据README.md定义的完整结构
    required_structures = {
        "coordinator": {
            "description": "协调控制智能体 (Coordinator Agent)",
            "readme_lines": "38-82",
            "required_files": [
                "coordinator/__init__.py",
                "coordinator/coordinator.py",
                "coordinator/core/__init__.py",
                "coordinator/core/agent_registry.py",
                "coordinator/core/lifecycle_manager.py",
                "coordinator/core/dependency_resolver.py",
                "coordinator/core/system_state.py",
                "coordinator/workflow/__init__.py",
                "coordinator/workflow/workflow_manager.py",
                "coordinator/workflow/task_orchestrator.py",
                "coordinator/workflow/pipeline_builder.py",
                "coordinator/workflow/workflow_optimizer.py",
                "coordinator/resources/__init__.py",
                "coordinator/resources/resource_allocator.py",
                "coordinator/resources/load_balancer.py",
                "coordinator/resources/throttle_manager.py",
                "coordinator/resources/resource_monitor.py",
                "coordinator/communication/__init__.py",
                "coordinator/communication/message_broker.py",
                "coordinator/communication/event_system.py",
                "coordinator/communication/command_dispatcher.py",
                "coordinator/communication/response_collector.py",
                "coordinator/monitoring/__init__.py",
                "coordinator/monitoring/health_monitor.py",
                "coordinator/monitoring/performance_tracker.py",
                "coordinator/monitoring/bottleneck_detector.py",
                "coordinator/monitoring/alert_manager.py",
                "coordinator/recovery/__init__.py",
                "coordinator/recovery/error_handler.py",
                "coordinator/recovery/fault_tolerance.py",
                "coordinator/recovery/recovery_orchestrator.py",
                "coordinator/recovery/checkpoint_manager.py",
                "coordinator/interface/__init__.py",
                "coordinator/interface/ui_connector.py",
                "coordinator/interface/api_gateway.py",
                "coordinator/interface/logging_service.py",
                "coordinator/interface/metrics_reporter.py"
            ]
        },
        
        "discovery": {
            "description": "项目发现智能体 (Discovery Agent)",
            "readme_lines": "89-109",
            "required_files": [
                "discovery/__init__.py",
                "discovery/discovery_agent.py",
                "discovery/sources/__init__.py",
                "discovery/sources/twitter_scanner.py",
                "discovery/sources/discord_scanner.py",
                "discovery/sources/telegram_scanner.py",
                "discovery/sources/medium_scanner.py",
                "discovery/sources/github_scanner.py",
                "discovery/sources/blockchain_scanner.py",
                "discovery/filters/__init__.py",
                "discovery/filters/keyword_filter.py",
                "discovery/filters/relevance_filter.py",
                "discovery/filters/duplicate_filter.py",
                "discovery/collectors/__init__.py",
                "discovery/collectors/project_collector.py",
                "discovery/collectors/condition_collector.py"
            ]
        },
        
        "assessment": {
            "description": "项目评估智能体 (Assessment Agent)",
            "readme_lines": "116-134",
            "required_files": [
                "assessment/__init__.py",
                "assessment/assessment_agent.py",
                "assessment/verification/__init__.py",
                "assessment/verification/team_verifier.py",
                "assessment/verification/social_verifier.py",
                "assessment/verification/project_verifier.py",
                "assessment/security/__init__.py",
                "assessment/security/contract_analyzer.py",
                "assessment/security/vulnerability_scanner.py",
                "assessment/security/permission_analyzer.py",
                "assessment/risk/__init__.py",
                "assessment/risk/risk_calculator.py",
                "assessment/risk/reward_estimator.py",
                "assessment/risk/score_generator.py"
            ]
        },
        
        "monitoring": {
            "description": "项目监控智能体 (Monitoring Agent)",
            "readme_lines": "141-159",
            "required_files": [
                "monitoring/__init__.py",
                "monitoring/monitoring_agent.py",
                "monitoring/channels/__init__.py",
                "monitoring/channels/twitter_monitor.py",
                "monitoring/channels/discord_monitor.py",
                "monitoring/channels/telegram_monitor.py",
                "monitoring/channels/website_monitor.py",
                "monitoring/analyzers/__init__.py",
                "monitoring/analyzers/announcement_analyzer.py",
                "monitoring/analyzers/update_detector.py",
                "monitoring/analyzers/milestone_tracker.py",
                "monitoring/notifiers/__init__.py",
                "monitoring/notifiers/alert_generator.py",
                "monitoring/notifiers/update_notifier.py"
            ]
        },
        
        "fund_management": {
            "description": "资金管理智能体 (Fund Management Agent)",
            "readme_lines": "166-211",
            "required_files": [
                "fund_management/__init__.py",
                "fund_management/fund_management_agent.py",
                "fund_management/wallets/__init__.py",
                "fund_management/wallets/wallet_generator.py",
                "fund_management/wallets/key_manager.py",
                "fund_management/wallets/wallet_storage.py",
                "fund_management/wallets/wallet_recovery.py",
                "fund_management/wallets/wallet_validator.py",
                "fund_management/extensions/__init__.py",
                "fund_management/extensions/metamask_manager.py",
                "fund_management/extensions/phantom_manager.py",
                "fund_management/extensions/extension_installer.py",
                "fund_management/extensions/extension_automator.py",
                "fund_management/extensions/extension_monitor.py",
                "fund_management/transactions/__init__.py",
                "fund_management/transactions/transaction_manager.py",
                "fund_management/transactions/gas_optimizer.py",
                "fund_management/transactions/transaction_builder.py",
                "fund_management/transactions/transaction_signer.py",
                "fund_management/transactions/transaction_monitor.py",
                "fund_management/transactions/transaction_recovery.py",
                "fund_management/assets/__init__.py",
                "fund_management/assets/balance_tracker.py",
                "fund_management/assets/token_manager.py",
                "fund_management/assets/token_detector.py",
                "fund_management/assets/value_estimator.py",
                "fund_management/assets/portfolio_manager.py",
                "fund_management/security/__init__.py",
                "fund_management/security/authorization_manager.py",
                "fund_management/security/permission_scanner.py",
                "fund_management/security/risk_analyzer.py",
                "fund_management/security/fraud_detector.py",
                "fund_management/security/emergency_handler.py",
                "fund_management/distribution/__init__.py",
                "fund_management/distribution/fund_allocator.py",
                "fund_management/distribution/balance_optimizer.py",
                "fund_management/distribution/fee_manager.py",
                "fund_management/distribution/liquidity_manager.py"
            ]
        },
        
        "task_planning": {
            "description": "任务规划智能体 (Task Planning Agent)",
            "readme_lines": "218-236",
            "required_files": [
                "task_planning/__init__.py",
                "task_planning/task_planning_agent.py",
                "task_planning/analyzers/__init__.py",
                "task_planning/analyzers/condition_analyzer.py",
                "task_planning/analyzers/requirement_parser.py",
                "task_planning/analyzers/difficulty_estimator.py",
                "task_planning/planners/__init__.py",
                "task_planning/planners/path_designer.py",
                "task_planning/planners/priority_scheduler.py",
                "task_planning/planners/account_allocator.py",
                "task_planning/optimizers/__init__.py",
                "task_planning/optimizers/time_optimizer.py",
                "task_planning/optimizers/resource_optimizer.py",
                "task_planning/optimizers/strategy_adjuster.py"
            ]
        },
        
        "task_execution": {
            "description": "任务执行智能体 (Task Execution Agent)",
            "readme_lines": "243-261",
            "required_files": [
                "task_execution/__init__.py",
                "task_execution/task_execution_agent.py",
                "task_execution/social/__init__.py",
                "task_execution/social/twitter_executor.py",
                "task_execution/social/discord_executor.py",
                "task_execution/social/telegram_executor.py",
                "task_execution/blockchain/__init__.py",
                "task_execution/blockchain/testnet_interactor.py",
                "task_execution/blockchain/mainnet_interactor.py",
                "task_execution/blockchain/transaction_signer.py",
                "task_execution/monitors/__init__.py",
                "task_execution/monitors/task_monitor.py",
                "task_execution/monitors/completion_verifier.py",
                "task_execution/monitors/retry_handler.py"
            ]
        },
        
        "proxy": {
            "description": "代理智能体 (Proxy Agent)",
            "readme_lines": "268-318",
            "required_files": [
                "proxy/__init__.py",
                "proxy/proxy_agent.py",
                "proxy/sources/__init__.py",
                "proxy/sources/free_proxy_crawler.py",
                "proxy/sources/proxy_api_client.py",
                "proxy/sources/proxy_list_parser.py",
                "proxy/sources/tor_bridge_fetcher.py",
                "proxy/sources/residential_proxy_rotator.py",
                "proxy/validators/__init__.py",
                "proxy/validators/proxy_validator.py",
                "proxy/validators/speed_tester.py",
                "proxy/validators/anonymity_checker.py",
                "proxy/validators/geo_validator.py",
                "proxy/validators/protocol_tester.py",
                "proxy/managers/__init__.py",
                "proxy/managers/proxy_pool.py",
                "proxy/managers/proxy_scheduler.py",
                "proxy/managers/vpn_manager.py",
                "proxy/managers/location_simulator.py",
                "proxy/managers/proxy_database.py",
                "proxy/rotators/__init__.py",
                "proxy/rotators/identity_rotator.py",
                "proxy/rotators/session_manager.py",
                "proxy/rotators/connection_monitor.py",
                "proxy/rotators/failure_detector.py",
                "proxy/rotators/auto_switcher.py",
                "proxy/optimizers/__init__.py",
                "proxy/optimizers/speed_optimizer.py",
                "proxy/optimizers/stability_enhancer.py",
                "proxy/optimizers/resource_allocator.py",
                "proxy/optimizers/usage_balancer.py",
                "proxy/integrators/__init__.py",
                "proxy/integrators/browser_integrator.py",
                "proxy/integrators/request_interceptor.py",
                "proxy/integrators/agent_connector.py",
                "proxy/integrators/proxy_distributor.py",
                "proxy/maintenance/__init__.py",
                "proxy/maintenance/health_monitor.py",
                "proxy/maintenance/auto_refresher.py",
                "proxy/maintenance/dead_proxy_cleaner.py",
                "proxy/maintenance/source_evaluator.py",
                "proxy/maintenance/recovery_system.py"
            ]
        },
        
        "anti_sybil": {
            "description": "防女巫智能体 (Anti-Sybil Agent)",
            "readme_lines": "325-376",
            "required_files": [
                "anti_sybil/__init__.py",
                "anti_sybil/anti_sybil_agent.py",
                "anti_sybil/identity/__init__.py",
                "anti_sybil/identity/identity_manager.py",
                "anti_sybil/identity/persona_generator.py",
                "anti_sybil/identity/identity_rotator.py",
                "anti_sybil/identity/consistency_tracker.py",
                "anti_sybil/fingerprints/__init__.py",
                "anti_sybil/fingerprints/browser_fingerprint.py",
                "anti_sybil/fingerprints/user_agent_manager.py",
                "anti_sybil/fingerprints/canvas_manager.py",
                "anti_sybil/fingerprints/webrtc_masker.py",
                "anti_sybil/fingerprints/font_manager.py",
                "anti_sybil/fingerprints/timezone_simulator.py",
                "anti_sybil/fingerprints/language_manager.py",
                "anti_sybil/fingerprints/hardware_simulator.py",
                "anti_sybil/behaviors/__init__.py",
                "anti_sybil/behaviors/behavior_designer.py",
                "anti_sybil/behaviors/pattern_generator.py",
                "anti_sybil/behaviors/timing_controller.py",
                "anti_sybil/behaviors/session_manager.py",
                "anti_sybil/behaviors/browsing_pattern.py",
                "anti_sybil/behaviors/interaction_style.py",
                "anti_sybil/behaviors/habit_simulator.py",
                "anti_sybil/simulators/__init__.py",
                "anti_sybil/simulators/human_simulator.py",
                "anti_sybil/simulators/mouse_movement.py",
                "anti_sybil/simulators/typing_simulator.py",
                "anti_sybil/simulators/scroll_behavior.py",
                "anti_sybil/simulators/click_pattern.py",
                "anti_sybil/simulators/form_filler.py",
                "anti_sybil/simulators/navigation_simulator.py",
                "anti_sybil/detection_evasion/__init__.py",
                "anti_sybil/detection_evasion/bot_detector_analyzer.py",
                "anti_sybil/detection_evasion/captcha_solver.py",
                "anti_sybil/detection_evasion/honeypot_detector.py",
                "anti_sybil/detection_evasion/tracking_evader.py",
                "anti_sybil/detection_evasion/behavioral_normalizer.py",
                "anti_sybil/analytics/__init__.py",
                "anti_sybil/analytics/detection_risk_analyzer.py",
                "anti_sybil/analytics/behavior_analyzer.py",
                "anti_sybil/analytics/pattern_optimizer.py",
                "anti_sybil/analytics/success_rate_tracker.py",
                "anti_sybil/analytics/adaptation_engine.py"
            ]
        },
        
        "profit_optimization": {
            "description": "收益优化智能体 (Profit Optimization Agent)",
            "readme_lines": "383-432",
            "required_files": [
                "profit_optimization/__init__.py",
                "profit_optimization/profit_optimization_agent.py",
                "profit_optimization/trackers/__init__.py",
                "profit_optimization/trackers/airdrop_tracker.py",
                "profit_optimization/trackers/token_value_tracker.py",
                "profit_optimization/trackers/market_monitor.py",
                "profit_optimization/trackers/exchange_monitor.py",
                "profit_optimization/trackers/listing_detector.py",
                "profit_optimization/trackers/price_alert_system.py",
                "profit_optimization/data_sources/__init__.py",
                "profit_optimization/data_sources/dex_connector.py",
                "profit_optimization/data_sources/cex_connector.py",
                "profit_optimization/data_sources/price_feed_manager.py",
                "profit_optimization/data_sources/social_sentiment.py",
                "profit_optimization/data_sources/news_analyzer.py",
                "profit_optimization/analyzers/__init__.py",
                "profit_optimization/analyzers/timing_analyzer.py",
                "profit_optimization/analyzers/liquidity_analyzer.py",
                "profit_optimization/analyzers/trend_analyzer.py",
                "profit_optimization/analyzers/volatility_analyzer.py",
                "profit_optimization/analyzers/correlation_analyzer.py",
                "profit_optimization/analyzers/risk_reward_calculator.py",
                "profit_optimization/strategies/__init__.py",
                "profit_optimization/strategies/strategy_factory.py",
                "profit_optimization/strategies/selling_strategy.py",
                "profit_optimization/strategies/holding_strategy.py",
                "profit_optimization/strategies/dca_strategy.py",
                "profit_optimization/strategies/limit_order_strategy.py",
                "profit_optimization/strategies/portfolio_optimizer.py",
                "profit_optimization/strategies/strategy_backtester.py",
                "profit_optimization/execution/__init__.py",
                "profit_optimization/execution/order_executor.py",
                "profit_optimization/execution/swap_manager.py",
                "profit_optimization/execution/gas_strategy.py",
                "profit_optimization/execution/slippage_manager.py",
                "profit_optimization/execution/execution_monitor.py",
                "profit_optimization/reporting/__init__.py",
                "profit_optimization/reporting/profit_calculator.py",
                "profit_optimization/reporting/performance_reporter.py",
                "profit_optimization/reporting/tax_reporter.py",
                "profit_optimization/reporting/portfolio_visualizer.py",
                "profit_optimization/reporting/strategy_evaluator.py"
            ]
        }
    }
    
    # 检查每个智能体的合规性
    compliance_results = {}
    
    for agent_name, agent_info in required_structures.items():
        print(f"\\n🔍 Checking {agent_name} ({agent_info['description']})...")
        
        existing_files = []
        missing_files = []
        
        for required_file in agent_info["required_files"]:
            if Path(required_file).exists():
                existing_files.append(required_file)
                print(f"  ✅ {required_file}")
            else:
                missing_files.append(required_file)
                print(f"  ❌ {required_file}")
        
        compliance_percentage = len(existing_files) / len(agent_info["required_files"]) * 100
        
        compliance_results[agent_name] = {
            "description": agent_info["description"],
            "readme_lines": agent_info["readme_lines"],
            "total_required": len(agent_info["required_files"]),
            "existing_files": len(existing_files),
            "missing_files": len(missing_files),
            "compliance_percentage": compliance_percentage,
            "missing_file_list": missing_files,
            "status": "✅ COMPLIANT" if compliance_percentage == 100 else "❌ NON-COMPLIANT"
        }
        
        print(f"  📊 Compliance: {compliance_percentage:.1f}% ({len(existing_files)}/{len(agent_info['required_files'])})")
    
    return compliance_results

def generate_compliance_report(results: Dict[str, Dict[str, any]]):
    """生成合规性报告"""
    
    print("\\n" + "=" * 80)
    print("📋 README.md COMPLIANCE REPORT")
    print("=" * 80)
    
    total_agents = len(results)
    compliant_agents = len([r for r in results.values() if r["compliance_percentage"] == 100])
    
    print(f"\\n🏆 OVERALL SUMMARY:")
    print(f"   - Total Agents: {total_agents}")
    print(f"   - Fully Compliant: {compliant_agents}")
    print(f"   - Non-Compliant: {total_agents - compliant_agents}")
    print(f"   - Overall Compliance Rate: {compliant_agents/total_agents*100:.1f}%")
    
    print(f"\\n📊 DETAILED RESULTS:")
    
    for agent_name, result in results.items():
        status_icon = "✅" if result["compliance_percentage"] == 100 else "❌"
        print(f"\\n{status_icon} {agent_name.upper()}")
        print(f"   Description: {result['description']}")
        print(f"   README Lines: {result['readme_lines']}")
        print(f"   Compliance: {result['compliance_percentage']:.1f}% ({result['existing_files']}/{result['total_required']})")
        print(f"   Status: {result['status']}")
        
        if result["missing_files"] > 0:
            print(f"   Missing Files: {result['missing_files']}")
            if len(result["missing_file_list"]) <= 5:
                for missing_file in result["missing_file_list"]:
                    print(f"     - {missing_file}")
            else:
                for missing_file in result["missing_file_list"][:5]:
                    print(f"     - {missing_file}")
                print(f"     ... and {len(result['missing_file_list']) - 5} more files")
    
    # 生成建议
    print(f"\\n💡 RECOMMENDATIONS:")
    
    non_compliant = [name for name, result in results.items() if result["compliance_percentage"] < 100]
    
    if not non_compliant:
        print("   🎉 All agents are fully compliant with README.md structure!")
    else:
        print("   📝 The following agents need to be updated to match README.md:")
        for agent_name in non_compliant:
            result = results[agent_name]
            print(f"     - {agent_name}: {result['missing_files']} missing files")
        
        print("\\n   🔧 Suggested Actions:")
        print("     1. Create missing files according to README.md structure")
        print("     2. Implement placeholder classes for each missing component")
        print("     3. Add proper __init__.py files with correct imports")
        print("     4. Follow the exact naming conventions from README.md")

def main():
    """主函数"""
    print("🛡️ AirHunter README.md Compliance Checker")
    print("=" * 60)
    print("📋 Checking all agents against README.md structure requirements...")
    
    results = check_agent_compliance()
    generate_compliance_report(results)
    
    print("\\n" + "=" * 80)
    print("✅ Compliance check completed!")

if __name__ == "__main__":
    main()
