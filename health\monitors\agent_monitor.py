"""
Agent Monitor

Monitors the health and performance of all AirHunter agents,
tracking their status, resource usage, and operational metrics.
"""

import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import psutil


class AgentStatus(Enum):
    """Agent status enumeration."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    OFFLINE = "offline"
    STARTING = "starting"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class AgentMetrics:
    """Agent performance metrics."""
    agent_id: str
    agent_name: str
    status: AgentStatus = AgentStatus.OFFLINE
    
    # Performance metrics
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    memory_mb: float = 0.0
    
    # Operational metrics
    tasks_completed: int = 0
    tasks_failed: int = 0
    tasks_pending: int = 0
    uptime_seconds: float = 0.0
    
    # Health indicators
    last_heartbeat: Optional[datetime] = None
    error_count: int = 0
    warning_count: int = 0
    
    # Response times
    avg_response_time: float = 0.0
    max_response_time: float = 0.0
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def update_timestamp(self):
        """Update the last updated timestamp."""
        self.updated_at = datetime.utcnow()
    
    def is_responsive(self, timeout_seconds: int = 60) -> bool:
        """
        Check if agent is responsive based on last heartbeat.
        
        Args:
            timeout_seconds: Timeout for considering agent unresponsive
            
        Returns:
            bool: True if agent is responsive
        """
        if not self.last_heartbeat:
            return False
        
        return (datetime.utcnow() - self.last_heartbeat).total_seconds() < timeout_seconds
    
    def get_success_rate(self) -> float:
        """
        Calculate task success rate.
        
        Returns:
            float: Success rate (0.0 to 1.0)
        """
        total_tasks = self.tasks_completed + self.tasks_failed
        if total_tasks == 0:
            return 1.0
        return self.tasks_completed / total_tasks
    
    def get_health_score(self) -> float:
        """
        Calculate overall health score.
        
        Returns:
            float: Health score (0.0 to 1.0)
        """
        if self.status == AgentStatus.OFFLINE:
            return 0.0
        
        # Base score from status
        status_scores = {
            AgentStatus.HEALTHY: 1.0,
            AgentStatus.WARNING: 0.7,
            AgentStatus.CRITICAL: 0.3,
            AgentStatus.ERROR: 0.1,
            AgentStatus.STARTING: 0.5,
            AgentStatus.STOPPING: 0.2
        }
        
        base_score = status_scores.get(self.status, 0.0)
        
        # Adjust for performance metrics
        cpu_penalty = max(0, (self.cpu_usage - 80) / 20) * 0.2  # Penalty for high CPU
        memory_penalty = max(0, (self.memory_usage - 80) / 20) * 0.2  # Penalty for high memory
        
        # Adjust for success rate
        success_bonus = self.get_success_rate() * 0.2
        
        # Adjust for responsiveness
        responsive_bonus = 0.1 if self.is_responsive() else -0.3
        
        final_score = base_score - cpu_penalty - memory_penalty + success_bonus + responsive_bonus
        return max(0.0, min(1.0, final_score))


class AgentMonitor:
    """
    Agent monitoring system.
    
    Tracks the health, performance, and status of all AirHunter agents
    with real-time monitoring and alerting capabilities.
    """
    
    def __init__(self, check_interval: float = 30.0, heartbeat_timeout: int = 120):
        """
        Initialize agent monitor.
        
        Args:
            check_interval: Interval between health checks (seconds)
            heartbeat_timeout: Timeout for agent heartbeats (seconds)
        """
        self.check_interval = check_interval
        self.heartbeat_timeout = heartbeat_timeout
        self.logger = logging.getLogger(__name__)
        
        # Agent tracking
        self._agents: Dict[str, AgentMetrics] = {}
        self._agent_callbacks: Dict[str, List[Callable]] = {}
        
        # Monitoring control
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
        
        # Alert thresholds
        self.cpu_warning_threshold = 70.0
        self.cpu_critical_threshold = 90.0
        self.memory_warning_threshold = 70.0
        self.memory_critical_threshold = 90.0
        self.error_rate_threshold = 0.1
        
        # Statistics
        self._stats = {
            "total_agents": 0,
            "healthy_agents": 0,
            "warning_agents": 0,
            "critical_agents": 0,
            "offline_agents": 0,
            "total_checks": 0,
            "last_check": None
        }
    
    def start_monitoring(self):
        """Start agent monitoring."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            name="AgentMonitor",
            daemon=True
        )
        self._monitor_thread.start()
        
        self.logger.info("Agent monitoring started")
    
    def stop_monitoring(self):
        """Stop agent monitoring."""
        if not self._monitoring:
            return
        
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        
        self.logger.info("Agent monitoring stopped")
    
    def register_agent(self, agent_id: str, agent_name: str) -> AgentMetrics:
        """
        Register an agent for monitoring.
        
        Args:
            agent_id: Unique agent identifier
            agent_name: Human-readable agent name
            
        Returns:
            AgentMetrics: Agent metrics object
        """
        with self._lock:
            if agent_id not in self._agents:
                metrics = AgentMetrics(
                    agent_id=agent_id,
                    agent_name=agent_name,
                    status=AgentStatus.STARTING
                )
                self._agents[agent_id] = metrics
                self._agent_callbacks[agent_id] = []
                
                self.logger.info(f"Registered agent: {agent_name} ({agent_id})")
                return metrics
            else:
                return self._agents[agent_id]
    
    def unregister_agent(self, agent_id: str):
        """
        Unregister an agent from monitoring.
        
        Args:
            agent_id: Agent identifier to unregister
        """
        with self._lock:
            if agent_id in self._agents:
                agent_name = self._agents[agent_id].agent_name
                del self._agents[agent_id]
                del self._agent_callbacks[agent_id]
                
                self.logger.info(f"Unregistered agent: {agent_name} ({agent_id})")
    
    def update_agent_heartbeat(self, agent_id: str):
        """
        Update agent heartbeat timestamp.
        
        Args:
            agent_id: Agent identifier
        """
        with self._lock:
            if agent_id in self._agents:
                self._agents[agent_id].last_heartbeat = datetime.utcnow()
                self._agents[agent_id].update_timestamp()
    
    def update_agent_metrics(self, agent_id: str, **metrics):
        """
        Update agent performance metrics.
        
        Args:
            agent_id: Agent identifier
            **metrics: Metric values to update
        """
        with self._lock:
            if agent_id in self._agents:
                agent = self._agents[agent_id]
                
                # Update provided metrics
                for key, value in metrics.items():
                    if hasattr(agent, key):
                        setattr(agent, key, value)
                
                agent.update_timestamp()
                
                # Update status based on metrics
                self._update_agent_status(agent)
    
    def update_agent_task_stats(self, agent_id: str, completed: int = 0, 
                               failed: int = 0, pending: int = 0):
        """
        Update agent task statistics.
        
        Args:
            agent_id: Agent identifier
            completed: Number of completed tasks to add
            failed: Number of failed tasks to add
            pending: Current number of pending tasks
        """
        with self._lock:
            if agent_id in self._agents:
                agent = self._agents[agent_id]
                agent.tasks_completed += completed
                agent.tasks_failed += failed
                agent.tasks_pending = pending
                agent.update_timestamp()
                
                # Update status based on task performance
                self._update_agent_status(agent)
    
    def get_agent_metrics(self, agent_id: str) -> Optional[AgentMetrics]:
        """
        Get agent metrics.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            Optional[AgentMetrics]: Agent metrics or None if not found
        """
        with self._lock:
            return self._agents.get(agent_id)
    
    def get_all_agents(self) -> Dict[str, AgentMetrics]:
        """
        Get all agent metrics.
        
        Returns:
            Dict[str, AgentMetrics]: All agent metrics
        """
        with self._lock:
            return self._agents.copy()
    
    def get_agents_by_status(self, status: AgentStatus) -> List[AgentMetrics]:
        """
        Get agents by status.
        
        Args:
            status: Agent status to filter by
            
        Returns:
            List[AgentMetrics]: Agents with specified status
        """
        with self._lock:
            return [agent for agent in self._agents.values() 
                   if agent.status == status]
    
    def add_status_callback(self, agent_id: str, callback: Callable[[AgentMetrics], None]):
        """
        Add callback for agent status changes.
        
        Args:
            agent_id: Agent identifier
            callback: Callback function to call on status change
        """
        with self._lock:
            if agent_id in self._agent_callbacks:
                self._agent_callbacks[agent_id].append(callback)
    
    def get_system_health_summary(self) -> Dict[str, Any]:
        """
        Get system health summary.
        
        Returns:
            Dict[str, Any]: System health summary
        """
        with self._lock:
            healthy = len([a for a in self._agents.values() if a.status == AgentStatus.HEALTHY])
            warning = len([a for a in self._agents.values() if a.status == AgentStatus.WARNING])
            critical = len([a for a in self._agents.values() if a.status == AgentStatus.CRITICAL])
            offline = len([a for a in self._agents.values() if a.status == AgentStatus.OFFLINE])
            
            total_agents = len(self._agents)
            avg_health_score = 0.0
            if total_agents > 0:
                avg_health_score = sum(a.get_health_score() for a in self._agents.values()) / total_agents
            
            return {
                "total_agents": total_agents,
                "healthy_agents": healthy,
                "warning_agents": warning,
                "critical_agents": critical,
                "offline_agents": offline,
                "average_health_score": avg_health_score,
                "system_status": self._get_overall_system_status(),
                "last_check": self._stats["last_check"],
                "total_checks": self._stats["total_checks"]
            }
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self._monitoring:
            try:
                self._perform_health_check()
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
    
    def _perform_health_check(self):
        """Perform health check on all agents."""
        with self._lock:
            current_time = datetime.utcnow()
            
            for agent in self._agents.values():
                # Check responsiveness
                if not agent.is_responsive(self.heartbeat_timeout):
                    if agent.status != AgentStatus.OFFLINE:
                        agent.status = AgentStatus.OFFLINE
                        self._trigger_status_callbacks(agent)
                
                # Update uptime
                if agent.status != AgentStatus.OFFLINE:
                    agent.uptime_seconds = (current_time - agent.created_at).total_seconds()
                
                # Update overall status
                old_status = agent.status
                self._update_agent_status(agent)
                
                if old_status != agent.status:
                    self._trigger_status_callbacks(agent)
            
            # Update statistics
            self._update_statistics()
            self._stats["last_check"] = current_time
            self._stats["total_checks"] += 1
    
    def _update_agent_status(self, agent: AgentMetrics):
        """Update agent status based on current metrics."""
        if not agent.is_responsive(self.heartbeat_timeout):
            agent.status = AgentStatus.OFFLINE
            return
        
        # Check for critical conditions
        if (agent.cpu_usage > self.cpu_critical_threshold or
            agent.memory_usage > self.memory_critical_threshold or
            agent.get_success_rate() < (1.0 - self.error_rate_threshold)):
            agent.status = AgentStatus.CRITICAL
            return
        
        # Check for warning conditions
        if (agent.cpu_usage > self.cpu_warning_threshold or
            agent.memory_usage > self.memory_warning_threshold):
            agent.status = AgentStatus.WARNING
            return
        
        # Default to healthy
        agent.status = AgentStatus.HEALTHY
    
    def _trigger_status_callbacks(self, agent: AgentMetrics):
        """Trigger status change callbacks for an agent."""
        callbacks = self._agent_callbacks.get(agent.agent_id, [])
        for callback in callbacks:
            try:
                callback(agent)
            except Exception as e:
                self.logger.error(f"Status callback error for {agent.agent_id}: {e}")
    
    def _update_statistics(self):
        """Update monitoring statistics."""
        status_counts = {}
        for status in AgentStatus:
            status_counts[status] = len([a for a in self._agents.values() if a.status == status])
        
        self._stats.update({
            "total_agents": len(self._agents),
            "healthy_agents": status_counts[AgentStatus.HEALTHY],
            "warning_agents": status_counts[AgentStatus.WARNING],
            "critical_agents": status_counts[AgentStatus.CRITICAL],
            "offline_agents": status_counts[AgentStatus.OFFLINE]
        })
    
    def _get_overall_system_status(self) -> str:
        """Get overall system status."""
        if self._stats["critical_agents"] > 0:
            return "critical"
        elif self._stats["warning_agents"] > 0:
            return "warning"
        elif self._stats["healthy_agents"] > 0:
            return "healthy"
        else:
            return "offline"
