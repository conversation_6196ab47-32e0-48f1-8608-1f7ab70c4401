"""
Twitter Handler

This module handles Twitter-related tasks for the Task Execution Agent.
"""

import logging
import os
import json
import time
import random
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import tweepy

logger = logging.getLogger("TwitterHandler")

class TwitterHandler:
    """
    Handler for Twitter-related tasks.
    
    Capabilities:
    - Account registration and setup
    - Posting tweets
    - Liking and retweeting
    - Following accounts
    - Engagement with communities
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Twitter handler.
        
        Args:
            config: Configuration for the handler
        """
        self.config = config or {}
        self.api = None
        self.client = None
        self.authenticated = False
        
        # Initialize API if credentials are available
        if self._has_credentials():
            self._initialize_api()
        
        logger.info("Twitter handler initialized")
    
    def _has_credentials(self) -> bool:
        """
        Check if Twitter API credentials are available.
        
        Returns:
            bool: True if credentials are available, False otherwise
        """
        required_fields = ["api_key", "api_secret", "access_token", "access_token_secret"]
        return all(field in self.config for field in required_fields)
    
    def _initialize_api(self) -> bool:
        """
        Initialize the Twitter API client.
        
        Returns:
            bool: True if initialized successfully, False otherwise
        """
        try:
            # Set up authentication
            auth = tweepy.OAuth1UserHandler(
                self.config["api_key"],
                self.config["api_secret"],
                self.config["access_token"],
                self.config["access_token_secret"]
            )
            
            # Create API object
            self.api = tweepy.API(auth, wait_on_rate_limit=True)
            
            # Create Client object (v2 API)
            self.client = tweepy.Client(
                consumer_key=self.config["api_key"],
                consumer_secret=self.config["api_secret"],
                access_token=self.config["access_token"],
                access_token_secret=self.config["access_token_secret"]
            )
            
            # Test API connection
            self.api.verify_credentials()
            
            self.authenticated = True
            logger.info("Twitter API initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Twitter API: {e}")
            self.authenticated = False
            return False
    
    def register(self, **params) -> Dict[str, Any]:
        """
        Register a new Twitter account or set up an existing one.
        
        Args:
            **params: Parameters for registration
            
        Returns:
            Dict containing registration result
        """
        # This is a placeholder for the actual implementation
        # In a real implementation, this would involve browser automation
        # to register a new Twitter account or set up an existing one
        
        logger.info("Twitter registration requested")
        
        # Simulate registration process
        time.sleep(2)
        
        return {
            "success": True,
            "message": "Twitter registration simulated",
            "account_info": {
                "username": params.get("username", "test_user"),
                "registered_at": datetime.now().isoformat()
            }
        }
    
    def post(self, **params) -> Dict[str, Any]:
        """
        Post a tweet.
        
        Args:
            **params: Parameters for posting
                - text: Tweet text
                - media: Optional list of media paths
                
        Returns:
            Dict containing post result
        """
        if not self.authenticated:
            return {
                "success": False,
                "error": "Not authenticated"
            }
        
        try:
            text = params.get("text", "")
            media_paths = params.get("media", [])
            
            if not text and not media_paths:
                return {
                    "success": False,
                    "error": "Tweet must contain text or media"
                }
            
            # Upload media if provided
            media_ids = []
            if media_paths:
                for media_path in media_paths:
                    if os.path.exists(media_path):
                        media = self.api.media_upload(media_path)
                        media_ids.append(media.media_id)
            
            # Post tweet
            if media_ids:
                tweet = self.api.update_status(status=text, media_ids=media_ids)
            else:
                tweet = self.api.update_status(status=text)
            
            logger.info(f"Posted tweet: {tweet.id}")
            
            return {
                "success": True,
                "tweet_id": tweet.id,
                "tweet_url": f"https://twitter.com/user/status/{tweet.id}"
            }
        except Exception as e:
            logger.error(f"Error posting tweet: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def like(self, **params) -> Dict[str, Any]:
        """
        Like a tweet.
        
        Args:
            **params: Parameters for liking
                - tweet_id: ID of the tweet to like
                
        Returns:
            Dict containing like result
        """
        if not self.authenticated:
            return {
                "success": False,
                "error": "Not authenticated"
            }
        
        try:
            tweet_id = params.get("tweet_id")
            
            if not tweet_id:
                return {
                    "success": False,
                    "error": "Tweet ID is required"
                }
            
            # Like tweet
            self.api.create_favorite(tweet_id)
            
            logger.info(f"Liked tweet: {tweet_id}")
            
            return {
                "success": True,
                "tweet_id": tweet_id
            }
        except Exception as e:
            logger.error(f"Error liking tweet: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def retweet(self, **params) -> Dict[str, Any]:
        """
        Retweet a tweet.
        
        Args:
            **params: Parameters for retweeting
                - tweet_id: ID of the tweet to retweet
                
        Returns:
            Dict containing retweet result
        """
        if not self.authenticated:
            return {
                "success": False,
                "error": "Not authenticated"
            }
        
        try:
            tweet_id = params.get("tweet_id")
            
            if not tweet_id:
                return {
                    "success": False,
                    "error": "Tweet ID is required"
                }
            
            # Retweet
            retweet = self.api.retweet(tweet_id)
            
            logger.info(f"Retweeted tweet: {tweet_id}")
            
            return {
                "success": True,
                "tweet_id": tweet_id,
                "retweet_id": retweet.id
            }
        except Exception as e:
            logger.error(f"Error retweeting: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def follow(self, **params) -> Dict[str, Any]:
        """
        Follow a Twitter account.
        
        Args:
            **params: Parameters for following
                - username: Username to follow
                
        Returns:
            Dict containing follow result
        """
        if not self.authenticated:
            return {
                "success": False,
                "error": "Not authenticated"
            }
        
        try:
            username = params.get("username")
            
            if not username:
                return {
                    "success": False,
                    "error": "Username is required"
                }
            
            # Follow user
            user = self.api.create_friendship(screen_name=username)
            
            logger.info(f"Followed user: {username}")
            
            return {
                "success": True,
                "username": username,
                "user_id": user.id
            }
        except Exception as e:
            logger.error(f"Error following user: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def engage(self, **params) -> Dict[str, Any]:
        """
        Engage with Twitter content.
        
        Args:
            **params: Parameters for engagement
                - engagement_type: Type of engagement (like_retweet, reply, etc.)
                - target: Target account or hashtag
                - count: Number of engagements to perform
                
        Returns:
            Dict containing engagement result
        """
        if not self.authenticated:
            return {
                "success": False,
                "error": "Not authenticated"
            }
        
        try:
            engagement_type = params.get("engagement_type", "like_retweet")
            target = params.get("target")
            count = params.get("count", 3)
            
            if not target:
                return {
                    "success": False,
                    "error": "Target is required"
                }
            
            # Get tweets to engage with
            if target.startswith("#"):
                # Search for hashtag
                tweets = self.api.search_tweets(q=target, count=count*2, result_type="recent")
            else:
                # Get user timeline
                tweets = self.api.user_timeline(screen_name=target, count=count*2)
            
            # Limit to requested count
            tweets = tweets[:count]
            
            # Perform engagement
            results = []
            
            for tweet in tweets:
                tweet_id = tweet.id
                
                if engagement_type == "like" or engagement_type == "like_retweet":
                    # Like the tweet
                    try:
                        self.api.create_favorite(tweet_id)
                        results.append({
                            "action": "like",
                            "tweet_id": tweet_id,
                            "success": True
                        })
                    except Exception as e:
                        results.append({
                            "action": "like",
                            "tweet_id": tweet_id,
                            "success": False,
                            "error": str(e)
                        })
                
                if engagement_type == "retweet" or engagement_type == "like_retweet":
                    # Retweet
                    try:
                        self.api.retweet(tweet_id)
                        results.append({
                            "action": "retweet",
                            "tweet_id": tweet_id,
                            "success": True
                        })
                    except Exception as e:
                        results.append({
                            "action": "retweet",
                            "tweet_id": tweet_id,
                            "success": False,
                            "error": str(e)
                        })
                
                if engagement_type == "reply":
                    # Reply to the tweet
                    try:
                        reply_text = params.get("reply_text", f"Great tweet! #{random.randint(1000, 9999)}")
                        reply = self.api.update_status(
                            status=reply_text,
                            in_reply_to_status_id=tweet_id,
                            auto_populate_reply_metadata=True
                        )
                        results.append({
                            "action": "reply",
                            "tweet_id": tweet_id,
                            "reply_id": reply.id,
                            "success": True
                        })
                    except Exception as e:
                        results.append({
                            "action": "reply",
                            "tweet_id": tweet_id,
                            "success": False,
                            "error": str(e)
                        })
            
            logger.info(f"Performed {len(results)} Twitter engagements")
            
            return {
                "success": True,
                "engagement_count": len(results),
                "results": results
            }
        except Exception as e:
            logger.error(f"Error performing Twitter engagement: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def check_mentions(self, **params) -> Dict[str, Any]:
        """
        Check for mentions of the authenticated user.
        
        Args:
            **params: Parameters for checking mentions
                - count: Number of mentions to retrieve
                - since_id: Only return mentions newer than this ID
                
        Returns:
            Dict containing mentions
        """
        if not self.authenticated:
            return {
                "success": False,
                "error": "Not authenticated"
            }
        
        try:
            count = params.get("count", 10)
            since_id = params.get("since_id")
            
            # Get mentions
            if since_id:
                mentions = self.api.mentions_timeline(count=count, since_id=since_id)
            else:
                mentions = self.api.mentions_timeline(count=count)
            
            # Format results
            mention_data = []
            for mention in mentions:
                mention_data.append({
                    "id": mention.id,
                    "text": mention.text,
                    "created_at": mention.created_at.isoformat(),
                    "user": mention.user.screen_name,
                    "user_id": mention.user.id
                })
            
            logger.info(f"Retrieved {len(mention_data)} mentions")
            
            return {
                "success": True,
                "mentions": mention_data,
                "count": len(mention_data)
            }
        except Exception as e:
            logger.error(f"Error checking mentions: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def search(self, **params) -> Dict[str, Any]:
        """
        Search for tweets.
        
        Args:
            **params: Parameters for searching
                - query: Search query
                - count: Number of results to retrieve
                - result_type: Type of results (recent, popular, mixed)
                
        Returns:
            Dict containing search results
        """
        if not self.authenticated:
            return {
                "success": False,
                "error": "Not authenticated"
            }
        
        try:
            query = params.get("query")
            count = params.get("count", 10)
            result_type = params.get("result_type", "recent")
            
            if not query:
                return {
                    "success": False,
                    "error": "Query is required"
                }
            
            # Search tweets
            tweets = self.api.search_tweets(
                q=query,
                count=count,
                result_type=result_type,
                tweet_mode="extended"
            )
            
            # Format results
            tweet_data = []
            for tweet in tweets:
                tweet_data.append({
                    "id": tweet.id,
                    "text": tweet.full_text if hasattr(tweet, "full_text") else tweet.text,
                    "created_at": tweet.created_at.isoformat(),
                    "user": tweet.user.screen_name,
                    "user_id": tweet.user.id,
                    "likes": tweet.favorite_count,
                    "retweets": tweet.retweet_count
                })
            
            logger.info(f"Retrieved {len(tweet_data)} tweets for query: {query}")
            
            return {
                "success": True,
                "tweets": tweet_data,
                "count": len(tweet_data),
                "query": query
            }
        except Exception as e:
            logger.error(f"Error searching tweets: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def close(self) -> None:
        """Close the handler and clean up resources."""
        logger.info("Closing Twitter handler")
        # No specific cleanup needed for Twitter API


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    
    # Create a config with your Twitter API credentials
    config = {
        "api_key": "YOUR_API_KEY",
        "api_secret": "YOUR_API_SECRET",
        "access_token": "YOUR_ACCESS_TOKEN",
        "access_token_secret": "YOUR_ACCESS_TOKEN_SECRET"
    }
    
    # Initialize handler
    handler = TwitterHandler(config)
    
    # Test search
    result = handler.search(query="#ethereum", count=5)
    print(f"Search result: {result}")
    
    # Close handler
    handler.close()