"""
Connection pool manager

This module provides connection pooling functionality for network operations.
"""

import time
import queue
import logging
import threading
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from contextlib import contextmanager

@dataclass
class Connection:
    """Connection data class"""
    id: str
    host: str
    port: int
    created_at: float
    last_used: float
    is_alive: bool = True
    conn: Any = None

class ConnectionPool:
    """Manager for connection pooling"""
    
    def __init__(
        self,
        pool_size: int = 10,
        max_overflow: int = 5,
        timeout: int = 30,
        recycle: int = 3600
    ):
        """
        Initialize connection pool
        
        Args:
            pool_size: Base pool size
            max_overflow: Maximum number of additional connections
            timeout: Connection timeout in seconds
            recycle: Connection recycle time in seconds
        """
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        self.timeout = timeout
        self.recycle = recycle
        
        self._pool = queue.Queue(maxsize=pool_size)
        self._overflow = queue.Queue(maxsize=max_overflow)
        self._active_count = 0
        self._overflow_count = 0
        self._lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
        
        # Start maintenance thread
        self._maintenance_thread = threading.Thread(target=self._maintain_pool)
        self._maintenance_thread.daemon = True
        self._maintenance_thread.start()
    
    def _create_connection(self, host: str, port: int) -> Connection:
        """
        Create new connection
        
        Args:
            host: Host address
            port: Port number
            
        Returns:
            Connection: New connection object
        """
        try:
            conn_id = f"{host}:{port}_{int(time.time())}"
            conn = Connection(
                id=conn_id,
                host=host,
                port=port,
                created_at=time.time(),
                last_used=time.time()
            )
            
            # Here you would typically establish the actual network connection
            # For now, we're just creating the connection object
            
            self.logger.debug(f"Created new connection: {conn_id}")
            return conn
            
        except Exception as e:
            self.logger.error(f"Failed to create connection: {str(e)}")
            raise
    
    @contextmanager
    def get_connection(
        self,
        host: str,
        port: int
    ) -> Connection:
        """
        Get connection from pool
        
        Args:
            host: Host address
            port: Port number
            
        Yields:
            Connection: Connection object
        """
        connection = None
        try:
            connection = self._get_connection(host, port)
            yield connection
        finally:
            if connection:
                self._return_connection(connection)
    
    def _get_connection(
        self,
        host: str,
        port: int
    ) -> Connection:
        """
        Get or create connection
        
        Args:
            host: Host address
            port: Port number
            
        Returns:
            Connection: Connection object
        """
        try:
            # Try to get connection from main pool
            connection = self._pool.get(block=False)
            if self._is_connection_valid(connection):
                return connection
                
            # Connection invalid, create new one
            self._dispose_connection(connection)
            
        except queue.Empty:
            # Main pool empty, try overflow
            try:
                connection = self._overflow.get(block=False)
                if self._is_connection_valid(connection):
                    return connection
                    
                # Connection invalid, create new one
                self._dispose_connection(connection)
                
            except queue.Empty:
                # Create new connection
                with self._lock:
                    if self._overflow_count < self.max_overflow:
                        self._overflow_count += 1
                        connection = self._create_connection(host, port)
                        return connection
                        
                # Wait for available connection
                try:
                    return self._pool.get(timeout=self.timeout)
                except queue.Empty:
                    raise TimeoutError("Connection pool timeout")
    
    def _return_connection(self, connection: Connection) -> None:
        """
        Return connection to pool
        
        Args:
            connection: Connection to return
        """
        if not connection.is_alive:
            self._dispose_connection(connection)
            return
            
        connection.last_used = time.time()
        
        try:
            # Try to return to main pool
            self._pool.put_nowait(connection)
        except queue.Full:
            # Main pool full, try overflow
            try:
                self._overflow.put_nowait(connection)
            except queue.Full:
                # Both pools full, dispose connection
                self._dispose_connection(connection)
    
    def _is_connection_valid(self, connection: Connection) -> bool:
        """
        Check if connection is valid
        
        Args:
            connection: Connection to check
            
        Returns:
            bool: True if valid
        """
        if not connection.is_alive:
            return False
            
        # Check if connection needs recycling
        if self.recycle > 0:
            if time.time() - connection.created_at > self.recycle:
                return False
        
        # Here you would typically check the actual connection status
        # For now, we're just checking the is_alive flag
        
        return True
    
    def _dispose_connection(self, connection: Connection) -> None:
        """
        Dispose of connection
        
        Args:
            connection: Connection to dispose
        """
        try:
            # Here you would typically close the actual connection
            connection.is_alive = False
            connection.conn = None
            
            with self._lock:
                if self._overflow_count > 0:
                    self._overflow_count -= 1
                    
            self.logger.debug(f"Disposed connection: {connection.id}")
            
        except Exception as e:
            self.logger.error(f"Error disposing connection: {str(e)}")
    
    def _maintain_pool(self) -> None:
        """Maintain connection pool"""
        while True:
            time.sleep(60)  # Run maintenance every minute
            try:
                self._check_expired_connections()
                self._balance_pools()
            except Exception as e:
                self.logger.error(f"Pool maintenance error: {str(e)}")
    
    def _check_expired_connections(self) -> None:
        """Check and remove expired connections"""
        current_time = time.time()
        
        # Check main pool
        valid_connections = queue.Queue(maxsize=self.pool_size)
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                if self._is_connection_valid(conn):
                    valid_connections.put_nowait(conn)
                else:
                    self._dispose_connection(conn)
            except queue.Empty:
                break
                
        self._pool = valid_connections
        
        # Check overflow pool
        valid_connections = queue.Queue(maxsize=self.max_overflow)
        while not self._overflow.empty():
            try:
                conn = self._overflow.get_nowait()
                if self._is_connection_valid(conn):
                    valid_connections.put_nowait(conn)
                else:
                    self._dispose_connection(conn)
            except queue.Empty:
                break
                
        self._overflow = valid_connections
    
    def _balance_pools(self) -> None:
        """Balance connections between main and overflow pools"""
        # Move connections from overflow to main pool if possible
        while not self._pool.full() and not self._overflow.empty():
            try:
                conn = self._overflow.get_nowait()
                self._pool.put_nowait(conn)
                with self._lock:
                    self._overflow_count -= 1
            except (queue.Empty, queue.Full):
                break
    
    def clear(self) -> None:
        """Clear all connections"""
        # Clear main pool
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                self._dispose_connection(conn)
            except queue.Empty:
                break
                
        # Clear overflow pool
        while not self._overflow.empty():
            try:
                conn = self._overflow.get_nowait()
                self._dispose_connection(conn)
            except queue.Empty:
                break
                
        with self._lock:
            self._overflow_count = 0
    
    def get_stats(self) -> Dict[str, int]:
        """
        Get pool statistics
        
        Returns:
            Dict[str, int]: Pool statistics
        """
        return {
            'pool_size': self._pool.qsize(),
            'overflow_size': self._overflow.qsize(),
            'overflow_count': self._overflow_count
        }

# Create global connection pool instance
connection_pool = ConnectionPool()
