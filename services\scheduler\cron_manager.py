"""
Cron Manager

Provides cron-like scheduling functionality for recurring tasks.
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Callable, Optional
from dataclasses import dataclass
import re


@dataclass
class CronJob:
    """Cron job definition."""
    id: str
    name: str
    schedule: str  # Cron expression
    function: Callable
    args: tuple = ()
    kwargs: dict = None
    enabled: bool = True
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    run_count: int = 0

    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}


class CronManager:
    """Cron-like task scheduler."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._jobs: Dict[str, CronJob] = {}
        self._running = False
        self._scheduler_thread = None
        self._lock = threading.RLock()

        # Statistics
        self._stats = {
            'total_jobs': 0,
            'active_jobs': 0,
            'total_runs': 0,
            'failed_runs': 0
        }

    def start(self):
        """Start the cron scheduler."""
        with self._lock:
            if self._running:
                return

            self._running = True
            self._scheduler_thread = threading.Thread(
                target=self._scheduler_loop,
                name="CronScheduler",
                daemon=True
            )
            self._scheduler_thread.start()

            self.logger.info("Cron manager started")

    def stop(self):
        """Stop the cron scheduler."""
        with self._lock:
            if not self._running:
                return

            self._running = False

            if self._scheduler_thread:
                self._scheduler_thread.join(timeout=5.0)

            self.logger.info("Cron manager stopped")

    def add_job(self, job_id: str, name: str, schedule: str,
                function: Callable, *args, **kwargs) -> bool:
        """Add a cron job."""
        try:
            job = CronJob(
                id=job_id,
                name=name,
                schedule=schedule,
                function=function,
                args=args,
                kwargs=kwargs
            )

            # Calculate next run time
            job.next_run = self._calculate_next_run(schedule)

            with self._lock:
                self._jobs[job_id] = job
                self._stats['total_jobs'] = len(self._jobs)
                self._stats['active_jobs'] = len([j for j in self._jobs.values() if j.enabled])

            self.logger.info(f"Added cron job: {name} ({schedule})")
            return True

        except Exception as e:
            self.logger.error(f"Error adding cron job: {e}")
            return False

    def remove_job(self, job_id: str) -> bool:
        """Remove a cron job."""
        with self._lock:
            if job_id in self._jobs:
                del self._jobs[job_id]
                self._stats['total_jobs'] = len(self._jobs)
                self._stats['active_jobs'] = len([j for j in self._jobs.values() if j.enabled])
                self.logger.info(f"Removed cron job: {job_id}")
                return True
            return False

    def enable_job(self, job_id: str) -> bool:
        """Enable a cron job."""
        with self._lock:
            if job_id in self._jobs:
                self._jobs[job_id].enabled = True
                self._stats['active_jobs'] = len([j for j in self._jobs.values() if j.enabled])
                return True
            return False

    def disable_job(self, job_id: str) -> bool:
        """Disable a cron job."""
        with self._lock:
            if job_id in self._jobs:
                self._jobs[job_id].enabled = False
                self._stats['active_jobs'] = len([j for j in self._jobs.values() if j.enabled])
                return True
            return False

    def get_jobs(self) -> List[CronJob]:
        """Get all cron jobs."""
        with self._lock:
            return list(self._jobs.values())

    def get_statistics(self) -> Dict[str, Any]:
        """Get cron manager statistics."""
        with self._lock:
            return self._stats.copy()

    def _scheduler_loop(self):
        """Main scheduler loop."""
        while self._running:
            try:
                current_time = datetime.now()

                with self._lock:
                    jobs_to_run = []
                    for job in self._jobs.values():
                        if (job.enabled and job.next_run and
                            current_time >= job.next_run):
                            jobs_to_run.append(job)

                # Run jobs outside of lock
                for job in jobs_to_run:
                    self._run_job(job)

                time.sleep(1)  # Check every second

            except Exception as e:
                self.logger.error(f"Scheduler loop error: {e}")
                time.sleep(5)

    def _run_job(self, job: CronJob):
        """Run a cron job."""
        try:
            self.logger.debug(f"Running cron job: {job.name}")

            # Update job timing
            job.last_run = datetime.now()
            job.next_run = self._calculate_next_run(job.schedule)
            job.run_count += 1

            # Execute job function
            job.function(*job.args, **job.kwargs)

            with self._lock:
                self._stats['total_runs'] += 1

            self.logger.debug(f"Completed cron job: {job.name}")

        except Exception as e:
            self.logger.error(f"Error running cron job {job.name}: {e}")
            with self._lock:
                self._stats['failed_runs'] += 1

    def _calculate_next_run(self, schedule: str) -> datetime:
        """Calculate next run time from cron schedule."""
        # Simplified cron parser - supports basic patterns
        # Format: minute hour day month weekday
        # * means any, numbers mean specific values

        try:
            parts = schedule.split()
            if len(parts) != 5:
                raise ValueError("Invalid cron format")

            minute, hour, day, month, weekday = parts

            now = datetime.now()
            next_run = now.replace(second=0, microsecond=0)

            # Simple implementation - just add 1 minute for * * * * *
            if schedule == "* * * * *":
                next_run += timedelta(minutes=1)
            elif schedule.startswith("*/"):
                # Handle */N patterns for minutes
                interval = int(schedule.split()[0][2:])
                next_run += timedelta(minutes=interval)
            else:
                # Default to 1 hour for other patterns
                next_run += timedelta(hours=1)

            return next_run

        except Exception as e:
            self.logger.error(f"Error calculating next run: {e}")
            # Default to 1 hour from now
            return datetime.now() + timedelta(hours=1)
