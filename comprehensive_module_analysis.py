#!/usr/bin/env python3
"""
AirHunter 项目 - 全面功能模块完成度分析

检查所有智能体的功能实现完整性、代码质量和架构合规性
"""

import os
import ast
import json
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime


class ModuleAnalyzer:
    """模块分析器"""
    
    def __init__(self):
        self.agents = {
            "coordinator": "协调控制智能体",
            "discovery": "项目发现智能体", 
            "assessment": "项目评估智能体",
            "monitoring": "项目监控智能体",
            "fund_management": "资金管理智能体",
            "task_planning": "任务规划智能体",
            "task_execution": "任务执行智能体",
            "proxy": "代理智能体",
            "anti_sybil": "防女巫智能体",
            "profit_optimization": "收益优化智能体"
        }
        
        self.analysis_results = {}
    
    def analyze_all_agents(self) -> Dict[str, Any]:
        """分析所有智能体"""
        print("🔍 开始全面功能模块完成度分析...")
        print("=" * 80)
        
        overall_stats = {
            "total_agents": len(self.agents),
            "analyzed_agents": 0,
            "total_files": 0,
            "total_classes": 0,
            "total_methods": 0,
            "total_lines": 0,
            "agents_with_main": 0,
            "agents_with_tests": 0,
            "completion_scores": []
        }
        
        for agent_name, agent_desc in self.agents.items():
            print(f"\n🤖 分析 {agent_name} ({agent_desc})...")
            
            if os.path.exists(agent_name):
                analysis = self.analyze_agent(agent_name, agent_desc)
                self.analysis_results[agent_name] = analysis
                
                # 更新总体统计
                overall_stats["analyzed_agents"] += 1
                overall_stats["total_files"] += analysis["file_count"]
                overall_stats["total_classes"] += analysis["class_count"]
                overall_stats["total_methods"] += analysis["method_count"]
                overall_stats["total_lines"] += analysis["total_lines"]
                overall_stats["completion_scores"].append(analysis["completion_score"])
                
                if analysis["has_main_file"]:
                    overall_stats["agents_with_main"] += 1
                if analysis["has_tests"]:
                    overall_stats["agents_with_tests"] += 1
                
                print(f"   ✅ 完成度: {analysis['completion_score']:.1f}%")
            else:
                print(f"   ❌ 目录不存在: {agent_name}")
        
        # 计算平均完成度
        if overall_stats["completion_scores"]:
            overall_stats["average_completion"] = sum(overall_stats["completion_scores"]) / len(overall_stats["completion_scores"])
        else:
            overall_stats["average_completion"] = 0.0
        
        return {
            "timestamp": datetime.now().isoformat(),
            "overall_stats": overall_stats,
            "agent_details": self.analysis_results
        }
    
    def analyze_agent(self, agent_path: str, agent_desc: str) -> Dict[str, Any]:
        """分析单个智能体"""
        analysis = {
            "name": agent_path,
            "description": agent_desc,
            "file_count": 0,
            "class_count": 0,
            "method_count": 0,
            "total_lines": 0,
            "has_main_file": False,
            "has_init_file": False,
            "has_tests": False,
            "modules": {},
            "main_agent_file": None,
            "completion_score": 0.0,
            "quality_indicators": {
                "has_docstrings": 0,
                "has_error_handling": 0,
                "has_logging": 0,
                "has_type_hints": 0,
                "has_async_methods": 0
            }
        }
        
        # 检查主要文件
        main_file = f"{agent_path}/{agent_path}_agent.py"
        if os.path.exists(main_file):
            analysis["has_main_file"] = True
            analysis["main_agent_file"] = main_file
        
        init_file = f"{agent_path}/__init__.py"
        if os.path.exists(init_file):
            analysis["has_init_file"] = True
        
        # 检查测试文件
        test_patterns = [f"test_{agent_path}.py", f"tests/{agent_path}", f"{agent_path}/tests"]
        for pattern in test_patterns:
            if os.path.exists(pattern):
                analysis["has_tests"] = True
                break
        
        # 遍历所有Python文件
        for root, dirs, files in os.walk(agent_path):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    file_analysis = self.analyze_python_file(file_path)
                    
                    analysis["file_count"] += 1
                    analysis["class_count"] += file_analysis["class_count"]
                    analysis["method_count"] += file_analysis["method_count"]
                    analysis["total_lines"] += file_analysis["line_count"]
                    
                    # 更新质量指标
                    for key in analysis["quality_indicators"]:
                        if file_analysis["quality"][key]:
                            analysis["quality_indicators"][key] += 1
                    
                    # 记录模块信息
                    rel_path = os.path.relpath(file_path, agent_path)
                    analysis["modules"][rel_path] = file_analysis
        
        # 计算完成度评分
        analysis["completion_score"] = self.calculate_completion_score(analysis)
        
        return analysis
    
    def analyze_python_file(self, file_path: str) -> Dict[str, Any]:
        """分析Python文件"""
        analysis = {
            "path": file_path,
            "line_count": 0,
            "class_count": 0,
            "method_count": 0,
            "function_count": 0,
            "quality": {
                "has_docstrings": False,
                "has_error_handling": False,
                "has_logging": False,
                "has_type_hints": False,
                "has_async_methods": False
            },
            "classes": [],
            "functions": [],
            "imports": []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                analysis["line_count"] = len(content.splitlines())
            
            # 解析AST
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    analysis["class_count"] += 1
                    analysis["classes"].append(node.name)
                    
                    # 检查类文档字符串
                    if ast.get_docstring(node):
                        analysis["quality"]["has_docstrings"] = True
                
                elif isinstance(node, ast.FunctionDef) or isinstance(node, ast.AsyncFunctionDef):
                    if isinstance(node, ast.AsyncFunctionDef):
                        analysis["quality"]["has_async_methods"] = True
                    
                    analysis["method_count"] += 1
                    analysis["functions"].append(node.name)
                    
                    # 检查函数文档字符串
                    if ast.get_docstring(node):
                        analysis["quality"]["has_docstrings"] = True
                    
                    # 检查类型注解
                    if node.returns or any(arg.annotation for arg in node.args.args):
                        analysis["quality"]["has_type_hints"] = True
                
                elif isinstance(node, ast.Try):
                    analysis["quality"]["has_error_handling"] = True
                
                elif isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            analysis["imports"].append(alias.name)
                            if "logging" in alias.name:
                                analysis["quality"]["has_logging"] = True
                    else:
                        if node.module:
                            analysis["imports"].append(node.module)
                            if "logging" in node.module:
                                analysis["quality"]["has_logging"] = True
        
        except Exception as e:
            print(f"   ⚠️  解析文件失败 {file_path}: {e}")
        
        return analysis
    
    def calculate_completion_score(self, analysis: Dict[str, Any]) -> float:
        """计算完成度评分"""
        score = 0.0
        max_score = 100.0
        
        # 基础结构 (30分)
        if analysis["has_init_file"]:
            score += 5
        if analysis["has_main_file"]:
            score += 15
        if analysis["file_count"] > 0:
            score += 10
        
        # 代码量 (20分)
        if analysis["class_count"] > 0:
            score += 10
        if analysis["method_count"] > 10:
            score += 10
        
        # 代码质量 (30分)
        quality_score = sum(1 for v in analysis["quality_indicators"].values() if v > 0)
        score += (quality_score / 5) * 30
        
        # 测试覆盖 (10分)
        if analysis["has_tests"]:
            score += 10
        
        # 模块完整性 (10分)
        if analysis["file_count"] >= 5:
            score += 5
        if analysis["file_count"] >= 10:
            score += 5
        
        return min(score, max_score)

def main():
    """主函数"""
    analyzer = ModuleAnalyzer()
    results = analyzer.analyze_all_agents()
    
    # 生成报告
    print("\n" + "=" * 80)
    print("📊 AirHunter 项目功能模块完成度分析报告")
    print("=" * 80)
    
    stats = results["overall_stats"]
    print(f"\n🎯 总体统计:")
    print(f"   • 智能体总数: {stats['total_agents']}")
    print(f"   • 已分析智能体: {stats['analyzed_agents']}")
    print(f"   • 总文件数: {stats['total_files']}")
    print(f"   • 总类数: {stats['total_classes']}")
    print(f"   • 总方法数: {stats['total_methods']}")
    print(f"   • 总代码行数: {stats['total_lines']:,}")
    print(f"   • 平均完成度: {stats['average_completion']:.1f}%")
    print(f"   • 有主文件的智能体: {stats['agents_with_main']}/{stats['total_agents']}")
    print(f"   • 有测试的智能体: {stats['agents_with_tests']}/{stats['total_agents']}")
    
    print(f"\n📋 各智能体详细分析:")
    for agent_name, analysis in results["agent_details"].items():
        print(f"\n🤖 {agent_name} ({analysis['description']})")
        print(f"   📁 文件数: {analysis['file_count']}")
        print(f"   🏗️  类数: {analysis['class_count']}")
        print(f"   ⚙️  方法数: {analysis['method_count']}")
        print(f"   📝 代码行数: {analysis['total_lines']:,}")
        print(f"   ✅ 完成度: {analysis['completion_score']:.1f}%")
        print(f"   🔧 主文件: {'✅' if analysis['has_main_file'] else '❌'}")
        print(f"   🧪 测试: {'✅' if analysis['has_tests'] else '❌'}")
        
        quality = analysis['quality_indicators']
        print(f"   📊 质量指标:")
        print(f"      - 文档字符串: {quality['has_docstrings']} 文件")
        print(f"      - 错误处理: {quality['has_error_handling']} 文件")
        print(f"      - 日志记录: {quality['has_logging']} 文件")
        print(f"      - 类型注解: {quality['has_type_hints']} 文件")
        print(f"      - 异步方法: {quality['has_async_methods']} 文件")
    
    # 保存详细报告
    with open("module_analysis_report.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细报告已保存到: module_analysis_report.json")
    print(f"📅 分析时间: {results['timestamp']}")

if __name__ == "__main__":
    main()
