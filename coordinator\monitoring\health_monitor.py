"""
健康监控器

该模块提供系统健康监控功能，负责监控系统和智能体的健康状态。
"""

import logging
import threading
import time
from typing import Dict, List, Any, Optional, Set, Callable

from coordinator.core.system_state import SystemState
from coordinator.communication.event_system import EventSystem


class HealthMonitor:
    """健康监控器，负责监控系统和智能体的健康状态"""
    
    def __init__(self, system_state: SystemState, event_system: EventSystem, check_interval: float = 30.0):
        """
        初始化健康监控器
        
        Args:
            system_state: 系统状态实例
            event_system: 事件系统实例
            check_interval: 健康检查间隔（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.system_state = system_state
        self.event_system = event_system
        self.check_interval = check_interval
        self._lock = threading.RLock()
        self._running = False
        self._monitor_thread = None
        self._health_checks: Dict[str, Callable[[], Dict[str, Any]]] = {}
        self._agent_health_checks: Dict[str, Dict[str, Callable[[], Dict[str, Any]]]] = {}
    
    def start(self) -> None:
        """启动健康监控器"""
        with self._lock:
            if self._running:
                self.logger.warning("健康监控器已经在运行")
                return
            
            self._running = True
            self._monitor_thread = threading.Thread(
                target=self._monitor_loop,
                name="HealthMonitor",
                daemon=True
            )
            self._monitor_thread.start()
            
            self.logger.info("健康监控器已启动")
    
    def stop(self) -> None:
        """停止健康监控器"""
        with self._lock:
            if not self._running:
                self.logger.warning("健康监控器未在运行")
                return
            
            self._running = False
            if self._monitor_thread:
                self._monitor_thread.join(timeout=5.0)
                self._monitor_thread = None
            
            self.logger.info("健康监控器已停止")
    
    def register_health_check(self, name: str, check_func: Callable[[], Dict[str, Any]]) -> None:
        """
        注册健康检查
        
        Args:
            name: 健康检查名称
            check_func: 健康检查函数，返回包含健康状态的字典
        """
        with self._lock:
            self._health_checks[name] = check_func
            self.logger.info(f"已注册健康检查 '{name}'")
    
    def unregister_health_check(self, name: str) -> bool:
        """
        注销健康检查
        
        Args:
            name: 健康检查名称
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if name not in self._health_checks:
                self.logger.warning(f"健康检查 '{name}' 不存在")
                return False
            
            del self._health_checks[name]
            self.logger.info(f"已注销健康检查 '{name}'")
            return True
    
    def register_agent_health_check(self, agent_name: str, check_name: str, check_func: Callable[[], Dict[str, Any]]) -> None:
        """
        注册智能体健康检查
        
        Args:
            agent_name: 智能体名称
            check_name: 健康检查名称
            check_func: 健康检查函数，返回包含健康状态的字典
        """
        with self._lock:
            if agent_name not in self._agent_health_checks:
                self._agent_health_checks[agent_name] = {}
            
            self._agent_health_checks[agent_name][check_name] = check_func
            self.logger.info(f"已为智能体 '{agent_name}' 注册健康检查 '{check_name}'")
    
    def unregister_agent_health_check(self, agent_name: str, check_name: str) -> bool:
        """
        注销智能体健康检查
        
        Args:
            agent_name: 智能体名称
            check_name: 健康检查名称
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_health_checks:
                self.logger.warning(f"智能体 '{agent_name}' 没有注册健康检查")
                return False
            
            if check_name not in self._agent_health_checks[agent_name]:
                self.logger.warning(f"智能体 '{agent_name}' 的健康检查 '{check_name}' 不存在")
                return False
            
            del self._agent_health_checks[agent_name][check_name]
            
            # 如果智能体没有健康检查了，删除它
            if not self._agent_health_checks[agent_name]:
                del self._agent_health_checks[agent_name]
            
            self.logger.info(f"已注销智能体 '{agent_name}' 的健康检查 '{check_name}'")
            return True
    
    def unregister_agent(self, agent_name: str) -> bool:
        """
        注销智能体的所有健康检查
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_health_checks:
                self.logger.warning(f"智能体 '{agent_name}' 没有注册健康检查")
                return False
            
            del self._agent_health_checks[agent_name]
            self.logger.info(f"已注销智能体 '{agent_name}' 的所有健康检查")
            return True
    
    def _monitor_loop(self) -> None:
        """健康监控循环"""
        self.logger.info("健康监控循环已启动")
        
        while self._running:
            try:
                # 执行系统健康检查
                self._check_system_health()
                
                # 执行智能体健康检查
                self._check_agent_health()
                
                # 等待下一次检查
                for _ in range(int(self.check_interval * 2)):  # 分成更小的间隔，以便更快地响应停止信号
                    if not self._running:
                        break
                    time.sleep(0.5)
            
            except Exception as e:
                self.logger.error(f"健康监控循环出错: {str(e)}")
                time.sleep(5.0)  # 出错后等待一段时间再继续
        
        self.logger.info("健康监控循环已停止")
    
    def _check_system_health(self) -> None:
        """执行系统健康检查"""
        self.logger.debug("执行系统健康检查")
        
        # 获取健康检查函数的副本
        with self._lock:
            health_checks = self._health_checks.copy()
        
        # 执行每个健康检查
        for name, check_func in health_checks.items():
            try:
                result = check_func()
                
                # 检查结果
                if result.get("status") == "healthy":
                    self.logger.debug(f"系统健康检查 '{name}' 通过")
                else:
                    # 记录警告或错误
                    message = result.get("message", f"系统健康检查 '{name}' 失败")
                    details = result.get("details", {})
                    
                    if result.get("status") == "warning":
                        self.system_state.add_warning(message, "system", details)
                        
                        # 发布警告事件
                        self.event_system.publish("system.health.warning", {
                            "check": name,
                            "message": message,
                            "details": details
                        })
                    else:
                        self.system_state.add_error(message, "system", details)
                        
                        # 发布错误事件
                        self.event_system.publish("system.health.error", {
                            "check": name,
                            "message": message,
                            "details": details
                        })
            
            except Exception as e:
                self.logger.error(f"执行系统健康检查 '{name}' 时出错: {str(e)}")
                
                # 记录错误
                self.system_state.add_error(f"执行系统健康检查 '{name}' 时出错: {str(e)}", "system")
                
                # 发布错误事件
                self.event_system.publish("system.health.error", {
                    "check": name,
                    "message": f"执行健康检查时出错: {str(e)}",
                    "details": {"exception": str(e)}
                })
    
    def _check_agent_health(self) -> None:
        """执行智能体健康检查"""
        self.logger.debug("执行智能体健康检查")
        
        # 获取智能体健康检查函数的副本
        with self._lock:
            agent_health_checks = {agent: checks.copy() for agent, checks in self._agent_health_checks.items()}
        
        # 执行每个智能体的健康检查
        for agent_name, checks in agent_health_checks.items():
            for check_name, check_func in checks.items():
                try:
                    result = check_func()
                    
                    # 检查结果
                    if result.get("status") == "healthy":
                        self.logger.debug(f"智能体 '{agent_name}' 的健康检查 '{check_name}' 通过")
                    else:
                        # 记录警告或错误
                        message = result.get("message", f"智能体 '{agent_name}' 的健康检查 '{check_name}' 失败")
                        details = result.get("details", {})
                        
                        if result.get("status") == "warning":
                            self.system_state.add_warning(message, agent_name, details)
                            
                            # 发布警告事件
                            self.event_system.publish("agent.health.warning", {
                                "agent": agent_name,
                                "check": check_name,
                                "message": message,
                                "details": details
                            })
                        else:
                            self.system_state.add_error(message, agent_name, details)
                            
                            # 发布错误事件
                            self.event_system.publish("agent.health.error", {
                                "agent": agent_name,
                                "check": check_name,
                                "message": message,
                                "details": details
                            })
                
                except Exception as e:
                    self.logger.error(f"执行智能体 '{agent_name}' 的健康检查 '{check_name}' 时出错: {str(e)}")
                    
                    # 记录错误
                    self.system_state.add_error(f"执行健康检查 '{check_name}' 时出错: {str(e)}", agent_name)
                    
                    # 发布错误事件
                    self.event_system.publish("agent.health.error", {
                        "agent": agent_name,
                        "check": check_name,
                        "message": f"执行健康检查时出错: {str(e)}",
                        "details": {"exception": str(e)}
                    })
    
    def run_health_check(self, name: str) -> Optional[Dict[str, Any]]:
        """
        立即运行系统健康检查
        
        Args:
            name: 健康检查名称
            
        Returns:
            健康检查结果，如果健康检查不存在则返回None
        """
        with self._lock:
            if name not in self._health_checks:
                self.logger.warning(f"健康检查 '{name}' 不存在")
                return None
            
            check_func = self._health_checks[name]
        
        try:
            result = check_func()
            return result
        except Exception as e:
            self.logger.error(f"执行健康检查 '{name}' 时出错: {str(e)}")
            return {
                "status": "error",
                "message": f"执行健康检查时出错: {str(e)}",
                "details": {"exception": str(e)}
            }
    
    def run_agent_health_check(self, agent_name: str, check_name: str) -> Optional[Dict[str, Any]]:
        """
        立即运行智能体健康检查
        
        Args:
            agent_name: 智能体名称
            check_name: 健康检查名称
            
        Returns:
            健康检查结果，如果健康检查不存在则返回None
        """
        with self._lock:
            if agent_name not in self._agent_health_checks:
                self.logger.warning(f"智能体 '{agent_name}' 没有注册健康检查")
                return None
            
            if check_name not in self._agent_health_checks[agent_name]:
                self.logger.warning(f"智能体 '{agent_name}' 的健康检查 '{check_name}' 不存在")
                return None
            
            check_func = self._agent_health_checks[agent_name][check_name]
        
        try:
            result = check_func()
            return result
        except Exception as e:
            self.logger.error(f"执行智能体 '{agent_name}' 的健康检查 '{check_name}' 时出错: {str(e)}")
            return {
                "status": "error",
                "message": f"执行健康检查时出错: {str(e)}",
                "details": {"exception": str(e)}
            }
    
    def run_all_health_checks(self) -> Dict[str, Dict[str, Any]]:
        """
        立即运行所有系统健康检查
        
        Returns:
            健康检查结果字典，键为健康检查名称，值为健康检查结果
        """
        results = {}
        
        with self._lock:
            health_checks = self._health_checks.copy()
        
        for name, check_func in health_checks.items():
            try:
                result = check_func()
                results[name] = result
            except Exception as e:
                self.logger.error(f"执行健康检查 '{name}' 时出错: {str(e)}")
                results[name] = {
                    "status": "error",
                    "message": f"执行健康检查时出错: {str(e)}",
                    "details": {"exception": str(e)}
                }
        
        return results
    
    def run_all_agent_health_checks(self, agent_name: str) -> Dict[str, Dict[str, Any]]:
        """
        立即运行智能体的所有健康检查
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            健康检查结果字典，键为健康检查名称，值为健康检查结果
        """
        results = {}
        
        with self._lock:
            if agent_name not in self._agent_health_checks:
                self.logger.warning(f"智能体 '{agent_name}' 没有注册健康检查")
                return results
            
            checks = self._agent_health_checks[agent_name].copy()
        
        for check_name, check_func in checks.items():
            try:
                result = check_func()
                results[check_name] = result
            except Exception as e:
                self.logger.error(f"执行智能体 '{agent_name}' 的健康检查 '{check_name}' 时出错: {str(e)}")
                results[check_name] = {
                    "status": "error",
                    "message": f"执行健康检查时出错: {str(e)}",
                    "details": {"exception": str(e)}
                }
        
        return results
    
    def get_health_checks(self) -> List[str]:
        """
        获取所有系统健康检查名称
        
        Returns:
            健康检查名称列表
        """
        with self._lock:
            return list(self._health_checks.keys())
    
    def get_agent_health_checks(self, agent_name: str) -> List[str]:
        """
        获取智能体的所有健康检查名称
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            健康检查名称列表
        """
        with self._lock:
            if agent_name not in self._agent_health_checks:
                return []
            
            return list(self._agent_health_checks[agent_name].keys())
    
    def get_all_agent_health_checks(self) -> Dict[str, List[str]]:
        """
        获取所有智能体的健康检查名称
        
        Returns:
            健康检查名称字典，键为智能体名称，值为健康检查名称列表
        """
        with self._lock:
            return {agent: list(checks.keys()) for agent, checks in self._agent_health_checks.items()}