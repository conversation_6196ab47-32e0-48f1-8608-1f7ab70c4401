#!/usr/bin/env python3
"""
快速测试修复效果
"""

import os
import sys

def test_basic_imports():
    """测试基础导入"""
    print("🧪 测试基础导入...")
    
    try:
        # 测试配置管理器
        from common.config_manager import ConfigManager
        print("  ✅ ConfigManager 导入成功")
        
        config_manager = ConfigManager()
        print("  ✅ ConfigManager 实例创建成功")
        
    except Exception as e:
        print(f"  ❌ ConfigManager 失败: {e}")
    
    try:
        # 测试错误处理
        from common.error_handling import ErrorHandler
        print("  ✅ ErrorHandler 导入成功")
        
        error_handler = ErrorHandler()
        print("  ✅ ErrorHandler 实例创建成功")
        
    except Exception as e:
        print(f"  ❌ ErrorHandler 失败: {e}")
    
    try:
        # 测试占位符模块
        from coordinator.workflow.workflow_manager import WorkflowManager
        print("  ✅ WorkflowManager 导入成功")
        
        wm = WorkflowManager(None, None, None)
        print("  ✅ WorkflowManager 实例创建成功")
        
    except Exception as e:
        print(f"  ❌ WorkflowManager 失败: {e}")


def test_file_structure():
    """测试文件结构"""
    print("\n🏗️ 测试文件结构...")
    
    key_files = [
        "config_unified.json",
        "common/config_manager.py",
        "common/error_handling.py",
        "coordinator/workflow/workflow_manager.py",
        "coordinator/resources/resource_allocator.py",
        "discovery/sources/twitter_monitor.py"
    ]
    
    for file_path in key_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} 不存在")


def main():
    """主函数"""
    print("🚀 AirHunter 快速修复测试")
    print(f"📁 工作目录: {os.getcwd()}")
    
    test_file_structure()
    test_basic_imports()
    
    print("\n✅ 快速测试完成")


if __name__ == "__main__":
    main()
