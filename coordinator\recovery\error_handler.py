"""
错误处理器

该模块提供错误处理功能，负责处理系统和智能体的错误。
"""

import logging
import threading
import time
import traceback
from typing import Dict, List, Any, Optional, Set, Callable, Tuple

from coordinator.core.system_state import SystemState
from coordinator.communication.event_system import EventSystem


class ErrorHandler:
    """错误处理器，负责处理系统和智能体的错误"""
    
    def __init__(self, system_state: SystemState, event_system: EventSystem):
        """
        初始化错误处理器
        
        Args:
            system_state: 系统状态实例
            event_system: 事件系统实例
        """
        self.logger = logging.getLogger(__name__)
        self.system_state = system_state
        self.event_system = event_system
        self._lock = threading.RLock()
        self._error_handlers: Dict[str, List[Callable[[Dict[str, Any]], bool]]] = {}  # error_type -> [handler_func]
        self._agent_error_handlers: Dict[str, Dict[str, List[Callable[[Dict[str, Any]], bool]]]] = {}  # agent_name -> {error_type -> [handler_func]}
        
        # 注册事件监听器
        self._error_subscriber_id = self.event_system.subscribe("system.health.error", self._handle_system_error)
        self._agent_error_subscriber_id = self.event_system.subscribe("agent.health.error", self._handle_agent_error)
    
    def register_error_handler(self, error_type: str, handler_func: Callable[[Dict[str, Any]], bool]) -> None:
        """
        注册错误处理器
        
        Args:
            error_type: 错误类型
            handler_func: 处理函数，接收错误数据并返回是否成功处理
        """
        with self._lock:
            if error_type not in self._error_handlers:
                self._error_handlers[error_type] = []
            
            self._error_handlers[error_type].append(handler_func)
            self.logger.info(f"已注册错误处理器，类型: '{error_type}'")
    
    def unregister_error_handler(self, error_type: str, handler_func: Callable[[Dict[str, Any]], bool]) -> bool:
        """
        注销错误处理器
        
        Args:
            error_type: 错误类型
            handler_func: 处理函数
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if error_type not in self._error_handlers:
                self.logger.warning(f"错误类型 '{error_type}' 没有注册处理器")
                return False
            
            if handler_func not in self._error_handlers[error_type]:
                self.logger.warning(f"处理函数未注册到错误类型 '{error_type}'")
                return False
            
            self._error_handlers[error_type].remove(handler_func)
            
            # 如果没有处理器了，删除错误类型
            if not self._error_handlers[error_type]:
                del self._error_handlers[error_type]
            
            self.logger.info(f"已注销错误处理器，类型: '{error_type}'")
            return True
    
    def register_agent_error_handler(self, agent_name: str, error_type: str, handler_func: Callable[[Dict[str, Any]], bool]) -> None:
        """
        注册智能体错误处理器
        
        Args:
            agent_name: 智能体名称
            error_type: 错误类型
            handler_func: 处理函数，接收错误数据并返回是否成功处理
        """
        with self._lock:
            if agent_name not in self._agent_error_handlers:
                self._agent_error_handlers[agent_name] = {}
            
            if error_type not in self._agent_error_handlers[agent_name]:
                self._agent_error_handlers[agent_name][error_type] = []
            
            self._agent_error_handlers[agent_name][error_type].append(handler_func)
            self.logger.info(f"已为智能体 '{agent_name}' 注册错误处理器，类型: '{error_type}'")
    
    def unregister_agent_error_handler(self, agent_name: str, error_type: str, handler_func: Callable[[Dict[str, Any]], bool]) -> bool:
        """
        注销智能体错误处理器
        
        Args:
            agent_name: 智能体名称
            error_type: 错误类型
            handler_func: 处理函数
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_error_handlers:
                self.logger.warning(f"智能体 '{agent_name}' 没有注册错误处理器")
                return False
            
            if error_type not in self._agent_error_handlers[agent_name]:
                self.logger.warning(f"智能体 '{agent_name}' 的错误类型 '{error_type}' 没有注册处理器")
                return False
            
            if handler_func not in self._agent_error_handlers[agent_name][error_type]:
                self.logger.warning(f"处理函数未注册到智能体 '{agent_name}' 的错误类型 '{error_type}'")
                return False
            
            self._agent_error_handlers[agent_name][error_type].remove(handler_func)
            
            # 如果没有处理器了，删除错误类型
            if not self._agent_error_handlers[agent_name][error_type]:
                del self._agent_error_handlers[agent_name][error_type]
            
            # 如果没有错误类型了，删除智能体
            if not self._agent_error_handlers[agent_name]:
                del self._agent_error_handlers[agent_name]
            
            self.logger.info(f"已注销智能体 '{agent_name}' 的错误处理器，类型: '{error_type}'")
            return True
    
    def unregister_agent(self, agent_name: str) -> bool:
        """
        注销智能体的所有错误处理器
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_error_handlers:
                self.logger.warning(f"智能体 '{agent_name}' 没有注册错误处理器")
                return False
            
            del self._agent_error_handlers[agent_name]
            self.logger.info(f"已注销智能体 '{agent_name}' 的所有错误处理器")
            return True
    
    def handle_error(self, error_type: str, error_data: Dict[str, Any]) -> bool:
        """
        处理错误
        
        Args:
            error_type: 错误类型
            error_data: 错误数据
            
        Returns:
            如果成功处理则返回True，否则返回False
        """
        self.logger.info(f"处理错误，类型: '{error_type}'")
        
        # 获取错误处理器
        handlers = []
        with self._lock:
            if error_type in self._error_handlers:
                handlers.extend(self._error_handlers[error_type])
            
            # 添加通用错误处理器
            if "*" in self._error_handlers:
                handlers.extend(self._error_handlers["*"])
        
        if not handlers:
            self.logger.warning(f"没有找到错误类型 '{error_type}' 的处理器")
            return False
        
        # 调用处理器
        success = False
        for handler in handlers:
            try:
                if handler(error_data):
                    success = True
                    self.logger.info(f"错误类型 '{error_type}' 已成功处理")
                    break
            except Exception as e:
                self.logger.error(f"调用错误处理器时出错: {str(e)}")
                self.logger.error(traceback.format_exc())
        
        return success
    
    def handle_agent_error(self, agent_name: str, error_type: str, error_data: Dict[str, Any]) -> bool:
        """
        处理智能体错误
        
        Args:
            agent_name: 智能体名称
            error_type: 错误类型
            error_data: 错误数据
            
        Returns:
            如果成功处理则返回True，否则返回False
        """
        self.logger.info(f"处理智能体 '{agent_name}' 的错误，类型: '{error_type}'")
        
        # 获取错误处理器
        handlers = []
        with self._lock:
            # 添加智能体特定的错误处理器
            if agent_name in self._agent_error_handlers:
                if error_type in self._agent_error_handlers[agent_name]:
                    handlers.extend(self._agent_error_handlers[agent_name][error_type])
                
                # 添加智能体通用错误处理器
                if "*" in self._agent_error_handlers[agent_name]:
                    handlers.extend(self._agent_error_handlers[agent_name]["*"])
            
            # 添加全局错误处理器
            if error_type in self._error_handlers:
                handlers.extend(self._error_handlers[error_type])
            
            # 添加全局通用错误处理器
            if "*" in self._error_handlers:
                handlers.extend(self._error_handlers["*"])
        
        if not handlers:
            self.logger.warning(f"没有找到智能体 '{agent_name}' 的错误类型 '{error_type}' 的处理器")
            return False
        
        # 调用处理器
        success = False
        for handler in handlers:
            try:
                # 添加智能体名称到错误数据
                error_data_with_agent = error_data.copy()
                error_data_with_agent["agent_name"] = agent_name
                
                if handler(error_data_with_agent):
                    success = True
                    self.logger.info(f"智能体 '{agent_name}' 的错误类型 '{error_type}' 已成功处理")
                    break
            except Exception as e:
                self.logger.error(f"调用智能体 '{agent_name}' 的错误处理器时出错: {str(e)}")
                self.logger.error(traceback.format_exc())
        
        return success
    
    def _handle_system_error(self, error_data: Dict[str, Any]) -> None:
        """
        处理系统错误事件
        
        Args:
            error_data: 错误数据
        """
        check = error_data.get("check", "unknown")
        message = error_data.get("message", "未知错误")
        details = error_data.get("details", {})
        
        self.logger.error(f"收到系统错误事件: {message}")
        
        # 构造错误类型
        error_type = f"system.{check}"
        
        # 处理错误
        if not self.handle_error(error_type, error_data):
            # 如果没有成功处理，记录到系统状态
            self.system_state.add_error(message, "system", details)
    
    def _handle_agent_error(self, error_data: Dict[str, Any]) -> None:
        """
        处理智能体错误事件
        
        Args:
            error_data: 错误数据
        """
        agent = error_data.get("agent", "unknown")
        check = error_data.get("check", "unknown")
        message = error_data.get("message", "未知错误")
        details = error_data.get("details", {})
        
        self.logger.error(f"收到智能体 '{agent}' 的错误事件: {message}")
        
        # 构造错误类型
        error_type = f"agent.{check}"
        
        # 处理错误
        if not self.handle_agent_error(agent, error_type, error_data):
            # 如果没有成功处理，记录到系统状态
            self.system_state.add_error(message, agent, details)
    
    def get_error_handlers(self) -> Dict[str, int]:
        """
        获取所有错误处理器
        
        Returns:
            错误处理器字典，键为错误类型，值为处理器数量
        """
        with self._lock:
            return {error_type: len(handlers) for error_type, handlers in self._error_handlers.items()}
    
    def get_agent_error_handlers(self, agent_name: str) -> Dict[str, int]:
        """
        获取智能体的所有错误处理器
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            错误处理器字典，键为错误类型，值为处理器数量
        """
        with self._lock:
            if agent_name not in self._agent_error_handlers:
                return {}
            
            return {error_type: len(handlers) for error_type, handlers in self._agent_error_handlers[agent_name].items()}
    
    def get_all_agent_error_handlers(self) -> Dict[str, Dict[str, int]]:
        """
        获取所有智能体的错误处理器
        
        Returns:
            错误处理器字典，键为智能体名称，值为错误类型字典
        """
        with self._lock:
            return {
                agent_name: {error_type: len(handlers) for error_type, handlers in error_types.items()}
                for agent_name, error_types in self._agent_error_handlers.items()
            }