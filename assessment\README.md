# 项目评估智能体 (Assessment Agent)

项目评估智能体是AirHunter系统的核心组件之一，负责验证项目真实性，分析智能合约安全性，评估项目风险和潜在收益。

## 功能特点

项目评估智能体提供以下主要功能：

1. **项目真实性验证**
   - 团队验证：验证项目团队成员的背景和经验
   - 社交媒体验证：验证项目社交媒体的真实性和活跃度
   - 项目网站验证：验证项目网站的真实性和内容质量

2. **智能合约安全分析**
   - 合约分析：分析智能合约的基本信息和风险
   - 漏洞扫描：扫描智能合约中的安全漏洞
   - 权限分析：分析智能合约的权限结构和集中化程度

3. **风险和收益评估**
   - 风险计算：计算项目的整体风险分数
   - 收益估算：估算项目的潜在收益
   - 评分生成：生成项目的社区评分和团队评分

## 组件结构

项目评估智能体由以下主要组件组成：

```
assessment/
├── __init__.py
├── assessment_agent.py  # 主控制类
├── main.py              # 命令行接口
├── verification/        # 验证组件
│   ├── __init__.py
│   ├── team_verifier.py
│   ├── social_verifier.py
│   └── project_verifier.py
├── security/            # 安全分析组件
│   ├── __init__.py
│   ├── contract_analyzer.py
│   ├── vulnerability_scanner.py
│   └── permission_analyzer.py
└── risk/                # 风险评估组件
    ├── __init__.py
    ├── risk_calculator.py
    ├── reward_estimator.py
    └── score_generator.py
```

## 使用方法

### 配置

项目评估智能体的配置文件位于 `config/assessment_config.json`，包含以下主要配置项：

- `assessment_interval`：评估间隔时间（秒）
- `max_projects_per_batch`：每批最多评估的项目数量
- `min_confidence_score`：最小置信度分数
- `verification`：验证组件配置
- `security`：安全分析组件配置
- `risk`：风险评估组件配置

### 命令行使用

可以通过命令行使用项目评估智能体：

```bash
# 评估单个项目
python -m assessment.main --project-id <project_id>

# 评估所有新项目
python -m assessment.main --assess-all

# 指定配置文件
python -m assessment.main --config <config_path>

# 设置日志级别
python -m assessment.main --log-level DEBUG
```

### 代码中使用

可以在代码中使用项目评估智能体：

```python
from assessment.assessment_agent import AssessmentAgent
from discovery.models.project import Project

# 加载配置
config = {...}

# 创建项目评估智能体
assessment_agent = AssessmentAgent(config)

# 评估单个项目
project = ...  # 获取项目
assessed_project = assessment_agent.assess_project(project)

# 批量评估项目
projects = [...]  # 获取项目列表
assessed_projects = assessment_agent.assess_projects(projects)

# 启动评估循环
assessment_agent.start()

# 停止评估循环
assessment_agent.stop()
```

## 示例

查看 `examples/assessment_example.py` 了解如何使用项目评估智能体评估项目。

## 依赖项

项目评估智能体依赖以下外部库：

- `requests`：用于HTTP请求
- `beautifulsoup4`：用于HTML解析
- `python-whois`：用于WHOIS查询（可选）

对于智能合约安全分析，还可以选择性地使用以下工具：

- `mythril`：用于智能合约漏洞扫描
- `slither`：用于智能合约静态分析

## 注意事项

- 项目评估智能体需要访问互联网以验证项目信息和分析智能合约
- 某些功能（如WHOIS查询、智能合约分析）需要API密钥
- 评估结果仅供参考，不构成投资建议