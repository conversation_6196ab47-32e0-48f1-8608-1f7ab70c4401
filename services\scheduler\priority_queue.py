"""
Priority Queue

Provides priority-based task queuing with support for different priority levels.
"""

import heapq
import threading
import time
from typing import Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum


class Priority(Enum):
    """Task priority levels."""
    CRITICAL = 1
    HIGH = 2
    NORMAL = 3
    LOW = 4
    BACKGROUND = 5


@dataclass
class PriorityTask:
    """Priority task wrapper."""
    priority: Priority
    task_id: str
    task_data: Any
    created_at: datetime = field(default_factory=datetime.utcnow)

    def __lt__(self, other):
        """Comparison for heap ordering."""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.created_at < other.created_at


class PriorityQueue:
    """Priority-based task queue."""

    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self._heap: List[PriorityTask] = []
        self._lock = threading.RLock()

        # Statistics
        self._stats = {
            'tasks_added': 0,
            'tasks_removed': 0,
            'current_size': 0,
            'max_size_reached': 0
        }

    def put(self, task_id: str, task_data: Any, priority: Priority = Priority.NORMAL) -> bool:
        """Add task to queue."""
        try:
            with self._lock:
                if len(self._heap) >= self.max_size:
                    self._stats['max_size_reached'] += 1
                    return False

                task = PriorityTask(priority, task_id, task_data)
                heapq.heappush(self._heap, task)

                self._stats['tasks_added'] += 1
                self._stats['current_size'] = len(self._heap)

                return True

        except Exception:
            return False

    def get(self, timeout: float = None) -> Optional[PriorityTask]:
        """Get highest priority task from queue."""
        start_time = time.time()

        while True:
            with self._lock:
                if self._heap:
                    task = heapq.heappop(self._heap)
                    self._stats['tasks_removed'] += 1
                    self._stats['current_size'] = len(self._heap)
                    return task

            if timeout is not None and (time.time() - start_time) >= timeout:
                return None

            time.sleep(0.01)  # Small delay to avoid busy waiting

    def peek(self) -> Optional[PriorityTask]:
        """Peek at highest priority task without removing it."""
        with self._lock:
            return self._heap[0] if self._heap else None

    def size(self) -> int:
        """Get current queue size."""
        with self._lock:
            return len(self._heap)

    def empty(self) -> bool:
        """Check if queue is empty."""
        with self._lock:
            return len(self._heap) == 0

    def clear(self):
        """Clear all tasks from queue."""
        with self._lock:
            self._heap.clear()
            self._stats['current_size'] = 0

    def get_statistics(self) -> dict:
        """Get queue statistics."""
        with self._lock:
            return self._stats.copy()
