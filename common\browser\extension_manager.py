"""
Browser extension manager

This module provides utilities for managing browser extensions.
"""

import os
import json
import shutil
import logging
import zipfile
from typing import Dict, List, Optional
from pathlib import Path

class ExtensionManager:
    """Manager for browser extensions"""
    
    def __init__(self, extension_dir: str):
        """
        Initialize extension manager
        
        Args:
            extension_dir: Base directory for extensions
        """
        self.extension_dir = extension_dir
        self.extensions = {}
        self.logger = logging.getLogger(__name__)
        os.makedirs(extension_dir, exist_ok=True)
    
    def install_extension(
        self,
        extension_path: str,
        extension_id: Optional[str] = None
    ) -> str:
        """
        Install extension from file
        
        Args:
            extension_path: Path to .crx or .zip file
            extension_id: Optional extension ID
            
        Returns:
            str: Installed extension ID
        """
        try:
            # Extract extension if needed
            if extension_path.endswith('.crx'):
                extension_id = self._install_crx(extension_path, extension_id)
            elif extension_path.endswith('.zip'):
                extension_id = self._install_zip(extension_path, extension_id)
            else:
                raise ValueError("Unsupported extension format")
            
            self.extensions[extension_id] = {
                'path': os.path.join(self.extension_dir, extension_id),
                'enabled': True
            }
            
            self.logger.info(f"Installed extension {extension_id}")
            return extension_id
            
        except Exception as e:
            self.logger.error(f"Failed to install extension: {str(e)}")
            raise
    
    def _install_crx(self, crx_path: str, extension_id: Optional[str] = None) -> str:
        """
        Install .crx extension
        
        Args:
            crx_path: Path to .crx file
            extension_id: Optional extension ID
            
        Returns:
            str: Extension ID
        """
        # Extract extension ID from manifest if not provided
        if not extension_id:
            with zipfile.ZipFile(crx_path) as zf:
                manifest = json.loads(zf.read('manifest.json'))
                extension_id = manifest.get('id')
        
        if not extension_id:
            raise ValueError("Extension ID not found")
        
        # Create extension directory
        ext_dir = os.path.join(self.extension_dir, extension_id)
        os.makedirs(ext_dir, exist_ok=True)
        
        # Extract extension
        with zipfile.ZipFile(crx_path) as zf:
            zf.extractall(ext_dir)
        
        return extension_id
    
    def _install_zip(self, zip_path: str, extension_id: Optional[str] = None) -> str:
        """
        Install .zip extension
        
        Args:
            zip_path: Path to .zip file
            extension_id: Optional extension ID
            
        Returns:
            str: Extension ID
        """
        # Extract extension ID from manifest if not provided
        if not extension_id:
            with zipfile.ZipFile(zip_path) as zf:
                manifest = json.loads(zf.read('manifest.json'))
                extension_id = manifest.get('id')
        
        if not extension_id:
            raise ValueError("Extension ID not found")
        
        # Create extension directory
        ext_dir = os.path.join(self.extension_dir, extension_id)
        os.makedirs(ext_dir, exist_ok=True)
        
        # Extract extension
        with zipfile.ZipFile(zip_path) as zf:
            zf.extractall(ext_dir)
        
        return extension_id
    
    def uninstall_extension(self, extension_id: str) -> None:
        """
        Uninstall extension
        
        Args:
            extension_id: Extension ID to uninstall
        """
        try:
            ext_dir = os.path.join(self.extension_dir, extension_id)
            if os.path.exists(ext_dir):
                shutil.rmtree(ext_dir)
                self.extensions.pop(extension_id, None)
                self.logger.info(f"Uninstalled extension {extension_id}")
            else:
                self.logger.warning(f"Extension {extension_id} not found")
        except Exception as e:
            self.logger.error(f"Failed to uninstall extension: {str(e)}")
            raise
    
    def configure_extension(
        self,
        extension_id: str,
        config: Dict
    ) -> None:
        """
        Configure extension settings
        
        Args:
            extension_id: Extension ID
            config: Configuration dictionary
        """
        try:
            ext_dir = os.path.join(self.extension_dir, extension_id)
            config_file = os.path.join(ext_dir, 'config.json')
            
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=4)
                
            self.logger.info(f"Configured extension {extension_id}")
        except Exception as e:
            self.logger.error(f"Failed to configure extension: {str(e)}")
            raise
    
    def get_extension_path(self, extension_id: str) -> Optional[str]:
        """
        Get extension installation path
        
        Args:
            extension_id: Extension ID
            
        Returns:
            Optional[str]: Extension path or None if not found
        """
        ext_info = self.extensions.get(extension_id)
        return ext_info['path'] if ext_info else None
    
    def list_extensions(self) -> Dict[str, Dict]:
        """
        List installed extensions
        
        Returns:
            Dict[str, Dict]: Dictionary of installed extensions
        """
        return self.extensions
    
    def enable_extension(self, extension_id: str) -> None:
        """
        Enable extension
        
        Args:
            extension_id: Extension ID to enable
        """
        if extension_id in self.extensions:
            self.extensions[extension_id]['enabled'] = True
            self.logger.info(f"Enabled extension {extension_id}")
        else:
            self.logger.warning(f"Extension {extension_id} not found")
    
    def disable_extension(self, extension_id: str) -> None:
        """
        Disable extension
        
        Args:
            extension_id: Extension ID to disable
        """
        if extension_id in self.extensions:
            self.extensions[extension_id]['enabled'] = False
            self.logger.info(f"Disabled extension {extension_id}")
        else:
            self.logger.warning(f"Extension {extension_id} not found")
    
    def cleanup(self) -> None:
        """Clean up extension directory"""
        try:
            if os.path.exists(self.extension_dir):
                shutil.rmtree(self.extension_dir)
            self.extensions = {}
            self.logger.info("Cleaned up extensions")
        except Exception as e:
            self.logger.error(f"Failed to cleanup extensions: {str(e)}")

# Create global extension manager instance
extension_manager = ExtensionManager(os.path.join(os.getcwd(), 'data', 'extensions'))
