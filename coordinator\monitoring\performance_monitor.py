"""
性能监控器

该模块提供系统性能监控功能，负责收集和分析系统和智能体的性能指标。
"""

import logging
import threading
import time
import psutil
import os
from typing import Dict, List, Any, Optional, Set, Callable

from coordinator.core.system_state import SystemState
from coordinator.communication.event_system import EventSystem


class PerformanceMonitor:
    """性能监控器，负责收集和分析系统和智能体的性能指标"""
    
    def __init__(self, system_state: SystemState, event_system: EventSystem, check_interval: float = 10.0):
        """
        初始化性能监控器
        
        Args:
            system_state: 系统状态实例
            event_system: 事件系统实例
            check_interval: 性能检查间隔（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.system_state = system_state
        self.event_system = event_system
        self.check_interval = check_interval
        self._lock = threading.RLock()
        self._running = False
        self._monitor_thread = None
        self._metrics: Dict[str, Dict[str, Any]] = {}  # 性能指标历史数据
        self._agent_metrics: Dict[str, Dict[str, Dict[str, Any]]] = {}  # 智能体性能指标历史数据
        self._metric_collectors: Dict[str, Callable[[], Dict[str, Any]]] = {}  # 指标收集器
        self._agent_metric_collectors: Dict[str, Dict[str, Callable[[], Dict[str, Any]]]] = {}  # 智能体指标收集器
        self._history_size = 100  # 历史数据大小
        
        # 注册默认的系统指标收集器
        self.register_metric_collector("system.cpu", self._collect_cpu_metrics)
        self.register_metric_collector("system.memory", self._collect_memory_metrics)
        self.register_metric_collector("system.disk", self._collect_disk_metrics)
        self.register_metric_collector("system.network", self._collect_network_metrics)
    
    def start(self) -> None:
        """启动性能监控器"""
        with self._lock:
            if self._running:
                self.logger.warning("性能监控器已经在运行")
                return
            
            self._running = True
            self._monitor_thread = threading.Thread(
                target=self._monitor_loop,
                name="PerformanceMonitor",
                daemon=True
            )
            self._monitor_thread.start()
            
            self.logger.info("性能监控器已启动")
    
    def stop(self) -> None:
        """停止性能监控器"""
        with self._lock:
            if not self._running:
                self.logger.warning("性能监控器未在运行")
                return
            
            self._running = False
            if self._monitor_thread:
                self._monitor_thread.join(timeout=5.0)
                self._monitor_thread = None
            
            self.logger.info("性能监控器已停止")
    
    def register_metric_collector(self, name: str, collector_func: Callable[[], Dict[str, Any]]) -> None:
        """
        注册指标收集器
        
        Args:
            name: 指标名称
            collector_func: 指标收集函数，返回包含指标的字典
        """
        with self._lock:
            self._metric_collectors[name] = collector_func
            self._metrics[name] = []
            self.logger.info(f"已注册指标收集器 '{name}'")
    
    def unregister_metric_collector(self, name: str) -> bool:
        """
        注销指标收集器
        
        Args:
            name: 指标名称
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if name not in self._metric_collectors:
                self.logger.warning(f"指标收集器 '{name}' 不存在")
                return False
            
            del self._metric_collectors[name]
            if name in self._metrics:
                del self._metrics[name]
            
            self.logger.info(f"已注销指标收集器 '{name}'")
            return True
    
    def register_agent_metric_collector(self, agent_name: str, metric_name: str, collector_func: Callable[[], Dict[str, Any]]) -> None:
        """
        注册智能体指标收集器
        
        Args:
            agent_name: 智能体名称
            metric_name: 指标名称
            collector_func: 指标收集函数，返回包含指标的字典
        """
        with self._lock:
            if agent_name not in self._agent_metric_collectors:
                self._agent_metric_collectors[agent_name] = {}
                self._agent_metrics[agent_name] = {}
            
            self._agent_metric_collectors[agent_name][metric_name] = collector_func
            self._agent_metrics[agent_name][metric_name] = []
            
            self.logger.info(f"已为智能体 '{agent_name}' 注册指标收集器 '{metric_name}'")
    
    def unregister_agent_metric_collector(self, agent_name: str, metric_name: str) -> bool:
        """
        注销智能体指标收集器
        
        Args:
            agent_name: 智能体名称
            metric_name: 指标名称
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_metric_collectors:
                self.logger.warning(f"智能体 '{agent_name}' 没有注册指标收集器")
                return False
            
            if metric_name not in self._agent_metric_collectors[agent_name]:
                self.logger.warning(f"智能体 '{agent_name}' 的指标收集器 '{metric_name}' 不存在")
                return False
            
            del self._agent_metric_collectors[agent_name][metric_name]
            
            if agent_name in self._agent_metrics and metric_name in self._agent_metrics[agent_name]:
                del self._agent_metrics[agent_name][metric_name]
            
            # 如果智能体没有指标收集器了，删除它
            if not self._agent_metric_collectors[agent_name]:
                del self._agent_metric_collectors[agent_name]
                if agent_name in self._agent_metrics:
                    del self._agent_metrics[agent_name]
            
            self.logger.info(f"已注销智能体 '{agent_name}' 的指标收集器 '{metric_name}'")
            return True
    
    def unregister_agent(self, agent_name: str) -> bool:
        """
        注销智能体的所有指标收集器
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_metric_collectors:
                self.logger.warning(f"智能体 '{agent_name}' 没有注册指标收集器")
                return False
            
            del self._agent_metric_collectors[agent_name]
            if agent_name in self._agent_metrics:
                del self._agent_metrics[agent_name]
            
            self.logger.info(f"已注销智能体 '{agent_name}' 的所有指标收集器")
            return True
    
    def _monitor_loop(self) -> None:
        """性能监控循环"""
        self.logger.info("性能监控循环已启动")
        
        while self._running:
            try:
                # 收集系统指标
                self._collect_metrics()
                
                # 收集智能体指标
                self._collect_agent_metrics()
                
                # 更新系统状态
                self._update_system_state()
                
                # 等待下一次检查
                for _ in range(int(self.check_interval * 2)):  # 分成更小的间隔，以便更快地响应停止信号
                    if not self._running:
                        break
                    time.sleep(0.5)
            
            except Exception as e:
                self.logger.error(f"性能监控循环出错: {str(e)}")
                time.sleep(5.0)  # 出错后等待一段时间再继续
        
        self.logger.info("性能监控循环已停止")
    
    def _collect_metrics(self) -> None:
        """收集系统指标"""
        self.logger.debug("收集系统指标")
        
        # 获取指标收集器的副本
        with self._lock:
            metric_collectors = self._metric_collectors.copy()
        
        # 收集每个指标
        for name, collector_func in metric_collectors.items():
            try:
                metrics = collector_func()
                
                # 添加时间戳
                metrics["timestamp"] = time.time()
                
                # 存储指标
                with self._lock:
                    if name in self._metrics:
                        self._metrics[name].append(metrics)
                        
                        # 限制历史数据大小
                        if len(self._metrics[name]) > self._history_size:
                            self._metrics[name] = self._metrics[name][-self._history_size:]
                
                # 发布指标事件
                self.event_system.publish("system.metrics", {
                    "name": name,
                    "metrics": metrics
                })
            
            except Exception as e:
                self.logger.error(f"收集指标 '{name}' 时出错: {str(e)}")
    
    def _collect_agent_metrics(self) -> None:
        """收集智能体指标"""
        self.logger.debug("收集智能体指标")
        
        # 获取智能体指标收集器的副本
        with self._lock:
            agent_metric_collectors = {agent: collectors.copy() for agent, collectors in self._agent_metric_collectors.items()}
        
        # 收集每个智能体的指标
        for agent_name, collectors in agent_metric_collectors.items():
            for metric_name, collector_func in collectors.items():
                try:
                    metrics = collector_func()
                    
                    # 添加时间戳
                    metrics["timestamp"] = time.time()
                    
                    # 存储指标
                    with self._lock:
                        if agent_name in self._agent_metrics and metric_name in self._agent_metrics[agent_name]:
                            self._agent_metrics[agent_name][metric_name].append(metrics)
                            
                            # 限制历史数据大小
                            if len(self._agent_metrics[agent_name][metric_name]) > self._history_size:
                                self._agent_metrics[agent_name][metric_name] = self._agent_metrics[agent_name][metric_name][-self._history_size:]
                    
                    # 发布指标事件
                    self.event_system.publish("agent.metrics", {
                        "agent": agent_name,
                        "name": metric_name,
                        "metrics": metrics
                    })
                    
                    # 更新智能体指标
                    self.system_state.set_agent_metric(agent_name, metric_name, metrics)
                
                except Exception as e:
                    self.logger.error(f"收集智能体 '{agent_name}' 的指标 '{metric_name}' 时出错: {str(e)}")
    
    def _update_system_state(self) -> None:
        """更新系统状态"""
        try:
            # 获取最新的系统指标
            cpu_metrics = self.get_latest_metrics("system.cpu")
            memory_metrics = self.get_latest_metrics("system.memory")
            disk_metrics = self.get_latest_metrics("system.disk")
            network_metrics = self.get_latest_metrics("system.network")
            
            if cpu_metrics and memory_metrics and disk_metrics:
                # 更新系统资源使用情况
                self.system_state.update_resources(
                    cpu_usage=cpu_metrics.get("usage", 0.0),
                    memory_usage=memory_metrics.get("percent", 0.0),
                    disk_usage=disk_metrics.get("percent", 0.0),
                    network_usage=network_metrics.get("usage_percent", 0.0) if network_metrics else 0.0
                )
        
        except Exception as e:
            self.logger.error(f"更新系统状态时出错: {str(e)}")
    
    def _collect_cpu_metrics(self) -> Dict[str, Any]:
        """
        收集CPU指标
        
        Returns:
            CPU指标字典
        """
        cpu_percent = psutil.cpu_percent(interval=0.1)
        cpu_times = psutil.cpu_times_percent(interval=0.1)
        
        return {
            "usage": cpu_percent,
            "user": cpu_times.user,
            "system": cpu_times.system,
            "idle": cpu_times.idle,
            "count": psutil.cpu_count(),
            "count_logical": psutil.cpu_count(logical=True)
        }
    
    def _collect_memory_metrics(self) -> Dict[str, Any]:
        """
        收集内存指标
        
        Returns:
            内存指标字典
        """
        memory = psutil.virtual_memory()
        
        return {
            "total": memory.total,
            "available": memory.available,
            "used": memory.used,
            "free": memory.free,
            "percent": memory.percent
        }
    
    def _collect_disk_metrics(self) -> Dict[str, Any]:
        """
        收集磁盘指标
        
        Returns:
            磁盘指标字典
        """
        # 获取当前目录所在的磁盘
        path = os.path.abspath(os.path.dirname(__file__))
        disk = psutil.disk_usage(path)
        
        return {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": disk.percent
        }
    
    def _collect_network_metrics(self) -> Dict[str, Any]:
        """
        收集网络指标
        
        Returns:
            网络指标字典
        """
        # 获取网络IO计数器
        net_io = psutil.net_io_counters()
        
        # 计算网络使用率需要两个时间点的数据
        # 这里简化处理，只返回当前的计数器值
        return {
            "bytes_sent": net_io.bytes_sent,
            "bytes_recv": net_io.bytes_recv,
            "packets_sent": net_io.packets_sent,
            "packets_recv": net_io.packets_recv,
            "errin": net_io.errin,
            "errout": net_io.errout,
            "dropin": net_io.dropin,
            "dropout": net_io.dropout,
            "usage_percent": 0.0  # 简化处理，实际应该计算两个时间点之间的变化率
        }
    
    def get_latest_metrics(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取最新的指标
        
        Args:
            name: 指标名称
            
        Returns:
            最新的指标，如果没有则返回None
        """
        with self._lock:
            if name not in self._metrics or not self._metrics[name]:
                return None
            
            return self._metrics[name][-1]
    
    def get_metrics_history(self, name: str, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取指标历史
        
        Args:
            name: 指标名称
            limit: 限制返回的历史数量
            
        Returns:
            指标历史列表
        """
        with self._lock:
            if name not in self._metrics:
                return []
            
            history = self._metrics[name]
            if limit:
                return history[-limit:]
            else:
                return history.copy()
    
    def get_agent_latest_metrics(self, agent_name: str, metric_name: str) -> Optional[Dict[str, Any]]:
        """
        获取智能体最新的指标
        
        Args:
            agent_name: 智能体名称
            metric_name: 指标名称
            
        Returns:
            最新的指标，如果没有则返回None
        """
        with self._lock:
            if agent_name not in self._agent_metrics:
                return None
            
            if metric_name not in self._agent_metrics[agent_name] or not self._agent_metrics[agent_name][metric_name]:
                return None
            
            return self._agent_metrics[agent_name][metric_name][-1]
    
    def get_agent_metrics_history(self, agent_name: str, metric_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取智能体指标历史
        
        Args:
            agent_name: 智能体名称
            metric_name: 指标名称
            limit: 限制返回的历史数量
            
        Returns:
            指标历史列表
        """
        with self._lock:
            if agent_name not in self._agent_metrics:
                return []
            
            if metric_name not in self._agent_metrics[agent_name]:
                return []
            
            history = self._agent_metrics[agent_name][metric_name]
            if limit:
                return history[-limit:]
            else:
                return history.copy()
    
    def get_all_latest_metrics(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有最新的指标
        
        Returns:
            指标字典，键为指标名称，值为最新的指标
        """
        result = {}
        
        with self._lock:
            for name in self._metrics:
                if self._metrics[name]:
                    result[name] = self._metrics[name][-1]
        
        return result
    
    def get_all_agent_latest_metrics(self, agent_name: str) -> Dict[str, Dict[str, Any]]:
        """
        获取智能体所有最新的指标
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            指标字典，键为指标名称，值为最新的指标
        """
        result = {}
        
        with self._lock:
            if agent_name in self._agent_metrics:
                for metric_name in self._agent_metrics[agent_name]:
                    if self._agent_metrics[agent_name][metric_name]:
                        result[metric_name] = self._agent_metrics[agent_name][metric_name][-1]
        
        return result
    
    def get_metric_collectors(self) -> List[str]:
        """
        获取所有指标收集器名称
        
        Returns:
            指标收集器名称列表
        """
        with self._lock:
            return list(self._metric_collectors.keys())
    
    def get_agent_metric_collectors(self, agent_name: str) -> List[str]:
        """
        获取智能体的所有指标收集器名称
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            指标收集器名称列表
        """
        with self._lock:
            if agent_name not in self._agent_metric_collectors:
                return []
            
            return list(self._agent_metric_collectors[agent_name].keys())
    
    def get_all_agent_metric_collectors(self) -> Dict[str, List[str]]:
        """
        获取所有智能体的指标收集器名称
        
        Returns:
            指标收集器名称字典，键为智能体名称，值为指标收集器名称列表
        """
        with self._lock:
            return {agent: list(collectors.keys()) for agent, collectors in self._agent_metric_collectors.items()}