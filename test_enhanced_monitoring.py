#!/usr/bin/env python3
"""
测试增强后的监控智能体

验证闭环运行和多账号支持功能
"""

import asyncio
import logging
import json
import os
import time
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_enhanced_monitoring_agent():
    """测试增强后的监控智能体"""
    logger.info("🧪 开始测试增强后的监控智能体...")
    
    try:
        # 导入增强后的监控智能体
        from monitoring.monitoring_agent import MonitoringAgent
        
        # 创建测试配置
        test_config = {
            "check_interval_minutes": 1,  # 1分钟检查一次（测试用）
            "data_storage_path": "test_data/monitoring",
            "max_history_days": 30,
            "max_errors": 3,
            "notification_channels": ["console"],
            "priority_keywords": ["airdrop", "token", "launch"],
            "monitored_channels": {
                "twitter": True,
                "discord": True,
                "telegram": True,
                "website": True
            }
        }
        
        # 保存测试配置
        os.makedirs("test_data", exist_ok=True)
        with open("test_data/monitoring_config.json", "w") as f:
            json.dump(test_config, f, indent=2)
        
        # 创建监控智能体实例
        agent = MonitoringAgent("test_data/monitoring_config.json")
        logger.info("✅ 监控智能体创建成功")
        
        # 测试多账号功能
        await test_multi_account_features(agent)
        
        # 测试持续运行功能
        await test_continuous_operation(agent)
        
        # 测试项目监控功能
        await test_project_monitoring(agent)
        
        # 测试错误恢复功能
        await test_error_recovery(agent)
        
        logger.info("🎉 所有测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_multi_account_features(agent):
    """测试多账号功能"""
    logger.info("📱 测试多账号功能...")
    
    try:
        # 添加测试账号
        accounts = [
            {
                "id": "twitter_account_1",
                "data": {
                    "platform": "twitter",
                    "username": "test_user_1",
                    "api_key": "test_key_1",
                    "proxy": "proxy1.example.com:8080"
                }
            },
            {
                "id": "discord_account_1", 
                "data": {
                    "platform": "discord",
                    "username": "test_user_2",
                    "token": "test_token_1",
                    "proxy": "proxy2.example.com:8080"
                }
            },
            {
                "id": "telegram_account_1",
                "data": {
                    "platform": "telegram",
                    "username": "test_user_3",
                    "phone": "+**********",
                    "proxy": "proxy3.example.com:8080"
                }
            }
        ]
        
        # 添加账号
        for account in accounts:
            success = agent.add_account(account["id"], account["data"])
            if success:
                logger.info(f"  ✅ 添加账号成功: {account['id']}")
            else:
                logger.error(f"  ❌ 添加账号失败: {account['id']}")
        
        # 测试账号切换
        for account in accounts:
            success = agent.switch_account(account["id"])
            if success:
                current = agent.get_current_account()
                logger.info(f"  ✅ 切换到账号: {account['id']} ({current.get('username', 'unknown')})")
            else:
                logger.error(f"  ❌ 切换账号失败: {account['id']}")
        
        # 启用账号轮换
        agent.enable_account_rotation(interval=30)  # 30秒轮换一次（测试用）
        logger.info("  ✅ 启用账号轮换")
        
        # 测试账号轮换
        for i in range(3):
            success = agent.rotate_account()
            current = agent.get_current_account()
            if current:
                logger.info(f"  ✅ 轮换 {i+1}: 当前账号 {current.get('username', 'unknown')}")
            time.sleep(1)
        
        # 获取账号统计
        stats = agent.get_account_stats()
        logger.info(f"  📊 账号统计: {len(stats)} 个账号")
        
        logger.info("✅ 多账号功能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 多账号功能测试失败: {e}")


async def test_continuous_operation(agent):
    """测试持续运行功能"""
    logger.info("🔄 测试持续运行功能...")
    
    try:
        # 启动持续运行
        success = await agent.start()
        if success:
            logger.info("  ✅ 持续运行启动成功")
        else:
            logger.error("  ❌ 持续运行启动失败")
            return
        
        # 运行几个周期
        logger.info("  🕐 运行测试周期...")
        await asyncio.sleep(5)  # 等待几秒让它运行
        
        # 检查运行状态
        if agent.is_running():
            logger.info("  ✅ 智能体正在运行")
        else:
            logger.error("  ❌ 智能体未在运行")
        
        # 获取运行统计
        stats = agent.get_operation_stats()
        logger.info(f"  📊 运行统计: {stats}")
        
        # 测试暂停和恢复
        await agent.pause_operation()
        logger.info("  ⏸️ 暂停运行")
        
        await asyncio.sleep(2)
        
        await agent.resume_operation()
        logger.info("  ▶️ 恢复运行")
        
        await asyncio.sleep(3)
        
        # 停止持续运行
        success = await agent.stop()
        if success:
            logger.info("  ✅ 持续运行停止成功")
        else:
            logger.error("  ❌ 持续运行停止失败")
        
        logger.info("✅ 持续运行功能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 持续运行功能测试失败: {e}")


async def test_project_monitoring(agent):
    """测试项目监控功能"""
    logger.info("📊 测试项目监控功能...")
    
    try:
        # 添加测试项目
        test_projects = [
            {
                "id": "test_project_1",
                "name": "Test Airdrop Project 1",
                "twitter": "@testproject1",
                "discord": "https://discord.gg/testproject1",
                "telegram": "@testproject1_official",
                "website": "https://testproject1.com"
            },
            {
                "id": "test_project_2", 
                "name": "Test DeFi Project 2",
                "twitter": "@testproject2",
                "website": "https://testproject2.com"
            }
        ]
        
        # 添加项目到监控
        for project in test_projects:
            success = agent.add_project(project)
            if success:
                logger.info(f"  ✅ 添加监控项目: {project['name']}")
            else:
                logger.error(f"  ❌ 添加监控项目失败: {project['name']}")
        
        # 启动监控并运行一个周期
        await agent.start()
        logger.info("  🔍 开始监控...")
        
        # 等待一个监控周期完成
        await asyncio.sleep(3)
        
        # 检查监控状态
        status = agent.status()
        logger.info(f"  📊 监控状态: {status}")
        
        # 获取项目更新
        for project in test_projects:
            updates = agent.get_project_updates(project["id"])
            logger.info(f"  📰 项目 {project['name']} 更新数: {len(updates)}")
        
        # 获取所有更新
        all_updates = agent.get_all_updates()
        logger.info(f"  📰 总更新数: {len(all_updates)}")
        
        await agent.stop()
        logger.info("✅ 项目监控功能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 项目监控功能测试失败: {e}")


async def test_error_recovery(agent):
    """测试错误恢复功能"""
    logger.info("🛡️ 测试错误恢复功能...")
    
    try:
        # 模拟错误情况
        logger.info("  🔧 模拟错误情况...")
        
        # 启动监控
        await agent.start()
        
        # 模拟连续错误
        original_method = agent._check_single_project
        error_count = 0
        
        def mock_error_method(*args, **kwargs):
            nonlocal error_count
            error_count += 1
            if error_count <= 3:  # 前3次抛出错误
                raise Exception(f"模拟错误 {error_count}")
            return original_method(*args, **kwargs)
        
        # 替换方法以模拟错误
        agent._check_single_project = mock_error_method
        
        # 等待错误恢复机制触发
        await asyncio.sleep(5)
        
        # 检查恢复状态
        stats = agent.get_operation_stats()
        logger.info(f"  📊 错误恢复后统计: {stats}")
        
        # 恢复原方法
        agent._check_single_project = original_method
        
        await agent.stop()
        logger.info("✅ 错误恢复功能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 错误恢复功能测试失败: {e}")


async def main():
    """主函数"""
    logger.info("🚀 开始增强监控智能体测试")
    
    try:
        success = await test_enhanced_monitoring_agent()
        
        if success:
            logger.info("🎉 所有测试通过！")
            return 0
        else:
            logger.error("❌ 测试失败")
            return 1
            
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return 1
    finally:
        # 清理测试数据
        import shutil
        if os.path.exists("test_data"):
            shutil.rmtree("test_data")
            logger.info("🧹 清理测试数据完成")


if __name__ == "__main__":
    exit(asyncio.run(main()))
