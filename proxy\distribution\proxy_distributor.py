"""
代理分发器

该模块负责分发代理给客户端使用。
"""

import logging
import threading
import time
from typing import Dict, List, Any, Optional, Set, Tuple

from proxy.models.proxy import Proxy
from proxy.management.proxy_pool import ProxyPool


class ProxyDistributor:
    """代理分发器，负责分发代理给客户端使用"""
    
    def __init__(self, config: Dict[str, Any], proxy_pool: ProxyPool):
        """
        初始化代理分发器
        
        Args:
            config: 配置字典，包含代理分发器的配置信息
            proxy_pool: 代理池实例
        """
        self.config = config
        self.proxy_pool = proxy_pool
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        
        # 配置参数
        self.rotation_interval = config.get('rotation_interval', 600)  # 代理轮换间隔（秒）
        self.max_uses = config.get('max_uses', 50)  # 最大使用次数
        self.sticky_sessions = config.get('sticky_sessions', True)  # 是否启用粘性会话
        
        # 客户端会话
        self._client_sessions: Dict[str, Dict[str, Any]] = {}  # client_id -> session_info
        self._proxy_uses: Dict[str, int] = {}  # proxy_url -> use_count
    
    def get_proxy(self, protocol: str = None, country: str = None, anonymity: str = None) -> Optional[Proxy]:
        """
        获取代理
        
        Args:
            protocol: 代理协议，如 'http', 'https', 'socks4', 'socks5'
            country: 代理国家/地区
            anonymity: 代理匿名级别，如 'transparent', 'anonymous', 'elite'
            
        Returns:
            代理对象，如果没有可用的代理则返回None
        """
        # 生成客户端 ID
        client_id = self._generate_client_id(protocol, country, anonymity)
        
        with self._lock:
            # 检查是否有粘性会话
            if self.sticky_sessions and client_id in self._client_sessions:
                session = self._client_sessions[client_id]
                proxy = session.get('proxy')
                last_used = session.get('last_used', 0)
                use_count = session.get('use_count', 0)
                
                # 检查代理是否需要轮换
                now = time.time()
                if (proxy and proxy.is_valid and 
                    now - last_used < self.rotation_interval and 
                    use_count < self.max_uses):
                    
                    # 更新会话信息
                    session['last_used'] = now
                    session['use_count'] += 1
                    
                    # 更新代理使用计数
                    proxy_url = proxy.url
                    self._proxy_uses[proxy_url] = self._proxy_uses.get(proxy_url, 0) + 1
                    
                    self.logger.debug(f"使用粘性会话代理: {proxy.host}:{proxy.port} (client_id: {client_id})")
                    return proxy
            
            # 获取新代理
            proxy = self.proxy_pool.get_proxy(protocol, country, anonymity)
            
            if not proxy:
                return None
            
            # 创建或更新会话
            self._client_sessions[client_id] = {
                'proxy': proxy,
                'last_used': time.time(),
                'use_count': 1
            }
            
            # 更新代理使用计数
            proxy_url = proxy.url
            self._proxy_uses[proxy_url] = self._proxy_uses.get(proxy_url, 0) + 1
            
            self.logger.debug(f"分配新代理: {proxy.host}:{proxy.port} (client_id: {client_id})")
            return proxy
    
    def get_proxies(self, count: int, protocol: str = None, country: str = None, anonymity: str = None) -> List[Proxy]:
        """
        获取多个代理
        
        Args:
            count: 要获取的代理数量
            protocol: 代理协议，如 'http', 'https', 'socks4', 'socks5'
            country: 代理国家/地区
            anonymity: 代理匿名级别，如 'transparent', 'anonymous', 'elite'
            
        Returns:
            代理对象列表
        """
        with self._lock:
            # 获取代理
            proxies = self.proxy_pool.get_proxies(count, protocol, country, anonymity)
            
            # 更新代理使用计数
            for proxy in proxies:
                proxy_url = proxy.url
                self._proxy_uses[proxy_url] = self._proxy_uses.get(proxy_url, 0) + 1
            
            self.logger.debug(f"分配 {len(proxies)} 个代理")
            return proxies
    
    def report_proxy_status(self, proxy: Proxy, success: bool, response_time: float = None) -> None:
        """
        报告代理状态
        
        Args:
            proxy: 代理对象
            success: 是否成功使用
            response_time: 响应时间（秒）
        """
        # 更新代理池中的代理状态
        self.proxy_pool.update_proxy_status(proxy, success, response_time)
        
        # 如果代理失败，从会话中移除
        if not success:
            with self._lock:
                for client_id, session in list(self._client_sessions.items()):
                    session_proxy = session.get('proxy')
                    if session_proxy and session_proxy.url == proxy.url:
                        del self._client_sessions[client_id]
                        self.logger.debug(f"从会话中移除失败的代理: {proxy.host}:{proxy.port} (client_id: {client_id})")
    
    def rotate_proxy(self, client_id: str) -> Optional[Proxy]:
        """
        轮换代理
        
        Args:
            client_id: 客户端 ID
            
        Returns:
            新的代理对象，如果没有可用的代理则返回None
        """
        with self._lock:
            if client_id not in self._client_sessions:
                self.logger.warning(f"客户端 {client_id} 没有会话")
                return None
            
            session = self._client_sessions[client_id]
            old_proxy = session.get('proxy')
            
            # 获取会话参数
            protocol = old_proxy.protocol if old_proxy else None
            country = old_proxy.country if old_proxy else None
            anonymity = old_proxy.anonymity if old_proxy else None
            
            # 获取新代理
            new_proxy = self.proxy_pool.get_proxy(protocol, country, anonymity)
            
            if not new_proxy:
                return None
            
            # 更新会话
            session['proxy'] = new_proxy
            session['last_used'] = time.time()
            session['use_count'] = 1
            
            # 更新代理使用计数
            proxy_url = new_proxy.url
            self._proxy_uses[proxy_url] = self._proxy_uses.get(proxy_url, 0) + 1
            
            self.logger.debug(f"轮换代理: {old_proxy.host}:{old_proxy.port} -> {new_proxy.host}:{new_proxy.port} (client_id: {client_id})")
            return new_proxy
    
    def clear_sessions(self) -> None:
        """清除所有会话"""
        with self._lock:
            self._client_sessions.clear()
            self._proxy_uses.clear()
            self.logger.info("已清除所有会话")
    
    def cleanup_sessions(self, max_age: float = 3600.0) -> int:
        """
        清理过期会话
        
        Args:
            max_age: 最大会话年龄（秒）
            
        Returns:
            清理的会话数量
        """
        with self._lock:
            now = time.time()
            to_remove = []
            
            for client_id, session in self._client_sessions.items():
                last_used = session.get('last_used', 0)
                if now - last_used > max_age:
                    to_remove.append(client_id)
            
            # 移除过期会话
            for client_id in to_remove:
                del self._client_sessions[client_id]
            
            count = len(to_remove)
            if count > 0:
                self.logger.info(f"清理了 {count} 个过期会话")
            
            return count
    
    def _generate_client_id(self, protocol: str = None, country: str = None, anonymity: str = None) -> str:
        """
        生成客户端 ID
        
        Args:
            protocol: 代理协议
            country: 代理国家/地区
            anonymity: 代理匿名级别
            
        Returns:
            客户端 ID
        """
        # 使用请求参数作为客户端 ID
        parts = []
        
        if protocol:
            parts.append(f"protocol={protocol}")
        
        if country:
            parts.append(f"country={country}")
        
        if anonymity:
            parts.append(f"anonymity={anonymity}")
        
        # 如果没有参数，使用默认 ID
        if not parts:
            return "default"
        
        return "&".join(parts)
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取分发器状态
        
        Returns:
            分发器状态字典
        """
        with self._lock:
            return {
                "sessions": len(self._client_sessions),
                "proxy_uses": len(self._proxy_uses),
                "config": {
                    "rotation_interval": self.rotation_interval,
                    "max_uses": self.max_uses,
                    "sticky_sessions": self.sticky_sessions
                }
            }