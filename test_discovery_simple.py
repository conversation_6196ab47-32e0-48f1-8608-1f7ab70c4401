#!/usr/bin/env python3
"""
简化的项目发现智能体测试

直接测试发现智能体的功能
"""

import logging
import json
import os
import time
import sys

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_discovery_agent():
    """测试项目发现智能体"""
    logger.info("🔍 开始测试项目发现智能体...")
    
    try:
        # 1. 创建测试配置
        config = create_test_config()
        
        # 2. 测试源管理器
        test_source_manager(config)
        
        # 3. 测试发现智能体
        test_full_discovery_agent(config)
        
        logger.info("🎉 测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_test_config():
    """创建测试配置"""
    logger.info("📋 创建测试配置...")
    
    config = {
        'discovery_interval': 10,  # 10秒
        'max_projects_per_source': 3,
        'storage_file': 'test_projects.json',
        'storage_enabled': True,
        'sources': {
            'enabled_sources': ['enhanced_aggregator'],
            'enhanced_aggregator': {
                'enabled': True,
                'timeout': 30,
                'use_mock_data': True
            }
        },
        'filters': {
            'enabled': True,
            'min_score': 0.1
        },
        'collectors': {
            'enabled': True,
            'timeout': 30
        }
    }
    
    logger.info("  ✅ 测试配置创建成功")
    return config


def test_source_manager(config):
    """测试源管理器"""
    logger.info("🔧 测试源管理器...")
    
    try:
        from discovery.sources.source_manager import SourceManager
        
        # 创建源管理器
        source_manager = SourceManager(config['sources'])
        logger.info("  ✅ 源管理器创建成功")
        
        # 检查可用源
        sources = source_manager.get_sources()
        logger.info(f"  📊 可用源数量: {len(sources)}")
        logger.info(f"  📋 源列表: {list(sources.keys())}")
        
        # 测试获取项目
        logger.info("  🔍 测试获取项目...")
        projects = source_manager.get_projects(3)
        logger.info(f"  📰 获取到项目数量: {len(projects)}")
        
        if projects:
            logger.info("  ✅ 源管理器工作正常")
            for i, project in enumerate(projects[:2]):
                logger.info(f"    项目 {i+1}: {project.get('name', 'Unknown')} - {project.get('url', 'No URL')}")
        else:
            logger.warning("  ⚠️ 源管理器未获取到任何项目")
            
            # 检查具体的源
            for source_name, source in sources.items():
                logger.info(f"  🔍 直接测试源: {source_name}")
                try:
                    source_projects = source.get_projects(2)
                    logger.info(f"    📰 {source_name} 获取到 {len(source_projects)} 个项目")
                    if source_projects:
                        for j, proj in enumerate(source_projects[:1]):
                            logger.info(f"      项目: {proj.get('name', 'Unknown')}")
                except Exception as e:
                    logger.error(f"    ❌ {source_name} 测试失败: {e}")
        
        return source_manager
        
    except Exception as e:
        logger.error(f"  ❌ 源管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_full_discovery_agent(config):
    """测试完整的发现智能体"""
    logger.info("🚀 测试完整的发现智能体...")
    
    try:
        from discovery.discovery_agent import DiscoveryAgent
        
        # 创建发现智能体
        discovery_agent = DiscoveryAgent(config)
        logger.info("  ✅ 发现智能体创建成功")
        
        # 手动触发一次发现
        logger.info("  🔍 手动触发项目发现...")
        discovery_agent._discover_projects()
        
        # 检查发现的项目
        projects = discovery_agent.get_projects()
        logger.info(f"  📊 发现的项目数量: {len(projects)}")
        
        if projects:
            logger.info("  ✅ 发现智能体工作正常！")
            for i, project in enumerate(projects[:3]):
                logger.info(f"    项目 {i+1}: {project.name} - {project.blockchain.value} - {project.project_type.value}")
                logger.info(f"      URL: {project.url}")
                logger.info(f"      描述: {project.description[:100]}...")
        else:
            logger.warning("  ⚠️ 发现智能体未发现任何项目")
            
            # 检查组件状态
            logger.info("  🔍 检查组件状态...")
            
            # 检查源管理器
            if discovery_agent.source_manager:
                sources = discovery_agent.source_manager.get_sources()
                logger.info(f"    源管理器: {len(sources)} 个源")
            else:
                logger.error("    ❌ 源管理器未初始化")
            
            # 检查过滤器管理器
            if discovery_agent.filter_manager:
                logger.info("    ✅ 过滤器管理器已初始化")
            else:
                logger.error("    ❌ 过滤器管理器未初始化")
            
            # 检查收集器管理器
            if discovery_agent.collector_manager:
                logger.info("    ✅ 收集器管理器已初始化")
            else:
                logger.error("    ❌ 收集器管理器未初始化")
        
        # 获取统计信息
        stats = discovery_agent.get_stats()
        logger.info(f"  📊 统计信息: {stats}")
        
        return len(projects) > 0
        
    except Exception as e:
        logger.error(f"  ❌ 发现智能体测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_mock_enhanced_aggregator():
    """创建模拟的增强聚合器源"""
    logger.info("🔧 创建模拟的增强聚合器源...")
    
    # 检查文件是否已存在
    if os.path.exists('discovery/sources/enhanced_aggregator.py'):
        logger.info("  ✅ 增强聚合器源文件已存在")
        return
    
    mock_content = '''"""
增强聚合器源 - 模拟版本
"""

import logging
import time
import random
from typing import Dict, List, Any

from discovery.sources.base_source import BaseProjectSource


class EnhancedAggregatorSource(BaseProjectSource):
    """增强聚合器源 - 模拟版本"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.use_mock_data = config.get('use_mock_data', True)
        self.logger.info("增强聚合器源初始化完成（模拟模式）")
    
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """获取项目（模拟数据）"""
        if not self.use_mock_data:
            return []
        
        self.logger.info(f"生成 {count} 个模拟项目")
        
        projects = []
        
        # 模拟项目数据
        mock_projects = [
            {
                'name': 'LayerZero Airdrop',
                'description': 'Cross-chain interoperability protocol airdrop',
                'blockchain': 'ethereum',
                'project_type': 'airdrop'
            },
            {
                'name': 'zkSync Era Testnet',
                'description': 'Layer 2 scaling solution testnet',
                'blockchain': 'ethereum',
                'project_type': 'testnet'
            },
            {
                'name': 'Arbitrum Odyssey',
                'description': 'Arbitrum ecosystem exploration campaign',
                'blockchain': 'arbitrum',
                'project_type': 'campaign'
            },
            {
                'name': 'Optimism Quests',
                'description': 'Optimism network quests and rewards',
                'blockchain': 'optimism',
                'project_type': 'quest'
            },
            {
                'name': 'Polygon zkEVM',
                'description': 'Polygon zero-knowledge Ethereum Virtual Machine',
                'blockchain': 'polygon',
                'project_type': 'testnet'
            }
        ]
        
        # 随机选择项目
        selected_projects = random.sample(mock_projects, min(count, len(mock_projects)))
        
        for i, project_data in enumerate(selected_projects):
            project = {
                'id': f"enhanced_aggregator_{int(time.time())}_{i}",
                'name': project_data['name'],
                'description': project_data['description'],
                'url': f"https://example-{i}.com",
                'project_type': project_data['project_type'],
                'blockchain': project_data['blockchain'],
                'discovery_source': 'enhanced_aggregator',
                'source_url': 'https://mock-aggregator.com',
                'discovery_time': time.time()
            }
            
            projects.append(project)
        
        self.logger.info(f"生成了 {len(projects)} 个模拟项目")
        return projects
'''
    
    # 写入模拟文件
    with open('discovery/sources/enhanced_aggregator.py', 'w', encoding='utf-8') as f:
        f.write(mock_content)
    
    logger.info("  ✅ 模拟增强聚合器源创建成功")


def main():
    """主函数"""
    logger.info("🚀 开始项目发现智能体简化测试")
    
    try:
        # 创建模拟数据源
        create_mock_enhanced_aggregator()
        
        # 运行测试
        success = test_discovery_agent()
        
        if success:
            logger.info("🎉 测试完成，发现智能体工作正常！")
            return 0
        else:
            logger.error("❌ 测试发现问题")
            return 1
            
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return 1
    finally:
        # 清理测试文件
        if os.path.exists("test_projects.json"):
            os.remove("test_projects.json")
            logger.info("🧹 清理测试文件完成")


if __name__ == "__main__":
    exit(main())
