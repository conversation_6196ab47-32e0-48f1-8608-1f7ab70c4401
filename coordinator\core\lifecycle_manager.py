"""
生命周期管理器

该模块提供智能体生命周期管理功能，负责启动、停止和重启智能体。
"""

import logging
import threading
from typing import Dict, Any, List, Set

from coordinator.core.agent_registry import AgentRegistry


class LifecycleManager:
    """智能体生命周期管理器，负责管理智能体的启动、停止和重启"""
    
    def __init__(self, agent_registry: AgentRegistry):
        """
        初始化生命周期管理器
        
        Args:
            agent_registry: 智能体注册表实例
        """
        self.agent_registry = agent_registry
        self.logger = logging.getLogger(__name__)
        self.running_agents: Set[str] = set()
        self._lock = threading.RLock()
        
    def start_agent(self, agent_name: str) -> bool:
        """
        启动指定的智能体
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果智能体成功启动则返回True，否则返回False
        """
        with self._lock:
            agent = self.agent_registry.get(agent_name)
            if not agent:
                self.logger.error(f"无法启动不存在的智能体 '{agent_name}'")
                return False
            
            if agent_name in self.running_agents:
                self.logger.warning(f"智能体 '{agent_name}' 已经在运行")
                return True
            
            try:
                # 假设所有智能体都有start方法
                agent.start()
                self.running_agents.add(agent_name)
                self.logger.info(f"智能体 '{agent_name}' 已启动")
                return True
            except Exception as e:
                self.logger.error(f"启动智能体 '{agent_name}' 时出错: {str(e)}")
                return False
    
    def stop_agent(self, agent_name: str) -> bool:
        """
        停止指定的智能体
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果智能体成功停止则返回True，否则返回False
        """
        with self._lock:
            agent = self.agent_registry.get(agent_name)
            if not agent:
                self.logger.error(f"无法停止不存在的智能体 '{agent_name}'")
                return False
            
            if agent_name not in self.running_agents:
                self.logger.warning(f"智能体 '{agent_name}' 未在运行")
                return True
            
            try:
                # 假设所有智能体都有stop方法
                agent.stop()
                self.running_agents.remove(agent_name)
                self.logger.info(f"智能体 '{agent_name}' 已停止")
                return True
            except Exception as e:
                self.logger.error(f"停止智能体 '{agent_name}' 时出错: {str(e)}")
                return False
    
    def restart_agent(self, agent_name: str) -> bool:
        """
        重启指定的智能体
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果智能体成功重启则返回True，否则返回False
        """
        with self._lock:
            self.logger.info(f"正在重启智能体 '{agent_name}'")
            if self.stop_agent(agent_name):
                return self.start_agent(agent_name)
            return False
    
    def start_all_agents(self) -> Dict[str, bool]:
        """
        启动所有已注册的智能体
        
        Returns:
            包含所有智能体启动结果的字典，键为智能体名称，值为启动是否成功
        """
        self.logger.info("正在启动所有智能体...")
        results = {}
        for agent_name in self.agent_registry.get_names():
            results[agent_name] = self.start_agent(agent_name)
        return results
    
    def stop_all_agents(self) -> Dict[str, bool]:
        """
        停止所有正在运行的智能体
        
        Returns:
            包含所有智能体停止结果的字典，键为智能体名称，值为停止是否成功
        """
        self.logger.info("正在停止所有智能体...")
        results = {}
        # 创建一个副本以避免在迭代过程中修改集合
        running_agents = list(self.running_agents)
        for agent_name in running_agents:
            results[agent_name] = self.stop_agent(agent_name)
        return results
    
    def get_running_agents(self) -> List[str]:
        """
        获取所有正在运行的智能体名称
        
        Returns:
            包含所有正在运行的智能体名称的列表
        """
        with self._lock:
            return list(self.running_agents)
    
    def is_agent_running(self, agent_name: str) -> bool:
        """
        检查指定的智能体是否正在运行
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果智能体正在运行则返回True，否则返回False
        """
        with self._lock:
            return agent_name in self.running_agents