"""
Discord Handler

This module handles Discord-related tasks for the Task Execution Agent.
"""

import logging
import os
import json
import time
import random
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import discord
from discord.ext import commands

logger = logging.getLogger("DiscordHandler")

class DiscordHandler:
    """
    Handler for Discord-related tasks.
    
    Capabilities:
    - Server joining and setup
    - Message sending
    - Channel monitoring
    - Role management
    - Community engagement
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Discord handler.
        
        Args:
            config: Configuration for the handler
        """
        self.config = config or {}
        self.client = None
        self.bot = None
        self.authenticated = False
        self.loop = None
        
        # Initialize client if token is available
        if self._has_token():
            self._initialize_client()
        
        logger.info("Discord handler initialized")
    
    def _has_token(self) -> bool:
        """
        Check if Discord token is available.
        
        Returns:
            bool: True if token is available, False otherwise
        """
        return "token" in self.config and self.config["token"]
    
    def _initialize_client(self) -> bool:
        """
        Initialize the Discord client.
        
        Returns:
            bool: True if initialized successfully, False otherwise
        """
        try:
            # Create event loop
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            # Set up intents
            intents = discord.Intents.default()
            intents.message_content = True
            intents.members = True
            
            # Create client
            self.client = discord.Client(intents=intents)
            
            # Create bot
            self.bot = commands.Bot(command_prefix="!", intents=intents)
            
            # Set up event handlers
            @self.client.event
            async def on_ready():
                logger.info(f"Logged in as {self.client.user}")
                self.authenticated = True
            
            # Start client in background
            if self.config.get("auto_connect", False):
                self._connect_client()
            
            logger.info("Discord client initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Discord client: {e}")
            return False
    
    def _connect_client(self) -> bool:
        """
        Connect the Discord client.
        
        Returns:
            bool: True if connected successfully, False otherwise
        """
        if not self._has_token():
            logger.error("Discord token not available")
            return False
        
        try:
            # Run client in a separate thread
            def run_client():
                asyncio.set_event_loop(self.loop)
                self.loop.run_until_complete(self.client.start(self.config["token"]))
            
            import threading
            thread = threading.Thread(target=run_client)
            thread.daemon = True
            thread.start()
            
            # Wait for client to connect
            timeout = 30
            start_time = time.time()
            while not self.authenticated and time.time() - start_time < timeout:
                time.sleep(1)
            
            if not self.authenticated:
                logger.error("Timed out waiting for Discord client to connect")
                return False
            
            logger.info("Discord client connected")
            return True
        except Exception as e:
            logger.error(f"Error connecting Discord client: {e}")
            return False
    
    def _run_coroutine(self, coroutine):
        """
        Run a coroutine in the event loop.
        
        Args:
            coroutine: Coroutine to run
            
        Returns:
            Result of the coroutine
        """
        if self.loop and self.loop.is_running():
            # Create a future in the running loop
            future = asyncio.run_coroutine_threadsafe(coroutine, self.loop)
            return future.result(timeout=30)
        else:
            # Create a new loop if needed
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(coroutine)
    
    def register(self, **params) -> Dict[str, Any]:
        """
        Register with a Discord server.
        
        Args:
            **params: Parameters for registration
                - server_invite: Invite link or code
                
        Returns:
            Dict containing registration result
        """
        # This is a placeholder for the actual implementation
        # In a real implementation, this would involve browser automation
        # to join a Discord server using an invite link
        
        logger.info("Discord registration requested")
        
        server_invite = params.get("server_invite")
        if not server_invite:
            return {
                "success": False,
                "error": "Server invite is required"
            }
        
        # Simulate registration process
        time.sleep(2)
        
        return {
            "success": True,
            "message": "Discord registration simulated",
            "server_info": {
                "invite": server_invite,
                "joined_at": datetime.now().isoformat()
            }
        }
    
    def join_server(self, **params) -> Dict[str, Any]:
        """
        Join a Discord server using an invite.
        
        Args:
            **params: Parameters for joining
                - invite_code: Invite code or full URL
                
        Returns:
            Dict containing join result
        """
        if not self.authenticated:
            # Try to connect
            if not self._connect_client():
                return {
                    "success": False,
                    "error": "Not authenticated"
                }
        
        try:
            invite_code = params.get("invite_code")
            
            if not invite_code:
                return {
                    "success": False,
                    "error": "Invite code is required"
                }
            
            # Extract code from URL if needed
            if "/" in invite_code:
                invite_code = invite_code.split("/")[-1]
            
            # Join server
            async def join_server_async():
                invite = await self.client.fetch_invite(invite_code)
                guild = await invite.accept()
                return {
                    "id": guild.id,
                    "name": guild.name,
                    "member_count": guild.member_count
                }
            
            guild_info = self._run_coroutine(join_server_async())
            
            logger.info(f"Joined Discord server: {guild_info['name']}")
            
            return {
                "success": True,
                "guild": guild_info
            }
        except Exception as e:
            logger.error(f"Error joining Discord server: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def send_message(self, **params) -> Dict[str, Any]:
        """
        Send a message to a Discord channel.
        
        Args:
            **params: Parameters for sending
                - channel_id: Channel ID
                - content: Message content
                - embed: Optional embed data
                
        Returns:
            Dict containing send result
        """
        if not self.authenticated:
            # Try to connect
            if not self._connect_client():
                return {
                    "success": False,
                    "error": "Not authenticated"
                }
        
        try:
            channel_id = params.get("channel_id")
            content = params.get("content")
            embed_data = params.get("embed")
            
            if not channel_id:
                return {
                    "success": False,
                    "error": "Channel ID is required"
                }
            
            if not content and not embed_data:
                return {
                    "success": False,
                    "error": "Message must contain content or embed"
                }
            
            # Send message
            async def send_message_async():
                channel = await self.client.fetch_channel(channel_id)
                
                embed = None
                if embed_data:
                    embed = discord.Embed(
                        title=embed_data.get("title"),
                        description=embed_data.get("description"),
                        color=embed_data.get("color", 0x00ff00)
                    )
                    
                    if "fields" in embed_data:
                        for field in embed_data["fields"]:
                            embed.add_field(
                                name=field.get("name", ""),
                                value=field.get("value", ""),
                                inline=field.get("inline", False)
                            )
                
                message = await channel.send(content=content, embed=embed)
                return {
                    "id": message.id,
                    "channel_id": channel.id,
                    "guild_id": channel.guild.id if hasattr(channel, "guild") else None
                }
            
            message_info = self._run_coroutine(send_message_async())
            
            logger.info(f"Sent Discord message: {message_info['id']}")
            
            return {
                "success": True,
                "message": message_info
            }
        except Exception as e:
            logger.error(f"Error sending Discord message: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def react(self, **params) -> Dict[str, Any]:
        """
        React to a Discord message.
        
        Args:
            **params: Parameters for reacting
                - channel_id: Channel ID
                - message_id: Message ID
                - emoji: Emoji to react with
                
        Returns:
            Dict containing reaction result
        """
        if not self.authenticated:
            # Try to connect
            if not self._connect_client():
                return {
                    "success": False,
                    "error": "Not authenticated"
                }
        
        try:
            channel_id = params.get("channel_id")
            message_id = params.get("message_id")
            emoji = params.get("emoji")
            
            if not channel_id or not message_id:
                return {
                    "success": False,
                    "error": "Channel ID and message ID are required"
                }
            
            if not emoji:
                return {
                    "success": False,
                    "error": "Emoji is required"
                }
            
            # React to message
            async def react_async():
                channel = await self.client.fetch_channel(channel_id)
                message = await channel.fetch_message(message_id)
                await message.add_reaction(emoji)
                return {
                    "message_id": message.id,
                    "channel_id": channel.id
                }
            
            reaction_info = self._run_coroutine(react_async())
            
            logger.info(f"Reacted to Discord message: {reaction_info['message_id']}")
            
            return {
                "success": True,
                "reaction": reaction_info
            }
        except Exception as e:
            logger.error(f"Error reacting to Discord message: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_messages(self, **params) -> Dict[str, Any]:
        """
        Get messages from a Discord channel.
        
        Args:
            **params: Parameters for getting messages
                - channel_id: Channel ID
                - limit: Maximum number of messages to retrieve
                
        Returns:
            Dict containing messages
        """
        if not self.authenticated:
            # Try to connect
            if not self._connect_client():
                return {
                    "success": False,
                    "error": "Not authenticated"
                }
        
        try:
            channel_id = params.get("channel_id")
            limit = params.get("limit", 10)
            
            if not channel_id:
                return {
                    "success": False,
                    "error": "Channel ID is required"
                }
            
            # Get messages
            async def get_messages_async():
                channel = await self.client.fetch_channel(channel_id)
                messages = []
                
                async for message in channel.history(limit=limit):
                    messages.append({
                        "id": message.id,
                        "content": message.content,
                        "author": {
                            "id": message.author.id,
                            "name": message.author.name,
                            "discriminator": message.author.discriminator
                        },
                        "created_at": message.created_at.isoformat(),
                        "attachments": [a.url for a in message.attachments],
                        "embeds": len(message.embeds)
                    })
                
                return messages
            
            messages = self._run_coroutine(get_messages_async())
            
            logger.info(f"Retrieved {len(messages)} Discord messages from channel {channel_id}")
            
            return {
                "success": True,
                "messages": messages,
                "count": len(messages)
            }
        except Exception as e:
            logger.error(f"Error getting Discord messages: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def engage(self, **params) -> Dict[str, Any]:
        """
        Engage with a Discord server.
        
        Args:
            **params: Parameters for engagement
                - guild_id: Guild ID
                - channel_id: Optional specific channel ID
                - engagement_type: Type of engagement (chat, react, etc.)
                - min_messages: Minimum number of messages to send
                
        Returns:
            Dict containing engagement result
        """
        if not self.authenticated:
            # Try to connect
            if not self._connect_client():
                return {
                    "success": False,
                    "error": "Not authenticated"
                }
        
        try:
            guild_id = params.get("guild_id")
            channel_id = params.get("channel_id")
            engagement_type = params.get("engagement_type", "chat")
            min_messages = params.get("min_messages", 2)
            
            if not guild_id and not channel_id:
                return {
                    "success": False,
                    "error": "Either guild ID or channel ID is required"
                }
            
            # Get channels if guild ID is provided
            channels = []
            
            async def get_channels_async():
                if channel_id:
                    # Use specific channel
                    channel = await self.client.fetch_channel(channel_id)
                    return [channel]
                else:
                    # Get channels from guild
                    guild = await self.client.fetch_guild(guild_id)
                    text_channels = [c for c in guild.channels if isinstance(c, discord.TextChannel)]
                    # Filter to general or main channels
                    preferred_channels = [c for c in text_channels if c.name.lower() in ["general", "main", "chat"]]
                    return preferred_channels if preferred_channels else text_channels[:3]
            
            channels = self._run_coroutine(get_channels_async())
            
            # Perform engagement
            results = []
            
            if engagement_type == "chat":
                # Generate some chat messages
                messages = [
                    "Hello everyone! How's it going?",
                    "I'm new here, excited to be part of this community!",
                    "What's the latest news about this project?",
                    "Has anyone heard about the upcoming updates?",
                    "I've been following this project for a while, really impressed so far!"
                ]
                
                # Send messages
                for i in range(min_messages):
                    if not channels:
                        break
                    
                    # Select a random channel
                    channel = random.choice(channels)
                    
                    # Select a random message
                    message_text = random.choice(messages)
                    
                    # Send message
                    send_result = self.send_message(
                        channel_id=channel.id,
                        content=message_text
                    )
                    
                    results.append(send_result)
                    
                    # Wait a bit between messages
                    time.sleep(random.uniform(2, 5))
            
            elif engagement_type == "react":
                # Get recent messages and react to them
                for channel in channels:
                    # Get messages
                    messages_result = self.get_messages(
                        channel_id=channel.id,
                        limit=10
                    )
                    
                    if not messages_result["success"]:
                        continue
                    
                    # React to some messages
                    messages = messages_result["messages"]
                    react_count = min(min_messages, len(messages))
                    
                    for i in range(react_count):
                        message = messages[i]
                        
                        # Select a random emoji
                        emoji = random.choice(["👍", "❤️", "🎉", "🔥", "👏", "✅"])
                        
                        # React to message
                        react_result = self.react(
                            channel_id=channel.id,
                            message_id=message["id"],
                            emoji=emoji
                        )
                        
                        results.append(react_result)
                        
                        # Wait a bit between reactions
                        time.sleep(random.uniform(1, 3))
            
            logger.info(f"Performed {len(results)} Discord engagements")
            
            return {
                "success": True,
                "engagement_count": len(results),
                "results": results
            }
        except Exception as e:
            logger.error(f"Error performing Discord engagement: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def close(self) -> None:
        """Close the handler and clean up resources."""
        logger.info("Closing Discord handler")
        
        if self.client and self.client.is_ready():
            # Close client
            async def close_client():
                await self.client.close()
            
            try:
                self._run_coroutine(close_client())
            except Exception as e:
                logger.error(f"Error closing Discord client: {e}")
        
        self.authenticated = False


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    
    # Create a config with your Discord token
    config = {
        "token": "YOUR_DISCORD_BOT_TOKEN",
        "auto_connect": True
    }
    
    # Initialize handler
    handler = DiscordHandler(config)
    
    # Wait for authentication
    time.sleep(5)
    
    # Test sending a message (replace with a valid channel ID)
    result = handler.send_message(
        channel_id="CHANNEL_ID",
        content="Hello from Discord handler test!"
    )
    print(f"Send message result: {result}")
    
    # Close handler
    handler.close()