"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/AmbiguousFunctionContract.sol:AmbiguousFunctionContract  # noqa: E501
AMBIGUOUS_FUNCTION_CONTRACT_BYTECODE = "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"  # noqa: E501
AMBIGUOUS_FUNCTION_CONTRACT_RUNTIME = "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"  # noqa: E501
AMBIGUOUS_FUNCTION_CONTRACT_ABI = [
    {
        "inputs": [
            {"internalType": "bytes32", "name": "message", "type": "bytes32"},
            {"internalType": "bytes", "name": "signature", "type": "bytes"},
        ],
        "name": "isValidSignature",
        "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "message", "type": "bytes"},
            {"internalType": "bytes", "name": "signature", "type": "bytes"},
        ],
        "name": "isValidSignature",
        "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "isValidSignature",
        "outputs": [{"internalType": "string", "name": "result", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
]
AMBIGUOUS_FUNCTION_CONTRACT_DATA = {
    "bytecode": AMBIGUOUS_FUNCTION_CONTRACT_BYTECODE,
    "bytecode_runtime": AMBIGUOUS_FUNCTION_CONTRACT_RUNTIME,
    "abi": AMBIGUOUS_FUNCTION_CONTRACT_ABI,
}
