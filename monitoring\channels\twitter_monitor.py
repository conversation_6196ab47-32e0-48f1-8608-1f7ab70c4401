"""
Twitter Monitor

This module is responsible for monitoring Twitter accounts and hashtags
for project updates and announcements.
"""

import os
import json
import logging
import time
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
import tweepy

logger = logging.getLogger("TwitterMonitor")

class TwitterMonitor:
    """
    Monitors Twitter for project updates and announcements.
    
    Features:
    - Account monitoring
    - Hashtag tracking
    - Tweet analysis
    - Update detection
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Twitter monitor.
        
        Args:
            config_path: Path to configuration file
        """
        self.config = self._load_config(config_path)
        self.api = self._initialize_api()
        self.tweet_cache = {}
        self.last_check_times = {}
        
        # Ensure cache directory exists
        os.makedirs(self.config["cache_dir"], exist_ok=True)
        
        # Load cached tweets if available
        self._load_tweet_cache()
        
        logger.info("Twitter monitor initialized")
    
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "api_key": "",
            "api_secret": "",
            "access_token": "",
            "access_token_secret": "",
            "bearer_token": "",
            "cache_dir": "data/monitoring/twitter_cache",
            "max_tweets_per_check": 50,
            "include_retweets": False,
            "include_replies": False,
            "max_cache_age_days": 7
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    # Merge user config with defaults
                    for key, value in user_config.items():
                        default_config[key] = value
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def _initialize_api(self) -> Optional[tweepy.API]:
        """
        Initialize the Twitter API client.
        
        Returns:
            tweepy.API object or None if initialization failed
        """
        try:
            # Check if credentials are available
            if not all([
                self.config["api_key"],
                self.config["api_secret"],
                self.config["access_token"],
                self.config["access_token_secret"]
            ]):
                logger.warning("Twitter API credentials not configured")
                return None
            
            # Set up authentication
            auth = tweepy.OAuth1UserHandler(
                self.config["api_key"],
                self.config["api_secret"],
                self.config["access_token"],
                self.config["access_token_secret"]
            )
            
            # Create API object
            api = tweepy.API(auth, wait_on_rate_limit=True)
            
            # Test API connection
            api.verify_credentials()
            
            logger.info("Twitter API initialized successfully")
            return api
        except Exception as e:
            logger.error(f"Failed to initialize Twitter API: {e}")
            return None
    
    def _load_tweet_cache(self) -> None:
        """Load cached tweets from disk."""
        cache_file = os.path.join(self.config["cache_dir"], "tweet_cache.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    self.tweet_cache = json.load(f)
                logger.info(f"Loaded {len(self.tweet_cache)} cached tweets")
            except Exception as e:
                logger.error(f"Error loading tweet cache: {e}")
                self.tweet_cache = {}
    
    def _save_tweet_cache(self) -> None:
        """Save cached tweets to disk."""
        cache_file = os.path.join(self.config["cache_dir"], "tweet_cache.json")
        
        try:
            with open(cache_file, 'w') as f:
                json.dump(self.tweet_cache, f)
            logger.info(f"Saved {len(self.tweet_cache)} tweets to cache")
        except Exception as e:
            logger.error(f"Error saving tweet cache: {e}")
    
    def _clean_old_cache(self) -> None:
        """Remove old entries from the tweet cache."""
        try:
            current_time = datetime.now()
            max_age = timedelta(days=self.config["max_cache_age_days"])
            
            accounts_to_clean = []
            
            for account, tweets in self.tweet_cache.items():
                # Filter out old tweets
                new_tweets = {}
                for tweet_id, tweet_data in tweets.items():
                    tweet_time = datetime.fromisoformat(tweet_data["timestamp"])
                    if current_time - tweet_time <= max_age:
                        new_tweets[tweet_id] = tweet_data
                
                if new_tweets:
                    self.tweet_cache[account] = new_tweets
                else:
                    accounts_to_clean.append(account)
            
            # Remove empty accounts
            for account in accounts_to_clean:
                del self.tweet_cache[account]
            
            logger.info(f"Cleaned tweet cache, removed {len(accounts_to_clean)} accounts")
        except Exception as e:
            logger.error(f"Error cleaning tweet cache: {e}")
    
    def check_account(self, account: str, project_id: str) -> List[Dict[str, Any]]:
        """
        Check a Twitter account for new tweets.
        
        Args:
            account: Twitter account handle (without @)
            project_id: Project identifier
            
        Returns:
            List of update objects for new tweets
        """
        if not self.api:
            logger.error("Twitter API not initialized")
            return []
        
        updates = []
        
        try:
            logger.info(f"Checking Twitter account: @{account}")
            
            # Initialize cache for this account if needed
            if account not in self.tweet_cache:
                self.tweet_cache[account] = {}
            
            # Get tweets
            tweets = self.api.user_timeline(
                screen_name=account,
                count=self.config["max_tweets_per_check"],
                include_rts=self.config["include_retweets"],
                exclude_replies=not self.config["include_replies"],
                tweet_mode="extended"
            )
            
            # Process tweets
            for tweet in tweets:
                tweet_id = str(tweet.id)
                
                # Skip if already in cache
                if tweet_id in self.tweet_cache[account]:
                    continue
                
                # Create tweet data
                tweet_data = {
                    "id": tweet_id,
                    "text": tweet.full_text,
                    "timestamp": tweet.created_at.isoformat(),
                    "url": f"https://twitter.com/{account}/status/{tweet_id}",
                    "likes": tweet.favorite_count,
                    "retweets": tweet.retweet_count
                }
                
                # Add to cache
                self.tweet_cache[account][tweet_id] = tweet_data
                
                # Create update object
                update = {
                    "project_id": project_id,
                    "source": "twitter",
                    "source_id": account,
                    "type": "tweet",
                    "title": f"New tweet from @{account}",
                    "content": tweet.full_text,
                    "url": tweet_data["url"],
                    "timestamp": tweet_data["timestamp"],
                    "metadata": {
                        "likes": tweet_data["likes"],
                        "retweets": tweet_data["retweets"],
                        "tweet_id": tweet_id
                    }
                }
                
                updates.append(update)
            
            # Save updated cache
            self._save_tweet_cache()
            
            # Update last check time
            self.last_check_times[account] = datetime.now().isoformat()
            
            logger.info(f"Found {len(updates)} new tweets for @{account}")
            return updates
        except Exception as e:
            logger.error(f"Error checking Twitter account @{account}: {e}")
            return []
    
    def check_hashtag(self, hashtag: str, project_id: str) -> List[Dict[str, Any]]:
        """
        Check a hashtag for new tweets.
        
        Args:
            hashtag: Hashtag to check (without #)
            project_id: Project identifier
            
        Returns:
            List of update objects for new tweets
        """
        if not self.api:
            logger.error("Twitter API not initialized")
            return []
        
        updates = []
        
        try:
            logger.info(f"Checking Twitter hashtag: #{hashtag}")
            
            # Initialize cache for this hashtag if needed
            cache_key = f"hashtag_{hashtag}"
            if cache_key not in self.tweet_cache:
                self.tweet_cache[cache_key] = {}
            
            # Get tweets
            tweets = self.api.search_tweets(
                q=f"#{hashtag}",
                count=self.config["max_tweets_per_check"],
                result_type="recent",
                tweet_mode="extended"
            )
            
            # Process tweets
            for tweet in tweets:
                tweet_id = str(tweet.id)
                
                # Skip if already in cache
                if tweet_id in self.tweet_cache[cache_key]:
                    continue
                
                # Create tweet data
                tweet_data = {
                    "id": tweet_id,
                    "text": tweet.full_text,
                    "timestamp": tweet.created_at.isoformat(),
                    "url": f"https://twitter.com/{tweet.user.screen_name}/status/{tweet_id}",
                    "likes": tweet.favorite_count,
                    "retweets": tweet.retweet_count,
                    "user": tweet.user.screen_name
                }
                
                # Add to cache
                self.tweet_cache[cache_key][tweet_id] = tweet_data
                
                # Create update object
                update = {
                    "project_id": project_id,
                    "source": "twitter",
                    "source_id": f"hashtag_{hashtag}",
                    "type": "hashtag",
                    "title": f"New tweet with #{hashtag}",
                    "content": tweet.full_text,
                    "url": tweet_data["url"],
                    "timestamp": tweet_data["timestamp"],
                    "metadata": {
                        "likes": tweet_data["likes"],
                        "retweets": tweet_data["retweets"],
                        "tweet_id": tweet_id,
                        "user": tweet_data["user"]
                    }
                }
                
                updates.append(update)
            
            # Save updated cache
            self._save_tweet_cache()
            
            # Update last check time
            self.last_check_times[cache_key] = datetime.now().isoformat()
            
            logger.info(f"Found {len(updates)} new tweets for #{hashtag}")
            return updates
        except Exception as e:
            logger.error(f"Error checking Twitter hashtag #{hashtag}: {e}")
            return []
    
    def check_project(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check all Twitter sources for a project.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        updates = []
        project_id = project["id"]
        
        # Check account if available
        if "twitter" in project and project["twitter"]:
            account = project["twitter"].replace("@", "")
            account_updates = self.check_account(account, project_id)
            updates.extend(account_updates)
        
        # Check hashtags if available
        if "twitter_hashtags" in project and project["twitter_hashtags"]:
            for hashtag in project["twitter_hashtags"]:
                hashtag = hashtag.replace("#", "")
                hashtag_updates = self.check_hashtag(hashtag, project_id)
                updates.extend(hashtag_updates)
        
        # Clean old cache periodically
        if len(updates) > 0 or len(self.tweet_cache) > 100:
            self._clean_old_cache()
        
        return updates


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    
    # Create a config file with your Twitter API credentials
    config = {
        "api_key": "YOUR_API_KEY",
        "api_secret": "YOUR_API_SECRET",
        "access_token": "YOUR_ACCESS_TOKEN",
        "access_token_secret": "YOUR_ACCESS_TOKEN_SECRET",
        "cache_dir": "data/monitoring/twitter_cache"
    }
    
    os.makedirs("data/monitoring/twitter_cache", exist_ok=True)
    config_path = "data/monitoring/twitter_config.json"
    
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    # Initialize monitor
    monitor = TwitterMonitor(config_path)
    
    # Test project
    test_project = {
        "id": "test-project-123",
        "name": "Test Project",
        "twitter": "ethereum",  # Example: check Ethereum's Twitter
        "twitter_hashtags": ["ethereum"]
    }
    
    # Check for updates
    updates = monitor.check_project(test_project)
    
    print(f"Found {len(updates)} updates")
    for update in updates:
        print(f"- {update['title']}: {update['content'][:100]}...")