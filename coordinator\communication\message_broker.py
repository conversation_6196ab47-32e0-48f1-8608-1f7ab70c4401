"""
消息代理

该模块提供智能体间的消息传递功能，允许智能体之间进行异步通信。
"""

import logging
import threading
import queue
import uuid
from typing import Dict, List, Any, Callable, Optional, Set


class MessageBroker:
    """消息代理，负责智能体间的消息传递"""
    
    def __init__(self):
        """初始化消息代理"""
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        self._queues: Dict[str, queue.Queue] = {}  # topic -> queue
        self._subscribers: Dict[str, Set[str]] = {}  # topic -> set of subscriber_ids
        self._callbacks: Dict[str, Callable] = {}  # subscriber_id -> callback
        self._running = False
        self._worker_threads: List[threading.Thread] = []
    
    def start(self) -> None:
        """启动消息代理"""
        with self._lock:
            if self._running:
                self.logger.warning("消息代理已经在运行")
                return
            
            self._running = True
            self.logger.info("启动消息代理")
            
            # 为每个主题创建一个工作线程
            for topic in self._queues:
                thread = threading.Thread(
                    target=self._worker,
                    args=(topic,),
                    name=f"MessageBroker-{topic}",
                    daemon=True
                )
                thread.start()
                self._worker_threads.append(thread)
    
    def stop(self) -> None:
        """停止消息代理"""
        with self._lock:
            if not self._running:
                self.logger.warning("消息代理未在运行")
                return
            
            self._running = False
            self.logger.info("停止消息代理")
            
            # 等待所有工作线程结束
            for thread in self._worker_threads:
                thread.join(timeout=1.0)
            
            self._worker_threads = []
    
    def create_topic(self, topic: str) -> None:
        """
        创建一个新的主题
        
        Args:
            topic: 主题名称
        """
        with self._lock:
            if topic in self._queues:
                self.logger.warning(f"主题 '{topic}' 已存在")
                return
            
            self._queues[topic] = queue.Queue()
            self._subscribers[topic] = set()
            self.logger.info(f"已创建主题 '{topic}'")
            
            # 如果消息代理已经在运行，为新主题创建工作线程
            if self._running:
                thread = threading.Thread(
                    target=self._worker,
                    args=(topic,),
                    name=f"MessageBroker-{topic}",
                    daemon=True
                )
                thread.start()
                self._worker_threads.append(thread)
    
    def delete_topic(self, topic: str) -> None:
        """
        删除一个主题
        
        Args:
            topic: 主题名称
        """
        with self._lock:
            if topic not in self._queues:
                self.logger.warning(f"主题 '{topic}' 不存在")
                return
            
            # 移除所有订阅者
            for subscriber_id in list(self._subscribers[topic]):
                self.unsubscribe(subscriber_id, topic)
            
            # 删除主题
            del self._queues[topic]
            del self._subscribers[topic]
            self.logger.info(f"已删除主题 '{topic}'")
    
    def subscribe(self, callback: Callable[[str, Any], None], topic: str) -> str:
        """
        订阅主题
        
        Args:
            callback: 回调函数，接收主题和消息作为参数
            topic: 主题名称
            
        Returns:
            订阅者ID
        """
        with self._lock:
            # 如果主题不存在，创建它
            if topic not in self._queues:
                self.create_topic(topic)
            
            # 生成订阅者ID
            subscriber_id = str(uuid.uuid4())
            
            # 注册回调函数
            self._callbacks[subscriber_id] = callback
            
            # 添加到订阅者列表
            self._subscribers[topic].add(subscriber_id)
            
            self.logger.info(f"订阅者 '{subscriber_id}' 已订阅主题 '{topic}'")
            return subscriber_id
    
    def unsubscribe(self, subscriber_id: str, topic: str) -> bool:
        """
        取消订阅主题
        
        Args:
            subscriber_id: 订阅者ID
            topic: 主题名称
            
        Returns:
            如果成功取消订阅则返回True，否则返回False
        """
        with self._lock:
            if topic not in self._subscribers:
                self.logger.warning(f"主题 '{topic}' 不存在")
                return False
            
            if subscriber_id not in self._subscribers[topic]:
                self.logger.warning(f"订阅者 '{subscriber_id}' 未订阅主题 '{topic}'")
                return False
            
            # 从订阅者列表中移除
            self._subscribers[topic].remove(subscriber_id)
            
            # 如果这是订阅者的唯一订阅，删除回调函数
            is_subscribed_elsewhere = False
            for other_topic, subscribers in self._subscribers.items():
                if subscriber_id in subscribers:
                    is_subscribed_elsewhere = True
                    break
            
            if not is_subscribed_elsewhere and subscriber_id in self._callbacks:
                del self._callbacks[subscriber_id]
            
            self.logger.info(f"订阅者 '{subscriber_id}' 已取消订阅主题 '{topic}'")
            return True
    
    def publish(self, topic: str, message: Any) -> bool:
        """
        发布消息到主题
        
        Args:
            topic: 主题名称
            message: 消息内容
            
        Returns:
            如果成功发布则返回True，否则返回False
        """
        with self._lock:
            if topic not in self._queues:
                self.logger.warning(f"主题 '{topic}' 不存在")
                return False
            
            # 将消息放入队列
            self._queues[topic].put(message)
            self.logger.debug(f"已发布消息到主题 '{topic}'")
            return True
    
    def _worker(self, topic: str) -> None:
        """
        工作线程，负责从队列中取出消息并分发给订阅者
        
        Args:
            topic: 主题名称
        """
        self.logger.info(f"启动主题 '{topic}' 的工作线程")
        
        while self._running:
            try:
                # 从队列中取出消息，设置超时以便能够响应停止信号
                try:
                    message = self._queues[topic].get(timeout=0.1)
                except queue.Empty:
                    continue
                
                # 获取订阅者列表的副本
                with self._lock:
                    subscribers = list(self._subscribers[topic])
                
                # 分发消息给所有订阅者
                for subscriber_id in subscribers:
                    try:
                        callback = self._callbacks.get(subscriber_id)
                        if callback:
                            callback(topic, message)
                    except Exception as e:
                        self.logger.error(f"调用订阅者 '{subscriber_id}' 的回调函数时出错: {str(e)}")
                
                # 标记任务完成
                self._queues[topic].task_done()
            
            except Exception as e:
                self.logger.error(f"主题 '{topic}' 的工作线程出错: {str(e)}")
        
        self.logger.info(f"主题 '{topic}' 的工作线程已停止")
    
    def get_topics(self) -> List[str]:
        """
        获取所有主题
        
        Returns:
            主题名称列表
        """
        with self._lock:
            return list(self._queues.keys())
    
    def get_subscribers(self, topic: str) -> List[str]:
        """
        获取主题的所有订阅者
        
        Args:
            topic: 主题名称
            
        Returns:
            订阅者ID列表
        """
        with self._lock:
            if topic not in self._subscribers:
                return []
            
            return list(self._subscribers[topic])
    
    def is_running(self) -> bool:
        """
        检查消息代理是否正在运行
        
        Returns:
            如果消息代理正在运行则返回True，否则返回False
        """
        with self._lock:
            return self._running