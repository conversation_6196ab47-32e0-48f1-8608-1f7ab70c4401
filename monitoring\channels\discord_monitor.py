"""
Discord Monitor

This module is responsible for monitoring Discord servers and channels
for project updates and announcements.
"""

import os
import json
import logging
import time
import asyncio
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
import discord
from discord.ext import tasks

logger = logging.getLogger("DiscordMonitor")

class DiscordMonitor:
    """
    Monitors Discord for project updates and announcements.
    
    Features:
    - Server monitoring
    - Channel tracking
    - Message analysis
    - Update detection
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Discord monitor.
        
        Args:
            config_path: Path to configuration file
        """
        self.config = self._load_config(config_path)
        self.client = None
        self.message_cache = {}
        self.last_check_times = {}
        self.monitored_channels = {}
        self.running = False
        
        # Ensure cache directory exists
        os.makedirs(self.config["cache_dir"], exist_ok=True)
        
        # Load cached messages if available
        self._load_message_cache()
        
        logger.info("Discord monitor initialized")
    
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "token": "",
            "cache_dir": "data/monitoring/discord_cache",
            "max_messages_per_check": 50,
            "max_cache_age_days": 7,
            "check_interval_minutes": 15,
            "announcement_keywords": ["announcement", "update", "airdrop", "token", "launch"]
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    # Merge user config with defaults
                    for key, value in user_config.items():
                        default_config[key] = value
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def _load_message_cache(self) -> None:
        """Load cached messages from disk."""
        cache_file = os.path.join(self.config["cache_dir"], "message_cache.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    self.message_cache = json.load(f)
                logger.info(f"Loaded message cache with {len(self.message_cache)} channels")
            except Exception as e:
                logger.error(f"Error loading message cache: {e}")
                self.message_cache = {}
    
    def _save_message_cache(self) -> None:
        """Save cached messages to disk."""
        cache_file = os.path.join(self.config["cache_dir"], "message_cache.json")
        
        try:
            with open(cache_file, 'w') as f:
                json.dump(self.message_cache, f)
            logger.info(f"Saved message cache with {len(self.message_cache)} channels")
        except Exception as e:
            logger.error(f"Error saving message cache: {e}")
    
    def _clean_old_cache(self) -> None:
        """Remove old entries from the message cache."""
        try:
            current_time = datetime.now()
            max_age = timedelta(days=self.config["max_cache_age_days"])
            
            channels_to_clean = []
            
            for channel_id, messages in self.message_cache.items():
                # Filter out old messages
                new_messages = {}
                for message_id, message_data in messages.items():
                    message_time = datetime.fromisoformat(message_data["timestamp"])
                    if current_time - message_time <= max_age:
                        new_messages[message_id] = message_data
                
                if new_messages:
                    self.message_cache[channel_id] = new_messages
                else:
                    channels_to_clean.append(channel_id)
            
            # Remove empty channels
            for channel_id in channels_to_clean:
                del self.message_cache[channel_id]
            
            logger.info(f"Cleaned message cache, removed {len(channels_to_clean)} channels")
        except Exception as e:
            logger.error(f"Error cleaning message cache: {e}")
    
    async def _initialize_client(self) -> bool:
        """
        Initialize the Discord client.
        
        Returns:
            bool: True if initialized successfully, False otherwise
        """
        try:
            # Check if token is available
            if not self.config["token"]:
                logger.warning("Discord token not configured")
                return False
            
            # Create client with necessary intents
            intents = discord.Intents.default()
            intents.message_content = True
            
            self.client = discord.Client(intents=intents)
            
            # Set up event handlers
            @self.client.event
            async def on_ready():
                logger.info(f"Discord client logged in as {self.client.user}")
                self.check_channels.start()
            
            @self.client.event
            async def on_message(message):
                # Only process messages in monitored channels
                channel_id = str(message.channel.id)
                if channel_id in self.monitored_channels:
                    project_id = self.monitored_channels[channel_id]
                    await self._process_message(message, project_id)
            
            # Set up background task for checking channels
            @tasks.loop(minutes=self.config["check_interval_minutes"])
            async def check_channels():
                logger.info("Running scheduled Discord channel check")
                for channel_id, project_id in self.monitored_channels.items():
                    try:
                        channel = self.client.get_channel(int(channel_id))
                        if channel:
                            await self._check_channel_history(channel, project_id)
                    except Exception as e:
                        logger.error(f"Error checking channel {channel_id}: {e}")
            
            self.check_channels = check_channels
            
            logger.info("Discord client initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Discord client: {e}")
            return False
    
    async def _process_message(self, message, project_id: str) -> Optional[Dict[str, Any]]:
        """
        Process a Discord message.
        
        Args:
            message: Discord message object
            project_id: Project identifier
            
        Returns:
            Update object or None
        """
        try:
            channel_id = str(message.channel.id)
            message_id = str(message.id)
            
            # Initialize cache for this channel if needed
            if channel_id not in self.message_cache:
                self.message_cache[channel_id] = {}
            
            # Skip if already in cache
            if message_id in self.message_cache[channel_id]:
                return None
            
            # Create message data
            message_data = {
                "id": message_id,
                "content": message.content,
                "timestamp": message.created_at.isoformat(),
                "author": str(message.author),
                "author_id": str(message.author.id),
                "channel_id": channel_id,
                "channel_name": message.channel.name,
                "guild_id": str(message.guild.id) if message.guild else None,
                "guild_name": message.guild.name if message.guild else None,
                "attachments": [a.url for a in message.attachments],
                "embeds": [e.to_dict() for e in message.embeds]
            }
            
            # Add to cache
            self.message_cache[channel_id][message_id] = message_data
            
            # Save updated cache periodically
            if len(self.message_cache[channel_id]) % 10 == 0:
                self._save_message_cache()
            
            # Create update object
            update = {
                "project_id": project_id,
                "source": "discord",
                "source_id": channel_id,
                "type": "message",
                "title": f"New message in {message_data['channel_name']}",
                "content": message_data["content"],
                "url": f"https://discord.com/channels/{message_data['guild_id']}/{channel_id}/{message_id}",
                "timestamp": message_data["timestamp"],
                "metadata": {
                    "author": message_data["author"],
                    "channel_name": message_data["channel_name"],
                    "guild_name": message_data["guild_name"],
                    "attachments": message_data["attachments"],
                    "has_embeds": len(message_data["embeds"]) > 0
                }
            }
            
            # Check if this is an announcement
            is_announcement = self._is_announcement(message_data["content"])
            if is_announcement:
                update["type"] = "announcement"
                update["title"] = f"New announcement in {message_data['channel_name']}"
            
            logger.info(f"Processed new Discord message in {message_data['channel_name']}")
            return update
        except Exception as e:
            logger.error(f"Error processing Discord message: {e}")
            return None
    
    def _is_announcement(self, content: str) -> bool:
        """
        Check if a message is an announcement.
        
        Args:
            content: Message content
            
        Returns:
            bool: True if it's an announcement, False otherwise
        """
        content_lower = content.lower()
        
        # Check for announcement keywords
        for keyword in self.config["announcement_keywords"]:
            if keyword.lower() in content_lower:
                return True
        
        # Check for common announcement patterns
        if content.isupper() and len(content) > 20:  # All caps is often used for announcements
            return True
        
        if "📢" in content or "🔔" in content or "⚠️" in content:  # Common announcement emojis
            return True
        
        return False
    
    async def _check_channel_history(self, channel, project_id: str) -> List[Dict[str, Any]]:
        """
        Check a Discord channel's message history.
        
        Args:
            channel: Discord channel object
            project_id: Project identifier
            
        Returns:
            List of update objects
        """
        updates = []
        channel_id = str(channel.id)
        
        try:
            logger.info(f"Checking Discord channel: {channel.name} ({channel_id})")
            
            # Initialize cache for this channel if needed
            if channel_id not in self.message_cache:
                self.message_cache[channel_id] = {}
            
            # Get messages
            async for message in channel.history(limit=self.config["max_messages_per_check"]):
                message_id = str(message.id)
                
                # Skip if already in cache
                if message_id in self.message_cache[channel_id]:
                    continue
                
                # Process message
                update = await self._process_message(message, project_id)
                if update:
                    updates.append(update)
            
            # Update last check time
            self.last_check_times[channel_id] = datetime.now().isoformat()
            
            logger.info(f"Found {len(updates)} new messages in Discord channel {channel.name}")
            return updates
        except Exception as e:
            logger.error(f"Error checking Discord channel {channel_id}: {e}")
            return []
    
    def add_channel(self, channel_id: str, project_id: str) -> bool:
        """
        Add a Discord channel to monitor.
        
        Args:
            channel_id: Discord channel ID
            project_id: Project identifier
            
        Returns:
            bool: True if added successfully, False otherwise
        """
        try:
            self.monitored_channels[channel_id] = project_id
            logger.info(f"Added Discord channel {channel_id} to monitoring for project {project_id}")
            return True
        except Exception as e:
            logger.error(f"Error adding Discord channel {channel_id}: {e}")
            return False
    
    def remove_channel(self, channel_id: str) -> bool:
        """
        Remove a Discord channel from monitoring.
        
        Args:
            channel_id: Discord channel ID
            
        Returns:
            bool: True if removed successfully, False otherwise
        """
        try:
            if channel_id in self.monitored_channels:
                del self.monitored_channels[channel_id]
                logger.info(f"Removed Discord channel {channel_id} from monitoring")
                return True
            else:
                logger.warning(f"Discord channel {channel_id} not found in monitored channels")
                return False
        except Exception as e:
            logger.error(f"Error removing Discord channel {channel_id}: {e}")
            return False
    
    async def start(self) -> bool:
        """
        Start the Discord monitor.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            if self.running:
                logger.warning("Discord monitor is already running")
                return True
            
            # Initialize client
            success = await self._initialize_client()
            if not success:
                return False
            
            # Start client
            self.running = True
            
            logger.info("Discord monitor started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start Discord monitor: {e}")
            return False
    
    async def stop(self) -> bool:
        """
        Stop the Discord monitor.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        try:
            if not self.running:
                logger.warning("Discord monitor is not running")
                return True
            
            # Stop background task
            if hasattr(self, 'check_channels') and self.check_channels.is_running():
                self.check_channels.cancel()
            
            # Save cache
            self._save_message_cache()
            
            # Close client
            if self.client:
                await self.client.close()
            
            self.running = False
            
            logger.info("Discord monitor stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Error stopping Discord monitor: {e}")
            return False
    
    def check_project(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check all Discord channels for a project.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        # This is a synchronous wrapper for the async methods
        # In a real implementation, you would need to handle this differently
        
        updates = []
        project_id = project["id"]
        
        # Add channels to monitor
        if "discord_channels" in project and project["discord_channels"]:
            for channel_id in project["discord_channels"]:
                self.add_channel(channel_id, project_id)
        
        # The actual checking happens in the background task
        # This method just ensures the channels are registered
        
        logger.info(f"Registered Discord channels for project {project_id}")
        return updates
    
    async def run_client(self) -> None:
        """Run the Discord client."""
        if not self.config["token"]:
            logger.error("Discord token not configured")
            return
        
        try:
            await self.start()
            await self.client.start(self.config["token"])
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received")
        finally:
            await self.stop()


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    
    # Create a config file with your Discord token
    config = {
        "token": "YOUR_DISCORD_BOT_TOKEN",
        "cache_dir": "data/monitoring/discord_cache"
    }
    
    os.makedirs("data/monitoring/discord_cache", exist_ok=True)
    config_path = "data/monitoring/discord_config.json"
    
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    # Initialize monitor
    monitor = DiscordMonitor(config_path)
    
    # Test project
    test_project = {
        "id": "test-project-123",
        "name": "Test Project",
        "discord_channels": ["CHANNEL_ID_1", "CHANNEL_ID_2"]  # Replace with actual channel IDs
    }
    
    # Register channels
    monitor.check_project(test_project)
    
    # Run the client (this will block until interrupted)
    asyncio.run(monitor.run_client())