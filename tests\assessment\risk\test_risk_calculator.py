"""
风险计算器测试

该模块包含风险计算器的单元测试。
"""

import unittest
import os
import sys
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from assessment.risk.risk_calculator import RiskCalculator
from discovery.models.project import Project, ProjectStatus, TokenInfo, SocialChannel, TeamMember
from discovery.models.enums import ProjectType, BlockchainPlatform, SocialChannelType


class TestRiskCalculator(unittest.TestCase):
    """风险计算器测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试配置
        self.config = {
            'weights': {
                'team_verification': 0.2,
                'social_verification': 0.15,
                'project_verification': 0.15,
                'contract_analysis': 0.2,
                'vulnerability_scan': 0.2,
                'permission_analysis': 0.1
            }
        }
        
        # 创建测试项目
        self.project = self._create_test_project()
        
        # 创建测试验证结果
        self.team_result = {
            'verified': True,
            'confidence': 0.8,
            'team_size': 3,
            'known_members': 2,
            'has_technical_members': True,
            'has_business_members': True,
            'has_known_scammers': False,
            'average_experience': 3.5
        }
        
        self.social_result = {
            'verified': True,
            'confidence': 0.7,
            'channel_count': 3,
            'verified_channels': 2,
            'total_followers': 5000,
            'has_suspicious_content': False,
            'activity_level': 'medium'
        }
        
        self.project_result = {
            'verified': True,
            'confidence': 0.9,
            'website_available': True,
            'website_age_days': 60,
            'has_required_pages': True,
            'content_quality': 'high',
            'has_suspicious_content': False
        }
        
        self.contract_result = {
            'analyzed': True,
            'contract_verified': True,
            'contract_name': 'TestToken',
            'compiler_version': '0.8.0',
            'risk_level': 'low',
            'high_risk_functions': [],
            'medium_risk_functions': []
        }
        
        self.vulnerability_result = {
            'scanned': True,
            'critical_vulnerabilities': 0,
            'high_vulnerabilities': 0,
            'medium_vulnerabilities': 1,
            'low_vulnerabilities': 2
        }
        
        self.permission_result = {
            'analyzed': True,
            'centralization_level': 'low',
            'owner_privileges': ['mint', 'burn'],
            'has_proxy': False,
            'has_pausable': True
        }
    
    def _create_test_project(self):
        """创建测试项目"""
        # 创建团队成员
        team_members = [
            TeamMember(
                name="John Doe",
                role="CEO & Founder",
                bio="Blockchain enthusiast with 5 years of experience in DeFi projects.",
                links=["https://linkedin.com/in/johndoe", "https://github.com/johndoe"]
            ),
            TeamMember(
                name="Jane Smith",
                role="CTO",
                bio="Software engineer with expertise in smart contract development.",
                links=["https://linkedin.com/in/janesmith", "https://github.com/janesmith"]
            ),
            TeamMember(
                name="Mike Johnson",
                role="CMO",
                bio="Marketing specialist with experience in crypto projects.",
                links=["https://linkedin.com/in/mikejohnson"]
            )
        ]
        
        # 创建社交媒体渠道
        social_channels = [
            SocialChannel(
                channel_type=SocialChannelType.TWITTER,
                url="https://twitter.com/testproject",
                followers=3000
            ),
            SocialChannel(
                channel_type=SocialChannelType.TELEGRAM,
                url="https://t.me/testproject",
                followers=1500
            ),
            SocialChannel(
                channel_type=SocialChannelType.DISCORD,
                url="https://discord.gg/testproject",
                followers=500
            )
        ]
        
        # 创建代币信息
        token_info = TokenInfo(
            name="Test Token",
            symbol="TEST",
            blockchain=BlockchainPlatform.ETHEREUM,
            contract_address="******************************************",
            total_supply=1000000000,
            airdrop_amount=1000000,
            estimated_value=1.0
        )
        
        # 创建项目
        project = Project(
            id="test-project-001",
            name="Test Project",
            description="Test project for unit testing",
            url="https://testproject.io",
            project_type=ProjectType.AIRDROP,
            blockchain=BlockchainPlatform.ETHEREUM,
            status=ProjectStatus.NEW,
            team=team_members,
            social_channels=social_channels,
            token_info=token_info,
            tags=["test", "airdrop"]
        )
        
        return project
    
    def test_init(self):
        """测试初始化"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 验证初始化
        self.assertEqual(calculator.config, self.config)
        self.assertEqual(calculator.weights['team_verification'], 0.2)
        self.assertEqual(calculator.weights['social_verification'], 0.15)
        self.assertEqual(calculator.weights['project_verification'], 0.15)
        self.assertEqual(calculator.weights['contract_analysis'], 0.2)
        self.assertEqual(calculator.weights['vulnerability_scan'], 0.2)
        self.assertEqual(calculator.weights['permission_analysis'], 0.1)
    
    def test_calculate_with_all_results(self):
        """测试计算风险分数（所有结果）"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 计算风险分数
        risk_score = calculator.calculate(
            self.project,
            self.team_result,
            self.social_result,
            self.project_result,
            self.contract_result,
            self.vulnerability_result,
            self.permission_result
        )
        
        # 验证风险分数
        self.assertIsInstance(risk_score, float)
        self.assertGreaterEqual(risk_score, 0.0)
        self.assertLessEqual(risk_score, 100.0)
        
        # 由于所有结果都是良好的，风险分数应该较低
        self.assertLessEqual(risk_score, 50.0)
    
    def test_calculate_with_high_risk(self):
        """测试计算风险分数（高风险）"""
        # 修改测试结果为高风险
        self.team_result['has_known_scammers'] = True
        self.contract_result['risk_level'] = 'high'
        self.vulnerability_result['critical_vulnerabilities'] = 2
        self.permission_result['centralization_level'] = 'high'
        
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 计算风险分数
        risk_score = calculator.calculate(
            self.project,
            self.team_result,
            self.social_result,
            self.project_result,
            self.contract_result,
            self.vulnerability_result,
            self.permission_result
        )
        
        # 验证风险分数
        self.assertIsInstance(risk_score, float)
        self.assertGreaterEqual(risk_score, 0.0)
        self.assertLessEqual(risk_score, 100.0)
        
        # 由于多个高风险因素，风险分数应该较高
        self.assertGreaterEqual(risk_score, 70.0)
    
    def test_calculate_with_partial_results(self):
        """测试计算风险分数（部分结果）"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 计算风险分数（没有合约分析结果）
        risk_score = calculator.calculate(
            self.project,
            self.team_result,
            self.social_result,
            self.project_result,
            None,
            None,
            None
        )
        
        # 验证风险分数
        self.assertIsInstance(risk_score, float)
        self.assertGreaterEqual(risk_score, 0.0)
        self.assertLessEqual(risk_score, 100.0)
    
    def test_calculate_with_no_results(self):
        """测试计算风险分数（无结果）"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 计算风险分数（没有任何结果）
        risk_score = calculator.calculate(
            self.project,
            None,
            None,
            None,
            None,
            None,
            None
        )
        
        # 验证风险分数
        self.assertIsInstance(risk_score, float)
        self.assertGreaterEqual(risk_score, 0.0)
        self.assertLessEqual(risk_score, 100.0)
        
        # 由于没有任何结果，风险分数应该是默认值
        self.assertEqual(risk_score, 50.0)
    
    def test_calculate_team_risk(self):
        """测试计算团队风险分数"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 计算团队风险分数
        team_risk = calculator._calculate_team_risk(self.project, self.team_result)
        
        # 验证团队风险分数
        self.assertIsInstance(team_risk, float)
        self.assertGreaterEqual(team_risk, 0.0)
        self.assertLessEqual(team_risk, 100.0)
    
    def test_calculate_social_risk(self):
        """测试计算社交媒体风险分数"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 计算社交媒体风险分数
        social_risk = calculator._calculate_social_risk(self.project, self.social_result)
        
        # 验证社交媒体风险分数
        self.assertIsInstance(social_risk, float)
        self.assertGreaterEqual(social_risk, 0.0)
        self.assertLessEqual(social_risk, 100.0)
    
    def test_calculate_project_risk(self):
        """测试计算项目真实性风险分数"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 计算项目真实性风险分数
        project_risk = calculator._calculate_project_risk(self.project, self.project_result)
        
        # 验证项目真实性风险分数
        self.assertIsInstance(project_risk, float)
        self.assertGreaterEqual(project_risk, 0.0)
        self.assertLessEqual(project_risk, 100.0)
    
    def test_calculate_contract_risk(self):
        """测试计算合约风险分数"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 计算合约风险分数
        contract_risk = calculator._calculate_contract_risk(self.project, self.contract_result)
        
        # 验证合约风险分数
        self.assertIsInstance(contract_risk, float)
        self.assertGreaterEqual(contract_risk, 0.0)
        self.assertLessEqual(contract_risk, 100.0)
    
    def test_calculate_vulnerability_risk(self):
        """测试计算漏洞风险分数"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 计算漏洞风险分数
        vulnerability_risk = calculator._calculate_vulnerability_risk(self.project, self.vulnerability_result)
        
        # 验证漏洞风险分数
        self.assertIsInstance(vulnerability_risk, float)
        self.assertGreaterEqual(vulnerability_risk, 0.0)
        self.assertLessEqual(vulnerability_risk, 100.0)
    
    def test_calculate_permission_risk(self):
        """测试计算权限风险分数"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 计算权限风险分数
        permission_risk = calculator._calculate_permission_risk(self.project, self.permission_result)
        
        # 验证权限风险分数
        self.assertIsInstance(permission_risk, float)
        self.assertGreaterEqual(permission_risk, 0.0)
        self.assertLessEqual(permission_risk, 100.0)
    
    def test_get_stats(self):
        """测试获取统计信息"""
        # 创建风险计算器
        calculator = RiskCalculator(self.config)
        
        # 获取统计信息
        stats = calculator.get_stats()
        
        # 验证统计信息
        self.assertIsInstance(stats, dict)
        self.assertIn('total_calculations', stats)
        self.assertIn('high_risk_projects', stats)
        self.assertIn('medium_risk_projects', stats)
        self.assertIn('low_risk_projects', stats)


if __name__ == '__main__':
    unittest.main()