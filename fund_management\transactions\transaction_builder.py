"""
Transaction Builder

交易构建器，负责构建各种类型的区块链交易。
"""

import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime


class TransactionBuilder:
    """
    交易构建器
    
    负责构建不同类型的区块链交易，包括转账、合约调用、代币交易等。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化交易构建器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 支持的交易类型
        self.supported_transaction_types = [
            "transfer",           # ETH转账
            "erc20_transfer",     # ERC20代币转账
            "contract_call",      # 合约调用
            "contract_deploy",    # 合约部署
            "multi_send",         # 批量发送
            "swap",              # 代币交换
            "approve",           # 代币授权
            "stake",             # 质押
            "unstake"            # 取消质押
        ]
        
        # 常用合约ABI
        self.common_abis = {
            "erc20": [
                "function transfer(address to, uint256 amount) returns (bool)",
                "function approve(address spender, uint256 amount) returns (bool)",
                "function balanceOf(address account) view returns (uint256)"
            ]
        }
    
    async def initialize(self) -> bool:
        """
        初始化交易构建器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("Transaction Builder initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Transaction Builder: {e}")
            return False
    
    async def build_transfer_transaction(self, from_address: str, to_address: str,
                                       amount: Union[int, float], gas_settings: Dict[str, Any],
                                       nonce: Optional[int] = None) -> Dict[str, Any]:
        """
        构建ETH转账交易
        
        Args:
            from_address: 发送方地址
            to_address: 接收方地址
            amount: 转账金额 (ETH)
            gas_settings: Gas设置
            nonce: 交易nonce
            
        Returns:
            Dict[str, Any]: 构建的交易数据
        """
        try:
            # 转换金额为wei
            amount_wei = int(amount * 1e18) if isinstance(amount, float) else amount
            
            transaction = {
                "type": "transfer",
                "from": from_address,
                "to": to_address,
                "value": amount_wei,
                "gas": gas_settings.get("gas_limit", 21000),
                "gasPrice": gas_settings.get("gas_price", 20) * 1e9,  # Convert to wei
                "nonce": nonce,
                "data": "0x",
                "chainId": self.config.get("chain_id", 1),
                "created_at": datetime.utcnow().isoformat()
            }
            
            # EIP-1559支持
            if "max_fee_per_gas" in gas_settings:
                transaction.update({
                    "maxFeePerGas": gas_settings["max_fee_per_gas"] * 1e9,
                    "maxPriorityFeePerGas": gas_settings["max_priority_fee_per_gas"] * 1e9
                })
                del transaction["gasPrice"]  # EIP-1559不使用gasPrice
            
            self.logger.info(f"Built transfer transaction: {amount} ETH from {from_address} to {to_address}")
            return transaction
            
        except Exception as e:
            self.logger.error(f"Failed to build transfer transaction: {e}")
            return {}

    async def build_erc20_transfer_transaction(self, from_address: str, to_address: str,
                                             token_address: str, amount: Union[int, float],
                                             decimals: int, gas_settings: Dict[str, Any],
                                             nonce: Optional[int] = None) -> Dict[str, Any]:
        """
        构建ERC20代币转账交易
        
        Args:
            from_address: 发送方地址
            to_address: 接收方地址
            token_address: 代币合约地址
            amount: 转账金额
            decimals: 代币精度
            gas_settings: Gas设置
            nonce: 交易nonce
            
        Returns:
            Dict[str, Any]: 构建的交易数据
        """
        try:
            # 转换金额为最小单位
            amount_units = int(amount * (10 ** decimals))
            
            # 构建transfer函数调用数据
            # transfer(address,uint256) = 0xa9059cbb
            function_selector = "0xa9059cbb"
            to_address_padded = to_address[2:].zfill(64) if to_address.startswith("0x") else to_address.zfill(64)
            amount_hex = hex(amount_units)[2:].zfill(64)
            
            call_data = function_selector + to_address_padded + amount_hex
            
            transaction = {
                "type": "erc20_transfer",
                "from": from_address,
                "to": token_address,
                "value": 0,  # ERC20转账不发送ETH
                "gas": gas_settings.get("gas_limit", 60000),
                "gasPrice": gas_settings.get("gas_price", 20) * 1e9,
                "nonce": nonce,
                "data": call_data,
                "chainId": self.config.get("chain_id", 1),
                "token_address": token_address,
                "token_amount": amount,
                "token_decimals": decimals,
                "created_at": datetime.utcnow().isoformat()
            }
            
            # EIP-1559支持
            if "max_fee_per_gas" in gas_settings:
                transaction.update({
                    "maxFeePerGas": gas_settings["max_fee_per_gas"] * 1e9,
                    "maxPriorityFeePerGas": gas_settings["max_priority_fee_per_gas"] * 1e9
                })
                del transaction["gasPrice"]
            
            self.logger.info(f"Built ERC20 transfer: {amount} tokens from {from_address} to {to_address}")
            return transaction
            
        except Exception as e:
            self.logger.error(f"Failed to build ERC20 transfer transaction: {e}")
            return {}
    
    async def build_contract_call_transaction(self, from_address: str, contract_address: str,
                                            function_data: str, value: int,
                                            gas_settings: Dict[str, Any],
                                            nonce: Optional[int] = None) -> Dict[str, Any]:
        """
        构建合约调用交易
        
        Args:
            from_address: 调用方地址
            contract_address: 合约地址
            function_data: 函数调用数据
            value: 发送的ETH数量 (wei)
            gas_settings: Gas设置
            nonce: 交易nonce
            
        Returns:
            Dict[str, Any]: 构建的交易数据
        """
        try:
            transaction = {
                "type": "contract_call",
                "from": from_address,
                "to": contract_address,
                "value": value,
                "gas": gas_settings.get("gas_limit", 100000),
                "gasPrice": gas_settings.get("gas_price", 20) * 1e9,
                "nonce": nonce,
                "data": function_data,
                "chainId": self.config.get("chain_id", 1),
                "created_at": datetime.utcnow().isoformat()
            }
            
            # EIP-1559支持
            if "max_fee_per_gas" in gas_settings:
                transaction.update({
                    "maxFeePerGas": gas_settings["max_fee_per_gas"] * 1e9,
                    "maxPriorityFeePerGas": gas_settings["max_priority_fee_per_gas"] * 1e9
                })
                del transaction["gasPrice"]
            
            self.logger.info(f"Built contract call transaction to {contract_address}")
            return transaction
            
        except Exception as e:
            self.logger.error(f"Failed to build contract call transaction: {e}")
            return {}
    
    async def build_approve_transaction(self, from_address: str, token_address: str,
                                      spender_address: str, amount: Union[int, float],
                                      decimals: int, gas_settings: Dict[str, Any],
                                      nonce: Optional[int] = None) -> Dict[str, Any]:
        """
        构建代币授权交易
        
        Args:
            from_address: 授权方地址
            token_address: 代币合约地址
            spender_address: 被授权方地址
            amount: 授权金额
            decimals: 代币精度
            gas_settings: Gas设置
            nonce: 交易nonce
            
        Returns:
            Dict[str, Any]: 构建的交易数据
        """
        try:
            # 转换金额为最小单位
            amount_units = int(amount * (10 ** decimals))
            
            # 构建approve函数调用数据
            # approve(address,uint256) = 0x095ea7b3
            function_selector = "0x095ea7b3"
            spender_padded = spender_address[2:].zfill(64) if spender_address.startswith("0x") else spender_address.zfill(64)
            amount_hex = hex(amount_units)[2:].zfill(64)
            
            call_data = function_selector + spender_padded + amount_hex
            
            transaction = {
                "type": "approve",
                "from": from_address,
                "to": token_address,
                "value": 0,
                "gas": gas_settings.get("gas_limit", 50000),
                "gasPrice": gas_settings.get("gas_price", 20) * 1e9,
                "nonce": nonce,
                "data": call_data,
                "chainId": self.config.get("chain_id", 1),
                "token_address": token_address,
                "spender_address": spender_address,
                "approve_amount": amount,
                "created_at": datetime.utcnow().isoformat()
            }
            
            # EIP-1559支持
            if "max_fee_per_gas" in gas_settings:
                transaction.update({
                    "maxFeePerGas": gas_settings["max_fee_per_gas"] * 1e9,
                    "maxPriorityFeePerGas": gas_settings["max_priority_fee_per_gas"] * 1e9
                })
                del transaction["gasPrice"]
            
            self.logger.info(f"Built approve transaction: {amount} tokens for {spender_address}")
            return transaction
            
        except Exception as e:
            self.logger.error(f"Failed to build approve transaction: {e}")
            return {}
    
    def validate_transaction(self, transaction: Dict[str, Any]) -> bool:
        """
        验证交易数据
        
        Args:
            transaction: 交易数据
            
        Returns:
            bool: 验证是否通过
        """
        try:
            required_fields = ["from", "to", "gas", "nonce", "chainId"]
            
            for field in required_fields:
                if field not in transaction:
                    self.logger.error(f"Missing required field: {field}")
                    return False
            
            # 验证地址格式
            if not self._is_valid_address(transaction["from"]):
                self.logger.error("Invalid from address")
                return False
            
            if not self._is_valid_address(transaction["to"]):
                self.logger.error("Invalid to address")
                return False
            
            # 验证gas限制
            if transaction["gas"] <= 0:
                self.logger.error("Invalid gas limit")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to validate transaction: {e}")
            return False
    
    def _is_valid_address(self, address: str) -> bool:
        """验证以太坊地址格式"""
        if not isinstance(address, str):
            return False
        if not address.startswith("0x"):
            return False
        if len(address) != 42:
            return False
        try:
            int(address[2:], 16)
            return True
        except ValueError:
            return False
