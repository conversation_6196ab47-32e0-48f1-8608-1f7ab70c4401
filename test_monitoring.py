#!/usr/bin/env python3
"""
项目监控智能体单元测试

测试项目监控智能体的核心功能
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from monitoring.monitoring_agent import MonitoringAgent
from discovery.models.project import Project, ProjectType, ProjectStatus, Blockchain


class TestMonitoringAgent(unittest.TestCase):
    """项目监控智能体测试类"""

    def setUp(self):
        """测试前准备"""
        self.config = {
            'monitoring': {
                'check_interval': 300,  # 5分钟
                'max_concurrent_checks': 10,
                'timeout': 30
            },
            'alerts': {
                'enabled': True,
                'channels': ['email', 'telegram'],
                'thresholds': {
                    'deadline_warning_days': 3,
                    'status_change_alert': True
                }
            },
            'storage': {
                'type': 'memory'
            }
        }
        
        # 模拟依赖组件
        with patch.multiple(
            'monitoring.monitoring_agent',
            ProjectTracker=MagicMock(),
            DeadlineMonitor=MagicMock(),
            StatusChecker=MagicMock(),
            AlertManager=MagicMock(),
            MetricsCollector=MagicMock()
        ):
            self.agent = MonitoringAgent(self.config)

    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.agent.agent_name, "monitoring")
        self.assertEqual(self.agent.agent_type, "monitoring")
        self.assertIsNotNone(self.agent.version)
        self.assertEqual(self.agent.status, "initialized")

    def test_start_monitoring(self):
        """测试开始监控"""
        # 模拟组件启动
        self.agent.project_tracker.start = Mock()
        self.agent.deadline_monitor.start = Mock()
        self.agent.status_checker.start = Mock()
        
        self.agent.start_monitoring()
        
        self.assertEqual(self.agent.status, "running")
        self.agent.project_tracker.start.assert_called_once()
        self.agent.deadline_monitor.start.assert_called_once()
        self.agent.status_checker.start.assert_called_once()

    def test_stop_monitoring(self):
        """测试停止监控"""
        # 设置为运行状态
        self.agent.status = "running"
        
        # 模拟组件停止
        self.agent.project_tracker.stop = Mock()
        self.agent.deadline_monitor.stop = Mock()
        self.agent.status_checker.stop = Mock()
        
        self.agent.stop_monitoring()
        
        self.assertEqual(self.agent.status, "stopped")
        self.agent.project_tracker.stop.assert_called_once()
        self.agent.deadline_monitor.stop.assert_called_once()
        self.agent.status_checker.stop.assert_called_once()

    def test_add_project_to_monitoring(self):
        """测试添加项目到监控"""
        project = Project(
            id="test_project_1",
            name="Test Project",
            description="A test project",
            project_type=ProjectType.AIRDROP,
            blockchain=Blockchain.ETHEREUM,
            website="https://test-project.com",
            discovery_time=datetime.now().timestamp()
        )
        
        # 模拟添加成功
        self.agent.project_tracker.add_project.return_value = True
        
        result = self.agent.add_project_to_monitoring(project)
        
        self.assertTrue(result)
        self.agent.project_tracker.add_project.assert_called_once_with(project)

    def test_remove_project_from_monitoring(self):
        """测试从监控中移除项目"""
        project_id = "test_project_1"
        
        # 模拟移除成功
        self.agent.project_tracker.remove_project.return_value = True
        
        result = self.agent.remove_project_from_monitoring(project_id)
        
        self.assertTrue(result)
        self.agent.project_tracker.remove_project.assert_called_once_with(project_id)

    def test_check_project_status(self):
        """测试检查项目状态"""
        project_id = "test_project_1"
        expected_status = {
            'project_id': project_id,
            'status': 'active',
            'last_check': datetime.now().isoformat(),
            'website_available': True,
            'social_active': True
        }
        
        # 模拟状态检查
        self.agent.status_checker.check_project.return_value = expected_status
        
        result = self.agent.check_project_status(project_id)
        
        self.assertEqual(result, expected_status)
        self.agent.status_checker.check_project.assert_called_once_with(project_id)

    def test_get_monitoring_status(self):
        """测试获取监控状态"""
        expected_status = {
            'total_projects': 50,
            'active_projects': 40,
            'inactive_projects': 5,
            'error_projects': 5,
            'last_update': datetime.now().isoformat()
        }
        
        # 模拟监控状态
        self.agent.project_tracker.get_status.return_value = expected_status
        
        result = self.agent.get_monitoring_status()
        
        self.assertEqual(result, expected_status)

    def test_get_project_alerts(self):
        """测试获取项目警报"""
        project_id = "test_project_1"
        expected_alerts = [
            {
                'id': 'alert_1',
                'type': 'deadline_warning',
                'message': 'Project deadline in 2 days',
                'severity': 'warning',
                'timestamp': datetime.now().isoformat()
            }
        ]
        
        # 模拟警报获取
        self.agent.alert_manager.get_project_alerts.return_value = expected_alerts
        
        result = self.agent.get_project_alerts(project_id)
        
        self.assertEqual(result, expected_alerts)
        self.agent.alert_manager.get_project_alerts.assert_called_once_with(project_id)

    def test_get_all_alerts(self):
        """测试获取所有警报"""
        expected_alerts = [
            {
                'id': 'alert_1',
                'project_id': 'project_1',
                'type': 'status_change',
                'message': 'Project status changed to inactive',
                'severity': 'error'
            },
            {
                'id': 'alert_2',
                'project_id': 'project_2',
                'type': 'deadline_warning',
                'message': 'Project deadline approaching',
                'severity': 'warning'
            }
        ]
        
        # 模拟获取所有警报
        self.agent.alert_manager.get_all_alerts.return_value = expected_alerts
        
        result = self.agent.get_all_alerts()
        
        self.assertEqual(result, expected_alerts)

    def test_acknowledge_alert(self):
        """测试确认警报"""
        alert_id = "alert_1"
        
        # 模拟确认成功
        self.agent.alert_manager.acknowledge_alert.return_value = True
        
        result = self.agent.acknowledge_alert(alert_id)
        
        self.assertTrue(result)
        self.agent.alert_manager.acknowledge_alert.assert_called_once_with(alert_id)

    def test_get_project_metrics(self):
        """测试获取项目指标"""
        project_id = "test_project_1"
        expected_metrics = {
            'uptime_percentage': 99.5,
            'response_time_avg': 250,
            'social_activity_score': 85,
            'community_growth_rate': 5.2,
            'last_activity': datetime.now().isoformat()
        }
        
        # 模拟指标收集
        self.agent.metrics_collector.get_project_metrics.return_value = expected_metrics
        
        result = self.agent.get_project_metrics(project_id)
        
        self.assertEqual(result, expected_metrics)
        self.agent.metrics_collector.get_project_metrics.assert_called_once_with(project_id)

    def test_get_system_metrics(self):
        """测试获取系统指标"""
        expected_metrics = {
            'total_checks_today': 1200,
            'successful_checks': 1150,
            'failed_checks': 50,
            'average_response_time': 300,
            'alerts_generated': 15,
            'system_uptime': 99.8
        }
        
        # 模拟系统指标
        self.agent.metrics_collector.get_system_metrics.return_value = expected_metrics
        
        result = self.agent.get_system_metrics()
        
        self.assertEqual(result, expected_metrics)

    def test_update_monitoring_config(self):
        """测试更新监控配置"""
        new_config = {
            'check_interval': 600,  # 10分钟
            'max_concurrent_checks': 20
        }
        
        result = self.agent.update_monitoring_config(new_config)
        
        self.assertTrue(result)
        self.assertEqual(self.agent.config['monitoring']['check_interval'], 600)
        self.assertEqual(self.agent.config['monitoring']['max_concurrent_checks'], 20)

    def test_get_agent_info(self):
        """测试获取智能体信息"""
        info = self.agent.get_agent_info()
        
        self.assertEqual(info["name"], "monitoring")
        self.assertEqual(info["type"], "monitoring")
        self.assertIn("capabilities", info)
        self.assertIn("status", info)
        self.assertIn("monitoring_config", info)

    def test_handle_deadline_alert(self):
        """测试处理截止日期警报"""
        project_id = "test_project_1"
        days_remaining = 2
        
        # 模拟处理截止日期警报
        with patch.object(self.agent, '_create_deadline_alert') as mock_create_alert:
            mock_alert = Mock()
            mock_create_alert.return_value = mock_alert
            
            with patch.object(self.agent.alert_manager, 'add_alert') as mock_add_alert:
                self.agent._handle_deadline_alert(project_id, days_remaining)
                
                mock_create_alert.assert_called_once_with(project_id, days_remaining)
                mock_add_alert.assert_called_once_with(mock_alert)

    def test_handle_status_change(self):
        """测试处理状态变化"""
        project_id = "test_project_1"
        old_status = "active"
        new_status = "inactive"
        
        # 模拟处理状态变化
        with patch.object(self.agent, '_create_status_change_alert') as mock_create_alert:
            mock_alert = Mock()
            mock_create_alert.return_value = mock_alert
            
            with patch.object(self.agent.alert_manager, 'add_alert') as mock_add_alert:
                self.agent._handle_status_change(project_id, old_status, new_status)
                
                mock_create_alert.assert_called_once_with(project_id, old_status, new_status)
                mock_add_alert.assert_called_once_with(mock_alert)


class TestMonitoringAgentIntegration(unittest.TestCase):
    """项目监控智能体集成测试"""

    def setUp(self):
        """测试前准备"""
        self.config = {
            'monitoring': {'check_interval': 60},
            'alerts': {'enabled': False},  # 禁用警报避免外部依赖
            'storage': {'type': 'memory'}
        }

    @patch('monitoring.monitoring_agent.ProjectTracker')
    @patch('monitoring.monitoring_agent.AlertManager')
    def test_full_monitoring_workflow(self, mock_alert_manager, mock_project_tracker):
        """测试完整监控工作流"""
        # 创建智能体
        agent = MonitoringAgent(self.config)
        
        # 创建测试项目
        project = Project(
            id="monitoring_test",
            name="Monitoring Test Project",
            description="Testing monitoring workflow",
            project_type=ProjectType.AIRDROP,
            blockchain=Blockchain.ETHEREUM,
            website="https://monitoring-test.com",
            discovery_time=datetime.now().timestamp()
        )
        
        # 模拟添加项目到监控
        mock_project_tracker.return_value.add_project.return_value = True
        
        # 执行监控工作流
        result = agent.add_project_to_monitoring(project)
        
        # 验证结果
        self.assertTrue(result)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
