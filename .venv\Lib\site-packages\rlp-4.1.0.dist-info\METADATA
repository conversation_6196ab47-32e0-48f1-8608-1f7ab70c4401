Metadata-Version: 2.2
Name: rlp
Version: 4.1.0
Summary: rlp: A package for Recursive Length Prefix encoding and decoding
Home-page: https://github.com/ethereum/pyrlp
Author: jnnk
Author-email: <EMAIL>
License: MIT
Keywords: rlp ethereum
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.8, <4
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: eth-utils>=2
Provides-Extra: dev
Requires-Dist: build>=0.9.0; extra == "dev"
Requires-Dist: bump_my_version>=0.19.0; extra == "dev"
Requires-Dist: ipython; extra == "dev"
Requires-Dist: pre-commit>=3.4.0; extra == "dev"
Requires-Dist: tox>=4.0.0; extra == "dev"
Requires-Dist: twine; extra == "dev"
Requires-Dist: wheel; extra == "dev"
Requires-Dist: sphinx>=6.0.0; extra == "dev"
Requires-Dist: sphinx-autobuild>=2021.3.14; extra == "dev"
Requires-Dist: sphinx_rtd_theme>=1.0.0; extra == "dev"
Requires-Dist: towncrier<25,>=24; extra == "dev"
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-xdist>=2.4.0; extra == "dev"
Requires-Dist: hypothesis<6.108.7,>=6.22.0; extra == "dev"
Provides-Extra: docs
Requires-Dist: sphinx>=6.0.0; extra == "docs"
Requires-Dist: sphinx-autobuild>=2021.3.14; extra == "docs"
Requires-Dist: sphinx_rtd_theme>=1.0.0; extra == "docs"
Requires-Dist: towncrier<25,>=24; extra == "docs"
Provides-Extra: test
Requires-Dist: pytest>=7.0.0; extra == "test"
Requires-Dist: pytest-xdist>=2.4.0; extra == "test"
Requires-Dist: hypothesis<6.108.7,>=6.22.0; extra == "test"
Provides-Extra: rust-backend
Requires-Dist: rusty-rlp>=0.2.1; extra == "rust-backend"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# pyrlp

[![Join the conversation on Discord](https://img.shields.io/discord/809793915578089484?color=blue&label=chat&logo=discord&logoColor=white)](https://discord.gg/GHryRvPB84)
[![Build Status](https://circleci.com/gh/ethereum/pyrlp.svg?style=shield)](https://circleci.com/gh/ethereum/pyrlp)
[![PyPI version](https://badge.fury.io/py/rlp.svg)](https://badge.fury.io/py/rlp)
[![Python versions](https://img.shields.io/pypi/pyversions/rlp.svg)](https://pypi.python.org/pypi/rlp)
[![Docs build](https://readthedocs.org/projects/pyrlp/badge/?version=latest)](https://pyrlp.readthedocs.io/en/latest/?badge=latest)

A package for Recursive Length Prefix encoding and decoding

Read the [documentation](https://pyrlp.readthedocs.io/).

View the [change log](https://pyrlp.readthedocs.io/en/latest/release_notes.html).

## Installation

```sh
python -m pip install rlp
```
