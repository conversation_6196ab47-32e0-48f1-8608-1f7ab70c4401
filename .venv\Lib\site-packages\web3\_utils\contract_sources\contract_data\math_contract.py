"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/MathContract.sol:MathContract
MATH_CONTRACT_BYTECODE = "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"  # noqa: E501
MATH_CONTRACT_RUNTIME = "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"  # noqa: E501
MATH_CONTRACT_ABI = [
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "value",
                "type": "uint256",
            }
        ],
        "name": "Increased",
        "type": "event",
    },
    {
        "inputs": [
            {"internalType": "int256", "name": "a", "type": "int256"},
            {"internalType": "int256", "name": "b", "type": "int256"},
        ],
        "name": "add",
        "outputs": [{"internalType": "int256", "name": "result", "type": "int256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "counter",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "incrementCounter",
        "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}],
        "name": "incrementCounter",
        "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "int256", "name": "a", "type": "int256"}],
        "name": "multiply7",
        "outputs": [{"internalType": "int256", "name": "result", "type": "int256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "return13",
        "outputs": [{"internalType": "int256", "name": "result", "type": "int256"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
MATH_CONTRACT_DATA = {
    "bytecode": MATH_CONTRACT_BYTECODE,
    "bytecode_runtime": MATH_CONTRACT_RUNTIME,
    "abi": MATH_CONTRACT_ABI,
}
