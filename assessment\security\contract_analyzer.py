"""
智能合约分析器

该模块实现了分析智能合约的功能。
"""

import logging
import requests
import time
import re
from typing import Dict, List, Any, Optional

from discovery.models.project import Project


class ContractAnalyzer:
    """智能合约分析器，负责分析智能合约"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化智能合约分析器
        
        Args:
            config: 配置字典，包含智能合约分析器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.timeout = config.get('timeout', 10)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        self.etherscan_api_key = config.get('etherscan_api_key', '')
        self.bscscan_api_key = config.get('bscscan_api_key', '')
        self.polygonscan_api_key = config.get('polygonscan_api_key', '')
        self.arbiscan_api_key = config.get('arbiscan_api_key', '')
        self.optimism_api_key = config.get('optimism_api_key', '')
        self.basescan_api_key = config.get('basescan_api_key', '')
        
        # 统计信息
        self._stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'verified_contracts': 0,
            'unverified_contracts': 0,
            'high_risk_contracts': 0,
            'medium_risk_contracts': 0,
            'low_risk_contracts': 0
        }
    
    def analyze(self, project: Project) -> Dict[str, Any]:
        """
        分析智能合约
        
        Args:
            project: 要分析的项目
            
        Returns:
            分析结果字典
        """
        self.logger.info(f"开始分析项目 {project.name} (ID: {project.id}) 的智能合约")
        
        try:
            # 更新统计信息
            self._stats['total_analyses'] += 1
            
            # 初始化结果
            result = {
                'analyzed': False,
                'contract_verified': False,
                'contract_address': None,
                'blockchain': None,
                'contract_name': None,
                'compiler_version': None,
                'is_proxy': False,
                'implementation_address': None,
                'has_source_code': False,
                'has_abi': False,
                'is_token': False,
                'token_standard': None,
                'risk_level': 'unknown',
                'warnings': [],
                'details': {}
            }
            
            # 获取合约地址和区块链
            if not project.token_info or not project.token_info.contract_address:
                self.logger.warning(f"项目 {project.name} 没有合约地址")
                result['warnings'].append("没有合约地址")
                self._stats['failed_analyses'] += 1
                return result
            
            contract_address = project.token_info.contract_address
            blockchain = project.token_info.blockchain or project.blockchain.value
            
            result['contract_address'] = contract_address
            result['blockchain'] = blockchain
            
            # 根据区块链选择分析方法
            if blockchain == 'ethereum':
                contract_info = self._analyze_ethereum_contract(contract_address)
            elif blockchain == 'binance':
                contract_info = self._analyze_binance_contract(contract_address)
            elif blockchain == 'polygon':
                contract_info = self._analyze_polygon_contract(contract_address)
            elif blockchain == 'arbitrum':
                contract_info = self._analyze_arbitrum_contract(contract_address)
            elif blockchain == 'optimism':
                contract_info = self._analyze_optimism_contract(contract_address)
            elif blockchain == 'base':
                contract_info = self._analyze_base_contract(contract_address)
            else:
                self.logger.warning(f"不支持的区块链: {blockchain}")
                result['warnings'].append(f"不支持的区块链: {blockchain}")
                self._stats['failed_analyses'] += 1
                return result
            
            # 更新结果
            if contract_info:
                result.update(contract_info)
                result['analyzed'] = True
                
                # 更新统计信息
                self._stats['successful_analyses'] += 1
                
                if result['contract_verified']:
                    self._stats['verified_contracts'] += 1
                else:
                    self._stats['unverified_contracts'] += 1
                
                if result['risk_level'] == 'high':
                    self._stats['high_risk_contracts'] += 1
                elif result['risk_level'] == 'medium':
                    self._stats['medium_risk_contracts'] += 1
                elif result['risk_level'] == 'low':
                    self._stats['low_risk_contracts'] += 1
            else:
                self.logger.warning(f"无法获取合约信息: {contract_address}")
                result['warnings'].append("无法获取合约信息")
                self._stats['failed_analyses'] += 1
            
            self.logger.info(f"项目 {project.name} 智能合约分析完成")
            
            return result
        
        except Exception as e:
            self.logger.error(f"分析项目 {project.name} 智能合约时出错: {str(e)}")
            self._stats['failed_analyses'] += 1
            
            return {
                'analyzed': False,
                'error': str(e),
                'warnings': ["分析过程中出错"]
            }
    
    def _analyze_ethereum_contract(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        分析以太坊合约
        
        Args:
            contract_address: 合约地址
            
        Returns:
            合约分析结果，如果无法分析则返回None
        """
        try:
            # 如果没有API密钥，无法分析
            if not self.etherscan_api_key:
                return {
                    'contract_verified': False,
                    'warnings': ["没有Etherscan API密钥"]
                }
            
            # 获取合约源代码
            api_url = f"https://api.etherscan.io/api?module=contract&action=getsourcecode&address={contract_address}&apikey={self.etherscan_api_key}"
            
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return {
                    'contract_verified': False,
                    'warnings': ["无法获取合约源代码"]
                }
            
            contract_data = data['result'][0]
            
            # 初始化结果
            result = {
                'contract_verified': contract_data.get('ABI') != 'Contract source code not verified',
                'contract_name': contract_data.get('ContractName'),
                'compiler_version': contract_data.get('CompilerVersion'),
                'is_proxy': contract_data.get('Proxy') == '1',
                'implementation_address': contract_data.get('Implementation'),
                'has_source_code': bool(contract_data.get('SourceCode')),
                'has_abi': contract_data.get('ABI') != 'Contract source code not verified',
                'details': {
                    'constructor_arguments': contract_data.get('ConstructorArguments'),
                    'library': contract_data.get('Library'),
                    'license_type': contract_data.get('LicenseType'),
                    'optimization': contract_data.get('OptimizationUsed') == '1',
                    'runs': contract_data.get('Runs'),
                    'swarm_source': contract_data.get('SwarmSource')
                }
            }
            
            # 检查是否为代币合约
            if result['has_abi']:
                result['is_token'] = self._is_token_contract(contract_data.get('ABI'))
                result['token_standard'] = self._detect_token_standard(contract_data.get('ABI'), contract_data.get('SourceCode'))
            
            # 分析源代码
            if result['has_source_code']:
                source_code = contract_data.get('SourceCode')
                risk_analysis = self._analyze_source_code(source_code)
                
                result['risk_level'] = risk_analysis['risk_level']
                result['warnings'].extend(risk_analysis['warnings'])
                result['details']['risk_factors'] = risk_analysis['risk_factors']
            else:
                result['risk_level'] = 'high'
                result['warnings'].append("合约未验证，无法分析源代码")
            
            return result
        
        except Exception as e:
            self.logger.error(f"分析以太坊合约 {contract_address} 时出错: {str(e)}")
            return None
    
    def _analyze_binance_contract(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        分析币安智能链合约
        
        Args:
            contract_address: 合约地址
            
        Returns:
            合约分析结果，如果无法分析则返回None
        """
        try:
            # 如果没有API密钥，无法分析
            if not self.bscscan_api_key:
                return {
                    'contract_verified': False,
                    'warnings': ["没有BscScan API密钥"]
                }
            
            # 获取合约源代码
            api_url = f"https://api.bscscan.com/api?module=contract&action=getsourcecode&address={contract_address}&apikey={self.bscscan_api_key}"
            
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return {
                    'contract_verified': False,
                    'warnings': ["无法获取合约源代码"]
                }
            
            contract_data = data['result'][0]
            
            # 初始化结果
            result = {
                'contract_verified': contract_data.get('ABI') != 'Contract source code not verified',
                'contract_name': contract_data.get('ContractName'),
                'compiler_version': contract_data.get('CompilerVersion'),
                'is_proxy': contract_data.get('Proxy') == '1',
                'implementation_address': contract_data.get('Implementation'),
                'has_source_code': bool(contract_data.get('SourceCode')),
                'has_abi': contract_data.get('ABI') != 'Contract source code not verified',
                'details': {
                    'constructor_arguments': contract_data.get('ConstructorArguments'),
                    'library': contract_data.get('Library'),
                    'license_type': contract_data.get('LicenseType'),
                    'optimization': contract_data.get('OptimizationUsed') == '1',
                    'runs': contract_data.get('Runs'),
                    'swarm_source': contract_data.get('SwarmSource')
                }
            }
            
            # 检查是否为代币合约
            if result['has_abi']:
                result['is_token'] = self._is_token_contract(contract_data.get('ABI'))
                result['token_standard'] = self._detect_token_standard(contract_data.get('ABI'), contract_data.get('SourceCode'))
            
            # 分析源代码
            if result['has_source_code']:
                source_code = contract_data.get('SourceCode')
                risk_analysis = self._analyze_source_code(source_code)
                
                result['risk_level'] = risk_analysis['risk_level']
                result['warnings'].extend(risk_analysis['warnings'])
                result['details']['risk_factors'] = risk_analysis['risk_factors']
            else:
                result['risk_level'] = 'high'
                result['warnings'].append("合约未验证，无法分析源代码")
            
            return result
        
        except Exception as e:
            self.logger.error(f"分析币安智能链合约 {contract_address} 时出错: {str(e)}")
            return None
    
    def _analyze_polygon_contract(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        分析Polygon合约
        
        Args:
            contract_address: 合约地址
            
        Returns:
            合约分析结果，如果无法分析则返回None
        """
        try:
            # 如果没有API密钥，无法分析
            if not self.polygonscan_api_key:
                return {
                    'contract_verified': False,
                    'warnings': ["没有PolygonScan API密钥"]
                }
            
            # 获取合约源代码
            api_url = f"https://api.polygonscan.com/api?module=contract&action=getsourcecode&address={contract_address}&apikey={self.polygonscan_api_key}"
            
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return {
                    'contract_verified': False,
                    'warnings': ["无法获取合约源代码"]
                }
            
            contract_data = data['result'][0]
            
            # 初始化结果
            result = {
                'contract_verified': contract_data.get('ABI') != 'Contract source code not verified',
                'contract_name': contract_data.get('ContractName'),
                'compiler_version': contract_data.get('CompilerVersion'),
                'is_proxy': contract_data.get('Proxy') == '1',
                'implementation_address': contract_data.get('Implementation'),
                'has_source_code': bool(contract_data.get('SourceCode')),
                'has_abi': contract_data.get('ABI') != 'Contract source code not verified',
                'details': {
                    'constructor_arguments': contract_data.get('ConstructorArguments'),
                    'library': contract_data.get('Library'),
                    'license_type': contract_data.get('LicenseType'),
                    'optimization': contract_data.get('OptimizationUsed') == '1',
                    'runs': contract_data.get('Runs'),
                    'swarm_source': contract_data.get('SwarmSource')
                }
            }
            
            # 检查是否为代币合约
            if result['has_abi']:
                result['is_token'] = self._is_token_contract(contract_data.get('ABI'))
                result['token_standard'] = self._detect_token_standard(contract_data.get('ABI'), contract_data.get('SourceCode'))
            
            # 分析源代码
            if result['has_source_code']:
                source_code = contract_data.get('SourceCode')
                risk_analysis = self._analyze_source_code(source_code)
                
                result['risk_level'] = risk_analysis['risk_level']
                result['warnings'].extend(risk_analysis['warnings'])
                result['details']['risk_factors'] = risk_analysis['risk_factors']
            else:
                result['risk_level'] = 'high'
                result['warnings'].append("合约未验证，无法分析源代码")
            
            return result
        
        except Exception as e:
            self.logger.error(f"分析Polygon合约 {contract_address} 时出错: {str(e)}")
            return None
    
    def _analyze_arbitrum_contract(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        分析Arbitrum合约
        
        Args:
            contract_address: 合约地址
            
        Returns:
            合约分析结果，如果无法分析则返回None
        """
        try:
            # 如果没有API密钥，无法分析
            if not self.arbiscan_api_key:
                return {
                    'contract_verified': False,
                    'warnings': ["没有Arbiscan API密钥"]
                }
            
            # 获取合约源代码
            api_url = f"https://api.arbiscan.io/api?module=contract&action=getsourcecode&address={contract_address}&apikey={self.arbiscan_api_key}"
            
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return {
                    'contract_verified': False,
                    'warnings': ["无法获取合约源代码"]
                }
            
            contract_data = data['result'][0]
            
            # 初始化结果
            result = {
                'contract_verified': contract_data.get('ABI') != 'Contract source code not verified',
                'contract_name': contract_data.get('ContractName'),
                'compiler_version': contract_data.get('CompilerVersion'),
                'is_proxy': contract_data.get('Proxy') == '1',
                'implementation_address': contract_data.get('Implementation'),
                'has_source_code': bool(contract_data.get('SourceCode')),
                'has_abi': contract_data.get('ABI') != 'Contract source code not verified',
                'details': {
                    'constructor_arguments': contract_data.get('ConstructorArguments'),
                    'library': contract_data.get('Library'),
                    'license_type': contract_data.get('LicenseType'),
                    'optimization': contract_data.get('OptimizationUsed') == '1',
                    'runs': contract_data.get('Runs'),
                    'swarm_source': contract_data.get('SwarmSource')
                }
            }
            
            # 检查是否为代币合约
            if result['has_abi']:
                result['is_token'] = self._is_token_contract(contract_data.get('ABI'))
                result['token_standard'] = self._detect_token_standard(contract_data.get('ABI'), contract_data.get('SourceCode'))
            
            # 分析源代码
            if result['has_source_code']:
                source_code = contract_data.get('SourceCode')
                risk_analysis = self._analyze_source_code(source_code)
                
                result['risk_level'] = risk_analysis['risk_level']
                result['warnings'].extend(risk_analysis['warnings'])
                result['details']['risk_factors'] = risk_analysis['risk_factors']
            else:
                result['risk_level'] = 'high'
                result['warnings'].append("合约未验证，无法分析源代码")
            
            return result
        
        except Exception as e:
            self.logger.error(f"分析Arbitrum合约 {contract_address} 时出错: {str(e)}")
            return None
    
    def _analyze_optimism_contract(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        分析Optimism合约
        
        Args:
            contract_address: 合约地址
            
        Returns:
            合约分析结果，如果无法分析则返回None
        """
        try:
            # 如果没有API密钥，无法分析
            if not self.optimism_api_key:
                return {
                    'contract_verified': False,
                    'warnings': ["没有Optimism API密钥"]
                }
            
            # 获取合约源代码
            api_url = f"https://api-optimistic.etherscan.io/api?module=contract&action=getsourcecode&address={contract_address}&apikey={self.optimism_api_key}"
            
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return {
                    'contract_verified': False,
                    'warnings': ["无法获取合约源代码"]
                }
            
            contract_data = data['result'][0]
            
            # 初始化结果
            result = {
                'contract_verified': contract_data.get('ABI') != 'Contract source code not verified',
                'contract_name': contract_data.get('ContractName'),
                'compiler_version': contract_data.get('CompilerVersion'),
                'is_proxy': contract_data.get('Proxy') == '1',
                'implementation_address': contract_data.get('Implementation'),
                'has_source_code': bool(contract_data.get('SourceCode')),
                'has_abi': contract_data.get('ABI') != 'Contract source code not verified',
                'details': {
                    'constructor_arguments': contract_data.get('ConstructorArguments'),
                    'library': contract_data.get('Library'),
                    'license_type': contract_data.get('LicenseType'),
                    'optimization': contract_data.get('OptimizationUsed') == '1',
                    'runs': contract_data.get('Runs'),
                    'swarm_source': contract_data.get('SwarmSource')
                }
            }
            
            # 检查是否为代币合约
            if result['has_abi']:
                result['is_token'] = self._is_token_contract(contract_data.get('ABI'))
                result['token_standard'] = self._detect_token_standard(contract_data.get('ABI'), contract_data.get('SourceCode'))
            
            # 分析源代码
            if result['has_source_code']:
                source_code = contract_data.get('SourceCode')
                risk_analysis = self._analyze_source_code(source_code)
                
                result['risk_level'] = risk_analysis['risk_level']
                result['warnings'].extend(risk_analysis['warnings'])
                result['details']['risk_factors'] = risk_analysis['risk_factors']
            else:
                result['risk_level'] = 'high'
                result['warnings'].append("合约未验证，无法分析源代码")
            
            return result
        
        except Exception as e:
            self.logger.error(f"分析Optimism合约 {contract_address} 时出错: {str(e)}")
            return None
    
    def _analyze_base_contract(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        分析Base合约
        
        Args:
            contract_address: 合约地址
            
        Returns:
            合约分析结果，如果无法分析则返回None
        """
        try:
            # 如果没有API密钥，无法分析
            if not self.basescan_api_key:
                return {
                    'contract_verified': False,
                    'warnings': ["没有BaseScan API密钥"]
                }
            
            # 获取合约源代码
            api_url = f"https://api.basescan.org/api?module=contract&action=getsourcecode&address={contract_address}&apikey={self.basescan_api_key}"
            
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return {
                    'contract_verified': False,
                    'warnings': ["无法获取合约源代码"]
                }
            
            contract_data = data['result'][0]
            
            # 初始化结果
            result = {
                'contract_verified': contract_data.get('ABI') != 'Contract source code not verified',
                'contract_name': contract_data.get('ContractName'),
                'compiler_version': contract_data.get('CompilerVersion'),
                'is_proxy': contract_data.get('Proxy') == '1',
                'implementation_address': contract_data.get('Implementation'),
                'has_source_code': bool(contract_data.get('SourceCode')),
                'has_abi': contract_data.get('ABI') != 'Contract source code not verified',
                'details': {
                    'constructor_arguments': contract_data.get('ConstructorArguments'),
                    'library': contract_data.get('Library'),
                    'license_type': contract_data.get('LicenseType'),
                    'optimization': contract_data.get('OptimizationUsed') == '1',
                    'runs': contract_data.get('Runs'),
                    'swarm_source': contract_data.get('SwarmSource')
                }
            }
            
            # 检查是否为代币合约
            if result['has_abi']:
                result['is_token'] = self._is_token_contract(contract_data.get('ABI'))
                result['token_standard'] = self._detect_token_standard(contract_data.get('ABI'), contract_data.get('SourceCode'))
            
            # 分析源代码
            if result['has_source_code']:
                source_code = contract_data.get('SourceCode')
                risk_analysis = self._analyze_source_code(source_code)
                
                result['risk_level'] = risk_analysis['risk_level']
                result['warnings'].extend(risk_analysis['warnings'])
                result['details']['risk_factors'] = risk_analysis['risk_factors']
            else:
                result['risk_level'] = 'high'
                result['warnings'].append("合约未验证，无法分析源代码")
            
            return result
        
        except Exception as e:
            self.logger.error(f"分析Base合约 {contract_address} 时出错: {str(e)}")
            return None
    
    def _is_token_contract(self, abi_str: str) -> bool:
        """
        检查是否为代币合约
        
        Args:
            abi_str: ABI字符串
            
        Returns:
            是否为代币合约
        """
        try:
            import json
            abi = json.loads(abi_str)
            
            # 检查是否包含代币相关函数
            token_functions = ['transfer', 'transferFrom', 'approve', 'allowance', 'balanceOf', 'totalSupply']
            
            function_names = []
            for item in abi:
                if item.get('type') == 'function':
                    function_names.append(item.get('name'))
            
            # 检查是否包含至少4个代币相关函数
            token_function_count = sum(1 for func in token_functions if func in function_names)
            
            return token_function_count >= 4
        
        except Exception as e:
            self.logger.error(f"检查代币合约时出错: {str(e)}")
            return False
    
    def _detect_token_standard(self, abi_str: str, source_code: str) -> Optional[str]:
        """
        检测代币标准
        
        Args:
            abi_str: ABI字符串
            source_code: 源代码
            
        Returns:
            代币标准，如果无法检测则返回None
        """
        try:
            # 首先从源代码中检测
            if source_code:
                # 检查ERC20
                if re.search(r'(ERC20|IERC20|is\s+ERC20)', source_code, re.IGNORECASE):
                    return 'ERC20'
                
                # 检查ERC721
                if re.search(r'(ERC721|IERC721|is\s+ERC721)', source_code, re.IGNORECASE):
                    return 'ERC721'
                
                # 检查ERC1155
                if re.search(r'(ERC1155|IERC1155|is\s+ERC1155)', source_code, re.IGNORECASE):
                    return 'ERC1155'
            
            # 如果源代码中没有检测到，尝试从ABI中检测
            import json
            abi = json.loads(abi_str)
            
            function_names = []
            for item in abi:
                if item.get('type') == 'function':
                    function_names.append(item.get('name'))
            
            # 检查ERC20
            erc20_functions = ['transfer', 'transferFrom', 'approve', 'allowance', 'balanceOf', 'totalSupply']
            erc20_count = sum(1 for func in erc20_functions if func in function_names)
            
            # 检查ERC721
            erc721_functions = ['ownerOf', 'safeTransferFrom', 'transferFrom', 'approve', 'getApproved', 'setApprovalForAll', 'isApprovedForAll', 'balanceOf']
            erc721_count = sum(1 for func in erc721_functions if func in function_names)
            
            # 检查ERC1155
            erc1155_functions = ['safeTransferFrom', 'safeBatchTransferFrom', 'balanceOf', 'balanceOfBatch', 'setApprovalForAll', 'isApprovedForAll']
            erc1155_count = sum(1 for func in erc1155_functions if func in function_names)
            
            # 确定标准
            if erc20_count >= 6:
                return 'ERC20'
            elif erc721_count >= 6:
                return 'ERC721'
            elif erc1155_count >= 5:
                return 'ERC1155'
            
            return None
        
        except Exception as e:
            self.logger.error(f"检测代币标准时出错: {str(e)}")
            return None
    
    def _analyze_source_code(self, source_code: str) -> Dict[str, Any]:
        """
        分析源代码
        
        Args:
            source_code: 源代码
            
        Returns:
            分析结果
        """
        # 初始化结果
        result = {
            'risk_level': 'low',
            'warnings': [],
            'risk_factors': {
                'high_risk': [],
                'medium_risk': [],
                'low_risk': []
            }
        }
        
        # 检查高风险因素
        high_risk_patterns = [
            (r'selfdestruct\s*\(', "合约包含自毁功能"),
            (r'delegatecall\s*\(', "合约使用delegatecall"),
            (r'assembly\s*{', "合约使用内联汇编"),
            (r'tx\.origin', "合约使用tx.origin进行身份验证"),
            (r'block\.(timestamp|number|difficulty)', "合约依赖区块属性进行随机性"),
            (r'function\s+transferOwnership', "合约包含所有权转移功能"),
            (r'function\s+mint', "合约包含铸币功能"),
            (r'function\s+burn', "合约包含销毁功能"),
            (r'function\s+pause', "合约包含暂停功能"),
            (r'function\s+blacklist', "合约包含黑名单功能")
        ]
        
        for pattern, warning in high_risk_patterns:
            if re.search(pattern, source_code):
                result['risk_factors']['high_risk'].append(warning)
        
        # 检查中风险因素
        medium_risk_patterns = [
            (r'require\s*\(\s*msg\.sender\s*==\s*owner', "合约使用简单的所有者检查"),
            (r'function\s+setFee', "合约包含设置费用功能"),
            (r'function\s+setTax', "合约包含设置税率功能"),
            (r'function\s+setMaxTxAmount', "合约包含设置最大交易金额功能"),
            (r'function\s+excludeFromFee', "合约包含排除费用功能"),
            (r'function\s+includeInFee', "合约包含包含费用功能"),
            (r'function\s+setMaxWalletSize', "合约包含设置最大钱包大小功能")
        ]
        
        for pattern, warning in medium_risk_patterns:
            if re.search(pattern, source_code):
                result['risk_factors']['medium_risk'].append(warning)
        
        # 检查低风险因素
        low_risk_patterns = [
            (r'function\s+renounceOwnership', "合约包含放弃所有权功能"),
            (r'function\s+setRouter', "合约包含设置路由器功能"),
            (r'function\s+setUniswapPair', "合约包含设置Uniswap对功能"),
            (r'function\s+setAutomatedMarketMakerPair', "合约包含设置自动做市商对功能")
        ]
        
        for pattern, warning in low_risk_patterns:
            if re.search(pattern, source_code):
                result['risk_factors']['low_risk'].append(warning)
        
        # 确定风险级别
        if result['risk_factors']['high_risk']:
            result['risk_level'] = 'high'
            result['warnings'].extend(result['risk_factors']['high_risk'])
        elif result['risk_factors']['medium_risk']:
            result['risk_level'] = 'medium'
            result['warnings'].extend(result['risk_factors']['medium_risk'])
        
        return result
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()