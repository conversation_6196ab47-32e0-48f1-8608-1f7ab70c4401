"""User Agent Manager - 用户代理管理器"""
import logging
import random

class UserAgentManager:
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
    
    def generate_user_agent(self) -> str:
        """生成用户代理"""
        agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
        ]
        return random.choice(agents)
