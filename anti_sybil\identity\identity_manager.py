"""
Identity Manager

身份管理器，负责创建、存储和管理数字身份。
"""

import logging
import json
import uuid
import os
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict


@dataclass
class Identity:
    """数字身份数据结构"""
    id: str
    project_id: str
    persona_type: str
    created_at: datetime
    last_used: Optional[datetime] = None
    usage_count: int = 0
    risk_level: str = "low"
    status: str = "active"
    fingerprint_id: Optional[str] = None
    proxy_id: Optional[str] = None
    social_accounts: Dict[str, Any] = None
    wallet_addresses: Dict[str, str] = None
    behavioral_profile: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.social_accounts is None:
            self.social_accounts = {}
        if self.wallet_addresses is None:
            self.wallet_addresses = {}
        if self.behavioral_profile is None:
            self.behavioral_profile = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        if self.last_used:
            data['last_used'] = self.last_used.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Identity':
        """从字典创建身份"""
        data = data.copy()
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data.get('last_used'):
            data['last_used'] = datetime.fromisoformat(data['last_used'])
        return cls(**data)


class IdentityManager:
    """身份管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.identities: Dict[str, Identity] = {}
        self.project_identities: Dict[str, List[str]] = {}
        
        # 配置参数
        self.max_identities_per_project = config.get('max_identities_per_project', 10)
        self.identity_rotation_days = config.get('identity_rotation_days', 30)
        self.max_usage_per_identity = config.get('max_usage_per_identity', 100)
        self.storage_path = config.get('storage_path', 'data/anti_sybil/identities.json')
    
    async def initialize(self) -> bool:
        """初始化身份管理器"""
        try:
            # 创建存储目录
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
            
            # 加载现有身份
            self._load_identities()
            
            self.logger.info("Identity Manager initialized")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize Identity Manager: {e}")
            return False
    
    async def create_identity(self, project_id: str, persona_type: str = "default") -> Optional[Identity]:
        """创建新身份"""
        try:
            # 检查项目身份数量限制
            if self._get_project_identity_count(project_id) >= self.max_identities_per_project:
                self.logger.warning(f"Max identities reached for project {project_id}")
                return None
            
            # 生成唯一身份ID
            identity_id = str(uuid.uuid4())
            
            # 创建身份对象
            identity = Identity(
                id=identity_id,
                project_id=project_id,
                persona_type=persona_type,
                created_at=datetime.utcnow()
            )
            
            # 存储身份
            self.identities[identity_id] = identity
            
            # 更新项目映射
            if project_id not in self.project_identities:
                self.project_identities[project_id] = []
            self.project_identities[project_id].append(identity_id)
            
            # 保存到存储
            self._save_identities()
            
            self.logger.info(f"Created identity {identity_id} for project {project_id}")
            return identity
            
        except Exception as e:
            self.logger.error(f"Failed to create identity: {e}")
            return None
    
    async def get_identity(self, identity_id: str) -> Optional[Identity]:
        """获取身份"""
        return self.identities.get(identity_id)
    
    async def get_project_identities(self, project_id: str) -> List[Identity]:
        """获取项目的所有身份"""
        identity_ids = self.project_identities.get(project_id, [])
        return [self.identities[id] for id in identity_ids if id in self.identities]
    
    async def select_best_identity(self, project_id: str, criteria: Dict[str, Any] = None) -> Optional[Identity]:
        """选择最佳身份"""
        try:
            identities = await self.get_project_identities(project_id)
            if not identities:
                return None
            
            # 过滤条件
            if criteria:
                identities = self._filter_identities(identities, criteria)
            
            if not identities:
                return None
            
            # 选择使用次数最少、风险最低的身份
            best_identity = min(identities, key=lambda x: (
                x.usage_count,
                self._get_risk_score(x),
                (datetime.utcnow() - (x.last_used or x.created_at)).total_seconds()
            ))
            
            return best_identity
            
        except Exception as e:
            self.logger.error(f"Failed to select identity: {e}")
            return None
    
    async def use_identity(self, identity_id: str, activity_type: str) -> bool:
        """记录身份使用"""
        try:
            identity = self.identities.get(identity_id)
            if not identity:
                return False
            
            # 更新使用信息
            identity.last_used = datetime.utcnow()
            identity.usage_count += 1
            
            # 检查是否需要轮换
            if self._needs_rotation(identity):
                identity.status = "needs_rotation"
                self.logger.info(f"Identity {identity_id} marked for rotation")
            
            # 保存更改
            self._save_identities()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to use identity: {e}")
            return False
    
    async def retire_identity(self, identity_id: str) -> bool:
        """退役身份"""
        try:
            if identity_id in self.identities:
                self.identities[identity_id].status = "retired"
                self._save_identities()
                self.logger.info(f"Retired identity {identity_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to retire identity: {e}")
            return False
    
    async def rotate_identity(self, identity_id: str) -> Optional[Identity]:
        """轮换身份"""
        try:
            old_identity = self.identities.get(identity_id)
            if not old_identity:
                return None
            
            # 创建新身份
            new_identity = await self.create_identity(
                old_identity.project_id,
                old_identity.persona_type
            )
            
            if new_identity:
                # 退役旧身份
                await self.retire_identity(identity_id)
                self.logger.info(f"Rotated identity {identity_id} -> {new_identity.id}")
            
            return new_identity
            
        except Exception as e:
            self.logger.error(f"Failed to rotate identity: {e}")
            return None
    
    async def cleanup_identities(self) -> int:
        """清理旧身份"""
        try:
            cleaned_count = 0
            current_time = datetime.utcnow()
            
            for identity_id, identity in list(self.identities.items()):
                # 删除很旧的未使用身份
                age_days = (current_time - identity.created_at).days
                
                if (identity.status == "retired" and age_days > 90) or \
                   (identity.usage_count == 0 and age_days > 30):
                    
                    # 从存储中删除
                    del self.identities[identity_id]
                    
                    # 从项目映射中删除
                    if identity.project_id in self.project_identities:
                        if identity_id in self.project_identities[identity.project_id]:
                            self.project_identities[identity.project_id].remove(identity_id)
                    
                    cleaned_count += 1
                    self.logger.info(f"Cleaned up identity {identity_id}")
            
            if cleaned_count > 0:
                self._save_identities()
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup identities: {e}")
            return 0
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            total_identities = len(self.identities)
            active_identities = len([i for i in self.identities.values() if i.status == "active"])
            
            # 风险分布
            risk_distribution = {"low": 0, "medium": 0, "high": 0}
            for identity in self.identities.values():
                risk_distribution[identity.risk_level] += 1
            
            # 使用统计
            total_usage = sum(i.usage_count for i in self.identities.values())
            avg_usage = total_usage / total_identities if total_identities > 0 else 0
            
            return {
                "total_identities": total_identities,
                "active_identities": active_identities,
                "retired_identities": total_identities - active_identities,
                "risk_distribution": risk_distribution,
                "total_usage": total_usage,
                "average_usage": round(avg_usage, 2),
                "projects_with_identities": len(self.project_identities)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get statistics: {e}")
            return {}
    
    def _get_project_identity_count(self, project_id: str) -> int:
        """获取项目的活跃身份数量"""
        identities = [self.identities[id] for id in self.project_identities.get(project_id, []) 
                     if id in self.identities]
        return len([i for i in identities if i.status == "active"])
    
    def _filter_identities(self, identities: List[Identity], criteria: Dict[str, Any]) -> List[Identity]:
        """根据条件过滤身份"""
        filtered = identities
        
        # 按风险级别过滤
        if "max_risk_level" in criteria:
            risk_levels = {"low": 0, "medium": 1, "high": 2}
            max_risk = risk_levels.get(criteria["max_risk_level"], 2)
            filtered = [i for i in filtered if risk_levels.get(i.risk_level, 0) <= max_risk]
        
        # 按状态过滤
        if "status" in criteria:
            filtered = [i for i in filtered if i.status == criteria["status"]]
        
        # 按最大使用次数过滤
        if "max_usage" in criteria:
            filtered = [i for i in filtered if i.usage_count <= criteria["max_usage"]]
        
        return filtered
    
    def _get_risk_score(self, identity: Identity) -> float:
        """计算身份风险分数"""
        risk_scores = {"low": 0.1, "medium": 0.5, "high": 0.9}
        base_score = risk_scores.get(identity.risk_level, 0.5)
        
        # 根据使用次数调整
        usage_factor = min(identity.usage_count / self.max_usage_per_identity, 1.0)
        
        # 根据年龄调整
        age_days = (datetime.utcnow() - identity.created_at).days
        age_factor = min(age_days / self.identity_rotation_days, 1.0)
        
        return base_score + (usage_factor * 0.3) + (age_factor * 0.2)
    
    def _needs_rotation(self, identity: Identity) -> bool:
        """检查身份是否需要轮换"""
        # 检查使用次数
        if identity.usage_count >= self.max_usage_per_identity:
            return True
        
        # 检查年龄
        age_days = (datetime.utcnow() - identity.created_at).days
        if age_days >= self.identity_rotation_days:
            return True
        
        # 检查风险级别
        if identity.risk_level == "high":
            return True
        
        return False
    
    def _load_identities(self):
        """从存储加载身份"""
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r') as f:
                    data = json.load(f)
                
                # 加载身份
                for identity_data in data.get('identities', []):
                    identity = Identity.from_dict(identity_data)
                    self.identities[identity.id] = identity
                
                # 重建项目映射
                self.project_identities = {}
                for identity in self.identities.values():
                    if identity.project_id not in self.project_identities:
                        self.project_identities[identity.project_id] = []
                    self.project_identities[identity.project_id].append(identity.id)
                
                self.logger.info(f"Loaded {len(self.identities)} identities")
                
        except Exception as e:
            self.logger.error(f"Failed to load identities: {e}")
    
    def _save_identities(self):
        """保存身份到存储"""
        try:
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
            
            data = {
                'identities': [identity.to_dict() for identity in self.identities.values()],
                'saved_at': datetime.utcnow().isoformat()
            }
            
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.logger.debug(f"Saved {len(self.identities)} identities")
            
        except Exception as e:
            self.logger.error(f"Failed to save identities: {e}")
