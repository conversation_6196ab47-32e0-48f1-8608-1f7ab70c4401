#!/usr/bin/env python3
"""
AirHunter 项目完成度分析

全面检查项目的实现状态、功能完整性和代码质量
"""

import os
import logging
import json
import ast
from typing import Dict, List, Any, Tuple
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ProjectAnalyzer:
    """项目分析器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.analysis_results = {}
        
    def analyze_project(self) -> Dict[str, Any]:
        """分析整个项目"""
        logger.info("🔍 开始分析 AirHunter 项目完成度...")
        
        # 1. 分析项目结构
        structure_analysis = self.analyze_project_structure()
        
        # 2. 分析智能体实现
        agents_analysis = self.analyze_agents()
        
        # 3. 分析核心模块
        modules_analysis = self.analyze_core_modules()
        
        # 4. 分析配置和文档
        config_analysis = self.analyze_configuration()
        
        # 5. 分析代码质量
        quality_analysis = self.analyze_code_quality()
        
        # 6. 计算总体完成度
        overall_completion = self.calculate_overall_completion(
            structure_analysis, agents_analysis, modules_analysis, 
            config_analysis, quality_analysis
        )
        
        return {
            'project_structure': structure_analysis,
            'agents': agents_analysis,
            'core_modules': modules_analysis,
            'configuration': config_analysis,
            'code_quality': quality_analysis,
            'overall_completion': overall_completion
        }
    
    def analyze_project_structure(self) -> Dict[str, Any]:
        """分析项目结构"""
        logger.info("📁 分析项目结构...")
        
        expected_structure = {
            'coordinator': '协调控制智能体',
            'discovery': '项目发现智能体',
            'assessment': '项目评估智能体',
            'monitoring': '监控智能体',
            'fund_management': '资金管理智能体',
            'task_planning': '任务规划智能体',
            'task_execution': '任务执行智能体',
            'proxy': '代理智能体',
            'anti_sybil': '反女巫智能体',
            'profit_optimization': '利润优化智能体',
            'reinforcement_learning': '强化学习智能体',
            'common': '公共模块'
        }
        
        structure_status = {}
        total_expected = len(expected_structure)
        found_count = 0
        
        for module_name, description in expected_structure.items():
            module_path = self.project_root / module_name
            if module_path.exists() and module_path.is_dir():
                # 检查是否有主要文件
                main_files = list(module_path.glob("*.py"))
                has_init = (module_path / "__init__.py").exists()
                
                status = {
                    'exists': True,
                    'has_init': has_init,
                    'file_count': len(main_files),
                    'description': description,
                    'completion': 'complete' if len(main_files) > 1 and has_init else 'partial'
                }
                found_count += 1
            else:
                status = {
                    'exists': False,
                    'has_init': False,
                    'file_count': 0,
                    'description': description,
                    'completion': 'missing'
                }
            
            structure_status[module_name] = status
        
        completion_rate = (found_count / total_expected) * 100
        
        return {
            'modules': structure_status,
            'total_expected': total_expected,
            'found_count': found_count,
            'completion_rate': completion_rate,
            'status': 'excellent' if completion_rate >= 90 else 'good' if completion_rate >= 70 else 'needs_work'
        }
    
    def analyze_agents(self) -> Dict[str, Any]:
        """分析智能体实现"""
        logger.info("🤖 分析智能体实现...")
        
        agents = {
            'coordinator': 'coordinator/coordinator_agent.py',
            'discovery': 'discovery/discovery_agent.py',
            'assessment': 'assessment/assessment_agent.py',
            'monitoring': 'monitoring/monitoring_agent.py',
            'fund_management': 'fund_management/fund_management_agent.py',
            'task_planning': 'task_planning/task_planning_agent.py',
            'task_execution': 'task_execution/task_execution_agent.py',
            'proxy': 'proxy/proxy_agent.py',
            'anti_sybil': 'anti_sybil/anti_sybil_agent.py',
            'profit_optimization': 'profit_optimization/profit_optimization_agent.py',
            'reinforcement_learning': 'reinforcement_learning/rl_agent.py'
        }
        
        agent_analysis = {}
        total_agents = len(agents)
        implemented_count = 0
        
        for agent_name, agent_path in agents.items():
            full_path = self.project_root / agent_path
            
            if full_path.exists():
                analysis = self.analyze_python_file(full_path)
                analysis['exists'] = True
                implemented_count += 1
                
                # 检查关键方法
                key_methods = ['__init__', 'start', 'stop']
                has_key_methods = all(method in analysis['methods'] for method in key_methods)
                
                # 检查是否有持续运行能力
                has_continuous_operation = any(
                    'loop' in method.lower() or 'continuous' in method.lower() 
                    for method in analysis['methods']
                )
                
                # 检查多账号支持
                has_multi_account = any(
                    'account' in method.lower() or 'multi' in method.lower()
                    for method in analysis['methods']
                )
                
                analysis.update({
                    'has_key_methods': has_key_methods,
                    'has_continuous_operation': has_continuous_operation,
                    'has_multi_account': has_multi_account,
                    'completion_score': self.calculate_agent_completion_score(analysis)
                })
            else:
                analysis = {
                    'exists': False,
                    'line_count': 0,
                    'class_count': 0,
                    'method_count': 0,
                    'has_key_methods': False,
                    'has_continuous_operation': False,
                    'has_multi_account': False,
                    'completion_score': 0
                }
            
            agent_analysis[agent_name] = analysis
        
        # 计算总体完成度
        total_score = sum(agent['completion_score'] for agent in agent_analysis.values())
        average_completion = total_score / total_agents if total_agents > 0 else 0
        
        return {
            'agents': agent_analysis,
            'total_agents': total_agents,
            'implemented_count': implemented_count,
            'implementation_rate': (implemented_count / total_agents) * 100,
            'average_completion': average_completion,
            'status': 'excellent' if average_completion >= 80 else 'good' if average_completion >= 60 else 'needs_work'
        }
    
    def analyze_core_modules(self) -> Dict[str, Any]:
        """分析核心模块"""
        logger.info("🔧 分析核心模块...")
        
        core_modules = {
            'common/error_handling.py': '错误处理模块',
            'common/config_manager.py': '配置管理模块',
            'common/mixins/continuous_operation.py': '持续运行混入',
            'config_unified.json': '统一配置文件'
        }
        
        module_analysis = {}
        implemented_count = 0
        
        for module_path, description in core_modules.items():
            full_path = self.project_root / module_path
            
            if full_path.exists():
                if module_path.endswith('.py'):
                    analysis = self.analyze_python_file(full_path)
                elif module_path.endswith('.json'):
                    analysis = self.analyze_json_file(full_path)
                else:
                    analysis = {'exists': True, 'type': 'other'}
                
                analysis['description'] = description
                implemented_count += 1
            else:
                analysis = {
                    'exists': False,
                    'description': description,
                    'completion_score': 0
                }
            
            module_analysis[module_path] = analysis
        
        implementation_rate = (implemented_count / len(core_modules)) * 100
        
        return {
            'modules': module_analysis,
            'total_modules': len(core_modules),
            'implemented_count': implemented_count,
            'implementation_rate': implementation_rate,
            'status': 'excellent' if implementation_rate >= 90 else 'good' if implementation_rate >= 70 else 'needs_work'
        }
    
    def analyze_configuration(self) -> Dict[str, Any]:
        """分析配置和文档"""
        logger.info("📋 分析配置和文档...")
        
        config_files = [
            'config_unified.json',
            'README.md',
            'requirements.txt',
            '.gitignore'
        ]
        
        doc_files = [
            'REINFORCEMENT_LEARNING_AGENT_DESIGN.md',
            'AGENT_ANALYSIS_REPORT.md',
            'FINAL_AGENT_CAPABILITY_REPORT.md',
            'IMPLEMENTATION_REPORT.md'
        ]
        
        config_status = {}
        found_configs = 0
        found_docs = 0
        
        # 检查配置文件
        for config_file in config_files:
            path = self.project_root / config_file
            if path.exists():
                config_status[config_file] = {'exists': True, 'type': 'config'}
                found_configs += 1
            else:
                config_status[config_file] = {'exists': False, 'type': 'config'}
        
        # 检查文档文件
        for doc_file in doc_files:
            path = self.project_root / doc_file
            if path.exists():
                config_status[doc_file] = {'exists': True, 'type': 'documentation'}
                found_docs += 1
            else:
                config_status[doc_file] = {'exists': False, 'type': 'documentation'}
        
        config_rate = (found_configs / len(config_files)) * 100
        doc_rate = (found_docs / len(doc_files)) * 100
        overall_rate = ((found_configs + found_docs) / (len(config_files) + len(doc_files))) * 100
        
        return {
            'files': config_status,
            'config_rate': config_rate,
            'documentation_rate': doc_rate,
            'overall_rate': overall_rate,
            'status': 'excellent' if overall_rate >= 80 else 'good' if overall_rate >= 60 else 'needs_work'
        }
    
    def analyze_code_quality(self) -> Dict[str, Any]:
        """分析代码质量"""
        logger.info("📊 分析代码质量...")
        
        python_files = list(self.project_root.rglob("*.py"))
        
        total_lines = 0
        total_classes = 0
        total_methods = 0
        files_with_docstrings = 0
        files_with_error_handling = 0
        
        for py_file in python_files:
            try:
                analysis = self.analyze_python_file(py_file)
                total_lines += analysis.get('line_count', 0)
                total_classes += analysis.get('class_count', 0)
                total_methods += analysis.get('method_count', 0)
                
                if analysis.get('has_docstrings', False):
                    files_with_docstrings += 1
                
                if analysis.get('has_error_handling', False):
                    files_with_error_handling += 1
                    
            except Exception as e:
                logger.warning(f"分析文件 {py_file} 时出错: {e}")
        
        total_files = len(python_files)
        docstring_rate = (files_with_docstrings / total_files) * 100 if total_files > 0 else 0
        error_handling_rate = (files_with_error_handling / total_files) * 100 if total_files > 0 else 0
        
        # 计算代码质量分数
        quality_score = (docstring_rate + error_handling_rate) / 2
        
        return {
            'total_files': total_files,
            'total_lines': total_lines,
            'total_classes': total_classes,
            'total_methods': total_methods,
            'docstring_rate': docstring_rate,
            'error_handling_rate': error_handling_rate,
            'quality_score': quality_score,
            'status': 'excellent' if quality_score >= 80 else 'good' if quality_score >= 60 else 'needs_work'
        }
    
    def analyze_python_file(self, file_path: Path) -> Dict[str, Any]:
        """分析Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 基本统计
            lines = content.split('\n')
            line_count = len(lines)
            
            # AST分析
            try:
                tree = ast.parse(content)
                
                classes = [node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
                methods = [node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
                
                class_count = len(classes)
                method_count = len(methods)
                
                # 检查文档字符串
                has_docstrings = any(
                    ast.get_docstring(node) for node in classes + methods
                )
                
                # 检查错误处理
                has_error_handling = 'try:' in content or 'except' in content or 'Error' in content
                
                # 提取方法名
                method_names = [method.name for method in methods]
                class_names = [cls.name for cls in classes]
                
                return {
                    'line_count': line_count,
                    'class_count': class_count,
                    'method_count': method_count,
                    'classes': class_names,
                    'methods': method_names,
                    'has_docstrings': has_docstrings,
                    'has_error_handling': has_error_handling,
                    'completion_score': min(100, (line_count / 10) + (class_count * 20) + (method_count * 5))
                }
                
            except SyntaxError:
                return {
                    'line_count': line_count,
                    'class_count': 0,
                    'method_count': 0,
                    'classes': [],
                    'methods': [],
                    'has_docstrings': False,
                    'has_error_handling': 'Error' in content,
                    'completion_score': min(50, line_count / 20),
                    'syntax_error': True
                }
                
        except Exception as e:
            return {
                'line_count': 0,
                'class_count': 0,
                'method_count': 0,
                'classes': [],
                'methods': [],
                'has_docstrings': False,
                'has_error_handling': False,
                'completion_score': 0,
                'error': str(e)
            }
    
    def analyze_json_file(self, file_path: Path) -> Dict[str, Any]:
        """分析JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return {
                'exists': True,
                'valid_json': True,
                'key_count': len(data) if isinstance(data, dict) else 0,
                'completion_score': 100 if data else 50
            }
        except Exception as e:
            return {
                'exists': True,
                'valid_json': False,
                'error': str(e),
                'completion_score': 0
            }
    
    def calculate_agent_completion_score(self, analysis: Dict[str, Any]) -> float:
        """计算智能体完成度分数"""
        score = 0
        
        # 基础分数（存在性）
        if analysis.get('exists', False):
            score += 20
        
        # 代码量分数
        line_count = analysis.get('line_count', 0)
        score += min(30, line_count / 20)  # 最多30分
        
        # 类和方法分数
        class_count = analysis.get('class_count', 0)
        method_count = analysis.get('method_count', 0)
        score += min(20, class_count * 10 + method_count * 2)  # 最多20分
        
        # 关键功能分数
        if analysis.get('has_key_methods', False):
            score += 15
        
        if analysis.get('has_continuous_operation', False):
            score += 10
        
        if analysis.get('has_multi_account', False):
            score += 5
        
        return min(100, score)
    
    def calculate_overall_completion(self, *analyses) -> Dict[str, Any]:
        """计算总体完成度"""
        scores = []
        
        for analysis in analyses:
            if 'completion_rate' in analysis:
                scores.append(analysis['completion_rate'])
            elif 'average_completion' in analysis:
                scores.append(analysis['average_completion'])
            elif 'overall_rate' in analysis:
                scores.append(analysis['overall_rate'])
            elif 'quality_score' in analysis:
                scores.append(analysis['quality_score'])
        
        overall_score = sum(scores) / len(scores) if scores else 0
        
        return {
            'overall_score': overall_score,
            'grade': self.get_grade(overall_score),
            'status': 'excellent' if overall_score >= 85 else 'good' if overall_score >= 70 else 'fair' if overall_score >= 50 else 'needs_work'
        }
    
    def get_grade(self, score: float) -> str:
        """获取等级"""
        if score >= 95:
            return 'A+'
        elif score >= 90:
            return 'A'
        elif score >= 85:
            return 'A-'
        elif score >= 80:
            return 'B+'
        elif score >= 75:
            return 'B'
        elif score >= 70:
            return 'B-'
        elif score >= 65:
            return 'C+'
        elif score >= 60:
            return 'C'
        elif score >= 50:
            return 'C-'
        else:
            return 'D'


def main():
    """主函数"""
    logger.info("🚀 开始 AirHunter 项目完成度分析")
    
    try:
        analyzer = ProjectAnalyzer()
        results = analyzer.analyze_project()
        
        # 保存分析结果
        with open('project_completion_report.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # 生成报告
        generate_completion_report(results)
        
        logger.info("🎉 项目完成度分析完成！")
        return 0
        
    except Exception as e:
        logger.error(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return 1


def generate_completion_report(results: Dict[str, Any]):
    """生成完成度报告"""
    logger.info("📊 生成完成度报告...")
    
    overall = results['overall_completion']
    
    print("\n" + "="*80)
    print("🎯 AirHunter 项目完成度分析报告")
    print("="*80)
    
    print(f"\n📊 总体评分: {overall['overall_score']:.1f}/100 ({overall['grade']})")
    print(f"🏆 项目状态: {overall['status'].upper()}")
    
    # 项目结构
    structure = results['project_structure']
    print(f"\n📁 项目结构: {structure['completion_rate']:.1f}% ({structure['found_count']}/{structure['total_expected']})")
    
    # 智能体实现
    agents = results['agents']
    print(f"🤖 智能体实现: {agents['average_completion']:.1f}% (平均)")
    print(f"   实现率: {agents['implementation_rate']:.1f}% ({agents['implemented_count']}/{agents['total_agents']})")
    
    # 核心模块
    modules = results['core_modules']
    print(f"🔧 核心模块: {modules['implementation_rate']:.1f}% ({modules['implemented_count']}/{modules['total_modules']})")
    
    # 配置文档
    config = results['configuration']
    print(f"📋 配置文档: {config['overall_rate']:.1f}%")
    
    # 代码质量
    quality = results['code_quality']
    print(f"📊 代码质量: {quality['quality_score']:.1f}%")
    print(f"   总代码行数: {quality['total_lines']:,}")
    print(f"   总类数: {quality['total_classes']}")
    print(f"   总方法数: {quality['total_methods']}")
    
    print("\n" + "="*80)


if __name__ == "__main__":
    exit(main())
