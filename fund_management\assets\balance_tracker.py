"""
Balance Tracker

余额跟踪器，负责实时跟踪各种代币和ETH的余额变化。
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta


class BalanceTracker:
    """
    余额跟踪器
    
    负责跟踪钱包地址的ETH和代币余额，提供实时余额监控和历史记录。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化余额跟踪器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 跟踪的钱包地址
        self.tracked_addresses: Dict[str, Dict] = {}
        
        # 余额历史记录
        self.balance_history: Dict[str, List[Dict]] = {}
        
        # 跟踪配置
        self.tracking_config = {
            "update_interval": config.get("update_interval", 30),  # 秒
            "history_retention_days": config.get("history_retention_days", 30),
            "min_balance_change": config.get("min_balance_change", 0.001)  # ETH
        }
        
        # 跟踪统计
        self.tracking_stats = {
            'total_addresses': 0,
            'total_updates': 0,
            'balance_changes_detected': 0,
            'last_update_time': None
        }
        
        # 监控任务
        self.tracking_task: Optional[asyncio.Task] = None
        self.is_tracking = False
    
    async def initialize(self) -> bool:
        """
        初始化余额跟踪器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 启动跟踪任务
            await self.start_tracking()
            
            self.logger.info("Balance Tracker initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Balance Tracker: {e}")
            return False
    
    async def add_address(self, address: str, label: str = "", 
                         tokens_to_track: List[str] = None) -> bool:
        """
        添加地址到跟踪列表
        
        Args:
            address: 钱包地址
            label: 地址标签
            tokens_to_track: 要跟踪的代币合约地址列表
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if not self._is_valid_address(address):
                self.logger.error(f"Invalid address format: {address}")
                return False
            
            tracking_data = {
                "address": address,
                "label": label,
                "tokens_to_track": tokens_to_track or [],
                "added_at": datetime.utcnow(),
                "last_updated": None,
                "current_balances": {},
                "previous_balances": {}
            }
            
            self.tracked_addresses[address] = tracking_data
            self.balance_history[address] = []
            self.tracking_stats['total_addresses'] += 1
            
            # 立即获取初始余额
            await self._update_address_balance(address)
            
            self.logger.info(f"Added address {address} to tracking with label: {label}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add address to tracking: {e}")
            return False
    
    async def remove_address(self, address: str) -> bool:
        """
        从跟踪列表移除地址
        
        Args:
            address: 钱包地址
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if address in self.tracked_addresses:
                del self.tracked_addresses[address]
                # 保留历史记录，但停止跟踪
                self.tracking_stats['total_addresses'] -= 1
                
                self.logger.info(f"Removed address {address} from tracking")
                return True
            else:
                self.logger.warning(f"Address {address} not found in tracking list")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to remove address from tracking: {e}")
            return False
    
    async def get_current_balance(self, address: str, token_address: str = None) -> Optional[float]:
        """
        获取当前余额
        
        Args:
            address: 钱包地址
            token_address: 代币合约地址，None表示ETH
            
        Returns:
            Optional[float]: 当前余额
        """
        try:
            if address not in self.tracked_addresses:
                self.logger.error(f"Address {address} not being tracked")
                return None
            
            tracking_data = self.tracked_addresses[address]
            current_balances = tracking_data.get("current_balances", {})
            
            if token_address is None:
                # 获取ETH余额
                return current_balances.get("ETH", 0.0)
            else:
                # 获取代币余额
                return current_balances.get(token_address, 0.0)
                
        except Exception as e:
            self.logger.error(f"Failed to get current balance: {e}")
            return None
    
    async def get_balance_history(self, address: str, days: int = 7) -> List[Dict]:
        """
        获取余额历史记录
        
        Args:
            address: 钱包地址
            days: 历史天数
            
        Returns:
            List[Dict]: 余额历史记录
        """
        try:
            if address not in self.balance_history:
                return []
            
            cutoff_time = datetime.utcnow() - timedelta(days=days)
            
            filtered_history = [
                record for record in self.balance_history[address]
                if datetime.fromisoformat(record["timestamp"]) >= cutoff_time
            ]
            
            return filtered_history
            
        except Exception as e:
            self.logger.error(f"Failed to get balance history: {e}")
            return []

    async def start_tracking(self):
        """启动余额跟踪"""
        try:
            if not self.is_tracking:
                self.is_tracking = True
                self.tracking_task = asyncio.create_task(self._tracking_loop())
                self.logger.info("Balance tracking started")
                
        except Exception as e:
            self.logger.error(f"Failed to start tracking: {e}")
    
    async def stop_tracking(self):
        """停止余额跟踪"""
        try:
            self.is_tracking = False
            if self.tracking_task:
                self.tracking_task.cancel()
                try:
                    await self.tracking_task
                except asyncio.CancelledError:
                    pass
                self.tracking_task = None
            
            self.logger.info("Balance tracking stopped")
            
        except Exception as e:
            self.logger.error(f"Failed to stop tracking: {e}")
    
    async def _tracking_loop(self):
        """跟踪循环"""
        while self.is_tracking:
            try:
                await self._update_all_balances()
                await asyncio.sleep(self.tracking_config["update_interval"])
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in tracking loop: {e}")
                await asyncio.sleep(10)  # 错误后等待
    
    async def _update_all_balances(self):
        """更新所有地址的余额"""
        try:
            for address in list(self.tracked_addresses.keys()):
                await self._update_address_balance(address)
            
            self.tracking_stats['last_update_time'] = datetime.utcnow().isoformat()
            
        except Exception as e:
            self.logger.error(f"Error updating all balances: {e}")
    
    async def _update_address_balance(self, address: str):
        """更新单个地址的余额"""
        try:
            tracking_data = self.tracked_addresses[address]
            
            # 保存之前的余额
            tracking_data["previous_balances"] = tracking_data.get("current_balances", {}).copy()
            
            # 获取新余额
            new_balances = await self._fetch_balances(address, tracking_data["tokens_to_track"])
            tracking_data["current_balances"] = new_balances
            tracking_data["last_updated"] = datetime.utcnow()
            
            # 检查余额变化
            balance_changed = self._check_balance_changes(
                tracking_data["previous_balances"], 
                new_balances
            )
            
            if balance_changed:
                self.tracking_stats['balance_changes_detected'] += 1
                
                # 记录到历史
                history_record = {
                    "timestamp": datetime.utcnow().isoformat(),
                    "balances": new_balances.copy(),
                    "changes": self._calculate_balance_changes(
                        tracking_data["previous_balances"], 
                        new_balances
                    )
                }
                
                self.balance_history[address].append(history_record)
                
                # 清理旧历史记录
                await self._cleanup_old_history(address)
            
            self.tracking_stats['total_updates'] += 1
            
        except Exception as e:
            self.logger.error(f"Error updating balance for {address}: {e}")
    
    async def _fetch_balances(self, address: str, token_addresses: List[str]) -> Dict[str, float]:
        """
        获取地址的余额
        
        Args:
            address: 钱包地址
            token_addresses: 代币合约地址列表
            
        Returns:
            Dict[str, float]: 余额字典
        """
        try:
            balances = {}
            
            # 模拟获取ETH余额
            # 实际实现需要调用区块链RPC
            import random
            balances["ETH"] = round(random.uniform(0.1, 10.0), 6)
            
            # 模拟获取代币余额
            for token_address in token_addresses:
                balances[token_address] = round(random.uniform(100, 10000), 2)
            
            return balances
            
        except Exception as e:
            self.logger.error(f"Error fetching balances: {e}")
            return {}
    
    def _check_balance_changes(self, previous_balances: Dict, current_balances: Dict) -> bool:
        """检查余额是否有显著变化"""
        try:
            min_change = self.tracking_config["min_balance_change"]
            
            for token, current_balance in current_balances.items():
                previous_balance = previous_balances.get(token, 0.0)
                change = abs(current_balance - previous_balance)
                
                if change >= min_change:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking balance changes: {e}")
            return False
    
    def _calculate_balance_changes(self, previous_balances: Dict, current_balances: Dict) -> Dict:
        """计算余额变化"""
        try:
            changes = {}
            
            all_tokens = set(previous_balances.keys()) | set(current_balances.keys())
            
            for token in all_tokens:
                previous = previous_balances.get(token, 0.0)
                current = current_balances.get(token, 0.0)
                change = current - previous
                
                if change != 0:
                    changes[token] = {
                        "previous": previous,
                        "current": current,
                        "change": change,
                        "change_percent": (change / previous * 100) if previous > 0 else 0
                    }
            
            return changes
            
        except Exception as e:
            self.logger.error(f"Error calculating balance changes: {e}")
            return {}
    
    async def _cleanup_old_history(self, address: str):
        """清理旧的历史记录"""
        try:
            retention_days = self.tracking_config["history_retention_days"]
            cutoff_time = datetime.utcnow() - timedelta(days=retention_days)
            
            if address in self.balance_history:
                self.balance_history[address] = [
                    record for record in self.balance_history[address]
                    if datetime.fromisoformat(record["timestamp"]) >= cutoff_time
                ]
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old history: {e}")
    
    def _is_valid_address(self, address: str) -> bool:
        """验证以太坊地址格式"""
        if not isinstance(address, str):
            return False
        if not address.startswith("0x"):
            return False
        if len(address) != 42:
            return False
        try:
            int(address[2:], 16)
            return True
        except ValueError:
            return False
    
    async def get_tracking_statistics(self) -> Dict[str, Any]:
        """
        获取跟踪统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            return {
                'tracking_stats': self.tracking_stats,
                'tracked_addresses_count': len(self.tracked_addresses),
                'total_history_records': sum(len(history) for history in self.balance_history.values()),
                'tracking_config': self.tracking_config,
                'is_tracking': self.is_tracking
            }
            
        except Exception as e:
            self.logger.error(f"Error getting tracking statistics: {e}")
            return {}
