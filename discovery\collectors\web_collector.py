"""
网站收集器

该模块实现了从项目网站收集信息的功能。
"""

import logging
import requests
import time
import random
import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin

from discovery.collectors.base_collector import BaseCollector


class WebCollector(BaseCollector):
    """网站收集器，从项目网站收集信息"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化网站收集器
        
        Args:
            config: 配置字典，包含网站收集器的配置信息
        """
        super().__init__(config)
        self.timeout = config.get('timeout', 10)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
        self.extract_social_links = config.get('extract_social_links', True)
        self.extract_requirements = config.get('extract_requirements', True)
        self.extract_token_info = config.get('extract_token_info', True)
    
    def collect(self, project: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        从项目网站收集信息
        
        Args:
            project: 项目字典
            
        Returns:
            收集到的项目信息字典，如果无法收集则返回None
        """
        # 检查项目URL
        if 'url' not in project or not project['url']:
            self.logger.warning("项目URL为空")
            return None
        
        url = project['url']
        
        # 获取网站内容
        html_content = self._get_website_content(url)
        if not html_content:
            return None
        
        # 解析HTML
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
        except Exception as e:
            self.logger.error(f"解析HTML时出错: {str(e)}")
            return None
        
        # 收集项目信息
        project_info = {}
        
        # 提取标题
        title = self._extract_title(soup)
        if title and ('name' not in project or not project['name']):
            project_info['name'] = title
        
        # 提取描述
        description = self._extract_description(soup)
        if description and ('description' not in project or not project['description']):
            project_info['description'] = description
        
        # 提取社交媒体链接
        if self.extract_social_links:
            social_channels = self._extract_social_links(soup, url)
            if social_channels:
                project_info['social_channels'] = social_channels
        
        # 提取参与要求
        if self.extract_requirements:
            requirements = self._extract_requirements(soup, html_content)
            if requirements:
                project_info['requirements'] = requirements
        
        # 提取代币信息
        if self.extract_token_info:
            token_info = self._extract_token_info(soup, html_content)
            if token_info:
                project_info['token_info'] = token_info
        
        # 提取项目类型
        project_type = self._extract_project_type(soup, html_content)
        if project_type and ('project_type' not in project or project['project_type'] == 'unknown'):
            project_info['project_type'] = project_type
        
        # 提取区块链平台
        blockchain = self._extract_blockchain(soup, html_content)
        if blockchain and ('blockchain' not in project or project['blockchain'] == 'other'):
            project_info['blockchain'] = blockchain
        
        # 提取时间信息
        time_info = self._extract_time_info(soup, html_content)
        if time_info:
            if 'start_time' in time_info and time_info['start_time']:
                project_info['start_time'] = time_info['start_time']
            
            if 'end_time' in time_info and time_info['end_time']:
                project_info['end_time'] = time_info['end_time']
        
        return project_info
    
    def _get_website_content(self, url: str) -> Optional[str]:
        """
        获取网站内容
        
        Args:
            url: 网站URL
            
        Returns:
            网站HTML内容，如果无法获取则返回None
        """
        for retry in range(self.max_retries):
            try:
                # 随机选择一个 User-Agent
                headers = {
                    'User-Agent': random.choice(self.user_agents)
                }
                
                # 发送请求
                response = requests.get(url, headers=headers, timeout=self.timeout)
                response.raise_for_status()
                
                return response.text
            
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"获取网站内容时出错 (重试 {retry+1}/{self.max_retries}): {str(e)}")
                
                if retry < self.max_retries - 1:
                    time.sleep(self.retry_delay)
        
        self.logger.error(f"无法获取网站内容: {url}")
        return None
    
    def _extract_title(self, soup: BeautifulSoup) -> Optional[str]:
        """
        提取网站标题
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            网站标题，如果无法提取则返回None
        """
        try:
            # 尝试从标题标签获取
            title_tag = soup.find('title')
            if title_tag and title_tag.text.strip():
                return title_tag.text.strip()
            
            # 尝试从h1标签获取
            h1_tag = soup.find('h1')
            if h1_tag and h1_tag.text.strip():
                return h1_tag.text.strip()
            
            # 尝试从其他标题标签获取
            for tag_name in ['h2', 'h3']:
                tag = soup.find(tag_name)
                if tag and tag.text.strip():
                    return tag.text.strip()
            
            return None
        
        except Exception as e:
            self.logger.error(f"提取标题时出错: {str(e)}")
            return None
    
    def _extract_description(self, soup: BeautifulSoup) -> Optional[str]:
        """
        提取网站描述
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            网站描述，如果无法提取则返回None
        """
        try:
            # 尝试从meta标签获取
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc and 'content' in meta_desc.attrs and meta_desc['content'].strip():
                return meta_desc['content'].strip()
            
            # 尝试从og:description获取
            og_desc = soup.find('meta', attrs={'property': 'og:description'})
            if og_desc and 'content' in og_desc.attrs and og_desc['content'].strip():
                return og_desc['content'].strip()
            
            # 尝试从p标签获取
            p_tags = soup.find_all('p')
            for p_tag in p_tags:
                text = p_tag.text.strip()
                if text and len(text) > 50:  # 只考虑较长的段落
                    return text[:200] + ('...' if len(text) > 200 else '')
            
            return None
        
        except Exception as e:
            self.logger.error(f"提取描述时出错: {str(e)}")
            return None
    
    def _extract_social_links(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, Any]]:
        """
        提取社交媒体链接
        
        Args:
            soup: BeautifulSoup对象
            base_url: 基础URL
            
        Returns:
            社交媒体链接列表
        """
        social_channels = []
        
        try:
            # 定义社交媒体平台的正则表达式
            social_patterns = {
                'twitter': r'(?:twitter\.com|x\.com)/([^/\s"\']+)',
                'telegram': r'(?:t\.me|telegram\.me)/([^/\s"\']+)',
                'discord': r'(?:discord\.gg|discord\.com/invite)/([^/\s"\']+)',
                'github': r'github\.com/([^/\s"\']+)',
                'medium': r'medium\.com/(?:@)?([^/\s"\']+)',
                'facebook': r'facebook\.com/([^/\s"\']+)',
                'instagram': r'instagram\.com/([^/\s"\']+)',
                'linkedin': r'linkedin\.com/(?:company|in)/([^/\s"\']+)',
                'youtube': r'youtube\.com/(?:channel|user|c)/([^/\s"\']+)',
                'reddit': r'reddit\.com/r/([^/\s"\']+)'
            }
            
            # 查找所有链接
            links = soup.find_all('a', href=True)
            
            for link in links:
                href = link['href']
                
                # 处理相对URL
                if not href.startswith(('http://', 'https://')):
                    href = urljoin(base_url, href)
                
                # 检查是否为社交媒体链接
                for platform, pattern in social_patterns.items():
                    match = re.search(pattern, href)
                    if match:
                        # 检查是否已存在
                        if not any(channel['platform'] == platform for channel in social_channels):
                            social_channels.append({
                                'platform': platform,
                                'url': href,
                                'followers': None
                            })
                        
                        break
            
            return social_channels
        
        except Exception as e:
            self.logger.error(f"提取社交媒体链接时出错: {str(e)}")
            return []
    
    def _extract_requirements(self, soup: BeautifulSoup, html_content: str) -> List[Dict[str, Any]]:
        """
        提取参与要求
        
        Args:
            soup: BeautifulSoup对象
            html_content: HTML内容
            
        Returns:
            参与要求列表
        """
        requirements = []
        
        try:
            # 查找可能包含要求的列表
            list_elements = soup.find_all(['ul', 'ol'])
            
            for list_element in list_elements:
                list_items = list_element.find_all('li')
                
                for item in list_items:
                    text = item.text.strip()
                    
                    # 检查是否为要求
                    if any(keyword in text.lower() for keyword in ['task', 'step', 'require', 'complete', 'follow', 'join', 'connect', 'verify']):
                        # 确定要求类型
                        req_type = 'other'
                        if any(keyword in text.lower() for keyword in ['twitter', 'tweet', 'retweet', 'follow']):
                            req_type = 'social'
                        elif any(keyword in text.lower() for keyword in ['wallet', 'address', 'connect', 'metamask', 'web3']):
                            req_type = 'wallet'
                        elif any(keyword in text.lower() for keyword in ['kyc', 'verify', 'verification', 'identity']):
                            req_type = 'kyc'
                        elif any(keyword in text.lower() for keyword in ['transaction', 'transfer', 'send', 'receive']):
                            req_type = 'onchain'
                        
                        # 确定难度
                        difficulty = 'medium'
                        if any(keyword in text.lower() for keyword in ['easy', 'simple', 'quick']):
                            difficulty = 'easy'
                        elif any(keyword in text.lower() for keyword in ['hard', 'difficult', 'complex']):
                            difficulty = 'hard'
                        
                        # 提取URL
                        urls = []
                        for a_tag in item.find_all('a', href=True):
                            urls.append(a_tag['href'])
                        
                        url = urls[0] if urls else None
                        
                        # 添加要求
                        requirements.append({
                            'type': req_type,
                            'description': text,
                            'platform': None,  # 无法可靠地确定平台
                            'difficulty': difficulty,
                            'estimated_time': None,  # 无法可靠地估计时间
                            'url': url,
                            'mandatory': True  # 默认为必须
                        })
            
            # 如果没有找到要求，尝试从文本中提取
            if not requirements:
                # 查找可能包含要求的段落
                paragraphs = soup.find_all('p')
                
                for p in paragraphs:
                    text = p.text.strip()
                    
                    # 检查是否为要求段落
                    if any(keyword in text.lower() for keyword in ['task', 'step', 'require', 'complete', 'follow', 'join', 'connect', 'verify']):
                        # 确定要求类型
                        req_type = 'other'
                        if any(keyword in text.lower() for keyword in ['twitter', 'tweet', 'retweet', 'follow']):
                            req_type = 'social'
                        elif any(keyword in text.lower() for keyword in ['wallet', 'address', 'connect', 'metamask', 'web3']):
                            req_type = 'wallet'
                        elif any(keyword in text.lower() for keyword in ['kyc', 'verify', 'verification', 'identity']):
                            req_type = 'kyc'
                        elif any(keyword in text.lower() for keyword in ['transaction', 'transfer', 'send', 'receive']):
                            req_type = 'onchain'
                        
                        # 添加要求
                        requirements.append({
                            'type': req_type,
                            'description': text[:200] + ('...' if len(text) > 200 else ''),
                            'platform': None,
                            'difficulty': 'medium',
                            'estimated_time': None,
                            'url': None,
                            'mandatory': True
                        })
            
            return requirements
        
        except Exception as e:
            self.logger.error(f"提取参与要求时出错: {str(e)}")
            return []
    
    def _extract_token_info(self, soup: BeautifulSoup, html_content: str) -> Optional[Dict[str, Any]]:
        """
        提取代币信息
        
        Args:
            soup: BeautifulSoup对象
            html_content: HTML内容
            
        Returns:
            代币信息字典，如果无法提取则返回None
        """
        try:
            token_info = {}
            
            # 提取代币名称
            name_patterns = [
                r'(?i)token\s+name\s*[:\-]\s*([A-Za-z0-9\s]+)',
                r'(?i)token\s*:\s*([A-Za-z0-9\s]+)',
                r'(?i)name\s*[:\-]\s*([A-Za-z0-9\s]+)'
            ]
            
            for pattern in name_patterns:
                match = re.search(pattern, html_content)
                if match:
                    token_info['name'] = match.group(1).strip()
                    break
            
            # 提取代币符号
            symbol_patterns = [
                r'(?i)token\s+symbol\s*[:\-]\s*([A-Za-z0-9]+)',
                r'(?i)symbol\s*[:\-]\s*([A-Za-z0-9]+)',
                r'(?i)ticker\s*[:\-]\s*([A-Za-z0-9]+)'
            ]
            
            for pattern in symbol_patterns:
                match = re.search(pattern, html_content)
                if match:
                    token_info['symbol'] = match.group(1).strip()
                    break
            
            # 提取合约地址
            contract_patterns = [
                r'(?i)contract\s+address\s*[:\-]\s*(0x[a-fA-F0-9]{40})',
                r'(?i)token\s+address\s*[:\-]\s*(0x[a-fA-F0-9]{40})',
                r'(?i)address\s*[:\-]\s*(0x[a-fA-F0-9]{40})'
            ]
            
            for pattern in contract_patterns:
                match = re.search(pattern, html_content)
                if match:
                    token_info['contract_address'] = match.group(1).strip()
                    break
            
            # 提取区块链平台
            blockchain_patterns = {
                'ethereum': [r'(?i)ethereum', r'(?i)erc20', r'(?i)erc721', r'(?i)eth'],
                'binance': [r'(?i)binance', r'(?i)bsc', r'(?i)bnb', r'(?i)bep20'],
                'solana': [r'(?i)solana', r'(?i)sol'],
                'polygon': [r'(?i)polygon', r'(?i)matic'],
                'avalanche': [r'(?i)avalanche', r'(?i)avax'],
                'arbitrum': [r'(?i)arbitrum', r'(?i)arb'],
                'optimism': [r'(?i)optimism', r'(?i)op'],
                'base': [r'(?i)base'],
                'cosmos': [r'(?i)cosmos', r'(?i)atom'],
                'polkadot': [r'(?i)polkadot', r'(?i)dot'],
                'near': [r'(?i)near'],
                'aptos': [r'(?i)aptos', r'(?i)apt'],
                'sui': [r'(?i)sui']
            }
            
            for blockchain, patterns in blockchain_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, html_content):
                        token_info['blockchain'] = blockchain
                        break
                
                if 'blockchain' in token_info:
                    break
            
            # 提取总供应量
            supply_patterns = [
                r'(?i)total\s+supply\s*[:\-]\s*([0-9,\.]+[KkMmBbTt]?)',
                r'(?i)supply\s*[:\-]\s*([0-9,\.]+[KkMmBbTt]?)',
                r'(?i)max\s+supply\s*[:\-]\s*([0-9,\.]+[KkMmBbTt]?)'
            ]
            
            for pattern in supply_patterns:
                match = re.search(pattern, html_content)
                if match:
                    supply_str = match.group(1).strip().replace(',', '')
                    
                    # 处理单位
                    multiplier = 1
                    if supply_str[-1].lower() == 'k':
                        multiplier = 1000
                        supply_str = supply_str[:-1]
                    elif supply_str[-1].lower() == 'm':
                        multiplier = 1000000
                        supply_str = supply_str[:-1]
                    elif supply_str[-1].lower() == 'b':
                        multiplier = 1000000000
                        supply_str = supply_str[:-1]
                    elif supply_str[-1].lower() == 't':
                        multiplier = 1000000000000
                        supply_str = supply_str[:-1]
                    
                    try:
                        token_info['total_supply'] = float(supply_str) * multiplier
                    except ValueError:
                        pass
                    
                    break
            
            # 提取空投数量
            airdrop_patterns = [
                r'(?i)airdrop\s+amount\s*[:\-]\s*([0-9,\.]+[KkMmBbTt]?)',
                r'(?i)airdrop\s*[:\-]\s*([0-9,\.]+[KkMmBbTt]?)',
                r'(?i)free\s+tokens\s*[:\-]\s*([0-9,\.]+[KkMmBbTt]?)'
            ]
            
            for pattern in airdrop_patterns:
                match = re.search(pattern, html_content)
                if match:
                    airdrop_str = match.group(1).strip().replace(',', '')
                    
                    # 处理单位
                    multiplier = 1
                    if airdrop_str[-1].lower() == 'k':
                        multiplier = 1000
                        airdrop_str = airdrop_str[:-1]
                    elif airdrop_str[-1].lower() == 'm':
                        multiplier = 1000000
                        airdrop_str = airdrop_str[:-1]
                    elif airdrop_str[-1].lower() == 'b':
                        multiplier = 1000000000
                        airdrop_str = airdrop_str[:-1]
                    elif airdrop_str[-1].lower() == 't':
                        multiplier = 1000000000000
                        airdrop_str = airdrop_str[:-1]
                    
                    try:
                        token_info['airdrop_amount'] = float(airdrop_str) * multiplier
                    except ValueError:
                        pass
                    
                    break
            
            return token_info if token_info else None
        
        except Exception as e:
            self.logger.error(f"提取代币信息时出错: {str(e)}")
            return None
    
    def _extract_project_type(self, soup: BeautifulSoup, html_content: str) -> Optional[str]:
        """
        提取项目类型
        
        Args:
            soup: BeautifulSoup对象
            html_content: HTML内容
            
        Returns:
            项目类型，如果无法提取则返回None
        """
        try:
            # 定义项目类型关键词
            type_keywords = {
                'airdrop': ['airdrop', 'free token', 'claim token', 'token distribution'],
                'testnet': ['testnet', 'test network', 'beta test', 'test phase'],
                'presale': ['presale', 'token sale', 'ico', 'initial coin offering', 'ido', 'initial dex offering'],
                'farming': ['farming', 'yield farming', 'liquidity mining', 'staking rewards'],
                'staking': ['staking', 'stake token', 'staking rewards', 'stake to earn']
            }
            
            # 检查HTML内容中的关键词
            for project_type, keywords in type_keywords.items():
                for keyword in keywords:
                    if re.search(r'(?i)\b' + re.escape(keyword) + r'\b', html_content):
                        return project_type
            
            return None
        
        except Exception as e:
            self.logger.error(f"提取项目类型时出错: {str(e)}")
            return None
    
    def _extract_blockchain(self, soup: BeautifulSoup, html_content: str) -> Optional[str]:
        """
        提取区块链平台
        
        Args:
            soup: BeautifulSoup对象
            html_content: HTML内容
            
        Returns:
            区块链平台，如果无法提取则返回None
        """
        try:
            # 定义区块链平台关键词
            blockchain_keywords = {
                'ethereum': ['ethereum', 'eth', 'erc20', 'erc721'],
                'binance': ['binance', 'bsc', 'bnb', 'bep20'],
                'solana': ['solana', 'sol'],
                'polygon': ['polygon', 'matic'],
                'avalanche': ['avalanche', 'avax'],
                'arbitrum': ['arbitrum', 'arb'],
                'optimism': ['optimism', 'op'],
                'base': ['base'],
                'cosmos': ['cosmos', 'atom'],
                'polkadot': ['polkadot', 'dot'],
                'near': ['near'],
                'aptos': ['aptos', 'apt'],
                'sui': ['sui']
            }
            
            # 检查HTML内容中的关键词
            for blockchain, keywords in blockchain_keywords.items():
                for keyword in keywords:
                    if re.search(r'(?i)\b' + re.escape(keyword) + r'\b', html_content):
                        return blockchain
            
            return None
        
        except Exception as e:
            self.logger.error(f"提取区块链平台时出错: {str(e)}")
            return None
    
    def _extract_time_info(self, soup: BeautifulSoup, html_content: str) -> Optional[Dict[str, float]]:
        """
        提取时间信息
        
        Args:
            soup: BeautifulSoup对象
            html_content: HTML内容
            
        Returns:
            时间信息字典，如果无法提取则返回None
        """
        try:
            time_info = {}
            
            # 提取开始时间
            start_patterns = [
                r'(?i)start(?:s|ing)?\s*(?:date|time)?\s*[:\-]\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
                r'(?i)start(?:s|ing)?\s*(?:date|time)?\s*[:\-]\s*(\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2,4})',
                r'(?i)launch(?:es|ing)?\s*(?:date|time)?\s*[:\-]\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
                r'(?i)launch(?:es|ing)?\s*(?:date|time)?\s*[:\-]\s*(\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2,4})'
            ]
            
            for pattern in start_patterns:
                match = re.search(pattern, html_content)
                if match:
                    start_date_str = match.group(1).strip()
                    try:
                        # 尝试解析日期
                        import dateparser
                        start_date = dateparser.parse(start_date_str)
                        if start_date:
                            time_info['start_time'] = start_date.timestamp()
                    except ImportError:
                        self.logger.warning("无法导入 dateparser 库，无法解析日期")
                    except Exception as e:
                        self.logger.error(f"解析开始日期时出错: {str(e)}")
                    
                    break
            
            # 提取结束时间
            end_patterns = [
                r'(?i)end(?:s|ing)?\s*(?:date|time)?\s*[:\-]\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
                r'(?i)end(?:s|ing)?\s*(?:date|time)?\s*[:\-]\s*(\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2,4})',
                r'(?i)close(?:s|ing)?\s*(?:date|time)?\s*[:\-]\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
                r'(?i)close(?:s|ing)?\s*(?:date|time)?\s*[:\-]\s*(\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2,4})'
            ]
            
            for pattern in end_patterns:
                match = re.search(pattern, html_content)
                if match:
                    end_date_str = match.group(1).strip()
                    try:
                        # 尝试解析日期
                        import dateparser
                        end_date = dateparser.parse(end_date_str)
                        if end_date:
                            time_info['end_time'] = end_date.timestamp()
                    except ImportError:
                        self.logger.warning("无法导入 dateparser 库，无法解析日期")
                    except Exception as e:
                        self.logger.error(f"解析结束日期时出错: {str(e)}")
                    
                    break
            
            return time_info if time_info else None
        
        except Exception as e:
            self.logger.error(f"提取时间信息时出错: {str(e)}")
            return None