"""
项目过滤器管理器

该模块负责管理多个项目过滤器，并过滤项目。
"""

import logging
import threading
from typing import Dict, List, Any, Optional, Set

from discovery.filters.keyword_filter import KeywordFilter
from discovery.filters.relevance_filter import RelevanceFilter
from discovery.filters.duplicate_filter import DuplicateFilter


class FilterManager:
    """项目过滤器管理器，负责管理多个项目过滤器，并过滤项目"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目过滤器管理器
        
        Args:
            config: 配置字典，包含项目过滤器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        
        # 初始化过滤器
        self.keyword_filter = KeywordFilter(config.get('keyword_filter', {}))
        self.relevance_filter = RelevanceFilter(config.get('relevance_filter', {}))
        self.duplicate_filter = DuplicateFilter(config.get('duplicate_filter', {}))
        
        # 过滤器启用状态
        self.keyword_filter_enabled = config.get('keyword_filter_enabled', True)
        self.relevance_filter_enabled = config.get('relevance_filter_enabled', True)
        self.duplicate_filter_enabled = config.get('duplicate_filter_enabled', True)
    
    def filter_projects(self, projects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤项目
        
        Args:
            projects: 项目列表
            
        Returns:
            过滤后的项目列表
        """
        with self._lock:
            filtered_projects = projects
            
            # 应用关键词过滤器
            if self.keyword_filter_enabled:
                filtered_projects = self.keyword_filter.filter(filtered_projects)
                self.logger.debug(f"关键词过滤后剩余 {len(filtered_projects)} 个项目")
            
            # 应用相关性过滤器
            if self.relevance_filter_enabled:
                filtered_projects = self.relevance_filter.filter(filtered_projects)
                self.logger.debug(f"相关性过滤后剩余 {len(filtered_projects)} 个项目")
            
            # 应用重复项过滤器
            if self.duplicate_filter_enabled:
                filtered_projects = self.duplicate_filter.filter(filtered_projects)
                self.logger.debug(f"重复项过滤后剩余 {len(filtered_projects)} 个项目")
            
            return filtered_projects
    
    def reset_filters(self) -> None:
        """重置过滤器"""
        with self._lock:
            self.duplicate_filter.reset()
            self.logger.info("已重置过滤器")
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            config: 新的配置字典
        """
        with self._lock:
            self.config = config
            
            # 更新过滤器配置
            self.keyword_filter.update_config(config.get('keyword_filter', {}))
            self.relevance_filter.update_config(config.get('relevance_filter', {}))
            self.duplicate_filter.update_config(config.get('duplicate_filter', {}))
            
            # 更新过滤器启用状态
            self.keyword_filter_enabled = config.get('keyword_filter_enabled', True)
            self.relevance_filter_enabled = config.get('relevance_filter_enabled', True)
            self.duplicate_filter_enabled = config.get('duplicate_filter_enabled', True)
            
            self.logger.info("已更新过滤器配置")