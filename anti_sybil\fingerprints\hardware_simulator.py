"""Hardware Simulator - 硬件模拟器"""
import logging
import random

class HardwareSimulator:
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
    
    def generate_hardware_info(self) -> dict:
        """生成硬件信息"""
        return {
            "cpu_cores": random.choice([2, 4, 6, 8]),
            "memory": random.choice([4, 8, 16])
        }
