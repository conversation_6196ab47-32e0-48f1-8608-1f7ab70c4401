# AirHunter - 协调控制智能体

协调控制智能体是 AirHunter 系统的核心组件，负责协调多个智能体之间的任务执行，管理系统资源，监控系统健康状态，并处理错误恢复。

## 功能特点

- **任务编排** - 协调多个智能体之间的任务执行
- **资源管理** - 分配和管理系统资源
- **健康监控** - 监控系统和智能体的健康状态
- **错误处理** - 处理系统和智能体的错误
- **自动恢复** - 在系统或智能体出现故障时自动恢复
- **负载均衡** - 在多个智能体之间分配工作负载

## 组件结构

```
coordinator/
├── __init__.py
├── coordinator.py                # 主协调器类
├── core/                         # 核心组件
│   ├── __init__.py
│   ├── agent_registry.py         # 智能体注册表
│   ├── lifecycle_manager.py      # 生命周期管理器
│   ├── dependency_resolver.py    # 依赖解析器
│   └── system_state.py           # 系统状态管理器
├── workflow/                     # 工作流组件
│   ├── __init__.py
│   ├── workflow_manager.py       # 工作流管理器
│   ├── task_orchestrator.py      # 任务编排器
│   ├── pipeline_builder.py       # 流程构建器
│   └── workflow_optimizer.py     # 工作流优化器
├── resources/                    # 资源组件
│   ├── __init__.py
│   ├── resource_allocator.py     # 资源分配器
│   ├── load_balancer.py          # 负载均衡器
│   ├── throttle_manager.py       # 节流管理器
│   └── resource_monitor.py       # 资源监控器
├── communication/                # 通信组件
│   ├── __init__.py
│   ├── message_broker.py         # 智能体间消息传递
│   ├── event_system.py           # 事件系统
│   ├── command_dispatcher.py     # 命令分发器
│   └── response_collector.py     # 响应收集器
├── monitoring/                   # 监控组件
│   ├── __init__.py
│   ├── health_monitor.py         # 健康监控器
│   ├── performance_monitor.py    # 性能监控器
│   ├── bottleneck_detector.py    # 瓶颈检测器
│   └── alert_manager.py          # 警报管理器
├── recovery/                     # 恢复组件
│   ├── __init__.py
│   ├── error_handler.py          # 错误处理
│   ├── auto_recovery.py          # 自动恢复
│   ├── recovery_orchestrator.py  # 恢复编排器
│   └── checkpoint_manager.py     # 检查点管理器
└── interface/                    # 接口组件
    ├── __init__.py
    ├── ui_connector.py           # UI连接器
    ├── api_gateway.py            # API网关
    ├── logging_service.py        # 日志服务
    └── metrics_reporter.py       # 指标报告器
```

## 使用方法

### 启动协调控制智能体

```bash
python -m coordinator.main --config config.json --log-level INFO
```

### 配置文件示例

```json
{
  "core": {
    "agent_registry": {
      "auto_discovery": true,
      "discovery_interval": 60
    },
    "lifecycle_manager": {
      "startup_timeout": 30,
      "shutdown_timeout": 30
    },
    "system_state": {
      "history_size": 100
    }
  },
  "workflow": {
    "task_orchestrator": {
      "max_concurrent_tasks": 10,
      "task_timeout": 3600
    }
  },
  "resources": {
    "resource_allocator": {
      "cpu_total": 100,
      "memory_total": 1024,
      "disk_total": 10240,
      "network_total": 100
    },
    "load_balancer": {
      "strategy": "least_loaded"
    }
  },
  "monitoring": {
    "health_monitor": {
      "check_interval": 30
    },
    "performance_monitor": {
      "check_interval": 10
    }
  },
  "recovery": {
    "error_handler": {
      "max_retries": 3
    },
    "auto_recovery": {
      "enabled": true
    }
  },
  "interface": {
    "logging_service": {
      "level": "INFO",
      "file_enabled": true,
      "console_enabled": true
    }
  }
}
```

### API 使用示例

```python
import requests
import json

# 获取系统状态
response = requests.get('http://localhost:8000/api/system/status')
status = response.json()
print(f"系统状态: {status['status']}")

# 创建任务
response = requests.post('http://localhost:8000/api/tasks', json={
    'task_type': 'proxy_acquisition',
    'params': {
        'count': 10,
        'protocol': 'http'
    },
    'priority': 1
})

task = response.json()
print(f"任务ID: {task['id']}")
```

## 依赖项

- Python 3.8+
- psutil
- requests
- threading

## 开发指南

### 添加新的任务类型

1. 在 `coordinator/workflow/task_orchestrator.py` 中注册新的任务类型
2. 实现任务执行逻辑

### 自定义错误处理

修改 `coordinator/recovery/error_handler.py` 文件中的错误处理逻辑。