"""
项目发现智能体的主类

该模块包含项目发现智能体的主类，负责监控和收集新的空投项目信息。
"""

import logging
import threading
import time
import json
import os
from typing import Dict, List, Any, Optional, Set, Tuple

from discovery.models.project import Project, ProjectType, BlockchainPlatform, ProjectStatus
from discovery.sources.source_manager import SourceManager
from discovery.filters.filter_manager import FilterManager
from discovery.collectors.collector_manager import CollectorManager


class DiscoveryAgent:
    """项目发现智能体的主类，负责监控和收集新的空投项目信息"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目发现智能体
        
        Args:
            config: 配置字典，包含项目发现智能体的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("初始化项目发现智能体...")
        
        # 初始化组件
        self.source_manager = SourceManager(config.get('sources', {}))
        self.filter_manager = FilterManager(config.get('filters', {}))
        self.collector_manager = CollectorManager(config.get('collectors', {}))
        
        # 运行状态
        self._running = False
        self._lock = threading.RLock()
        self._discovery_thread = None
        
        # 项目存储
        self._projects: Dict[str, Project] = {}  # 项目字典，键为项目ID
        self._project_urls: Set[str] = set()  # 项目URL集合，用于去重
        
        # 配置参数
        self.discovery_interval = config.get('discovery_interval', 3600)  # 默认每小时发现一次
        self.max_projects_per_source = config.get('max_projects_per_source', 10)  # 每个源最多发现的项目数
        self.storage_file = config.get('storage_file', 'projects.json')  # 项目存储文件
        self.storage_enabled = config.get('storage_enabled', True)  # 是否启用存储
        
        # 加载存储的项目
        if self.storage_enabled:
            self._load_projects()
        
        self.logger.info("项目发现智能体初始化完成")
    
    def start(self) -> None:
        """启动项目发现智能体"""
        with self._lock:
            if self._running:
                self.logger.warning("项目发现智能体已经在运行")
                return
            
            self._running = True
            
            # 启动发现线程
            self._discovery_thread = threading.Thread(
                target=self._discovery_loop,
                name="ProjectDiscovery",
                daemon=True
            )
            self._discovery_thread.start()
            
            self.logger.info("项目发现智能体已启动")
    
    def stop(self) -> None:
        """停止项目发现智能体"""
        with self._lock:
            if not self._running:
                self.logger.warning("项目发现智能体未在运行")
                return
            
            self._running = False
            
            # 等待线程结束
            if self._discovery_thread:
                self._discovery_thread.join(timeout=5.0)
            
            self._discovery_thread = None
            
            self.logger.info("项目发现智能体已停止")
    
    def get_project(self, project_id: str) -> Optional[Project]:
        """
        获取项目
        
        Args:
            project_id: 项目ID
            
        Returns:
            项目对象，如果不存在则返回None
        """
        with self._lock:
            return self._projects.get(project_id)
    
    def get_projects(self, status: Optional[ProjectStatus] = None, 
                    project_type: Optional[ProjectType] = None,
                    blockchain: Optional[BlockchainPlatform] = None,
                    limit: int = 100, offset: int = 0) -> List[Project]:
        """
        获取项目列表
        
        Args:
            status: 项目状态过滤
            project_type: 项目类型过滤
            blockchain: 区块链平台过滤
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            项目对象列表
        """
        with self._lock:
            # 过滤项目
            filtered_projects = []
            
            for project in self._projects.values():
                if status and project.status != status:
                    continue
                
                if project_type and project.project_type != project_type:
                    continue
                
                if blockchain and project.blockchain != blockchain:
                    continue
                
                filtered_projects.append(project)
            
            # 排序项目（按发现时间降序）
            filtered_projects.sort(key=lambda p: p.discovery_time, reverse=True)
            
            # 应用分页
            return filtered_projects[offset:offset + limit]
    
    def add_project(self, project: Project) -> bool:
        """
        添加项目
        
        Args:
            project: 项目对象
            
        Returns:
            如果成功添加则返回True，否则返回False
        """
        with self._lock:
            # 检查URL是否已存在
            if project.url in self._project_urls:
                self.logger.debug(f"项目URL已存在: {project.url}")
                return False
            
            # 添加项目
            self._projects[project.id] = project
            self._project_urls.add(project.url)
            
            self.logger.info(f"添加项目: {project.name} (ID: {project.id})")
            
            # 存储项目
            if self.storage_enabled:
                self._save_projects()
            
            return True
    
    def update_project(self, project: Project) -> bool:
        """
        更新项目
        
        Args:
            project: 项目对象
            
        Returns:
            如果成功更新则返回True，否则返回False
        """
        with self._lock:
            if project.id not in self._projects:
                self.logger.warning(f"项目不存在: {project.id}")
                return False
            
            # 更新项目
            self._projects[project.id] = project
            
            # 确保URL在集合中
            self._project_urls.add(project.url)
            
            self.logger.info(f"更新项目: {project.name} (ID: {project.id})")
            
            # 存储项目
            if self.storage_enabled:
                self._save_projects()
            
            return True
    
    def remove_project(self, project_id: str) -> bool:
        """
        移除项目
        
        Args:
            project_id: 项目ID
            
        Returns:
            如果成功移除则返回True，否则返回False
        """
        with self._lock:
            if project_id not in self._projects:
                self.logger.warning(f"项目不存在: {project_id}")
                return False
            
            # 获取项目URL
            project_url = self._projects[project_id].url
            
            # 移除项目
            del self._projects[project_id]
            
            # 检查URL是否被其他项目使用
            url_in_use = False
            for project in self._projects.values():
                if project.url == project_url:
                    url_in_use = True
                    break
            
            # 如果URL不再使用，从集合中移除
            if not url_in_use:
                self._project_urls.remove(project_url)
            
            self.logger.info(f"移除项目: {project_id}")
            
            # 存储项目
            if self.storage_enabled:
                self._save_projects()
            
            return True
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        with self._lock:
            # 统计项目状态
            status_counts = {}
            for status in ProjectStatus:
                status_counts[status.value] = 0
            
            for project in self._projects.values():
                status_counts[project.status.value] += 1
            
            # 统计项目类型
            type_counts = {}
            for project_type in ProjectType:
                type_counts[project_type.value] = 0
            
            for project in self._projects.values():
                type_counts[project.project_type.value] += 1
            
            # 统计区块链平台
            blockchain_counts = {}
            for blockchain in BlockchainPlatform:
                blockchain_counts[blockchain.value] = 0
            
            for project in self._projects.values():
                blockchain_counts[project.blockchain.value] += 1
            
            # 统计发现来源
            source_counts = {}
            for project in self._projects.values():
                source = project.discovery_source
                if source not in source_counts:
                    source_counts[source] = 0
                
                source_counts[source] += 1
            
            return {
                "total_projects": len(self._projects),
                "status_counts": status_counts,
                "type_counts": type_counts,
                "blockchain_counts": blockchain_counts,
                "source_counts": source_counts
            }
    
    def _discovery_loop(self) -> None:
        """项目发现循环"""
        self.logger.info("项目发现循环已启动")
        
        # 立即发现一次项目
        self._discover_projects()
        
        while self._running:
            try:
                # 等待下一次发现
                for _ in range(int(self.discovery_interval * 2)):  # 分成更小的间隔，以便更快地响应停止信号
                    if not self._running:
                        break
                    time.sleep(0.5)
                
                if not self._running:
                    break
                
                # 发现项目
                self._discover_projects()
            
            except Exception as e:
                self.logger.error(f"项目发现循环出错: {str(e)}")
                time.sleep(60.0)  # 出错后等待一段时间再继续
        
        self.logger.info("项目发现循环已停止")
    
    def _discover_projects(self) -> None:
        """发现项目"""
        self.logger.info("开始发现项目...")
        
        # 获取所有源
        sources = self.source_manager.get_sources()
        
        if not sources:
            self.logger.warning("没有可用的项目源")
            return
        
        # 从每个源获取项目
        for source_name, source in sources.items():
            try:
                self.logger.info(f"从源 '{source_name}' 获取项目")
                
                # 获取项目
                raw_projects = source.get_projects(self.max_projects_per_source)
                
                if not raw_projects:
                    self.logger.info(f"源 '{source_name}' 没有返回项目")
                    continue
                
                self.logger.info(f"从源 '{source_name}' 获取到 {len(raw_projects)} 个原始项目")
                
                # 过滤项目
                filtered_projects = self.filter_manager.filter_projects(raw_projects)
                
                self.logger.info(f"过滤后剩余 {len(filtered_projects)} 个项目")
                
                # 收集项目详细信息
                for raw_project in filtered_projects:
                    try:
                        # 检查URL是否已存在
                        if raw_project.get('url') in self._project_urls:
                            self.logger.debug(f"项目URL已存在: {raw_project.get('url')}")
                            continue
                        
                        # 收集项目详细信息
                        project_data = self.collector_manager.collect_project_info(raw_project)
                        
                        if not project_data:
                            self.logger.warning(f"无法收集项目信息: {raw_project.get('name')}")
                            continue
                        
                        # 创建项目对象
                        try:
                            project = Project(
                                name=project_data.get('name', 'Unknown'),
                                description=project_data.get('description', ''),
                                url=project_data.get('url', ''),
                                project_type=ProjectType(project_data.get('project_type', 'unknown')),
                                blockchain=BlockchainPlatform(project_data.get('blockchain', 'other')),
                                discovery_source=source_name,
                                source_url=project_data.get('source_url', '')
                            )
                            
                            # 设置可选字段
                            if 'start_time' in project_data:
                                project.start_time = project_data['start_time']
                            
                            if 'end_time' in project_data:
                                project.end_time = project_data['end_time']
                            
                            # 添加社交媒体渠道
                            for channel in project_data.get('social_channels', []):
                                project.add_social_channel(
                                    platform=channel.get('platform', ''),
                                    url=channel.get('url', ''),
                                    followers=channel.get('followers'),
                                    activity_level=channel.get('activity_level'),
                                    verified=channel.get('verified', False)
                                )
                            
                            # 添加参与要求
                            for req in project_data.get('requirements', []):
                                project.add_requirement(
                                    type=req.get('type', ''),
                                    description=req.get('description', ''),
                                    platform=req.get('platform'),
                                    difficulty=req.get('difficulty'),
                                    estimated_time=req.get('estimated_time'),
                                    url=req.get('url'),
                                    mandatory=req.get('mandatory', True)
                                )
                            
                            # 设置代币信息
                            token_info = project_data.get('token_info')
                            if token_info:
                                project.set_token_info(
                                    name=token_info.get('name'),
                                    symbol=token_info.get('symbol'),
                                    contract_address=token_info.get('contract_address'),
                                    blockchain=token_info.get('blockchain'),
                                    total_supply=token_info.get('total_supply'),
                                    airdrop_amount=token_info.get('airdrop_amount'),
                                    estimated_value=token_info.get('estimated_value'),
                                    tokenomics=token_info.get('tokenomics')
                                )
                            
                            # 添加项目
                            self.add_project(project)
                        
                        except Exception as e:
                            self.logger.error(f"创建项目对象时出错: {str(e)}")
                    
                    except Exception as e:
                        self.logger.error(f"收集项目信息时出错: {str(e)}")
            
            except Exception as e:
                self.logger.error(f"从源 '{source_name}' 获取项目时出错: {str(e)}")
        
        self.logger.info(f"项目发现完成，当前共有 {len(self._projects)} 个项目")
    
    def _save_projects(self) -> None:
        """保存项目到文件"""
        try:
            # 创建目录
            os.makedirs(os.path.dirname(self.storage_file), exist_ok=True)
            
            # 转换为字典列表
            project_list = [project.to_dict() for project in self._projects.values()]
            
            # 保存到文件
            with open(self.storage_file, 'w') as f:
                json.dump(project_list, f, indent=2)
            
            self.logger.debug(f"已保存 {len(project_list)} 个项目到文件")
        
        except Exception as e:
            self.logger.error(f"保存项目到文件时出错: {str(e)}")
    
    def _load_projects(self) -> None:
        """从文件加载项目"""
        if not os.path.exists(self.storage_file):
            self.logger.info(f"项目文件 {self.storage_file} 不存在")
            return
        
        try:
            # 从文件加载
            with open(self.storage_file, 'r') as f:
                project_list = json.load(f)
            
            # 转换为项目对象
            for project_dict in project_list:
                try:
                    project = Project.from_dict(project_dict)
                    self._projects[project.id] = project
                    self._project_urls.add(project.url)
                except Exception as e:
                    self.logger.error(f"解析项目时出错: {str(e)}")
            
            self.logger.info(f"已从文件加载 {len(self._projects)} 个项目")
        
        except Exception as e:
            self.logger.error(f"从文件加载项目时出错: {str(e)}")
    
    def clear_projects(self) -> None:
        """清空项目"""
        with self._lock:
            self._projects.clear()
            self._project_urls.clear()
            
            # 存储项目
            if self.storage_enabled:
                self._save_projects()
            
            self.logger.info("已清空项目")