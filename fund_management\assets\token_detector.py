"""
Token Detector

代币检测器，负责自动检测新的代币和空投代币。
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta


class TokenDetector:
    """
    代币检测器
    
    负责监控钱包地址，自动检测新出现的代币，特别是空投代币。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化代币检测器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 监控的钱包地址
        self.monitored_addresses: Set[str] = set()
        
        # 已知代币列表（用于检测新代币）
        self.known_tokens: Dict[str, Set[str]] = {}  # address -> set of token addresses
        
        # 检测到的新代币
        self.detected_tokens: List[Dict] = []
        
        # 检测配置
        self.detection_config = {
            "scan_interval": config.get("scan_interval", 60),  # 秒
            "min_token_balance": config.get("min_token_balance", 0.001),
            "max_detection_history": config.get("max_detection_history", 1000),
            "ignore_dust_tokens": config.get("ignore_dust_tokens", True)
        }
        
        # 检测统计
        self.detection_stats = {
            'total_scans': 0,
            'tokens_detected': 0,
            'addresses_monitored': 0,
            'last_scan_time': None
        }
        
        # 检测任务
        self.detection_task: Optional[asyncio.Task] = None
        self.is_detecting = False
        
        # 代币过滤规则
        self.filter_rules = {
            "min_balance": 0.001,
            "blacklisted_tokens": set(),
            "whitelist_only": False,
            "whitelisted_tokens": set()
        }
    
    async def initialize(self) -> bool:
        """
        初始化代币检测器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 启动检测任务
            await self.start_detection()
            
            self.logger.info("Token Detector initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Token Detector: {e}")
            return False
    
    async def add_address_to_monitor(self, address: str) -> bool:
        """
        添加地址到监控列表
        
        Args:
            address: 钱包地址
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if not self._is_valid_address(address):
                self.logger.error(f"Invalid address format: {address}")
                return False
            
            self.monitored_addresses.add(address)
            
            # 初始化已知代币列表
            if address not in self.known_tokens:
                self.known_tokens[address] = await self._get_current_tokens(address)
            
            self.detection_stats['addresses_monitored'] = len(self.monitored_addresses)
            
            self.logger.info(f"Added address {address} to monitoring")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add address to monitoring: {e}")
            return False
    
    async def remove_address_from_monitor(self, address: str) -> bool:
        """
        从监控列表移除地址
        
        Args:
            address: 钱包地址
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if address in self.monitored_addresses:
                self.monitored_addresses.remove(address)
                self.known_tokens.pop(address, None)
                
                self.detection_stats['addresses_monitored'] = len(self.monitored_addresses)
                
                self.logger.info(f"Removed address {address} from monitoring")
                return True
            else:
                self.logger.warning(f"Address {address} not in monitoring list")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to remove address from monitoring: {e}")
            return False
    
    async def start_detection(self):
        """启动代币检测"""
        try:
            if not self.is_detecting:
                self.is_detecting = True
                self.detection_task = asyncio.create_task(self._detection_loop())
                self.logger.info("Token detection started")
                
        except Exception as e:
            self.logger.error(f"Failed to start detection: {e}")
    
    async def stop_detection(self):
        """停止代币检测"""
        try:
            self.is_detecting = False
            if self.detection_task:
                self.detection_task.cancel()
                try:
                    await self.detection_task
                except asyncio.CancelledError:
                    pass
                self.detection_task = None
            
            self.logger.info("Token detection stopped")
            
        except Exception as e:
            self.logger.error(f"Failed to stop detection: {e}")
    
    async def _detection_loop(self):
        """检测循环"""
        while self.is_detecting:
            try:
                await self._scan_all_addresses()
                await asyncio.sleep(self.detection_config["scan_interval"])
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in detection loop: {e}")
                await asyncio.sleep(30)  # 错误后等待

    async def _scan_all_addresses(self):
        """扫描所有监控地址"""
        try:
            for address in list(self.monitored_addresses):
                await self._scan_address_for_new_tokens(address)
            
            self.detection_stats['total_scans'] += 1
            self.detection_stats['last_scan_time'] = datetime.utcnow().isoformat()
            
        except Exception as e:
            self.logger.error(f"Error scanning all addresses: {e}")
    
    async def _scan_address_for_new_tokens(self, address: str):
        """
        扫描单个地址的新代币
        
        Args:
            address: 钱包地址
        """
        try:
            # 获取当前代币列表
            current_tokens = await self._get_current_tokens(address)
            
            # 获取已知代币列表
            known_tokens = self.known_tokens.get(address, set())
            
            # 检测新代币
            new_tokens = current_tokens - known_tokens
            
            if new_tokens:
                for token_address in new_tokens:
                    await self._process_new_token(address, token_address)
                
                # 更新已知代币列表
                self.known_tokens[address] = current_tokens
                
                self.logger.info(f"Detected {len(new_tokens)} new tokens for address {address}")
            
        except Exception as e:
            self.logger.error(f"Error scanning address {address}: {e}")
    
    async def _get_current_tokens(self, address: str) -> Set[str]:
        """
        获取地址当前持有的代币
        
        Args:
            address: 钱包地址
            
        Returns:
            Set[str]: 代币合约地址集合
        """
        try:
            # 模拟获取代币列表
            # 实际实现需要调用区块链API或使用事件日志
            
            import random
            
            # 模拟一些常见代币
            possible_tokens = [
                "0xA0b86a33E6441E6C8C7F1C7C8C7F1C7C8C7F1C7C",  # USDC
                "0xdAC17F958D2ee523a2206206994597C13D831ec7",  # USDT
                "0x6B175474E89094C44Da98b954EedeAC495271d0F",  # DAI
                "0x1f9840a85d5aF5bf1D1762F925BDADdC4201F984",  # UNI
                "0x514910771AF9Ca656af840dff83E8264EcF986CA"   # LINK
            ]
            
            # 随机选择一些代币（模拟持有）
            num_tokens = random.randint(1, 3)
            current_tokens = set(random.sample(possible_tokens, num_tokens))
            
            # 偶尔添加新代币（模拟空投）
            if random.random() < 0.1:  # 10%概率
                new_token = f"0x{random.randint(10**39, 10**40-1):040x}"
                current_tokens.add(new_token)
            
            return current_tokens
            
        except Exception as e:
            self.logger.error(f"Error getting current tokens: {e}")
            return set()
    
    async def _process_new_token(self, wallet_address: str, token_address: str):
        """
        处理检测到的新代币
        
        Args:
            wallet_address: 钱包地址
            token_address: 代币合约地址
        """
        try:
            # 获取代币余额
            balance = await self._get_token_balance(wallet_address, token_address)
            
            # 应用过滤规则
            if not self._should_process_token(token_address, balance):
                return
            
            # 获取代币信息
            token_info = await self._get_token_info(token_address)
            
            # 创建检测记录
            detection_record = {
                "detection_id": f"det_{len(self.detected_tokens) + 1}",
                "wallet_address": wallet_address,
                "token_address": token_address,
                "token_info": token_info,
                "balance": balance,
                "detected_at": datetime.utcnow().isoformat(),
                "detection_type": self._classify_detection(token_info, balance),
                "processed": False
            }
            
            self.detected_tokens.append(detection_record)
            self.detection_stats['tokens_detected'] += 1
            
            # 清理旧记录
            await self._cleanup_old_detections()
            
            self.logger.info(f"Processed new token {token_info.get('symbol', 'UNKNOWN')} for {wallet_address}")
            
        except Exception as e:
            self.logger.error(f"Error processing new token: {e}")
    
    async def _get_token_balance(self, wallet_address: str, token_address: str) -> float:
        """获取代币余额"""
        try:
            # 模拟获取代币余额
            import random
            return round(random.uniform(1, 10000), 6)
        except Exception as e:
            self.logger.error(f"Error getting token balance: {e}")
            return 0.0
    
    async def _get_token_info(self, token_address: str) -> Dict[str, Any]:
        """获取代币信息"""
        try:
            # 模拟获取代币信息
            import random
            
            return {
                "symbol": f"TOKEN{random.randint(1, 999)}",
                "name": f"Test Token {random.randint(1, 999)}",
                "decimals": random.choice([6, 8, 18]),
                "total_supply": random.randint(1000000, 1000000000)
            }
        except Exception as e:
            self.logger.error(f"Error getting token info: {e}")
            return {"symbol": "UNKNOWN", "name": "Unknown Token", "decimals": 18}
    
    def _should_process_token(self, token_address: str, balance: float) -> bool:
        """检查是否应该处理该代币"""
        try:
            # 检查黑名单
            if token_address in self.filter_rules["blacklisted_tokens"]:
                return False
            
            # 检查白名单模式
            if self.filter_rules["whitelist_only"]:
                if token_address not in self.filter_rules["whitelisted_tokens"]:
                    return False
            
            # 检查最小余额
            if balance < self.filter_rules["min_balance"]:
                return False
            
            # 检查是否忽略灰尘代币
            if self.detection_config["ignore_dust_tokens"] and balance < 0.001:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking token filter: {e}")
            return False
    
    def _classify_detection(self, token_info: Dict, balance: float) -> str:
        """分类检测类型"""
        try:
            # 简单的分类逻辑
            if balance > 1000:
                return "airdrop"
            elif balance > 10:
                return "transfer"
            else:
                return "dust"
        except Exception:
            return "unknown"
    
    async def _cleanup_old_detections(self):
        """清理旧的检测记录"""
        try:
            max_records = self.detection_config["max_detection_history"]
            if len(self.detected_tokens) > max_records:
                # 保留最新的记录
                self.detected_tokens = self.detected_tokens[-max_records:]
        except Exception as e:
            self.logger.error(f"Error cleaning up old detections: {e}")
    
    def _is_valid_address(self, address: str) -> bool:
        """验证以太坊地址格式"""
        if not isinstance(address, str):
            return False
        if not address.startswith("0x"):
            return False
        if len(address) != 42:
            return False
        try:
            int(address[2:], 16)
            return True
        except ValueError:
            return False
    
    async def get_detected_tokens(self, wallet_address: str = None, 
                                detection_type: str = None) -> List[Dict]:
        """
        获取检测到的代币
        
        Args:
            wallet_address: 钱包地址过滤
            detection_type: 检测类型过滤
            
        Returns:
            List[Dict]: 检测记录列表
        """
        try:
            filtered_tokens = self.detected_tokens.copy()
            
            if wallet_address:
                filtered_tokens = [
                    token for token in filtered_tokens
                    if token["wallet_address"] == wallet_address
                ]
            
            if detection_type:
                filtered_tokens = [
                    token for token in filtered_tokens
                    if token["detection_type"] == detection_type
                ]
            
            return filtered_tokens
            
        except Exception as e:
            self.logger.error(f"Error getting detected tokens: {e}")
            return []
    
    async def mark_token_processed(self, detection_id: str) -> bool:
        """
        标记代币为已处理
        
        Args:
            detection_id: 检测ID
            
        Returns:
            bool: 标记是否成功
        """
        try:
            for detection in self.detected_tokens:
                if detection["detection_id"] == detection_id:
                    detection["processed"] = True
                    detection["processed_at"] = datetime.utcnow().isoformat()
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error marking token as processed: {e}")
            return False
    
    async def get_detection_statistics(self) -> Dict[str, Any]:
        """
        获取检测统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 统计检测类型分布
            type_distribution = {}
            processed_count = 0
            
            for detection in self.detected_tokens:
                detection_type = detection["detection_type"]
                type_distribution[detection_type] = type_distribution.get(detection_type, 0) + 1
                
                if detection.get("processed", False):
                    processed_count += 1
            
            return {
                'detection_stats': self.detection_stats,
                'total_detections': len(self.detected_tokens),
                'processed_detections': processed_count,
                'type_distribution': type_distribution,
                'detection_config': self.detection_config,
                'is_detecting': self.is_detecting
            }
            
        except Exception as e:
            self.logger.error(f"Error getting detection statistics: {e}")
            return {}
