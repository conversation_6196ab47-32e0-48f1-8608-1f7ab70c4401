"""
统一配置管理器

提供统一的配置加载、验证和管理功能
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_file: str = "config_unified.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config()
        self._validate_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            self.logger.warning(f"配置文件不存在: {self.config_file}")
            return {}
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.logger.info(f"成功加载配置文件: {self.config_file}")
            return config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _validate_config(self) -> None:
        """验证配置"""
        required_sections = [
            "system", "coordinator", "discovery_agent"
        ]
        
        for section in required_sections:
            if section not in self.config:
                self.logger.warning(f"缺少配置节: {section}")
    
    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """
        获取智能体配置
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            智能体配置字典
        """
        agent_key = f"{agent_name}_agent" if not agent_name.endswith("_agent") else agent_name
        return self.config.get(agent_key, {})
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        return self.config.get("system", {})
    
    def get_coordinator_config(self) -> Dict[str, Any]:
        """获取协调器配置"""
        return self.config.get("coordinator", {})
    
    def update_config(self, section: str, updates: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            section: 配置节名称
            updates: 更新内容
        """
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section].update(updates)
        self._save_config()
    
    def _save_config(self) -> None:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            self.logger.info("配置已保存")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")


# 全局配置管理器实例
_config_manager = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_agent_config(agent_name: str) -> Dict[str, Any]:
    """获取智能体配置的便捷函数"""
    return get_config_manager().get_agent_config(agent_name)
