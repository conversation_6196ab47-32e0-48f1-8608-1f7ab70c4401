"""
Wallet Model

Database model for wallet management including wallet addresses,
private keys, balances, and transaction history.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import json


class WalletType(Enum):
    """Wallet type enumeration."""
    ETHEREUM = "ethereum"
    SOLANA = "solana"
    BITCOIN = "bitcoin"
    POLYGON = "polygon"
    BSC = "bsc"


@dataclass
class WalletModel:
    """Wallet data model."""
    
    id: Optional[int] = None
    address: str = ""
    private_key: str = ""
    blockchain: str = ""
    wallet_type: WalletType = WalletType.ETHEREUM
    balance: float = 0.0
    created_at: datetime = None
    updated_at: datetime = None
    is_active: bool = True
    tags: List[str] = None
    notes: str = ""
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        if self.tags is None:
            self.tags = []
    
    @classmethod
    def create_table_sql(cls) -> str:
        """Get SQL for creating wallets table."""
        return """
        CREATE TABLE IF NOT EXISTS wallets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            address TEXT NOT NULL UNIQUE,
            private_key TEXT NOT NULL,
            blockchain TEXT NOT NULL,
            wallet_type TEXT NOT NULL,
            balance REAL DEFAULT 0.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            tags TEXT DEFAULT '[]',
            notes TEXT
        )
        """
