"""
Permission Scanner

权限扫描器，负责扫描和分析系统权限配置。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime


class PermissionScanner:
    """
    权限扫描器
    
    负责扫描系统权限配置，检测权限异常和安全风险。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化权限扫描器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 扫描结果存储
        self.scan_results: List[Dict] = []
        
        # 扫描统计
        self.scan_stats = {
            'total_scans': 0,
            'vulnerabilities_found': 0,
            'high_risk_issues': 0,
            'last_scan_time': None
        }
        
        # 权限风险规则
        self.risk_rules = {
            "excessive_permissions": {
                "description": "用户拥有过多权限",
                "risk_level": "medium",
                "threshold": 10
            },
            "admin_without_2fa": {
                "description": "管理员未启用双因素认证",
                "risk_level": "high"
            },
            "inactive_high_privilege": {
                "description": "高权限用户长期未活动",
                "risk_level": "medium",
                "threshold_days": 30
            }
        }
    
    async def initialize(self) -> bool:
        """
        初始化权限扫描器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("Permission Scanner initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Permission Scanner: {e}")
            return False
    
    async def scan_permissions(self, auth_manager) -> Dict[str, Any]:
        """
        扫描权限配置
        
        Args:
            auth_manager: 授权管理器实例
            
        Returns:
            Dict[str, Any]: 扫描结果
        """
        try:
            self.scan_stats['total_scans'] += 1
            
            scan_result = {
                "scan_id": f"scan_{len(self.scan_results) + 1}",
                "scan_time": datetime.utcnow().isoformat(),
                "vulnerabilities": [],
                "recommendations": [],
                "risk_score": 0.0
            }
            
            # 执行各种权限检查
            await self._check_excessive_permissions(auth_manager, scan_result)
            await self._check_admin_2fa(auth_manager, scan_result)
            await self._check_inactive_users(auth_manager, scan_result)
            
            # 计算风险评分
            scan_result["risk_score"] = self._calculate_risk_score(scan_result["vulnerabilities"])
            
            # 生成建议
            scan_result["recommendations"] = self._generate_recommendations(scan_result["vulnerabilities"])
            
            self.scan_results.append(scan_result)
            self.scan_stats['vulnerabilities_found'] += len(scan_result["vulnerabilities"])
            self.scan_stats['last_scan_time'] = datetime.utcnow().isoformat()
            
            # 统计高风险问题
            high_risk_count = len([v for v in scan_result["vulnerabilities"] if v["risk_level"] == "high"])
            self.scan_stats['high_risk_issues'] += high_risk_count
            
            self.logger.info(f"Permission scan completed: {len(scan_result['vulnerabilities'])} issues found")
            return scan_result
            
        except Exception as e:
            self.logger.error(f"Error scanning permissions: {e}")
            return {"scan_id": "error", "vulnerabilities": [], "error": str(e)}
    
    async def _check_excessive_permissions(self, auth_manager, scan_result: Dict):
        """检查过度权限"""
        try:
            threshold = self.risk_rules["excessive_permissions"]["threshold"]
            
            for username, user_data in auth_manager.users.items():
                user_permissions = await auth_manager._get_user_permissions(username)
                
                if len(user_permissions) > threshold:
                    vulnerability = {
                        "type": "excessive_permissions",
                        "user": username,
                        "description": f"用户拥有 {len(user_permissions)} 个权限，超过阈值 {threshold}",
                        "risk_level": "medium",
                        "permissions_count": len(user_permissions),
                        "detected_at": datetime.utcnow().isoformat()
                    }
                    scan_result["vulnerabilities"].append(vulnerability)
                    
        except Exception as e:
            self.logger.error(f"Error checking excessive permissions: {e}")
    
    async def _check_admin_2fa(self, auth_manager, scan_result: Dict):
        """检查管理员双因素认证"""
        try:
            for username, user_data in auth_manager.users.items():
                if "admin" in user_data.get("roles", []):
                    if not user_data.get("two_factor_enabled", False):
                        vulnerability = {
                            "type": "admin_without_2fa",
                            "user": username,
                            "description": "管理员用户未启用双因素认证",
                            "risk_level": "high",
                            "detected_at": datetime.utcnow().isoformat()
                        }
                        scan_result["vulnerabilities"].append(vulnerability)
                        
        except Exception as e:
            self.logger.error(f"Error checking admin 2FA: {e}")
    
    async def _check_inactive_users(self, auth_manager, scan_result: Dict):
        """检查不活跃用户"""
        try:
            threshold_days = self.risk_rules["inactive_high_privilege"]["threshold_days"]
            cutoff_time = datetime.utcnow() - timedelta(days=threshold_days)
            
            for username, user_data in auth_manager.users.items():
                last_login = user_data.get("last_login")
                if last_login:
                    last_login_time = datetime.fromisoformat(last_login)
                    if last_login_time < cutoff_time:
                        # 检查是否有高权限
                        user_permissions = await auth_manager._get_user_permissions(username)
                        high_risk_permissions = [p for p in user_permissions 
                                               if auth_manager.permissions.get(p, {}).get("risk_level") in ["high", "critical"]]
                        
                        if high_risk_permissions:
                            vulnerability = {
                                "type": "inactive_high_privilege",
                                "user": username,
                                "description": f"高权限用户 {threshold_days} 天未活动",
                                "risk_level": "medium",
                                "last_login": last_login,
                                "high_risk_permissions": high_risk_permissions,
                                "detected_at": datetime.utcnow().isoformat()
                            }
                            scan_result["vulnerabilities"].append(vulnerability)
                            
        except Exception as e:
            self.logger.error(f"Error checking inactive users: {e}")
    
    def _calculate_risk_score(self, vulnerabilities: List[Dict]) -> float:
        """计算风险评分"""
        try:
            risk_weights = {"low": 1, "medium": 3, "high": 7, "critical": 10}
            total_score = 0
            
            for vuln in vulnerabilities:
                risk_level = vuln.get("risk_level", "low")
                total_score += risk_weights.get(risk_level, 1)
            
            # 标准化到0-100分
            max_possible_score = len(vulnerabilities) * 10
            if max_possible_score > 0:
                return min(100, (total_score / max_possible_score) * 100)
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating risk score: {e}")
            return 0.0
    
    def _generate_recommendations(self, vulnerabilities: List[Dict]) -> List[str]:
        """生成安全建议"""
        try:
            recommendations = []
            
            vuln_types = set(v["type"] for v in vulnerabilities)
            
            if "excessive_permissions" in vuln_types:
                recommendations.append("审查用户权限，移除不必要的权限")
            
            if "admin_without_2fa" in vuln_types:
                recommendations.append("为所有管理员用户启用双因素认证")
            
            if "inactive_high_privilege" in vuln_types:
                recommendations.append("定期审查不活跃的高权限用户，考虑禁用或降权")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error generating recommendations: {e}")
            return []
