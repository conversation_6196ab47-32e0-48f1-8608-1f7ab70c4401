#!/usr/bin/env python3
"""
Fund Management Agent - Task 12

创建Security模块 - 安全管理模块
"""

from pathlib import Path

def create_file(path: str, content: str):
    """Create a file with the given content."""
    file_path = Path(path)
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Created: {path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create {path}: {e}")
        return False

def create_security_module():
    """创建Security模块的所有文件"""
    
    files_to_create = {
        # Security module __init__.py
        "fund_management/security/__init__.py": '''"""
Security

安全管理模块，负责授权管理、权限扫描、风险分析、欺诈检测和紧急情况处理。
"""

from .authorization_manager import AuthorizationManager
from .permission_scanner import PermissionScanner
from .risk_analyzer import RiskAnalyzer
from .fraud_detector import FraudDetector
from .emergency_handler import EmergencyHandler

__all__ = [
    "AuthorizationManager",
    "PermissionScanner",
    "RiskAnalyzer",
    "FraudDetector",
    "EmergencyHandler"
]
''',
        
        # Authorization Manager
        "fund_management/security/authorization_manager.py": '''"""
Authorization Manager

授权管理器，负责管理用户权限和访问控制。
"""

import logging
import hashlib
import secrets
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta


class AuthorizationManager:
    """
    授权管理器
    
    负责用户身份验证、权限管理和访问控制。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化授权管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 用户和权限存储
        self.users: Dict[str, Dict] = {}
        self.roles: Dict[str, Dict] = {}
        self.permissions: Dict[str, Dict] = {}
        self.active_sessions: Dict[str, Dict] = {}
        
        # 安全配置
        self.security_config = {
            "session_timeout_minutes": config.get("session_timeout_minutes", 30),
            "max_failed_attempts": config.get("max_failed_attempts", 3),
            "lockout_duration_minutes": config.get("lockout_duration_minutes", 15),
            "require_2fa": config.get("require_2fa", True),
            "password_min_length": config.get("password_min_length", 12)
        }
        
        # 授权统计
        self.auth_stats = {
            'total_login_attempts': 0,
            'successful_logins': 0,
            'failed_logins': 0,
            'active_sessions': 0,
            'permission_checks': 0,
            'permission_denials': 0
        }
        
        # 预定义权限
        self.default_permissions = {
            "wallet.create": {"description": "创建钱包", "risk_level": "medium"},
            "wallet.delete": {"description": "删除钱包", "risk_level": "high"},
            "transaction.send": {"description": "发送交易", "risk_level": "high"},
            "transaction.approve": {"description": "批准交易", "risk_level": "high"},
            "balance.view": {"description": "查看余额", "risk_level": "low"},
            "portfolio.manage": {"description": "管理投资组合", "risk_level": "medium"},
            "security.admin": {"description": "安全管理", "risk_level": "critical"}
        }
        
        # 预定义角色
        self.default_roles = {
            "viewer": {
                "description": "只读用户",
                "permissions": ["balance.view"]
            },
            "trader": {
                "description": "交易用户", 
                "permissions": ["balance.view", "transaction.send", "portfolio.manage"]
            },
            "admin": {
                "description": "管理员",
                "permissions": list(self.default_permissions.keys())
            }
        }
    
    async def initialize(self) -> bool:
        """
        初始化授权管理器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 加载默认权限和角色
            await self._load_default_permissions()
            await self._load_default_roles()
            
            self.logger.info("Authorization Manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Authorization Manager: {e}")
            return False
    
    async def create_user(self, username: str, password: str, email: str,
                         roles: List[str] = None) -> bool:
        """
        创建新用户
        
        Args:
            username: 用户名
            password: 密码
            email: 邮箱
            roles: 角色列表
            
        Returns:
            bool: 创建是否成功
        """
        try:
            if username in self.users:
                self.logger.error(f"User {username} already exists")
                return False
            
            # 验证密码强度
            if not self._validate_password(password):
                self.logger.error("Password does not meet security requirements")
                return False
            
            # 创建用户
            user_data = {
                "username": username,
                "email": email,
                "password_hash": self._hash_password(password),
                "roles": roles or ["viewer"],
                "created_at": datetime.utcnow().isoformat(),
                "last_login": None,
                "failed_attempts": 0,
                "locked_until": None,
                "is_active": True,
                "two_factor_enabled": False,
                "two_factor_secret": None
            }
            
            self.users[username] = user_data
            
            self.logger.info(f"Created user {username} with roles {roles}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating user: {e}")
            return False
    
    async def authenticate_user(self, username: str, password: str,
                              two_factor_code: str = None) -> Optional[str]:
        """
        用户身份验证
        
        Args:
            username: 用户名
            password: 密码
            two_factor_code: 双因素认证码
            
        Returns:
            Optional[str]: 会话令牌
        """
        try:
            self.auth_stats['total_login_attempts'] += 1
            
            if username not in self.users:
                self.logger.warning(f"Login attempt for non-existent user: {username}")
                self.auth_stats['failed_logins'] += 1
                return None
            
            user = self.users[username]
            
            # 检查账户是否被锁定
            if await self._is_user_locked(username):
                self.logger.warning(f"Login attempt for locked user: {username}")
                self.auth_stats['failed_logins'] += 1
                return None
            
            # 验证密码
            if not self._verify_password(password, user["password_hash"]):
                await self._handle_failed_login(username)
                self.auth_stats['failed_logins'] += 1
                return None
            
            # 验证双因素认证
            if user["two_factor_enabled"]:
                if not two_factor_code or not self._verify_2fa(user["two_factor_secret"], two_factor_code):
                    self.logger.warning(f"2FA verification failed for user: {username}")
                    self.auth_stats['failed_logins'] += 1
                    return None
            
            # 创建会话
            session_token = await self._create_session(username)
            
            # 更新用户信息
            user["last_login"] = datetime.utcnow().isoformat()
            user["failed_attempts"] = 0
            
            self.auth_stats['successful_logins'] += 1
            self.logger.info(f"User {username} authenticated successfully")
            
            return session_token
            
        except Exception as e:
            self.logger.error(f"Error authenticating user: {e}")
            self.auth_stats['failed_logins'] += 1
            return None
'''
    }
    
    # Create files
    success_count = 0
    for file_path, content in files_to_create.items():
        if create_file(file_path, content):
            success_count += 1
    
    print(f"📊 Security Module Task 12: Created {success_count}/{len(files_to_create)} files")
    return success_count == len(files_to_create)

def main():
    """主函数"""
    print("🚀 Fund Management Agent - Task 12")
    print("=" * 50)
    
    success = create_security_module()
    
    if success:
        print("✅ Task 12 completed: Security module foundation created!")
    else:
        print("❌ Task 12 failed")

if __name__ == "__main__":
    main()
