"""
日志服务

该模块提供日志服务功能，负责配置和管理系统日志。
"""

import logging
import logging.handlers
import os
import sys
from typing import Dict, Any, Optional


class LoggingService:
    """日志服务，负责配置和管理系统日志"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化日志服务
        
        Args:
            config: 日志配置
        """
        self.config = config or {}
        self.log_level = self._get_log_level()
        self.log_format = self.config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        self.log_dir = self.config.get("directory", "logs")
        self.log_file = self.config.get("file", "airhunter.log")
        self.max_bytes = self.config.get("max_bytes", 10 * 1024 * 1024)  # 10MB
        self.backup_count = self.config.get("backup_count", 5)
        self.console_enabled = self.config.get("console_enabled", True)
        self.file_enabled = self.config.get("file_enabled", True)
        self.root_logger = logging.getLogger()
    
    def setup(self) -> None:
        """设置日志系统"""
        # 清除现有的处理器
        for handler in self.root_logger.handlers[:]:
            self.root_logger.removeHandler(handler)
        
        # 设置日志级别
        self.root_logger.setLevel(self.log_level)
        
        # 创建格式化器
        formatter = logging.Formatter(self.log_format)
        
        # 添加控制台处理器
        if self.console_enabled:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            self.root_logger.addHandler(console_handler)
        
        # 添加文件处理器
        if self.file_enabled:
            # 确保日志目录存在
            os.makedirs(self.log_dir, exist_ok=True)
            
            # 创建文件处理器
            file_path = os.path.join(self.log_dir, self.log_file)
            file_handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=self.max_bytes,
                backupCount=self.backup_count
            )
            file_handler.setFormatter(formatter)
            self.root_logger.addHandler(file_handler)
        
        logging.info("日志系统已设置")
    
    def _get_log_level(self) -> int:
        """
        获取日志级别
        
        Returns:
            日志级别
        """
        level_str = self.config.get("level", "INFO").upper()
        level_map = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL
        }
        return level_map.get(level_str, logging.INFO)
    
    def set_level(self, level: str) -> None:
        """
        设置日志级别
        
        Args:
            level: 日志级别，可以是 "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"
        """
        level_str = level.upper()
        level_map = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL
        }
        
        if level_str in level_map:
            self.log_level = level_map[level_str]
            self.root_logger.setLevel(self.log_level)
            logging.info(f"日志级别已设置为 {level_str}")
        else:
            logging.warning(f"未知的日志级别: {level}")
    
    def get_level(self) -> str:
        """
        获取当前日志级别
        
        Returns:
            日志级别字符串
        """
        level_map = {
            logging.DEBUG: "DEBUG",
            logging.INFO: "INFO",
            logging.WARNING: "WARNING",
            logging.ERROR: "ERROR",
            logging.CRITICAL: "CRITICAL"
        }
        return level_map.get(self.log_level, "UNKNOWN")
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取命名日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            日志器实例
        """
        return logging.getLogger(name)
    
    def set_module_level(self, module_name: str, level: str) -> None:
        """
        设置模块的日志级别
        
        Args:
            module_name: 模块名称
            level: 日志级别，可以是 "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"
        """
        level_str = level.upper()
        level_map = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL
        }
        
        if level_str in level_map:
            logger = logging.getLogger(module_name)
            logger.setLevel(level_map[level_str])
            logging.info(f"模块 '{module_name}' 的日志级别已设置为 {level_str}")
        else:
            logging.warning(f"未知的日志级别: {level}")
    
    def get_module_level(self, module_name: str) -> str:
        """
        获取模块的当前日志级别
        
        Args:
            module_name: 模块名称
            
        Returns:
            日志级别字符串
        """
        logger = logging.getLogger(module_name)
        level_map = {
            logging.DEBUG: "DEBUG",
            logging.INFO: "INFO",
            logging.WARNING: "WARNING",
            logging.ERROR: "ERROR",
            logging.CRITICAL: "CRITICAL"
        }
        return level_map.get(logger.level, "UNKNOWN")
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取当前日志配置
        
        Returns:
            日志配置字典
        """
        return {
            "level": self.get_level(),
            "format": self.log_format,
            "directory": self.log_dir,
            "file": self.log_file,
            "max_bytes": self.max_bytes,
            "backup_count": self.backup_count,
            "console_enabled": self.console_enabled,
            "file_enabled": self.file_enabled
        }