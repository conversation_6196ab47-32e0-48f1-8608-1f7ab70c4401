"""
代理池

该模块负责管理代理池和代理的生命周期。
"""

import logging
import threading
import time
import json
import os
from typing import Dict, List, Any, Optional, Set, Tuple

from proxy.models.proxy import Proxy


class ProxyPool:
    """代理池，负责管理代理池和代理的生命周期"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化代理池
        
        Args:
            config: 配置字典，包含代理池的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        self._proxies: Dict[str, Proxy] = {}  # 代理字典，键为代理 URL
        
        # 配置参数
        self.max_size = config.get('max_size', 1000)
        self.max_age = config.get('max_age', 3600)  # 代理最大年龄（秒）
        self.max_fail_count = config.get('max_fail_count', 3)  # 最大失败次数
        self.persistence_file = config.get('persistence_file', 'proxies.json')
        self.persistence_enabled = config.get('persistence_enabled', True)
        
        # 加载持久化的代理
        if self.persistence_enabled:
            self._load_proxies()
    
    def add_proxy(self, proxy: Proxy) -> bool:
        """
        添加代理到池中
        
        Args:
            proxy: 代理对象
            
        Returns:
            如果成功添加则返回True，否则返回False
        """
        with self._lock:
            # 检查池大小
            if len(self._proxies) >= self.max_size:
                self.logger.warning(f"代理池已满 ({len(self._proxies)}/{self.max_size})，无法添加代理")
                return False
            
            # 检查代理是否已存在
            proxy_key = proxy.url
            if proxy_key in self._proxies:
                # 更新现有代理
                existing_proxy = self._proxies[proxy_key]
                existing_proxy.is_valid = proxy.is_valid
                existing_proxy.last_checked = proxy.last_checked
                
                if proxy.response_times:
                    existing_proxy.response_times = proxy.response_times
                
                if proxy.country and not existing_proxy.country:
                    existing_proxy.country = proxy.country
                
                if proxy.city and not existing_proxy.city:
                    existing_proxy.city = proxy.city
                
                if proxy.anonymity and not existing_proxy.anonymity:
                    existing_proxy.anonymity = proxy.anonymity
                
                self.logger.debug(f"更新代理: {proxy.host}:{proxy.port}")
                return True
            
            # 添加新代理
            self._proxies[proxy_key] = proxy
            self.logger.debug(f"添加代理: {proxy.host}:{proxy.port}")
            
            # 持久化代理
            if self.persistence_enabled:
                self._save_proxies()
            
            return True
    
    def remove_proxy(self, proxy: Proxy) -> bool:
        """
        从池中移除代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            如果成功移除则返回True，否则返回False
        """
        with self._lock:
            proxy_key = proxy.url
            if proxy_key not in self._proxies:
                self.logger.warning(f"代理 {proxy.host}:{proxy.port} 不在池中")
                return False
            
            # 移除代理
            del self._proxies[proxy_key]
            self.logger.debug(f"移除代理: {proxy.host}:{proxy.port}")
            
            # 持久化代理
            if self.persistence_enabled:
                self._save_proxies()
            
            return True
    
    def get_proxy(self, protocol: str = None, country: str = None, anonymity: str = None) -> Optional[Proxy]:
        """
        获取代理
        
        Args:
            protocol: 代理协议，如 'http', 'https', 'socks4', 'socks5'
            country: 代理国家/地区
            anonymity: 代理匿名级别，如 'transparent', 'anonymous', 'elite'
            
        Returns:
            代理对象，如果没有可用的代理则返回None
        """
        with self._lock:
            # 过滤代理
            candidates = []
            for proxy in self._proxies.values():
                if not proxy.is_valid:
                    continue
                
                if protocol and proxy.protocol != protocol:
                    continue
                
                if country and proxy.country and proxy.country.lower() != country.lower():
                    continue
                
                if anonymity and proxy.anonymity and proxy.anonymity != anonymity:
                    continue
                
                candidates.append(proxy)
            
            if not candidates:
                self.logger.warning(f"没有找到符合条件的代理 (protocol={protocol}, country={country}, anonymity={anonymity})")
                return None
            
            # 按成功率和响应时间排序
            candidates.sort(key=lambda p: (-p.success_rate, p.average_response_time or float('inf')))
            
            # 返回最佳代理
            best_proxy = candidates[0]
            
            # 更新代理使用信息
            best_proxy.last_used = time.time()
            
            self.logger.debug(f"获取代理: {best_proxy.host}:{best_proxy.port}")
            return best_proxy
    
    def get_proxies(self, count: int, protocol: str = None, country: str = None, anonymity: str = None) -> List[Proxy]:
        """
        获取多个代理
        
        Args:
            count: 要获取的代理数量
            protocol: 代理协议，如 'http', 'https', 'socks4', 'socks5'
            country: 代理国家/地区
            anonymity: 代理匿名级别，如 'transparent', 'anonymous', 'elite'
            
        Returns:
            代理对象列表
        """
        with self._lock:
            # 过滤代理
            candidates = []
            for proxy in self._proxies.values():
                if not proxy.is_valid:
                    continue
                
                if protocol and proxy.protocol != protocol:
                    continue
                
                if country and proxy.country and proxy.country.lower() != country.lower():
                    continue
                
                if anonymity and proxy.anonymity and proxy.anonymity != anonymity:
                    continue
                
                candidates.append(proxy)
            
            if not candidates:
                self.logger.warning(f"没有找到符合条件的代理 (protocol={protocol}, country={country}, anonymity={anonymity})")
                return []
            
            # 按成功率和响应时间排序
            candidates.sort(key=lambda p: (-p.success_rate, p.average_response_time or float('inf')))
            
            # 限制数量
            result = candidates[:count]
            
            # 更新代理使用信息
            for proxy in result:
                proxy.last_used = time.time()
            
            self.logger.debug(f"获取 {len(result)} 个代理")
            return result
    
    def get_all_proxies(self) -> List[Proxy]:
        """
        获取所有代理
        
        Returns:
            代理对象列表
        """
        with self._lock:
            return list(self._proxies.values())
    
    def get_valid_proxies(self) -> List[Proxy]:
        """
        获取所有有效代理
        
        Returns:
            有效代理对象列表
        """
        with self._lock:
            return [proxy for proxy in self._proxies.values() if proxy.is_valid]
    
    def update_proxy_status(self, proxy: Proxy, success: bool, response_time: float = None) -> None:
        """
        更新代理状态
        
        Args:
            proxy: 代理对象
            success: 是否成功使用
            response_time: 响应时间（秒）
        """
        with self._lock:
            proxy_key = proxy.url
            if proxy_key not in self._proxies:
                self.logger.warning(f"代理 {proxy.host}:{proxy.port} 不在池中")
                return
            
            stored_proxy = self._proxies[proxy_key]
            
            # 更新成功/失败计数
            if success:
                stored_proxy.success_count += 1
                if response_time:
                    stored_proxy.response_times.append(response_time)
                    # 限制响应时间历史大小
                    if len(stored_proxy.response_times) > 10:
                        stored_proxy.response_times = stored_proxy.response_times[-10:]
            else:
                stored_proxy.fail_count += 1
            
            # 检查是否需要标记为无效
            if stored_proxy.fail_count >= self.max_fail_count:
                stored_proxy.is_valid = False
                self.logger.info(f"代理 {proxy.host}:{proxy.port} 失败次数过多，标记为无效")
            
            # 持久化代理
            if self.persistence_enabled:
                self._save_proxies()
    
    def cleanup_expired_proxies(self) -> int:
        """
        清理过期代理
        
        Returns:
            清理的代理数量
        """
        with self._lock:
            now = time.time()
            to_remove = []
            
            for proxy_key, proxy in self._proxies.items():
                if now - proxy.last_checked > self.max_age:
                    to_remove.append(proxy_key)
            
            # 移除过期代理
            for proxy_key in to_remove:
                del self._proxies[proxy_key]
            
            count = len(to_remove)
            if count > 0:
                self.logger.info(f"清理了 {count} 个过期代理")
                
                # 持久化代理
                if self.persistence_enabled:
                    self._save_proxies()
            
            return count
    
    def cleanup_failed_proxies(self) -> int:
        """
        清理失败次数过多的代理
        
        Returns:
            清理的代理数量
        """
        with self._lock:
            to_remove = []
            
            for proxy_key, proxy in self._proxies.items():
                if not proxy.is_valid and proxy.fail_count >= self.max_fail_count:
                    to_remove.append(proxy_key)
            
            # 移除失败代理
            for proxy_key in to_remove:
                del self._proxies[proxy_key]
            
            count = len(to_remove)
            if count > 0:
                self.logger.info(f"清理了 {count} 个失败代理")
                
                # 持久化代理
                if self.persistence_enabled:
                    self._save_proxies()
            
            return count
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取代理池状态
        
        Returns:
            代理池状态字典
        """
        with self._lock:
            total = len(self._proxies)
            valid = sum(1 for proxy in self._proxies.values() if proxy.is_valid)
            
            # 统计协议
            protocols = {}
            for proxy in self._proxies.values():
                if proxy.is_valid:
                    protocols[proxy.protocol] = protocols.get(proxy.protocol, 0) + 1
            
            # 统计国家
            countries = {}
            for proxy in self._proxies.values():
                if proxy.is_valid and proxy.country:
                    countries[proxy.country] = countries.get(proxy.country, 0) + 1
            
            # 统计匿名级别
            anonymity = {}
            for proxy in self._proxies.values():
                if proxy.is_valid and proxy.anonymity:
                    anonymity[proxy.anonymity] = anonymity.get(proxy.anonymity, 0) + 1
            
            return {
                "total": total,
                "valid": valid,
                "protocols": protocols,
                "countries": countries,
                "anonymity": anonymity
            }
    
    def _save_proxies(self) -> None:
        """保存代理到文件"""
        try:
            # 转换为字典列表
            proxy_list = [proxy.to_dict() for proxy in self._proxies.values()]
            
            # 保存到文件
            with open(self.persistence_file, 'w') as f:
                json.dump(proxy_list, f)
            
            self.logger.debug(f"已保存 {len(proxy_list)} 个代理到文件")
        
        except Exception as e:
            self.logger.error(f"保存代理到文件时出错: {str(e)}")
    
    def _load_proxies(self) -> None:
        """从文件加载代理"""
        if not os.path.exists(self.persistence_file):
            self.logger.info(f"代理文件 {self.persistence_file} 不存在")
            return
        
        try:
            # 从文件加载
            with open(self.persistence_file, 'r') as f:
                proxy_list = json.load(f)
            
            # 转换为代理对象
            for proxy_dict in proxy_list:
                try:
                    proxy = Proxy.from_dict(proxy_dict)
                    self._proxies[proxy.url] = proxy
                except Exception as e:
                    self.logger.error(f"解析代理时出错: {str(e)}")
            
            self.logger.info(f"已从文件加载 {len(self._proxies)} 个代理")
        
        except Exception as e:
            self.logger.error(f"从文件加载代理时出错: {str(e)}")
    
    def clear(self) -> None:
        """清空代理池"""
        with self._lock:
            self._proxies.clear()
            
            # 持久化代理
            if self.persistence_enabled:
                self._save_proxies()
            
            self.logger.info("已清空代理池")