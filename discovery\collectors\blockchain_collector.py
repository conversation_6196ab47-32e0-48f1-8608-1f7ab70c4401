"""
区块链收集器

该模块实现了从区块链收集项目信息的功能。
"""

import logging
import requests
import time
import random
import re
from typing import Dict, List, Any, Optional

from discovery.collectors.base_collector import BaseCollector


class BlockchainCollector(BaseCollector):
    """区块链收集器，从区块链收集项目信息"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化区块链收集器
        
        Args:
            config: 配置字典，包含区块链收集器的配置信息
        """
        super().__init__(config)
        self.timeout = config.get('timeout', 10)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
        self.etherscan_api_key = config.get('etherscan_api_key', '')
        self.bscscan_api_key = config.get('bscscan_api_key', '')
        self.polygonscan_api_key = config.get('polygonscan_api_key', '')
    
    def collect(self, project: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        从区块链收集项目信息
        
        Args:
            project: 项目字典
            
        Returns:
            收集到的项目信息字典，如果无法收集则返回None
        """
        # 检查项目是否有区块链平台
        if 'blockchain' not in project or not project['blockchain']:
            return None
        
        blockchain = project['blockchain']
        
        # 检查项目是否有代币信息
        token_info = project.get('token_info', {})
        contract_address = token_info.get('contract_address') if token_info else None
        
        # 如果没有合约地址，尝试从项目URL中提取
        if not contract_address:
            contract_address = self._extract_contract_address_from_url(project.get('url', ''))
        
        # 如果仍然没有合约地址，无法收集区块链信息
        if not contract_address:
            return None
        
        # 收集区块链信息
        if blockchain == 'ethereum':
            return self._collect_ethereum_info(contract_address)
        elif blockchain == 'binance':
            return self._collect_binance_info(contract_address)
        elif blockchain == 'polygon':
            return self._collect_polygon_info(contract_address)
        elif blockchain == 'solana':
            return self._collect_solana_info(contract_address)
        elif blockchain == 'avalanche':
            return self._collect_avalanche_info(contract_address)
        elif blockchain == 'arbitrum':
            return self._collect_arbitrum_info(contract_address)
        elif blockchain == 'optimism':
            return self._collect_optimism_info(contract_address)
        elif blockchain == 'base':
            return self._collect_base_info(contract_address)
        else:
            return None
    
    def _extract_contract_address_from_url(self, url: str) -> Optional[str]:
        """
        从URL中提取合约地址
        
        Args:
            url: 项目URL
            
        Returns:
            合约地址，如果无法提取则返回None
        """
        # 以太坊、币安智能链、Polygon等区块链浏览器的URL模式
        patterns = [
            r'(?:etherscan\.io|bscscan\.com|polygonscan\.com|arbiscan\.io|optimistic\.etherscan\.io|basescan\.org)/(?:token|address)/([a-zA-Z0-9]{40,})',
            r'(?:solscan\.io|explorer\.solana\.com)/(?:token|address)/([a-zA-Z0-9]{32,})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def _collect_ethereum_info(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        收集以太坊信息
        
        Args:
            contract_address: 合约地址
            
        Returns:
            以太坊信息字典，如果无法收集则返回None
        """
        try:
            # 如果没有API密钥，无法收集详细信息
            if not self.etherscan_api_key:
                return {
                    'token_info': {
                        'contract_address': contract_address,
                        'blockchain': 'ethereum'
                    }
                }
            
            # 构建API URL
            api_url = f"https://api.etherscan.io/api?module=token&action=tokeninfo&contractaddress={contract_address}&apikey={self.etherscan_api_key}"
            
            # 发送请求
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析响应
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return {
                    'token_info': {
                        'contract_address': contract_address,
                        'blockchain': 'ethereum'
                    }
                }
            
            token_data = data['result'][0] if isinstance(data['result'], list) else data['result']
            
            # 创建代币信息
            token_info = {
                'name': token_data.get('name'),
                'symbol': token_data.get('symbol'),
                'contract_address': contract_address,
                'blockchain': 'ethereum',
                'total_supply': float(token_data.get('totalSupply', 0)) / (10 ** int(token_data.get('divisor', 18)))
            }
            
            # 创建项目信息
            project_info = {
                'token_info': token_info,
                'social_channels': []
            }
            
            # 添加社交媒体链接
            if 'website' in token_data and token_data['website']:
                project_info['url'] = token_data['website']
            
            if 'twitter' in token_data and token_data['twitter']:
                project_info['social_channels'].append({
                    'platform': 'twitter',
                    'url': token_data['twitter'],
                    'followers': None
                })
            
            if 'telegram' in token_data and token_data['telegram']:
                project_info['social_channels'].append({
                    'platform': 'telegram',
                    'url': token_data['telegram'],
                    'followers': None
                })
            
            if 'discord' in token_data and token_data['discord']:
                project_info['social_channels'].append({
                    'platform': 'discord',
                    'url': token_data['discord'],
                    'followers': None
                })
            
            return project_info
        
        except Exception as e:
            self.logger.error(f"收集以太坊信息时出错: {str(e)}")
            return {
                'token_info': {
                    'contract_address': contract_address,
                    'blockchain': 'ethereum'
                }
            }
    
    def _collect_binance_info(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        收集币安智能链信息
        
        Args:
            contract_address: 合约地址
            
        Returns:
            币安智能链信息字典，如果无法收集则返回None
        """
        try:
            # 如果没有API密钥，无法收集详细信息
            if not self.bscscan_api_key:
                return {
                    'token_info': {
                        'contract_address': contract_address,
                        'blockchain': 'binance'
                    }
                }
            
            # 构建API URL
            api_url = f"https://api.bscscan.com/api?module=token&action=tokeninfo&contractaddress={contract_address}&apikey={self.bscscan_api_key}"
            
            # 发送请求
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析响应
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return {
                    'token_info': {
                        'contract_address': contract_address,
                        'blockchain': 'binance'
                    }
                }
            
            token_data = data['result'][0] if isinstance(data['result'], list) else data['result']
            
            # 创建代币信息
            token_info = {
                'name': token_data.get('name'),
                'symbol': token_data.get('symbol'),
                'contract_address': contract_address,
                'blockchain': 'binance',
                'total_supply': float(token_data.get('totalSupply', 0)) / (10 ** int(token_data.get('divisor', 18)))
            }
            
            # 创建项目信息
            project_info = {
                'token_info': token_info,
                'social_channels': []
            }
            
            # 添加社交媒体链接
            if 'website' in token_data and token_data['website']:
                project_info['url'] = token_data['website']
            
            if 'twitter' in token_data and token_data['twitter']:
                project_info['social_channels'].append({
                    'platform': 'twitter',
                    'url': token_data['twitter'],
                    'followers': None
                })
            
            if 'telegram' in token_data and token_data['telegram']:
                project_info['social_channels'].append({
                    'platform': 'telegram',
                    'url': token_data['telegram'],
                    'followers': None
                })
            
            if 'discord' in token_data and token_data['discord']:
                project_info['social_channels'].append({
                    'platform': 'discord',
                    'url': token_data['discord'],
                    'followers': None
                })
            
            return project_info
        
        except Exception as e:
            self.logger.error(f"收集币安智能链信息时出错: {str(e)}")
            return {
                'token_info': {
                    'contract_address': contract_address,
                    'blockchain': 'binance'
                }
            }
    
    def _collect_polygon_info(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        收集 Polygon 信息
        
        Args:
            contract_address: 合约地址
            
        Returns:
            Polygon 信息字典，如果无法收集则返回None
        """
        try:
            # 如果没有API密钥，无法收集详细信息
            if not self.polygonscan_api_key:
                return {
                    'token_info': {
                        'contract_address': contract_address,
                        'blockchain': 'polygon'
                    }
                }
            
            # 构建API URL
            api_url = f"https://api.polygonscan.com/api?module=token&action=tokeninfo&contractaddress={contract_address}&apikey={self.polygonscan_api_key}"
            
            # 发送请求
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析响应
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return {
                    'token_info': {
                        'contract_address': contract_address,
                        'blockchain': 'polygon'
                    }
                }
            
            token_data = data['result'][0] if isinstance(data['result'], list) else data['result']
            
            # 创建代币信息
            token_info = {
                'name': token_data.get('name'),
                'symbol': token_data.get('symbol'),
                'contract_address': contract_address,
                'blockchain': 'polygon',
                'total_supply': float(token_data.get('totalSupply', 0)) / (10 ** int(token_data.get('divisor', 18)))
            }
            
            # 创建项目信息
            project_info = {
                'token_info': token_info,
                'social_channels': []
            }
            
            # 添加社交媒体链接
            if 'website' in token_data and token_data['website']:
                project_info['url'] = token_data['website']
            
            if 'twitter' in token_data and token_data['twitter']:
                project_info['social_channels'].append({
                    'platform': 'twitter',
                    'url': token_data['twitter'],
                    'followers': None
                })
            
            if 'telegram' in token_data and token_data['telegram']:
                project_info['social_channels'].append({
                    'platform': 'telegram',
                    'url': token_data['telegram'],
                    'followers': None
                })
            
            if 'discord' in token_data and token_data['discord']:
                project_info['social_channels'].append({
                    'platform': 'discord',
                    'url': token_data['discord'],
                    'followers': None
                })
            
            return project_info
        
        except Exception as e:
            self.logger.error(f"收集 Polygon 信息时出错: {str(e)}")
            return {
                'token_info': {
                    'contract_address': contract_address,
                    'blockchain': 'polygon'
                }
            }
    
    def _collect_solana_info(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        收集 Solana 信息
        
        Args:
            contract_address: 合约地址
            
        Returns:
            Solana 信息字典，如果无法收集则返回None
        """
        # Solana 没有官方的 API 密钥，使用公共 API
        try:
            # 构建API URL
            api_url = f"https://public-api.solscan.io/token/meta?tokenAddress={contract_address}"
            
            # 随机选择一个 User-Agent
            headers = {
                'User-Agent': random.choice(self.user_agents)
            }
            
            # 发送请求
            response = requests.get(api_url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析响应
            token_data = response.json()
            
            if not token_data:
                return {
                    'token_info': {
                        'contract_address': contract_address,
                        'blockchain': 'solana'
                    }
                }
            
            # 创建代币信息
            token_info = {
                'name': token_data.get('name'),
                'symbol': token_data.get('symbol'),
                'contract_address': contract_address,
                'blockchain': 'solana',
                'total_supply': token_data.get('totalSupply')
            }
            
            # 创建项目信息
            project_info = {
                'token_info': token_info,
                'social_channels': []
            }
            
            # 添加社交媒体链接
            if 'website' in token_data and token_data['website']:
                project_info['url'] = token_data['website']
            
            if 'twitter' in token_data and token_data['twitter']:
                project_info['social_channels'].append({
                    'platform': 'twitter',
                    'url': token_data['twitter'],
                    'followers': None
                })
            
            if 'telegram' in token_data and token_data['telegram']:
                project_info['social_channels'].append({
                    'platform': 'telegram',
                    'url': token_data['telegram'],
                    'followers': None
                })
            
            if 'discord' in token_data and token_data['discord']:
                project_info['social_channels'].append({
                    'platform': 'discord',
                    'url': token_data['discord'],
                    'followers': None
                })
            
            return project_info
        
        except Exception as e:
            self.logger.error(f"收集 Solana 信息时出错: {str(e)}")
            return {
                'token_info': {
                    'contract_address': contract_address,
                    'blockchain': 'solana'
                }
            }
    
    def _collect_avalanche_info(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        收集 Avalanche 信息
        
        Args:
            contract_address: 合约地址
            
        Returns:
            Avalanche 信息字典，如果无法收集则返回None
        """
        # 简化实现，仅返回基本信息
        return {
            'token_info': {
                'contract_address': contract_address,
                'blockchain': 'avalanche'
            }
        }
    
    def _collect_arbitrum_info(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        收集 Arbitrum 信息
        
        Args:
            contract_address: 合约地址
            
        Returns:
            Arbitrum 信息字典，如果无法收集则返回None
        """
        # 简化实现，仅返回基本信息
        return {
            'token_info': {
                'contract_address': contract_address,
                'blockchain': 'arbitrum'
            }
        }
    
    def _collect_optimism_info(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        收集 Optimism 信息
        
        Args:
            contract_address: 合约地址
            
        Returns:
            Optimism 信息字典，如果无法收集则返回None
        """
        # 简化实现，仅返回基本信息
        return {
            'token_info': {
                'contract_address': contract_address,
                'blockchain': 'optimism'
            }
        }
    
    def _collect_base_info(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """
        收集 Base 信息
        
        Args:
            contract_address: 合约地址
            
        Returns:
            Base 信息字典，如果无法收集则返回None
        """
        # 简化实现，仅返回基本信息
        return {
            'token_info': {
                'contract_address': contract_address,
                'blockchain': 'base'
            }
        }