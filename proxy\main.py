"""
代理智能体主模块

该模块是代理智能体的入口点。
"""

import logging
import argparse
import json
import os
import sys
import time
from typing import Dict, Any

from proxy.proxy_agent import ProxyAgent
from proxy.api.proxy_api import ProxyAPI
from proxy.config.default_config import DEFAULT_CONFIG


def setup_logging(log_level: str = 'INFO') -> None:
    """
    设置日志
    
    Args:
        log_level: 日志级别
    """
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    
    # 设置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 设置日志级别
    level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    level = level_map.get(log_level.upper(), logging.INFO)
    
    # 配置日志
    logging.basicConfig(
        level=level,
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/proxy_agent.log')
        ]
    )


def load_config(config_file: str = None) -> Dict[str, Any]:
    """
    加载配置
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        配置字典
    """
    config = DEFAULT_CONFIG.copy()
    
    if config_file and os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                user_config = json.load(f)
            
            # 合并配置
            _merge_configs(config, user_config)
            
            logging.info(f"已加载配置文件: {config_file}")
        
        except Exception as e:
            logging.error(f"加载配置文件时出错: {str(e)}")
    
    return config


def _merge_configs(base_config: Dict[str, Any], user_config: Dict[str, Any]) -> None:
    """
    合并配置
    
    Args:
        base_config: 基础配置
        user_config: 用户配置
    """
    for key, value in user_config.items():
        if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
            _merge_configs(base_config[key], value)
        else:
            base_config[key] = value


def main() -> None:
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='代理智能体')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--log-level', type=str, default='INFO', help='日志级别')
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 加载配置
    config = load_config(args.config)
    
    try:
        # 创建代理智能体
        proxy_agent = ProxyAgent(config)
        
        # 创建代理 API
        proxy_api = ProxyAPI(proxy_agent)
        
        # 启动代理智能体
        proxy_agent.start()
        
        logging.info("代理智能体已启动")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logging.info("接收到中断信号，正在停止...")
        
        # 停止代理智能体
        proxy_agent.stop()
        
        logging.info("代理智能体已停止")
    
    except Exception as e:
        logging.error(f"运行代理智能体时出错: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()