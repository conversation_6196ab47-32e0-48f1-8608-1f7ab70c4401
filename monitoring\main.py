"""
Monitoring Agent Main Module

This is the main entry point for the Monitoring Agent.
"""

import os
import sys
import json
import logging
import argparse
import time
import threading
from typing import Dict, Any, Optional

# Add parent directory to path to allow imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from monitoring.monitoring_agent import MonitoringAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("data/logs/monitoring.log")
    ]
)
logger = logging.getLogger("MonitoringMain")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Monitoring Agent")
    parser.add_argument("--config", type=str, default="config/monitoring_config.json",
                        help="Path to configuration file")
    parser.add_argument("--action", type=str, 
                        choices=["start", "stop", "status", "add_project", "remove_project", "check", "list_projects"],
                        default="start", help="Action to perform")
    parser.add_argument("--project_id", type=str, help="Project ID for project-specific actions")
    parser.add_argument("--project_file", type=str, help="JSON file containing project data")
    parser.add_argument("--continuous", action="store_true", help="Run in continuous monitoring mode")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from file."""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        else:
            logger.warning(f"Config file not found: {config_path}")
            return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def load_project_data(project_file: str) -> Dict[str, Any]:
    """Load project data from file."""
    try:
        if os.path.exists(project_file):
            with open(project_file, 'r') as f:
                return json.load(f)
        else:
            logger.error(f"Project file not found: {project_file}")
            return {}
    except Exception as e:
        logger.error(f"Error loading project data: {e}")
        return {}

def continuous_monitoring(agent: MonitoringAgent, interval_minutes: int):
    """Run continuous monitoring in a separate thread."""
    stop_event = threading.Event()
    
    def monitor_thread():
        try:
            agent.run_continuous_monitoring(stop_event)
        except Exception as e:
            logger.error(f"Error in monitoring thread: {e}")
    
    thread = threading.Thread(target=monitor_thread)
    thread.daemon = True
    thread.start()
    
    logger.info(f"Continuous monitoring started (interval: {interval_minutes} minutes)")
    logger.info("Press Ctrl+C to stop")
    
    try:
        while thread.is_alive():
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping continuous monitoring...")
        stop_event.set()
        thread.join(timeout=60)
        logger.info("Continuous monitoring stopped")

def main():
    """Main entry point for the Monitoring Agent."""
    # Parse arguments
    args = parse_arguments()
    
    # Ensure logs directory exists
    os.makedirs("data/logs", exist_ok=True)
    
    # Load configuration
    config = load_config(args.config)
    
    # Create agent
    agent = MonitoringAgent(args.config if os.path.exists(args.config) else None)
    
    # Perform requested action
    if args.action == "start":
        logger.info("Starting Monitoring Agent")
        success = agent.start()
        logger.info(f"Agent started: {success}")
        
        if args.continuous and success:
            interval_minutes = config.get("check_interval_minutes", 15)
            continuous_monitoring(agent, interval_minutes)
    
    elif args.action == "stop":
        logger.info("Stopping Monitoring Agent")
        success = agent.stop()
        logger.info(f"Agent stopped: {success}")
    
    elif args.action == "status":
        status = agent.status()
        logger.info(f"Agent status: {status}")
        print(json.dumps(status, indent=2))
    
    elif args.action == "add_project":
        if not args.project_file:
            logger.error("Project file is required for add_project action")
            return
        
        project_data = load_project_data(args.project_file)
        if not project_data:
            return
        
        logger.info(f"Adding project: {project_data.get('name', 'Unknown')}")
        success = agent.add_project(project_data)
        logger.info(f"Project added: {success}")
    
    elif args.action == "remove_project":
        if not args.project_id:
            logger.error("Project ID is required for remove_project action")
            return
        
        logger.info(f"Removing project: {args.project_id}")
        success = agent.remove_project(args.project_id)
        logger.info(f"Project removed: {success}")
    
    elif args.action == "check":
        agent.start()
        
        if args.project_id:
            logger.info(f"Checking project: {args.project_id}")
            updates = agent.check_for_updates(args.project_id)
        else:
            logger.info("Checking all projects")
            updates = agent.check_for_updates()
        
        logger.info(f"Found {len(updates)} updates")
        for update in updates:
            print(f"- {update['title']}")
            print(f"  Source: {update['source']}")
            print(f"  Time: {update['timestamp']}")
            print(f"  Content: {update['content'][:100]}...")
            print()
    
    elif args.action == "list_projects":
        # This would need to be implemented in the agent
        logger.info("Listing monitored projects")
        print("Project listing not implemented yet")

if __name__ == "__main__":
    main()