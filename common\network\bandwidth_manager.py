"""
Bandwidth manager for network operations

This module provides bandwidth monitoring and management functionality.
"""

import time
import threading
import logging
from typing import Dict, List, Optional
from collections import deque
from dataclasses import dataclass

@dataclass
class BandwidthUsage:
    """Bandwidth usage data class"""
    bytes_in: int = 0
    bytes_out: int = 0
    timestamp: float = 0.0

class BandwidthManager:
    """Manager for bandwidth monitoring and control"""
    
    def __init__(
        self,
        max_bandwidth: Optional[int] = None,
        window_size: int = 60
    ):
        """
        Initialize bandwidth manager
        
        Args:
            max_bandwidth: Maximum bandwidth in bytes per second
            window_size: Time window for statistics in seconds
        """
        self.max_bandwidth = max_bandwidth
        self.window_size = window_size
        
        self._usage_history = deque(maxlen=window_size)
        self._current_usage = BandwidthUsage(timestamp=time.time())
        self._lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # Priority queues for different traffic types
        self._priority_quotas = {
            'high': 0.5,    # 50% of bandwidth
            'medium': 0.3,  # 30% of bandwidth
            'low': 0.2      # 20% of bandwidth
        }
        
        # Start monitoring thread
        self._monitor_thread = threading.Thread(target=self._monitor_bandwidth)
        self._monitor_thread.daemon = True
        self._monitor_thread.start()
    
    def record_usage(
        self,
        bytes_in: int = 0,
        bytes_out: int = 0,
        priority: str = 'medium'
    ) -> None:
        """
        Record bandwidth usage
        
        Args:
            bytes_in: Incoming bytes
            bytes_out: Outgoing bytes
            priority: Traffic priority
        """
        with self._lock:
            current_time = time.time()
            
            # Check if we need to start a new time slice
            if current_time - self._current_usage.timestamp >= 1.0:
                self._usage_history.append(self._current_usage)
                self._current_usage = BandwidthUsage(timestamp=current_time)
            
            # Update current usage
            self._current_usage.bytes_in += bytes_in
            self._current_usage.bytes_out += bytes_out
            
            # Check bandwidth limits if set
            if self.max_bandwidth:
                quota = self.max_bandwidth * self._priority_quotas.get(priority, 0.2)
                current_total = self._current_usage.bytes_in + self._current_usage.bytes_out
                
                if current_total > quota:
                    sleep_time = (current_total - quota) / quota
                    time.sleep(sleep_time)
    
    def get_current_usage(self) -> Dict[str, int]:
        """
        Get current bandwidth usage
        
        Returns:
            Dict[str, int]: Current usage statistics
        """
        with self._lock:
            return {
                'bytes_in': self._current_usage.bytes_in,
                'bytes_out': self._current_usage.bytes_out,
                'timestamp': self._current_usage.timestamp
            }
    
    def get_average_usage(self, window: int = None) -> Dict[str, float]:
        """
        Get average bandwidth usage
        
        Args:
            window: Time window in seconds
            
        Returns:
            Dict[str, float]: Average usage statistics
        """
        if window is None:
            window = self.window_size
            
        with self._lock:
            if not self._usage_history:
                return {'bytes_in': 0.0, 'bytes_out': 0.0}
            
            # Calculate averages
            history = list(self._usage_history)[-window:]
            if not history:
                return {'bytes_in': 0.0, 'bytes_out': 0.0}
                
            avg_in = sum(usage.bytes_in for usage in history) / len(history)
            avg_out = sum(usage.bytes_out for usage in history) / len(history)
            
            return {
                'bytes_in': avg_in,
                'bytes_out': avg_out
            }
    
    def get_peak_usage(self, window: int = None) -> Dict[str, int]:
        """
        Get peak bandwidth usage
        
        Args:
            window: Time window in seconds
            
        Returns:
            Dict[str, int]: Peak usage statistics
        """
        if window is None:
            window = self.window_size
            
        with self._lock:
            if not self._usage_history:
                return {'bytes_in': 0, 'bytes_out': 0}
            
            # Calculate peaks
            history = list(self._usage_history)[-window:]
            if not history:
                return {'bytes_in': 0, 'bytes_out': 0}
                
            peak_in = max(usage.bytes_in for usage in history)
            peak_out = max(usage.bytes_out for usage in history)
            
            return {
                'bytes_in': peak_in,
                'bytes_out': peak_out
            }
    
    def set_bandwidth_limit(self, limit: Optional[int]) -> None:
        """
        Set bandwidth limit
        
        Args:
            limit: Maximum bandwidth in bytes per second
        """
        self.max_bandwidth = limit
        self.logger.info(f"Bandwidth limit set to: {limit} bytes/sec")
    
    def set_priority_quotas(self, quotas: Dict[str, float]) -> None:
        """
        Set priority bandwidth quotas
        
        Args:
            quotas: Dictionary of priority levels and their quotas
        """
        # Validate quotas sum to 1.0
        if abs(sum(quotas.values()) - 1.0) > 0.001:
            raise ValueError("Priority quotas must sum to 1.0")
            
        self._priority_quotas = quotas
        self.logger.info(f"Priority quotas updated: {quotas}")
    
    def _monitor_bandwidth(self) -> None:
        """Monitor bandwidth usage"""
        while True:
            time.sleep(1)
            try:
                current_time = time.time()
                
                with self._lock:
                    # Rotate usage statistics
                    if current_time - self._current_usage.timestamp >= 1.0:
                        self._usage_history.append(self._current_usage)
                        self._current_usage = BandwidthUsage(timestamp=current_time)
                        
                    # Log if approaching limits
                    if self.max_bandwidth:
                        current_total = (
                            self._current_usage.bytes_in +
                            self._current_usage.bytes_out
                        )
                        usage_percent = (current_total / self.max_bandwidth) * 100
                        
                        if usage_percent > 90:
                            self.logger.warning(
                                f"High bandwidth usage: {usage_percent:.1f}%"
                            )
                            
            except Exception as e:
                self.logger.error(f"Bandwidth monitoring error: {str(e)}")
    
    def get_usage_history(self) -> List[Dict[str, int]]:
        """
        Get bandwidth usage history
        
        Returns:
            List[Dict[str, int]]: Usage history
        """
        with self._lock:
            return [
                {
                    'bytes_in': usage.bytes_in,
                    'bytes_out': usage.bytes_out,
                    'timestamp': usage.timestamp
                }
                for usage in self._usage_history
            ]
    
    def reset_statistics(self) -> None:
        """Reset usage statistics"""
        with self._lock:
            self._usage_history.clear()
            self._current_usage = BandwidthUsage(timestamp=time.time())
        self.logger.info("Bandwidth statistics reset")

# Create global bandwidth manager instance
bandwidth_manager = BandwidthManager()
