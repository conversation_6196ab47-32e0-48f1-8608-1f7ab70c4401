"""
评分生成器

该模块实现了生成项目评分的功能。
"""

import logging
from typing import Dict, List, Any, Optional, Tuple

from discovery.models.project import Project


class ScoreGenerator:
    """评分生成器，负责生成项目评分"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化评分生成器
        
        Args:
            config: 配置字典，包含评分生成器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.community_weights = config.get('community_weights', {
            'social_activity': 0.3,
            'followers_count': 0.3,
            'engagement_level': 0.2,
            'growth_rate': 0.2
        })
        
        self.team_weights = config.get('team_weights', {
            'experience': 0.3,
            'background': 0.3,
            'transparency': 0.2,
            'track_record': 0.2
        })
        
        # 统计信息
        self._stats = {
            'total_generations': 0,
            'high_community_score': 0,
            'medium_community_score': 0,
            'low_community_score': 0,
            'high_team_score': 0,
            'medium_team_score': 0,
            'low_team_score': 0
        }
    
    def generate(self, project: Project, team_result: Optional[Dict[str, Any]] = None,
                social_result: Optional[Dict[str, Any]] = None, project_result: Optional[Dict[str, Any]] = None) -> Tuple[float, float]:
        """
        生成项目评分
        
        Args:
            project: 项目对象
            team_result: 团队验证结果
            social_result: 社交媒体验证结果
            project_result: 项目真实性验证结果
            
        Returns:
            社区评分和团队评分的元组 (community_score, team_score)，分数范围为0-100
        """
        self.logger.info(f"开始生成项目 {project.name} (ID: {project.id}) 的评分")
        
        try:
            # 更新统计信息
            self._stats['total_generations'] += 1
            
            # 生成社区评分
            community_score = self._generate_community_score(project, social_result)
            
            # 生成团队评分
            team_score = self._generate_team_score(project, team_result, project_result)
            
            # 更新统计信息
            if community_score >= 70:
                self._stats['high_community_score'] += 1
            elif community_score >= 40:
                self._stats['medium_community_score'] += 1
            else:
                self._stats['low_community_score'] += 1
            
            if team_score >= 70:
                self._stats['high_team_score'] += 1
            elif team_score >= 40:
                self._stats['medium_team_score'] += 1
            else:
                self._stats['low_team_score'] += 1
            
            self.logger.info(f"项目 {project.name} 评分生成完成: 社区评分 {community_score:.2f}, 团队评分 {team_score:.2f}")
            
            return community_score, team_score
        
        except Exception as e:
            self.logger.error(f"生成项目 {project.name} 评分时出错: {str(e)}")
            return 50.0, 50.0  # 出错时返回中等评分
    
    def _generate_community_score(self, project: Project, social_result: Optional[Dict[str, Any]]) -> float:
        """
        生成社区评分
        
        Args:
            project: 项目对象
            social_result: 社交媒体验证结果
            
        Returns:
            社区评分 (0-100)
        """
        # 计算社交活跃度分数
        social_activity_score = self._calculate_social_activity_score(project, social_result)
        
        # 计算粉丝数量分数
        followers_count_score = self._calculate_followers_count_score(project, social_result)
        
        # 计算参与度分数
        engagement_level_score = self._calculate_engagement_level_score(project, social_result)
        
        # 计算增长率分数
        growth_rate_score = self._calculate_growth_rate_score(project, social_result)
        
        # 计算加权社区评分
        community_score = (
            social_activity_score * self.community_weights.get('social_activity', 0.3) +
            followers_count_score * self.community_weights.get('followers_count', 0.3) +
            engagement_level_score * self.community_weights.get('engagement_level', 0.2) +
            growth_rate_score * self.community_weights.get('growth_rate', 0.2)
        )
        
        # 确保分数在0-100范围内
        return max(0, min(100, community_score))
    
    def _generate_team_score(self, project: Project, team_result: Optional[Dict[str, Any]],
                            project_result: Optional[Dict[str, Any]]) -> float:
        """
        生成团队评分
        
        Args:
            project: 项目对象
            team_result: 团队验证结果
            project_result: 项目真实性验证结果
            
        Returns:
            团队评分 (0-100)
        """
        # 计算经验分数
        experience_score = self._calculate_experience_score(project, team_result)
        
        # 计算背景分数
        background_score = self._calculate_background_score(project, team_result)
        
        # 计算透明度分数
        transparency_score = self._calculate_transparency_score(project, team_result, project_result)
        
        # 计算过往记录分数
        track_record_score = self._calculate_track_record_score(project, team_result)
        
        # 计算加权团队评分
        team_score = (
            experience_score * self.team_weights.get('experience', 0.3) +
            background_score * self.team_weights.get('background', 0.3) +
            transparency_score * self.team_weights.get('transparency', 0.2) +
            track_record_score * self.team_weights.get('track_record', 0.2)
        )
        
        # 确保分数在0-100范围内
        return max(0, min(100, team_score))
    
    def _calculate_social_activity_score(self, project: Project, social_result: Optional[Dict[str, Any]]) -> float:
        """
        计算社交活跃度分数
        
        Args:
            project: 项目对象
            social_result: 社交媒体验证结果
            
        Returns:
            社交活跃度分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 如果有社交媒体验证结果，使用其活动级别
        if social_result:
            activity_level = social_result.get('activity_level', 'unknown')
            
            if activity_level == 'high':
                score = 80.0
            elif activity_level == 'medium':
                score = 60.0
            elif activity_level == 'low':
                score = 40.0
            elif activity_level == 'very_low':
                score = 20.0
        else:
            # 根据社交媒体渠道数量调整分数
            channel_count = len(project.social_channels)
            
            if channel_count >= 4:
                score += 20.0
            elif channel_count >= 3:
                score += 10.0
            elif channel_count <= 1:
                score -= 20.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_followers_count_score(self, project: Project, social_result: Optional[Dict[str, Any]]) -> float:
        """
        计算粉丝数量分数
        
        Args:
            project: 项目对象
            social_result: 社交媒体验证结果
            
        Returns:
            粉丝数量分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 如果有社交媒体验证结果，使用其总粉丝数
        if social_result and 'total_followers' in social_result:
            total_followers = social_result.get('total_followers', 0)
        else:
            # 计算总粉丝数
            total_followers = 0
            
            for channel in project.social_channels:
                if channel.followers is not None:
                    total_followers += channel.followers
        
        # 根据总粉丝数调整分数
        if total_followers > 100000:
            score = 90.0
        elif total_followers > 50000:
            score = 80.0
        elif total_followers > 10000:
            score = 70.0
        elif total_followers > 5000:
            score = 60.0
        elif total_followers > 1000:
            score = 50.0
        elif total_followers > 500:
            score = 40.0
        elif total_followers > 100:
            score = 30.0
        else:
            score = 20.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_engagement_level_score(self, project: Project, social_result: Optional[Dict[str, Any]]) -> float:
        """
        计算参与度分数
        
        Args:
            project: 项目对象
            social_result: 社交媒体验证结果
            
        Returns:
            参与度分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 如果有社交媒体验证结果，使用其渠道结果
        if social_result and 'channel_results' in social_result:
            channel_results = social_result.get('channel_results', {})
            
            # 计算平均参与度
            engagement_scores = []
            
            for platform, result in channel_results.items():
                # 根据平台和粉丝数计算参与度
                if platform == 'twitter':
                    followers = result.get('followers', 0)
                    post_count = result.get('post_count', 0)
                    
                    if followers and post_count:
                        engagement_ratio = min(1.0, post_count / max(1, followers) * 1000)
                        engagement_scores.append(min(100, engagement_ratio * 20))
                
                elif platform == 'telegram':
                    followers = result.get('followers', 0)
                    
                    if followers:
                        engagement_scores.append(min(80, 40 + followers / 1000))
                
                elif platform == 'discord':
                    followers = result.get('followers', 0)
                    
                    if followers:
                        engagement_scores.append(min(80, 40 + followers / 500))
            
            # 计算平均参与度分数
            if engagement_scores:
                score = sum(engagement_scores) / len(engagement_scores)
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_growth_rate_score(self, project: Project, social_result: Optional[Dict[str, Any]]) -> float:
        """
        计算增长率分数
        
        Args:
            project: 项目对象
            social_result: 社交媒体验证结果
            
        Returns:
            增长率分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 目前没有足够的数据计算增长率
        # 使用项目发现时间作为替代指标
        import time
        current_time = time.time()
        discovery_time = project.discovery_time
        
        if discovery_time:
            time_diff = current_time - discovery_time
            
            if time_diff < 86400:  # 1天内
                score = 80.0  # 新项目，假设增长率高
            elif time_diff < 604800:  # 7天内
                score = 70.0
            elif time_diff < 2592000:  # 30天内
                score = 60.0
            else:
                score = 50.0  # 老项目，增长率可能较低
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_experience_score(self, project: Project, team_result: Optional[Dict[str, Any]]) -> float:
        """
        计算经验分数
        
        Args:
            project: 项目对象
            team_result: 团队验证结果
            
        Returns:
            经验分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 如果有团队验证结果，使用其平均经验年限
        if team_result:
            average_experience = team_result.get('average_experience', 0.0)
            
            if average_experience >= 5.0:
                score = 90.0
            elif average_experience >= 3.0:
                score = 80.0
            elif average_experience >= 2.0:
                score = 70.0
            elif average_experience >= 1.0:
                score = 60.0
            else:
                score = 40.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_background_score(self, project: Project, team_result: Optional[Dict[str, Any]]) -> float:
        """
        计算背景分数
        
        Args:
            project: 项目对象
            team_result: 团队验证结果
            
        Returns:
            背景分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 如果有团队验证结果，使用其团队成员信息
        if team_result:
            # 检查是否有技术和业务成员
            has_technical = team_result.get('has_technical_members', False)
            has_business = team_result.get('has_business_members', False)
            
            if has_technical and has_business:
                score += 30.0
            elif has_technical or has_business:
                score += 15.0
            
            # 检查团队规模
            team_size = team_result.get('team_size', 0)
            
            if team_size >= 5:
                score += 20.0
            elif team_size >= 3:
                score += 10.0
            
            # 检查已知诈骗者
            if team_result.get('has_known_scammers', False):
                score = 0.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_transparency_score(self, project: Project, team_result: Optional[Dict[str, Any]],
                                     project_result: Optional[Dict[str, Any]]) -> float:
        """
        计算透明度分数
        
        Args:
            project: 项目对象
            team_result: 团队验证结果
            project_result: 项目真实性验证结果
            
        Returns:
            透明度分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 根据团队验证结果调整分数
        if team_result:
            # 检查已知成员比例
            team_size = team_result.get('team_size', 0)
            known_members = team_result.get('known_members', 0)
            
            if team_size > 0:
                known_ratio = known_members / team_size
                
                if known_ratio >= 0.8:
                    score += 20.0
                elif known_ratio >= 0.5:
                    score += 10.0
                elif known_ratio < 0.2:
                    score -= 10.0
            
            # 检查团队链接
            team_links = team_result.get('team_links', [])
            
            if len(team_links) >= 3:
                score += 10.0
            elif len(team_links) >= 1:
                score += 5.0
        
        # 根据项目真实性验证结果调整分数
        if project_result:
            # 检查网站可用性
            if project_result.get('website_available', False):
                score += 10.0
            else:
                score -= 20.0
            
            # 检查必要页面
            if project_result.get('has_required_pages', False):
                score += 10.0
            
            # 检查内容质量
            content_quality = project_result.get('content_quality', 'unknown')
            
            if content_quality == 'high':
                score += 10.0
            elif content_quality == 'medium':
                score += 5.0
            elif content_quality == 'poor':
                score -= 10.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_track_record_score(self, project: Project, team_result: Optional[Dict[str, Any]]) -> float:
        """
        计算过往记录分数
        
        Args:
            project: 项目对象
            team_result: 团队验证结果
            
        Returns:
            过往记录分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 根据项目标签调整分数
        for tag in project.tags:
            if tag in ['experienced_team', 'successful_projects']:
                score += 20.0
            elif tag in ['new_team', 'no_track_record']:
                score -= 10.0
            elif tag in ['failed_projects', 'scam_history']:
                score -= 30.0
        
        # 根据团队验证结果调整分数
        if team_result:
            # 检查团队成员详细信息
            details = team_result.get('details', {})
            members = details.get('members', [])
            
            for member in members:
                # 检查成员经验
                experience_years = member.get('experience_years', 0)
                
                if experience_years >= 5:
                    score += 5.0
                elif experience_years >= 3:
                    score += 3.0
                
                # 检查成员角色
                role = member.get('role', '').lower()
                
                if 'founder' in role or 'ceo' in role:
                    # 创始人或CEO的经验更重要
                    if experience_years >= 5:
                        score += 10.0
                    elif experience_years >= 3:
                        score += 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()