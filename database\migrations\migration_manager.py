"""
Migration Manager

Handles database schema migrations and version management.
"""

import logging
from typing import List, Dict, Any
from pathlib import Path

from ..models.project_model import ProjectModel


class MigrationManager:
    """
    Database migration manager.
    
    Handles schema creation, updates, and version tracking.
    """
    
    def __init__(self, db_manager):
        """
        Initialize migration manager.
        
        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.current_version = 1
    
    def run_migrations(self) -> bool:
        """
        Run all pending migrations.
        
        Returns:
            bool: True if migrations successful
        """
        try:
            # Create migration tracking table
            self._create_migration_table()
            
            # Get current database version
            db_version = self._get_database_version()
            
            # Run migrations if needed
            if db_version < self.current_version:
                self.logger.info(f"Running migrations from version {db_version} to {self.current_version}")
                
                for version in range(db_version + 1, self.current_version + 1):
                    self._run_migration(version)
                
                self.logger.info("All migrations completed successfully")
            else:
                self.logger.info("Database is up to date")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Migration failed: {e}")
            return False
    
    def _create_migration_table(self):
        """Create migration tracking table."""
        sql = """
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version INTEGER PRIMARY KEY,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            description TEXT
        )
        """
        self.db_manager.execute_update(sql)
    
    def _get_database_version(self) -> int:
        """
        Get current database version.
        
        Returns:
            int: Current database version
        """
        try:
            result = self.db_manager.execute_query(
                "SELECT MAX(version) as version FROM schema_migrations"
            )
            if result and result[0]['version'] is not None:
                return result[0]['version']
            return 0
        except:
            return 0
    
    def _run_migration(self, version: int):
        """
        Run specific migration version.
        
        Args:
            version: Migration version to run
        """
        migration_method = getattr(self, f'_migration_v{version}', None)
        if migration_method:
            self.logger.info(f"Running migration v{version}")
            migration_method()
            self._record_migration(version)
        else:
            raise ValueError(f"Migration v{version} not found")
    
    def _record_migration(self, version: int, description: str = ""):
        """
        Record completed migration.
        
        Args:
            version: Migration version
            description: Migration description
        """
        self.db_manager.execute_update(
            "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
            (version, description)
        )
    
    def _migration_v1(self):
        """Initial database schema creation."""
        # Create projects table
        self.db_manager.execute_update(ProjectModel.create_table_sql())
        
        # Create indexes
        for index_sql in ProjectModel.create_indexes_sql():
            self.db_manager.execute_update(index_sql)
        
        # Create wallets table
        wallets_sql = """
        CREATE TABLE IF NOT EXISTS wallets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            address TEXT NOT NULL UNIQUE,
            private_key TEXT NOT NULL,
            blockchain TEXT NOT NULL,
            wallet_type TEXT NOT NULL,
            balance REAL DEFAULT 0.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            tags TEXT DEFAULT '[]',
            notes TEXT
        )
        """
        self.db_manager.execute_update(wallets_sql)
        
        # Create wallet indexes
        wallet_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_wallets_blockchain ON wallets(blockchain)",
            "CREATE INDEX IF NOT EXISTS idx_wallets_type ON wallets(wallet_type)",
            "CREATE INDEX IF NOT EXISTS idx_wallets_active ON wallets(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_wallets_created ON wallets(created_at)"
        ]
        for index_sql in wallet_indexes:
            self.db_manager.execute_update(index_sql)
        
        # Create proxies table
        proxies_sql = """
        CREATE TABLE IF NOT EXISTS proxies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            host TEXT NOT NULL,
            port INTEGER NOT NULL,
            username TEXT,
            password TEXT,
            proxy_type TEXT NOT NULL,
            country TEXT,
            city TEXT,
            is_active BOOLEAN DEFAULT 1,
            is_verified BOOLEAN DEFAULT 0,
            speed_score REAL DEFAULT 0.0,
            reliability_score REAL DEFAULT 0.0,
            last_checked TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            failure_count INTEGER DEFAULT 0,
            success_count INTEGER DEFAULT 0,
            UNIQUE(host, port)
        )
        """
        self.db_manager.execute_update(proxies_sql)
        
        # Create proxy indexes
        proxy_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_proxies_active ON proxies(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_proxies_verified ON proxies(is_verified)",
            "CREATE INDEX IF NOT EXISTS idx_proxies_type ON proxies(proxy_type)",
            "CREATE INDEX IF NOT EXISTS idx_proxies_country ON proxies(country)",
            "CREATE INDEX IF NOT EXISTS idx_proxies_speed ON proxies(speed_score)"
        ]
        for index_sql in proxy_indexes:
            self.db_manager.execute_update(index_sql)
        
        # Create tasks table
        tasks_sql = """
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            wallet_id INTEGER,
            task_type TEXT NOT NULL,
            status TEXT NOT NULL,
            priority INTEGER DEFAULT 5,
            scheduled_at TIMESTAMP,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            retry_count INTEGER DEFAULT 0,
            max_retries INTEGER DEFAULT 3,
            task_data TEXT,
            result_data TEXT,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects (id),
            FOREIGN KEY (wallet_id) REFERENCES wallets (id)
        )
        """
        self.db_manager.execute_update(tasks_sql)
        
        # Create task indexes
        task_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_tasks_project ON tasks(project_id)",
            "CREATE INDEX IF NOT EXISTS idx_tasks_wallet ON tasks(wallet_id)",
            "CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)",
            "CREATE INDEX IF NOT EXISTS idx_tasks_type ON tasks(task_type)",
            "CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority)",
            "CREATE INDEX IF NOT EXISTS idx_tasks_scheduled ON tasks(scheduled_at)"
        ]
        for index_sql in task_indexes:
            self.db_manager.execute_update(index_sql)
        
        # Create accounts table
        accounts_sql = """
        CREATE TABLE IF NOT EXISTS accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform TEXT NOT NULL,
            username TEXT NOT NULL,
            email TEXT,
            password_hash TEXT,
            auth_token TEXT,
            profile_data TEXT,
            is_active BOOLEAN DEFAULT 1,
            is_verified BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_used TIMESTAMP,
            failure_count INTEGER DEFAULT 0,
            UNIQUE(platform, username)
        )
        """
        self.db_manager.execute_update(accounts_sql)
        
        # Create account indexes
        account_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_accounts_platform ON accounts(platform)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_active ON accounts(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_verified ON accounts(is_verified)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_last_used ON accounts(last_used)"
        ]
        for index_sql in account_indexes:
            self.db_manager.execute_update(index_sql)
        
        self.logger.info("Initial database schema created")
    
    def get_migration_history(self) -> List[Dict[str, Any]]:
        """
        Get migration history.
        
        Returns:
            List[Dict[str, Any]]: Migration history
        """
        return self.db_manager.execute_query(
            "SELECT * FROM schema_migrations ORDER BY version"
        )
