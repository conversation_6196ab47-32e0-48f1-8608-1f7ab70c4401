"""
Task Execution Agent Main Module

This is the main entry point for the Task Execution Agent.
"""

import os
import sys
import json
import logging
import argparse
import time
from typing import Dict, Any, Optional

# Add parent directory to path to allow imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from task_execution.task_execution_agent import TaskExecutionAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("data/logs/task_execution.log")
    ]
)
logger = logging.getLogger("TaskExecutionMain")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Task Execution Agent")
    parser.add_argument("--config", type=str, default="config/task_execution_config.json",
                        help="Path to configuration file")
    parser.add_argument("--action", type=str, 
                        choices=["start", "stop", "status", "execute", "list_running"],
                        default="start", help="Action to perform")
    parser.add_argument("--task_file", type=str, help="JSON file containing task data")
    parser.add_argument("--task_id", type=str, help="Task ID for task-specific actions")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from file."""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        else:
            logger.warning(f"Config file not found: {config_path}")
            return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def load_task_data(task_file: str) -> Dict[str, Any]:
    """Load task data from file."""
    try:
        if os.path.exists(task_file):
            with open(task_file, 'r') as f:
                return json.load(f)
        else:
            logger.error(f"Task file not found: {task_file}")
            return {}
    except Exception as e:
        logger.error(f"Error loading task data: {e}")
        return {}

def main():
    """Main entry point for the Task Execution Agent."""
    # Parse arguments
    args = parse_arguments()
    
    # Ensure logs directory exists
    os.makedirs("data/logs", exist_ok=True)
    
    # Load configuration
    config = load_config(args.config)
    
    # Create agent
    agent = TaskExecutionAgent(args.config if os.path.exists(args.config) else None)
    
    # Perform requested action
    if args.action == "start":
        logger.info("Starting Task Execution Agent")
        success = agent.start()
        logger.info(f"Agent started: {success}")
    
    elif args.action == "stop":
        logger.info("Stopping Task Execution Agent")
        success = agent.stop()
        logger.info(f"Agent stopped: {success}")
    
    elif args.action == "status":
        agent.start()
        status = agent.status()
        logger.info(f"Agent status: {status}")
        print(json.dumps(status, indent=2))
    
    elif args.action == "execute":
        agent.start()
        
        if not args.task_file:
            logger.error("Task file is required for execute action")
            return
        
        task_data = load_task_data(args.task_file)
        if not task_data:
            return
        
        logger.info(f"Executing task: {task_data.get('name', 'Unnamed task')}")
        result = agent.execute_task(task_data)
        
        logger.info(f"Task execution result: {result['status']}")
        print(json.dumps(result, indent=2))
    
    elif args.action == "list_running":
        agent.start()
        
        running_tasks = agent.list_running_tasks()
        
        logger.info(f"Found {len(running_tasks)} running tasks")
        print(f"Running tasks ({len(running_tasks)}):")
        for task in running_tasks:
            print(f"- {task['task_id']}: {task['name']}")
            print(f"  Started: {task['start_time']}")
            print(f"  Running for: {task['execution_time']} seconds")
            print()

if __name__ == "__main__":
    main()