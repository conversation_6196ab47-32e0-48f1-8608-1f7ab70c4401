"""
基础持续运行智能体混入类

提供智能体持续运行的基础功能，包括循环执行、错误恢复、状态管理等
"""

import asyncio
import logging
import time
import threading
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timedelta


class ContinuousOperationMixin:
    """持续运行混入类"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 运行状态
        self._running = False
        self._paused = False
        self._loop_task = None
        
        # 配置参数
        self._loop_interval = getattr(self, 'loop_interval', 60)  # 默认60秒
        self._max_errors = getattr(self, 'max_errors', 10)  # 最大连续错误数
        self._error_backoff = getattr(self, 'error_backoff', 5)  # 错误后等待时间
        
        # 统计信息
        self._stats = {
            'start_time': None,
            'total_cycles': 0,
            'successful_cycles': 0,
            'failed_cycles': 0,
            'last_cycle_time': None,
            'consecutive_errors': 0,
            'total_uptime': 0
        }
        
        # 错误处理
        self._error_handlers = []
        self._recovery_strategies = []
    
    async def start_continuous_operation(self) -> bool:
        """启动持续运行"""
        if self._running:
            self.logger.warning("智能体已在运行中")
            return True
        
        try:
            self._running = True
            self._paused = False
            self._stats['start_time'] = datetime.now()
            
            # 启动主循环
            self._loop_task = asyncio.create_task(self._main_operation_loop())
            
            self.logger.info("智能体持续运行已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动持续运行失败: {e}")
            self._running = False
            return False
    
    async def stop_continuous_operation(self) -> bool:
        """停止持续运行"""
        if not self._running:
            self.logger.warning("智能体未在运行")
            return True
        
        try:
            self._running = False
            
            # 等待主循环结束
            if self._loop_task and not self._loop_task.done():
                self._loop_task.cancel()
                try:
                    await self._loop_task
                except asyncio.CancelledError:
                    pass
            
            # 更新统计信息
            if self._stats['start_time']:
                uptime = datetime.now() - self._stats['start_time']
                self._stats['total_uptime'] += uptime.total_seconds()
            
            self.logger.info("智能体持续运行已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止持续运行失败: {e}")
            return False
    
    async def pause_operation(self) -> bool:
        """暂停运行"""
        if not self._running:
            return False
        
        self._paused = True
        self.logger.info("智能体运行已暂停")
        return True
    
    async def resume_operation(self) -> bool:
        """恢复运行"""
        if not self._running or not self._paused:
            return False
        
        self._paused = False
        self.logger.info("智能体运行已恢复")
        return True
    
    async def _main_operation_loop(self):
        """主运行循环"""
        self.logger.info("主运行循环已启动")
        
        while self._running:
            try:
                # 检查是否暂停
                if self._paused:
                    await asyncio.sleep(1)
                    continue
                
                # 执行一个周期
                cycle_start = time.time()
                success = await self._execute_operation_cycle()
                cycle_duration = time.time() - cycle_start
                
                # 更新统计信息
                self._stats['total_cycles'] += 1
                self._stats['last_cycle_time'] = datetime.now()
                
                if success:
                    self._stats['successful_cycles'] += 1
                    self._stats['consecutive_errors'] = 0
                    self.logger.debug(f"周期执行成功，耗时: {cycle_duration:.2f}秒")
                else:
                    self._stats['failed_cycles'] += 1
                    self._stats['consecutive_errors'] += 1
                    self.logger.warning(f"周期执行失败，连续错误: {self._stats['consecutive_errors']}")
                
                # 检查是否需要错误恢复
                if self._stats['consecutive_errors'] >= self._max_errors:
                    self.logger.error("连续错误过多，尝试恢复...")
                    await self._attempt_recovery()
                
                # 等待下一个周期
                await self._wait_for_next_cycle(cycle_duration)
                
            except asyncio.CancelledError:
                self.logger.info("主运行循环被取消")
                break
            except Exception as e:
                self.logger.error(f"主运行循环异常: {e}")
                self._stats['failed_cycles'] += 1
                self._stats['consecutive_errors'] += 1
                await asyncio.sleep(self._error_backoff)
        
        self.logger.info("主运行循环已结束")
    
    async def _execute_operation_cycle(self) -> bool:
        """
        执行一个操作周期
        
        子类需要重写此方法实现具体的周期性操作
        
        Returns:
            bool: 执行是否成功
        """
        # 默认实现，子类应该重写
        self.logger.debug("执行默认操作周期")
        await asyncio.sleep(0.1)
        return True
    
    async def _wait_for_next_cycle(self, last_cycle_duration: float):
        """等待下一个周期"""
        # 计算等待时间，确保周期间隔
        wait_time = max(0, self._loop_interval - last_cycle_duration)
        
        if wait_time > 0:
            await asyncio.sleep(wait_time)
    
    async def _attempt_recovery(self) -> bool:
        """尝试错误恢复"""
        self.logger.info("开始错误恢复...")
        
        for strategy in self._recovery_strategies:
            try:
                if await strategy():
                    self.logger.info("错误恢复成功")
                    self._stats['consecutive_errors'] = 0
                    return True
            except Exception as e:
                self.logger.error(f"恢复策略失败: {e}")
        
        self.logger.error("所有恢复策略都失败")
        return False
    
    def add_error_handler(self, handler: Callable[[Exception], None]):
        """添加错误处理器"""
        self._error_handlers.append(handler)
    
    def add_recovery_strategy(self, strategy: Callable[[], bool]):
        """添加恢复策略"""
        self._recovery_strategies.append(strategy)
    
    def get_operation_stats(self) -> Dict[str, Any]:
        """获取运行统计信息"""
        stats = self._stats.copy()
        
        # 计算成功率
        if stats['total_cycles'] > 0:
            stats['success_rate'] = stats['successful_cycles'] / stats['total_cycles']
        else:
            stats['success_rate'] = 0
        
        # 计算当前运行时间
        if self._running and stats['start_time']:
            current_uptime = datetime.now() - stats['start_time']
            stats['current_uptime'] = current_uptime.total_seconds()
        else:
            stats['current_uptime'] = 0
        
        return stats
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._running
    
    def is_paused(self) -> bool:
        """检查是否已暂停"""
        return self._paused


class MultiAccountMixin:
    """多账号管理混入类"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 账号管理
        self._accounts = {}
        self._current_account = None
        self._account_rotation_enabled = True
        self._account_rotation_interval = 3600  # 1小时轮换一次
        self._last_rotation_time = None
        
        # 账号统计
        self._account_stats = {}
    
    def add_account(self, account_id: str, account_data: Dict[str, Any]) -> bool:
        """添加账号"""
        try:
            self._accounts[account_id] = {
                'data': account_data,
                'created_at': datetime.now(),
                'last_used': None,
                'usage_count': 0,
                'success_count': 0,
                'error_count': 0,
                'status': 'active'
            }
            
            self._account_stats[account_id] = {
                'total_operations': 0,
                'successful_operations': 0,
                'failed_operations': 0,
                'last_operation': None
            }
            
            self.logger.info(f"添加账号: {account_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加账号失败: {e}")
            return False
    
    def remove_account(self, account_id: str) -> bool:
        """移除账号"""
        try:
            if account_id in self._accounts:
                del self._accounts[account_id]
                del self._account_stats[account_id]
                
                if self._current_account == account_id:
                    self._current_account = None
                
                self.logger.info(f"移除账号: {account_id}")
                return True
            else:
                self.logger.warning(f"账号不存在: {account_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"移除账号失败: {e}")
            return False
    
    def switch_account(self, account_id: str) -> bool:
        """切换账号"""
        try:
            if account_id not in self._accounts:
                self.logger.error(f"账号不存在: {account_id}")
                return False
            
            if self._accounts[account_id]['status'] != 'active':
                self.logger.error(f"账号不可用: {account_id}")
                return False
            
            self._current_account = account_id
            self._accounts[account_id]['last_used'] = datetime.now()
            self._accounts[account_id]['usage_count'] += 1
            
            self.logger.info(f"切换到账号: {account_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"切换账号失败: {e}")
            return False
    
    def get_current_account(self) -> Optional[Dict[str, Any]]:
        """获取当前账号"""
        if self._current_account and self._current_account in self._accounts:
            return self._accounts[self._current_account]['data']
        return None
    
    def get_all_accounts(self) -> Dict[str, Dict[str, Any]]:
        """获取所有账号"""
        return {aid: acc['data'] for aid, acc in self._accounts.items()}
    
    def rotate_account(self) -> bool:
        """轮换账号"""
        try:
            if not self._account_rotation_enabled:
                return False
            
            # 检查是否需要轮换
            if self._last_rotation_time:
                time_since_rotation = datetime.now() - self._last_rotation_time
                if time_since_rotation.total_seconds() < self._account_rotation_interval:
                    return False
            
            # 选择下一个账号
            active_accounts = [aid for aid, acc in self._accounts.items() 
                             if acc['status'] == 'active']
            
            if not active_accounts:
                self.logger.warning("没有可用的账号")
                return False
            
            # 选择使用次数最少的账号
            next_account = min(active_accounts, 
                             key=lambda aid: self._accounts[aid]['usage_count'])
            
            if next_account != self._current_account:
                self.switch_account(next_account)
                self._last_rotation_time = datetime.now()
                self.logger.info(f"账号轮换到: {next_account}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"账号轮换失败: {e}")
            return False
    
    def update_account_stats(self, account_id: str, success: bool):
        """更新账号统计"""
        if account_id in self._account_stats:
            stats = self._account_stats[account_id]
            stats['total_operations'] += 1
            stats['last_operation'] = datetime.now()
            
            if success:
                stats['successful_operations'] += 1
                self._accounts[account_id]['success_count'] += 1
            else:
                stats['failed_operations'] += 1
                self._accounts[account_id]['error_count'] += 1
    
    def get_account_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取账号统计信息"""
        return self._account_stats.copy()
    
    def enable_account_rotation(self, interval: int = 3600):
        """启用账号轮换"""
        self._account_rotation_enabled = True
        self._account_rotation_interval = interval
        self.logger.info(f"启用账号轮换，间隔: {interval}秒")
    
    def disable_account_rotation(self):
        """禁用账号轮换"""
        self._account_rotation_enabled = False
        self.logger.info("禁用账号轮换")
