"""
基础项目收集器

该模块定义了项目收集器的基类。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional


class BaseCollector(ABC):
    """项目收集器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目收集器
        
        Args:
            config: 配置字典，包含项目收集器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def collect(self, project: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        收集项目信息
        
        Args:
            project: 项目字典
            
        Returns:
            收集到的项目信息字典，如果无法收集则返回None
        """
        pass
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            config: 新的配置字典
        """
        self.config = config
        self.logger.info(f"{self.__class__.__name__} 配置已更新")