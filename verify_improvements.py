#!/usr/bin/env python3
"""
AirHunter 项目改进验证脚本

验证所有已实施的改进措施
"""

import os
import sys
import importlib.util
import traceback
from pathlib import Path
from datetime import datetime


def check_syntax_fixes():
    """检查语法错误修复"""
    print("🔧 检查语法错误修复...")
    
    fixed_files = [
        "assessment/main.py",
        "assessment/risk/reward_estimator.py", 
        "assessment/risk/risk_calculator.py"
    ]
    
    all_fixed = True
    for file_path in fixed_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                compile(content, file_path, 'exec')
                print(f"  ✅ {file_path} - 语法正确")
            except SyntaxError as e:
                print(f"  ❌ {file_path} - 语法错误: {e}")
                all_fixed = False
            except Exception as e:
                print(f"  ⚠️  {file_path} - 其他错误: {e}")
                all_fixed = False
        else:
            print(f"  ❌ {file_path} - 文件不存在")
            all_fixed = False
    
    return all_fixed


def check_coordinator_main_file():
    """检查协调器主文件"""
    print("\n🤖 检查协调器主文件...")
    
    main_file = "coordinator/coordinator_agent.py"
    if os.path.exists(main_file):
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键类和方法
            required_elements = [
                "class CoordinatorAgent",
                "def __init__",
                "def start",
                "def stop",
                "def register_agent",
                "def get_system_status"
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if not missing_elements:
                print(f"  ✅ {main_file} - 包含所有必需元素")
                return True
            else:
                print(f"  ⚠️  {main_file} - 缺少元素: {missing_elements}")
                return False
                
        except Exception as e:
            print(f"  ❌ {main_file} - 读取错误: {e}")
            return False
    else:
        print(f"  ❌ {main_file} - 文件不存在")
        return False


def check_error_handling():
    """检查错误处理增强"""
    print("\n🛡️  检查错误处理增强...")
    
    # 检查错误处理模块
    error_handling_file = "common/error_handling.py"
    if os.path.exists(error_handling_file):
        try:
            with open(error_handling_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键类和函数
            required_elements = [
                "class AirHunterError",
                "class ErrorHandler",
                "def retry_on_error",
                "def safe_execute",
                "def validate_required_fields"
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if not missing_elements:
                print(f"  ✅ {error_handling_file} - 包含所有必需元素")
                error_handling_ok = True
            else:
                print(f"  ⚠️  {error_handling_file} - 缺少元素: {missing_elements}")
                error_handling_ok = False
                
        except Exception as e:
            print(f"  ❌ {error_handling_file} - 读取错误: {e}")
            error_handling_ok = False
    else:
        print(f"  ❌ {error_handling_file} - 文件不存在")
        error_handling_ok = False
    
    # 检查智能体中的错误处理集成
    discovery_file = "discovery/discovery_agent.py"
    if os.path.exists(discovery_file):
        try:
            with open(discovery_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查错误处理集成
            error_handling_features = [
                "ErrorHandler",
                "safe_execute",
                "retry_on_error",
                "error_handler.handle_error"
            ]
            
            integrated_features = []
            for feature in error_handling_features:
                if feature in content:
                    integrated_features.append(feature)
            
            print(f"  ✅ {discovery_file} - 集成了 {len(integrated_features)}/{len(error_handling_features)} 个错误处理特性")
            discovery_ok = len(integrated_features) >= 3
            
        except Exception as e:
            print(f"  ❌ {discovery_file} - 读取错误: {e}")
            discovery_ok = False
    else:
        print(f"  ❌ {discovery_file} - 文件不存在")
        discovery_ok = False
    
    return error_handling_ok and discovery_ok


def check_test_coverage():
    """检查测试覆盖"""
    print("\n🧪 检查测试覆盖...")
    
    test_files = [
        "test_coordinator.py",
        "test_discovery.py", 
        "test_monitoring.py",
        "run_basic_tests.py"
    ]
    
    existing_tests = 0
    for test_file in test_files:
        if os.path.exists(test_file):
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查测试内容
                if "unittest" in content or "pytest" in content or "def test_" in content:
                    print(f"  ✅ {test_file} - 有效的测试文件")
                    existing_tests += 1
                else:
                    print(f"  ⚠️  {test_file} - 文件存在但可能不是测试文件")
                    
            except Exception as e:
                print(f"  ❌ {test_file} - 读取错误: {e}")
        else:
            print(f"  ❌ {test_file} - 文件不存在")
    
    coverage_percentage = (existing_tests / len(test_files)) * 100
    print(f"  📊 测试覆盖: {existing_tests}/{len(test_files)} 文件 ({coverage_percentage:.1f}%)")
    
    return existing_tests >= 3


def check_project_structure():
    """检查项目结构完整性"""
    print("\n🏗️  检查项目结构完整性...")
    
    required_agents = [
        "coordinator", "discovery", "assessment", "monitoring",
        "fund_management", "task_planning", "task_execution", 
        "proxy", "anti_sybil", "profit_optimization"
    ]
    
    complete_agents = 0
    for agent in required_agents:
        agent_dir = agent
        main_file = f"{agent}/{agent}_agent.py"
        
        if os.path.exists(agent_dir) and os.path.isdir(agent_dir):
            if os.path.exists(main_file):
                print(f"  ✅ {agent} - 完整")
                complete_agents += 1
            else:
                print(f"  ⚠️  {agent} - 目录存在但缺少主文件")
        else:
            print(f"  ❌ {agent} - 目录不存在")
    
    structure_percentage = (complete_agents / len(required_agents)) * 100
    print(f"  📊 结构完整性: {complete_agents}/{len(required_agents)} 智能体 ({structure_percentage:.1f}%)")
    
    return complete_agents == len(required_agents)


def generate_improvement_report():
    """生成改进报告"""
    print("\n" + "=" * 60)
    print("📋 AirHunter 项目改进验证报告")
    print("=" * 60)
    print(f"🕒 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行所有检查
    checks = [
        ("语法错误修复", check_syntax_fixes),
        ("协调器主文件", check_coordinator_main_file),
        ("错误处理增强", check_error_handling),
        ("测试覆盖", check_test_coverage),
        ("项目结构", check_project_structure)
    ]
    
    results = {}
    total_score = 0
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
            if result:
                total_score += 20  # 每项检查20分
        except Exception as e:
            print(f"  ❌ {check_name} 检查失败: {e}")
            results[check_name] = False
    
    # 输出总结
    print(f"\n📊 改进验证总结:")
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 未通过"
        print(f"   {check_name}: {status}")
    
    print(f"\n🎯 总体评分: {total_score}/100")
    
    if total_score >= 90:
        status = "🟢 优秀"
        message = "所有主要改进都已成功实施！"
    elif total_score >= 80:
        status = "🟡 良好"
        message = "大部分改进已实施，还有少量工作需要完成。"
    elif total_score >= 60:
        status = "🟠 需要改进"
        message = "部分改进已实施，但还需要更多工作。"
    else:
        status = "🔴 需要重点关注"
        message = "改进实施不足，需要重点关注。"
    
    print(f"   状态: {status}")
    print(f"   评价: {message}")
    
    # 改进建议
    print(f"\n💡 下一步建议:")
    if not results.get("语法错误修复", False):
        print("   - 修复剩余的语法错误")
    if not results.get("协调器主文件", False):
        print("   - 完善协调器主文件实现")
    if not results.get("错误处理增强", False):
        print("   - 完成错误处理模块的集成")
    if not results.get("测试覆盖", False):
        print("   - 增加更多单元测试")
    if not results.get("项目结构", False):
        print("   - 补充缺失的智能体文件")
    
    if total_score >= 90:
        print("   - 考虑添加集成测试")
        print("   - 优化性能和资源使用")
        print("   - 完善文档和使用指南")
    
    return total_score >= 80


def main():
    """主函数"""
    print("🚀 AirHunter 项目改进验证")
    print(f"📁 工作目录: {os.getcwd()}")
    
    success = generate_improvement_report()
    
    if success:
        print("\n🎉 改进验证通过！项目状态良好。")
        return 0
    else:
        print("\n⚠️  改进验证发现问题，请查看上述建议。")
        return 1


if __name__ == "__main__":
    exit(main())
