"""
Web Handler

This module handles web-related tasks for the Task Execution Agent.
"""

import logging
import os
import json
import time
import random
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup
import urllib.parse

# Optional imports for browser automation
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

logger = logging.getLogger("WebHandler")

class WebHandler:
    """
    Handler for web-related tasks.
    
    Capabilities:
    - Website visiting
    - Form filling and submission
    - Content extraction
    - Web scraping
    - Browser automation
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the web handler.
        
        Args:
            config: Configuration for the handler
        """
        self.config = config or {}
        self.driver = None
        self.session = requests.Session()
        self.user_agent = self.config.get("user_agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        
        # Set up session headers
        self.session.headers.update({
            "User-Agent": self.user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0"
        })
        
        # Initialize browser if configured
        if self.config.get("use_browser", False) and SELENIUM_AVAILABLE:
            self._initialize_browser()
        
        logger.info("Web handler initialized")
    
    def _initialize_browser(self) -> bool:
        """
        Initialize the browser for automation.
        
        Returns:
            bool: True if initialized successfully, False otherwise
        """
        if not SELENIUM_AVAILABLE:
            logger.error("Selenium is not available. Browser automation is disabled.")
            return False
        
        try:
            # Set up Chrome options
            chrome_options = Options()
            
            # Add arguments from config
            arguments = self.config.get("browser_arguments", [])
            if not arguments:
                # Default arguments
                arguments = [
                    "--headless",
                    "--disable-gpu",
                    "--window-size=1920,1080",
                    "--no-sandbox",
                    "--disable-dev-shm-usage"
                ]
            
            for arg in arguments:
                chrome_options.add_argument(arg)
            
            # Set user agent
            chrome_options.add_argument(f"user-agent={self.user_agent}")
            
            # Initialize driver
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # Set default timeout
            self.driver.set_page_load_timeout(self.config.get("page_load_timeout", 30))
            
            logger.info("Browser initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            return False
    
    def visit(self, **params) -> Dict[str, Any]:
        """
        Visit a website.
        
        Args:
            **params: Parameters for visiting
                - url: URL to visit
                - use_browser: Whether to use browser (overrides config)
                - wait_for_selector: Optional CSS selector to wait for
                - wait_timeout: Timeout for waiting (seconds)
                
        Returns:
            Dict containing visit result
        """
        url = params.get("url")
        use_browser = params.get("use_browser", self.config.get("use_browser", False))
        wait_for_selector = params.get("wait_for_selector")
        wait_timeout = params.get("wait_timeout", 10)
        
        if not url:
            return {
                "success": False,
                "error": "URL is required"
            }
        
        try:
            if use_browser and SELENIUM_AVAILABLE:
                # Initialize browser if needed
                if not self.driver:
                    success = self._initialize_browser()
                    if not success:
                        raise Exception("Failed to initialize browser")
                
                # Visit URL
                self.driver.get(url)
                
                # Wait for selector if specified
                if wait_for_selector:
                    WebDriverWait(self.driver, wait_timeout).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, wait_for_selector))
                    )
                
                # Get page title and content
                title = self.driver.title
                content = self.driver.page_source
                
                logger.info(f"Visited URL with browser: {url}")
                
                return {
                    "success": True,
                    "url": url,
                    "title": title,
                    "content_length": len(content),
                    "method": "browser"
                }
            else:
                # Use requests
                response = self.session.get(url, timeout=wait_timeout)
                response.raise_for_status()
                
                # Parse content
                soup = BeautifulSoup(response.text, "html.parser")
                title = soup.title.string if soup.title else ""
                
                logger.info(f"Visited URL with requests: {url}")
                
                return {
                    "success": True,
                    "url": url,
                    "title": title,
                    "content_length": len(response.text),
                    "status_code": response.status_code,
                    "method": "requests"
                }
        except Exception as e:
            logger.error(f"Error visiting URL {url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def fill_form(self, **params) -> Dict[str, Any]:
        """
        Fill and submit a web form.
        
        Args:
            **params: Parameters for form filling
                - url: URL with the form
                - form_data: Dict mapping field selectors to values
                - submit_selector: Selector for the submit button
                - wait_for_selector: Optional selector to wait for after submission
                - wait_timeout: Timeout for waiting (seconds)
                
        Returns:
            Dict containing form submission result
        """
        if not SELENIUM_AVAILABLE:
            return {
                "success": False,
                "error": "Selenium is not available. Browser automation is required for form filling."
            }
        
        url = params.get("url")
        form_data = params.get("form_data", {})
        submit_selector = params.get("submit_selector")
        wait_for_selector = params.get("wait_for_selector")
        wait_timeout = params.get("wait_timeout", 10)
        
        if not url:
            return {
                "success": False,
                "error": "URL is required"
            }
        
        if not form_data:
            return {
                "success": False,
                "error": "Form data is required"
            }
        
        if not submit_selector:
            return {
                "success": False,
                "error": "Submit selector is required"
            }
        
        try:
            # Initialize browser if needed
            if not self.driver:
                success = self._initialize_browser()
                if not success:
                    raise Exception("Failed to initialize browser")
            
            # Visit URL
            self.driver.get(url)
            
            # Fill form fields
            for selector, value in form_data.items():
                # Wait for element
                element = WebDriverWait(self.driver, wait_timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                
                # Clear field if it's an input
                if element.tag_name in ["input", "textarea"]:
                    element.clear()
                
                # Fill field
                element.send_keys(value)
            
            # Submit form
            submit_button = WebDriverWait(self.driver, wait_timeout).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, submit_selector))
            )
            submit_button.click()
            
            # Wait for result if specified
            if wait_for_selector:
                WebDriverWait(self.driver, wait_timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, wait_for_selector))
                )
            
            # Get result
            current_url = self.driver.current_url
            title = self.driver.title
            
            logger.info(f"Submitted form at URL: {url}")
            
            return {
                "success": True,
                "url": url,
                "current_url": current_url,
                "title": title
            }
        except Exception as e:
            logger.error(f"Error filling form at URL {url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def extract_content(self, **params) -> Dict[str, Any]:
        """
        Extract content from a webpage.
        
        Args:
            **params: Parameters for content extraction
                - url: URL to extract from
                - selectors: Dict mapping names to CSS selectors
                - use_browser: Whether to use browser (overrides config)
                
        Returns:
            Dict containing extracted content
        """
        url = params.get("url")
        selectors = params.get("selectors", {})
        use_browser = params.get("use_browser", self.config.get("use_browser", False))
        
        if not url:
            return {
                "success": False,
                "error": "URL is required"
            }
        
        if not selectors:
            return {
                "success": False,
                "error": "Selectors are required"
            }
        
        try:
            # Visit the page
            visit_result = self.visit(url=url, use_browser=use_browser)
            
            if not visit_result["success"]:
                return visit_result
            
            # Extract content
            content = {}
            
            if use_browser and SELENIUM_AVAILABLE and self.driver:
                # Use Selenium
                for name, selector in selectors.items():
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            if len(elements) == 1:
                                content[name] = elements[0].text
                            else:
                                content[name] = [element.text for element in elements]
                    except NoSuchElementException:
                        content[name] = None
            else:
                # Use BeautifulSoup
                response = self.session.get(url)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.text, "html.parser")
                
                for name, selector in selectors.items():
                    elements = soup.select(selector)
                    if elements:
                        if len(elements) == 1:
                            content[name] = elements[0].get_text().strip()
                        else:
                            content[name] = [element.get_text().strip() for element in elements]
                    else:
                        content[name] = None
            
            logger.info(f"Extracted content from URL: {url}")
            
            return {
                "success": True,
                "url": url,
                "content": content
            }
        except Exception as e:
            logger.error(f"Error extracting content from URL {url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def search(self, **params) -> Dict[str, Any]:
        """
        Perform a web search.
        
        Args:
            **params: Parameters for searching
                - query: Search query
                - engine: Search engine to use (google, bing, duckduckgo)
                - num_results: Number of results to return
                
        Returns:
            Dict containing search results
        """
        query = params.get("query")
        engine = params.get("engine", "google")
        num_results = params.get("num_results", 5)
        
        if not query:
            return {
                "success": False,
                "error": "Query is required"
            }
        
        try:
            # Construct search URL
            if engine == "google":
                search_url = f"https://www.google.com/search?q={urllib.parse.quote(query)}"
            elif engine == "bing":
                search_url = f"https://www.bing.com/search?q={urllib.parse.quote(query)}"
            elif engine == "duckduckgo":
                search_url = f"https://duckduckgo.com/?q={urllib.parse.quote(query)}"
            else:
                return {
                    "success": False,
                    "error": f"Unsupported search engine: {engine}"
                }
            
            # Perform search
            response = self.session.get(search_url)
            response.raise_for_status()
            
            # Parse results
            soup = BeautifulSoup(response.text, "html.parser")
            results = []
            
            if engine == "google":
                # Parse Google search results
                for result in soup.select("div.g")[:num_results]:
                    title_element = result.select_one("h3")
                    link_element = result.select_one("a")
                    snippet_element = result.select_one("div.VwiC3b")
                    
                    if title_element and link_element:
                        title = title_element.get_text()
                        link = link_element["href"]
                        snippet = snippet_element.get_text() if snippet_element else ""
                        
                        results.append({
                            "title": title,
                            "url": link,
                            "snippet": snippet
                        })
            elif engine == "bing":
                # Parse Bing search results
                for result in soup.select("li.b_algo")[:num_results]:
                    title_element = result.select_one("h2 a")
                    snippet_element = result.select_one("div.b_caption p")
                    
                    if title_element:
                        title = title_element.get_text()
                        link = title_element["href"]
                        snippet = snippet_element.get_text() if snippet_element else ""
                        
                        results.append({
                            "title": title,
                            "url": link,
                            "snippet": snippet
                        })
            elif engine == "duckduckgo":
                # Parse DuckDuckGo search results
                for result in soup.select("div.result")[:num_results]:
                    title_element = result.select_one("h2.result__title a")
                    snippet_element = result.select_one("div.result__snippet")
                    
                    if title_element:
                        title = title_element.get_text()
                        link = title_element["href"]
                        snippet = snippet_element.get_text() if snippet_element else ""
                        
                        results.append({
                            "title": title,
                            "url": link,
                            "snippet": snippet
                        })
            
            logger.info(f"Performed web search for query: {query}")
            
            return {
                "success": True,
                "query": query,
                "engine": engine,
                "results": results,
                "count": len(results)
            }
        except Exception as e:
            logger.error(f"Error performing web search for query {query}: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "engine": engine
            }
    
    def download(self, **params) -> Dict[str, Any]:
        """
        Download a file from the web.
        
        Args:
            **params: Parameters for downloading
                - url: URL to download from
                - output_path: Path to save the file
                - timeout: Download timeout in seconds
                
        Returns:
            Dict containing download result
        """
        url = params.get("url")
        output_path = params.get("output_path")
        timeout = params.get("timeout", 60)
        
        if not url:
            return {
                "success": False,
                "error": "URL is required"
            }
        
        if not output_path:
            return {
                "success": False,
                "error": "Output path is required"
            }
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
            
            # Download file
            response = self.session.get(url, stream=True, timeout=timeout)
            response.raise_for_status()
            
            # Get file size
            file_size = int(response.headers.get("Content-Length", 0))
            
            # Save file
            with open(output_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # Get actual file size
            actual_size = os.path.getsize(output_path)
            
            logger.info(f"Downloaded file from URL {url} to {output_path}")
            
            return {
                "success": True,
                "url": url,
                "output_path": output_path,
                "file_size": actual_size,
                "expected_size": file_size,
                "content_type": response.headers.get("Content-Type")
            }
        except Exception as e:
            logger.error(f"Error downloading file from URL {url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "url": url,
                "output_path": output_path
            }
    
    def close(self) -> None:
        """Close the handler and clean up resources."""
        logger.info("Closing web handler")
        
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
            except Exception as e:
                logger.error(f"Error closing browser: {e}")
        
        try:
            self.session.close()
        except Exception as e:
            logger.error(f"Error closing session: {e}")


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    
    # Initialize handler
    handler = WebHandler()
    
    # Test visiting a website
    result = handler.visit(url="https://example.com")
    print(f"Visit result: {result}")
    
    # Test content extraction
    extract_result = handler.extract_content(
        url="https://example.com",
        selectors={
            "title": "h1",
            "paragraphs": "p"
        }
    )
    print(f"Extract result: {extract_result}")
    
    # Close handler
    handler.close()