"""
Fund Allocator

资金分配器，负责在多个钱包和账户之间分配资金。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime


class FundAllocator:
    """
    资金分配器
    
    负责根据策略和需求在多个钱包账户之间分配资金。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化资金分配器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 分配策略
        self.allocation_strategies = {
            "equal": self._equal_allocation,
            "weighted": self._weighted_allocation,
            "priority": self._priority_allocation,
            "risk_based": self._risk_based_allocation
        }
        
        # 分配历史
        self.allocation_history: List[Dict] = []
        
        # 分配统计
        self.allocation_stats = {
            'total_allocations': 0,
            'total_amount_allocated': 0.0,
            'successful_allocations': 0,
            'failed_allocations': 0
        }
    
    async def initialize(self) -> bool:
        """
        初始化资金分配器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("Fund Allocator initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Fund Allocator: {e}")
            return False
    
    async def allocate_funds(self, total_amount: float, accounts: List[Dict],
                           strategy: str = "equal", strategy_params: Dict = None) -> Dict[str, Any]:
        """
        分配资金
        
        Args:
            total_amount: 总金额
            accounts: 账户列表
            strategy: 分配策略
            strategy_params: 策略参数
            
        Returns:
            Dict[str, Any]: 分配结果
        """
        try:
            self.allocation_stats['total_allocations'] += 1
            
            if strategy not in self.allocation_strategies:
                self.logger.error(f"Unknown allocation strategy: {strategy}")
                self.allocation_stats['failed_allocations'] += 1
                return {"success": False, "error": "Unknown strategy"}
            
            # 执行分配策略
            allocation_func = self.allocation_strategies[strategy]
            allocation_result = await allocation_func(total_amount, accounts, strategy_params or {})
            
            if allocation_result["success"]:
                # 记录分配历史
                allocation_record = {
                    "allocation_id": f"alloc_{len(self.allocation_history) + 1}",
                    "total_amount": total_amount,
                    "strategy": strategy,
                    "accounts_count": len(accounts),
                    "allocations": allocation_result["allocations"],
                    "allocated_at": datetime.utcnow().isoformat()
                }
                
                self.allocation_history.append(allocation_record)
                self.allocation_stats['successful_allocations'] += 1
                self.allocation_stats['total_amount_allocated'] += total_amount
                
                self.logger.info(f"Successfully allocated {total_amount} using {strategy} strategy")
            else:
                self.allocation_stats['failed_allocations'] += 1
            
            return allocation_result
            
        except Exception as e:
            self.logger.error(f"Error allocating funds: {e}")
            self.allocation_stats['failed_allocations'] += 1
            return {"success": False, "error": str(e)}
    
    async def _equal_allocation(self, total_amount: float, accounts: List[Dict], params: Dict) -> Dict:
        """等额分配策略"""
        try:
            if not accounts:
                return {"success": False, "error": "No accounts provided"}
            
            amount_per_account = total_amount / len(accounts)
            allocations = []
            
            for account in accounts:
                allocations.append({
                    "account_id": account.get("id", "unknown"),
                    "account_address": account.get("address", ""),
                    "allocated_amount": amount_per_account,
                    "allocation_percentage": 100.0 / len(accounts)
                })
            
            return {
                "success": True,
                "strategy": "equal",
                "allocations": allocations,
                "total_allocated": total_amount
            }
            
        except Exception as e:
            self.logger.error(f"Error in equal allocation: {e}")
            return {"success": False, "error": str(e)}
    
    async def _weighted_allocation(self, total_amount: float, accounts: List[Dict], params: Dict) -> Dict:
        """加权分配策略"""
        try:
            weights = params.get("weights", [])
            if len(weights) != len(accounts):
                # 如果没有提供权重或权重数量不匹配，使用等权重
                weights = [1.0] * len(accounts)
            
            total_weight = sum(weights)
            if total_weight == 0:
                return {"success": False, "error": "Total weight cannot be zero"}
            
            allocations = []
            for i, account in enumerate(accounts):
                weight = weights[i]
                allocated_amount = total_amount * (weight / total_weight)
                allocation_percentage = (weight / total_weight) * 100
                
                allocations.append({
                    "account_id": account.get("id", "unknown"),
                    "account_address": account.get("address", ""),
                    "allocated_amount": allocated_amount,
                    "allocation_percentage": allocation_percentage,
                    "weight": weight
                })
            
            return {
                "success": True,
                "strategy": "weighted",
                "allocations": allocations,
                "total_allocated": total_amount,
                "weights_used": weights
            }
            
        except Exception as e:
            self.logger.error(f"Error in weighted allocation: {e}")
            return {"success": False, "error": str(e)}
    
    async def _priority_allocation(self, total_amount: float, accounts: List[Dict], params: Dict) -> Dict:
        """优先级分配策略"""
        try:
            # 按优先级排序账户
            sorted_accounts = sorted(accounts, key=lambda x: x.get("priority", 0), reverse=True)
            
            min_allocation = params.get("min_allocation", 0.0)
            allocations = []
            remaining_amount = total_amount
            
            for account in sorted_accounts:
                if remaining_amount <= 0:
                    allocated_amount = 0.0
                else:
                    # 高优先级账户获得更多资金
                    priority = account.get("priority", 1)
                    max_allocation = account.get("max_allocation", remaining_amount)
                    
                    allocated_amount = min(max_allocation, max(min_allocation, remaining_amount * 0.3))
                    remaining_amount -= allocated_amount
                
                allocations.append({
                    "account_id": account.get("id", "unknown"),
                    "account_address": account.get("address", ""),
                    "allocated_amount": allocated_amount,
                    "allocation_percentage": (allocated_amount / total_amount) * 100,
                    "priority": account.get("priority", 1)
                })
            
            return {
                "success": True,
                "strategy": "priority",
                "allocations": allocations,
                "total_allocated": total_amount - remaining_amount,
                "remaining_amount": remaining_amount
            }
            
        except Exception as e:
            self.logger.error(f"Error in priority allocation: {e}")
            return {"success": False, "error": str(e)}
    
    async def _risk_based_allocation(self, total_amount: float, accounts: List[Dict], params: Dict) -> Dict:
        """基于风险的分配策略"""
        try:
            risk_tolerance = params.get("risk_tolerance", 0.5)  # 0-1之间
            allocations = []
            
            for account in accounts:
                risk_level = account.get("risk_level", 0.5)  # 0-1之间，0为低风险
                
                # 根据风险容忍度和账户风险级别计算分配
                if risk_level <= risk_tolerance:
                    # 低风险账户获得更多资金
                    risk_factor = 1.0 - risk_level
                else:
                    # 高风险账户获得较少资金
                    risk_factor = 0.1
                
                allocated_amount = total_amount * risk_factor / len(accounts)
                
                allocations.append({
                    "account_id": account.get("id", "unknown"),
                    "account_address": account.get("address", ""),
                    "allocated_amount": allocated_amount,
                    "allocation_percentage": (allocated_amount / total_amount) * 100,
                    "risk_level": risk_level,
                    "risk_factor": risk_factor
                })
            
            return {
                "success": True,
                "strategy": "risk_based",
                "allocations": allocations,
                "total_allocated": sum(a["allocated_amount"] for a in allocations),
                "risk_tolerance": risk_tolerance
            }
            
        except Exception as e:
            self.logger.error(f"Error in risk-based allocation: {e}")
            return {"success": False, "error": str(e)}
