"""
Learning Module

Machine learning component that continuously learns from system operations
to improve performance, adapt to changes, and optimize strategies.
"""

import logging
import json
import pickle
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    # Fallback implementations
    class np:
        @staticmethod
        def mean(values):
            return sum(values) / len(values) if values else 0


class LearningModule:
    """
    Continuous learning system for AirHunter.
    
    Learns from:
    - Task execution outcomes and performance
    - Project success rates and characteristics
    - User behavior patterns and preferences
    - System performance and optimization opportunities
    """
    
    def __init__(self, learning_rate: float = 0.1, memory_size: int = 10000):
        """
        Initialize learning module.
        
        Args:
            learning_rate: Rate of learning adaptation
            memory_size: Maximum number of experiences to remember
        """
        self.learning_rate = learning_rate
        self.memory_size = memory_size
        self.logger = logging.getLogger(__name__)
        
        # Experience memory
        self.experiences = deque(maxlen=memory_size)
        
        # Learned knowledge
        self.knowledge_base = {
            'project_patterns': {},
            'task_strategies': {},
            'timing_patterns': {},
            'user_preferences': {},
            'performance_optimizations': {}
        }
        
        # Learning statistics
        self.stats = {
            'total_experiences': 0,
            'successful_adaptations': 0,
            'failed_adaptations': 0,
            'knowledge_items': 0,
            'last_learning': None
        }
        
        # Adaptation thresholds
        self.confidence_threshold = 0.7
        self.min_samples = 10
    
    def add_experience(self, experience_type: str, context: Dict[str, Any], 
                      action: str, outcome: Dict[str, Any], success: bool):
        """
        Add a new experience to the learning system.
        
        Args:
            experience_type: Type of experience (project, task, timing, etc.)
            context: Context information when action was taken
            action: Action that was taken
            outcome: Result of the action
            success: Whether the action was successful
        """
        try:
            experience = {
                'type': experience_type,
                'context': context,
                'action': action,
                'outcome': outcome,
                'success': success,
                'timestamp': datetime.utcnow(),
                'learned': False
            }
            
            self.experiences.append(experience)
            self.stats['total_experiences'] += 1
            
            # Trigger learning if we have enough similar experiences
            self._trigger_learning(experience_type)
            
            self.logger.debug(f"Added experience: {experience_type} - {'success' if success else 'failure'}")
            
        except Exception as e:
            self.logger.error(f"Error adding experience: {e}")
    
    def get_recommendation(self, context_type: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Get recommendation based on learned knowledge.
        
        Args:
            context_type: Type of context (project, task, timing, etc.)
            context: Current context information
            
        Returns:
            Optional[Dict[str, Any]]: Recommendation with confidence score
        """
        try:
            knowledge_key = f"{context_type}_patterns"
            if knowledge_key not in self.knowledge_base:
                return None
            
            patterns = self.knowledge_base[knowledge_key]
            if not patterns:
                return None
            
            # Find best matching pattern
            best_match = None
            best_score = 0
            
            for pattern_id, pattern in patterns.items():
                similarity = self._calculate_similarity(context, pattern['context'])
                if similarity > best_score and similarity > 0.5:
                    best_score = similarity
                    best_match = pattern
            
            if best_match and best_score > self.confidence_threshold:
                return {
                    'action': best_match['recommended_action'],
                    'confidence': best_score,
                    'reasoning': best_match.get('reasoning', []),
                    'expected_outcome': best_match.get('expected_outcome', {}),
                    'success_rate': best_match.get('success_rate', 0.5)
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting recommendation: {e}")
            return None
    
    def adapt_strategy(self, strategy_type: str, current_strategy: Dict[str, Any], 
                      performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adapt strategy based on performance data.
        
        Args:
            strategy_type: Type of strategy to adapt
            current_strategy: Current strategy parameters
            performance_data: Recent performance metrics
            
        Returns:
            Dict[str, Any]: Adapted strategy parameters
        """
        try:
            adapted_strategy = current_strategy.copy()
            
            # Get relevant experiences
            relevant_experiences = [
                exp for exp in self.experiences
                if exp['type'] == strategy_type and not exp['learned']
            ]
            
            if len(relevant_experiences) < self.min_samples:
                return adapted_strategy
            
            # Analyze performance patterns
            successful_experiences = [exp for exp in relevant_experiences if exp['success']]
            failed_experiences = [exp for exp in relevant_experiences if not exp['success']]
            
            if not successful_experiences:
                return adapted_strategy
            
            # Extract successful patterns
            successful_patterns = self._extract_patterns(successful_experiences)
            failed_patterns = self._extract_patterns(failed_experiences)
            
            # Adapt strategy parameters
            for param, value in current_strategy.items():
                if param in successful_patterns:
                    successful_values = successful_patterns[param]
                    failed_values = failed_patterns.get(param, [])
                    
                    # Calculate optimal value
                    if successful_values:
                        optimal_value = np.mean(successful_values)
                        
                        # Adjust current value towards optimal
                        adjustment = (optimal_value - value) * self.learning_rate
                        adapted_strategy[param] = value + adjustment
            
            # Mark experiences as learned
            for exp in relevant_experiences:
                exp['learned'] = True
            
            self.stats['successful_adaptations'] += 1
            self.stats['last_learning'] = datetime.utcnow()
            
            self.logger.info(f"Adapted strategy for {strategy_type}")
            return adapted_strategy
            
        except Exception as e:
            self.logger.error(f"Error adapting strategy: {e}")
            self.stats['failed_adaptations'] += 1
            return current_strategy
    
    def get_learning_insights(self) -> Dict[str, Any]:
        """
        Get insights about the learning process.
        
        Returns:
            Dict[str, Any]: Learning insights and statistics
        """
        try:
            # Calculate success rates by experience type
            type_stats = defaultdict(lambda: {'total': 0, 'successful': 0})
            
            for exp in self.experiences:
                exp_type = exp['type']
                type_stats[exp_type]['total'] += 1
                if exp['success']:
                    type_stats[exp_type]['successful'] += 1
            
            success_rates = {}
            for exp_type, stats in type_stats.items():
                if stats['total'] > 0:
                    success_rates[exp_type] = stats['successful'] / stats['total']
            
            # Get knowledge base statistics
            knowledge_stats = {}
            for knowledge_type, knowledge in self.knowledge_base.items():
                knowledge_stats[knowledge_type] = len(knowledge)
            
            # Recent learning activity
            recent_time = datetime.utcnow() - timedelta(hours=24)
            recent_experiences = [
                exp for exp in self.experiences 
                if exp['timestamp'] > recent_time
            ]
            
            return {
                'total_experiences': len(self.experiences),
                'recent_experiences_24h': len(recent_experiences),
                'success_rates_by_type': success_rates,
                'knowledge_base_stats': knowledge_stats,
                'learning_statistics': self.stats,
                'adaptation_rate': (
                    self.stats['successful_adaptations'] / 
                    max(1, self.stats['successful_adaptations'] + self.stats['failed_adaptations'])
                ),
                'memory_utilization': len(self.experiences) / self.memory_size
            }
            
        except Exception as e:
            self.logger.error(f"Error generating learning insights: {e}")
            return {'error': str(e)}
    
    def save_knowledge(self, filepath: str) -> bool:
        """
        Save learned knowledge to file.
        
        Args:
            filepath: Path to save knowledge
            
        Returns:
            bool: True if successful
        """
        try:
            data = {
                'knowledge_base': self.knowledge_base,
                'stats': self.stats,
                'learning_rate': self.learning_rate,
                'saved_at': datetime.utcnow().isoformat()
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            self.logger.info(f"Knowledge saved to {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save knowledge: {e}")
            return False
    
    def load_knowledge(self, filepath: str) -> bool:
        """
        Load learned knowledge from file.
        
        Args:
            filepath: Path to load knowledge from
            
        Returns:
            bool: True if successful
        """
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            self.knowledge_base = data.get('knowledge_base', {})
            self.stats.update(data.get('stats', {}))
            self.learning_rate = data.get('learning_rate', self.learning_rate)
            
            self.logger.info(f"Knowledge loaded from {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load knowledge: {e}")
            return False
    
    def _trigger_learning(self, experience_type: str):
        """Trigger learning process for specific experience type."""
        try:
            # Get recent experiences of this type
            recent_experiences = [
                exp for exp in self.experiences
                if exp['type'] == experience_type and not exp['learned']
            ]
            
            if len(recent_experiences) >= self.min_samples:
                self._learn_patterns(experience_type, recent_experiences)
                
        except Exception as e:
            self.logger.error(f"Error triggering learning: {e}")
    
    def _learn_patterns(self, experience_type: str, experiences: List[Dict[str, Any]]):
        """Learn patterns from experiences."""
        try:
            successful_experiences = [exp for exp in experiences if exp['success']]
            
            if len(successful_experiences) < 3:  # Need minimum successful examples
                return
            
            # Extract common patterns from successful experiences
            patterns = {}
            
            for i, exp in enumerate(successful_experiences):
                pattern_id = f"{experience_type}_pattern_{i}"
                
                # Calculate success rate for similar contexts
                similar_experiences = [
                    e for e in experiences
                    if self._calculate_similarity(exp['context'], e['context']) > 0.7
                ]
                
                success_count = len([e for e in similar_experiences if e['success']])
                success_rate = success_count / len(similar_experiences) if similar_experiences else 0
                
                if success_rate > 0.6:  # Only learn from patterns with good success rate
                    patterns[pattern_id] = {
                        'context': exp['context'],
                        'recommended_action': exp['action'],
                        'expected_outcome': exp['outcome'],
                        'success_rate': success_rate,
                        'sample_size': len(similar_experiences),
                        'learned_at': datetime.utcnow(),
                        'reasoning': [f"Based on {len(similar_experiences)} similar experiences"]
                    }
            
            # Update knowledge base
            knowledge_key = f"{experience_type}_patterns"
            if knowledge_key not in self.knowledge_base:
                self.knowledge_base[knowledge_key] = {}
            
            self.knowledge_base[knowledge_key].update(patterns)
            
            # Mark experiences as learned
            for exp in experiences:
                exp['learned'] = True
            
            self.stats['knowledge_items'] = sum(
                len(knowledge) for knowledge in self.knowledge_base.values()
            )
            
            self.logger.info(f"Learned {len(patterns)} patterns for {experience_type}")
            
        except Exception as e:
            self.logger.error(f"Error learning patterns: {e}")
    
    def _calculate_similarity(self, context1: Dict[str, Any], context2: Dict[str, Any]) -> float:
        """Calculate similarity between two contexts."""
        try:
            if not context1 or not context2:
                return 0.0
            
            common_keys = set(context1.keys()) & set(context2.keys())
            if not common_keys:
                return 0.0
            
            similarities = []
            
            for key in common_keys:
                val1, val2 = context1[key], context2[key]
                
                if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    # Numerical similarity
                    max_val = max(abs(val1), abs(val2), 1)
                    similarity = 1 - abs(val1 - val2) / max_val
                elif isinstance(val1, str) and isinstance(val2, str):
                    # String similarity (simple exact match)
                    similarity = 1.0 if val1 == val2 else 0.0
                elif isinstance(val1, bool) and isinstance(val2, bool):
                    # Boolean similarity
                    similarity = 1.0 if val1 == val2 else 0.0
                else:
                    # Default similarity for other types
                    similarity = 1.0 if val1 == val2 else 0.0
                
                similarities.append(similarity)
            
            return np.mean(similarities) if similarities else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating similarity: {e}")
            return 0.0
    
    def _extract_patterns(self, experiences: List[Dict[str, Any]]) -> Dict[str, List[Any]]:
        """Extract parameter patterns from experiences."""
        patterns = defaultdict(list)
        
        for exp in experiences:
            context = exp.get('context', {})
            for param, value in context.items():
                if isinstance(value, (int, float)):
                    patterns[param].append(value)
        
        return dict(patterns)
