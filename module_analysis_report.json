{"timestamp": "2025-06-08T23:13:29.527476", "overall_stats": {"total_agents": 10, "analyzed_agents": 10, "total_files": 333, "total_classes": 264, "total_methods": 1433, "total_lines": 41618, "agents_with_main": 10, "agents_with_tests": 2, "completion_scores": [90.0, 90.0, 94.0, 90.0, 90.0, 84.0, 90.0, 84.0, 100.0, 90.0], "average_completion": 90.2}, "agent_details": {"coordinator": {"name": "coordinator", "description": "协调控制智能体", "file_count": 40, "class_count": 32, "method_count": 281, "total_lines": 6405, "has_main_file": true, "has_init_file": true, "has_tests": false, "modules": {"coordinator.py": {"path": "coordinator\\coordinator.py", "line_count": 158, "class_count": 1, "method_count": 7, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["Coordinator"], "functions": ["__init__", "start", "stop", "register_agent", "get_agent", "get_system_status", "execute_workflow"], "imports": ["logging", "typing", "coordinator.core.agent_registry", "coordinator.core.lifecycle_manager", "coordinator.core.system_state", "coordinator.workflow.workflow_manager", "coordinator.resources.resource_allocator", "coordinator.communication.message_broker", "coordinator.communication.event_system", "coordinator.monitoring.health_monitor", "coordinator.recovery.error_handler", "coordinator.interface.logging_service"]}, "coordinator_agent.py": {"path": "coordinator\\coordinator_agent.py", "line_count": 306, "class_count": 1, "method_count": 18, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["CoordinatorAgent"], "functions": ["__init__", "_init_core_components", "_init_communication_components", "_init_resource_components", "_init_workflow_components", "_init_monitoring_components", "_init_recovery_components", "start", "stop", "_start_all_agents", "_stop_all_agents", "register_agent", "unregister_agent", "get_agent", "get_all_agents", "get_system_status", "execute_workflow", "get_agent_info"], "imports": ["logging", "asyncio", "typing", "datetime", "coordinator.core.agent_registry", "coordinator.core.lifecycle_manager", "coordinator.core.system_state", "coordinator.workflow.workflow_manager", "coordinator.resources.resource_allocator", "coordinator.communication.message_broker", "coordinator.communication.event_system", "coordinator.monitoring.health_monitor", "coordinator.recovery.error_handler", "coordinator.interface.logging_service"]}, "__init__.py": {"path": "coordinator\\__init__.py", "line_count": 8, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "communication\\command_dispatcher.py": {"path": "coordinator\\communication\\command_dispatcher.py", "line_count": 395, "class_count": 1, "method_count": 13, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["Command<PERSON><PERSON><PERSON><PERSON><PERSON>"], "functions": ["__init__", "start", "stop", "register_handler", "unregister_handler", "send_command", "send_command_async", "_handle_command", "_handle_response", "_send_response", "cleanup_pending_commands", "get_pending_commands", "get_registered_command_types"], "imports": ["logging", "threading", "uuid", "time", "typing", "coordinator.communication.message_broker"]}, "communication\\event_system.py": {"path": "coordinator\\communication\\event_system.py", "line_count": 212, "class_count": 1, "method_count": 10, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["EventSystem"], "functions": ["__init__", "start", "stop", "subscribe", "unsubscribe", "publish", "_handle_event", "_find_callback", "get_event_types", "get_subscribers"], "imports": ["logging", "threading", "typing", "coordinator.communication.message_broker"]}, "communication\\message_broker.py": {"path": "coordinator\\communication\\message_broker.py", "line_count": 269, "class_count": 1, "method_count": 12, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["MessageBroker"], "functions": ["__init__", "start", "stop", "create_topic", "delete_topic", "subscribe", "unsubscribe", "publish", "_worker", "get_topics", "get_subscribers", "is_running"], "imports": ["logging", "threading", "queue", "uuid", "typing"]}, "communication\\response_collector.py": {"path": "coordinator\\communication\\response_collector.py", "line_count": 315, "class_count": 1, "method_count": 11, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ResponseCollector"], "functions": ["__init__", "collect_responses", "collect_responses_async", "_send_command_to_agent", "_handle_agent_response", "_complete_task", "_check_timeout", "cancel_task", "get_active_tasks", "cleanup_tasks", "response_callback"], "imports": ["logging", "threading", "time", "uuid", "typing", "coordinator.communication.message_broker", "coordinator.communication.command_dispatcher"]}, "communication\\__init__.py": {"path": "coordinator\\communication\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "core\\agent_registry.py": {"path": "coordinator\\core\\agent_registry.py", "line_count": 104, "class_count": 1, "method_count": 8, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AgentRegistry"], "functions": ["__init__", "register", "unregister", "get", "get_all", "get_names", "exists", "count"], "imports": ["logging", "typing"]}, "core\\dependency_resolver.py": {"path": "coordinator\\core\\dependency_resolver.py", "line_count": 198, "class_count": 1, "method_count": 10, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["DependencyResolver"], "functions": ["__init__", "register_dependency", "unregister_dependency", "get_dependencies", "get_all_dependencies", "_collect_dependencies", "get_dependents", "get_startup_order", "get_shutdown_order", "check_circular_dependencies"], "imports": ["logging", "typing", "networkx", "coordinator.core.agent_registry"]}, "core\\lifecycle_manager.py": {"path": "coordinator\\core\\lifecycle_manager.py", "line_count": 154, "class_count": 1, "method_count": 8, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["LifecycleManager"], "functions": ["__init__", "start_agent", "stop_agent", "restart_agent", "start_all_agents", "stop_all_agents", "get_running_agents", "is_agent_running"], "imports": ["logging", "threading", "typing", "coordinator.core.agent_registry"]}, "core\\system_state.py": {"path": "coordinator\\core\\system_state.py", "line_count": 248, "class_count": 1, "method_count": 13, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SystemState"], "functions": ["__init__", "set_system_status", "set_agent_status", "set_agent_metric", "add_error", "add_warning", "update_resources", "get_status", "get_agent_status", "get_errors", "get_warnings", "clear_errors", "clear_warnings"], "imports": ["logging", "threading", "time", "typing"]}, "core\\__init__.py": {"path": "coordinator\\core\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "interface\\api_gateway.py": {"path": "coordinator\\interface\\api_gateway.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["APIGateway"], "functions": ["__init__", "handle_request"], "imports": ["logging"]}, "interface\\logging_service.py": {"path": "coordinator\\interface\\logging_service.py", "line_count": 198, "class_count": 1, "method_count": 9, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["LoggingService"], "functions": ["__init__", "setup", "_get_log_level", "set_level", "get_level", "get_logger", "set_module_level", "get_module_level", "get_config"], "imports": ["logging", "logging.handlers", "os", "sys", "typing"]}, "interface\\metrics_reporter.py": {"path": "coordinator\\interface\\metrics_reporter.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["MetricsReporter"], "functions": ["__init__", "report_metrics"], "imports": ["logging"]}, "interface\\ui_connector.py": {"path": "coordinator\\interface\\ui_connector.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["UIConnector"], "functions": ["__init__", "connect_ui"], "imports": ["logging"]}, "interface\\__init__.py": {"path": "coordinator\\interface\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "monitoring\\alert_manager.py": {"path": "coordinator\\monitoring\\alert_manager.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "functions": ["__init__", "manage_alert"], "imports": ["logging"]}, "monitoring\\bottleneck_detector.py": {"path": "coordinator\\monitoring\\bottleneck_detector.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BottleneckDetector"], "functions": ["__init__", "detect_bottlenecks"], "imports": ["logging"]}, "monitoring\\health_monitor.py": {"path": "coordinator\\monitoring\\health_monitor.py", "line_count": 452, "class_count": 1, "method_count": 18, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["HealthMonitor"], "functions": ["__init__", "start", "stop", "register_health_check", "unregister_health_check", "register_agent_health_check", "unregister_agent_health_check", "unregister_agent", "_monitor_loop", "_check_system_health", "_check_agent_health", "run_health_check", "run_agent_health_check", "run_all_health_checks", "run_all_agent_health_checks", "get_health_checks", "get_agent_health_checks", "get_all_agent_health_checks"], "imports": ["logging", "threading", "time", "typing", "coordinator.core.system_state", "coordinator.communication.event_system"]}, "monitoring\\performance_monitor.py": {"path": "coordinator\\monitoring\\performance_monitor.py", "line_count": 541, "class_count": 1, "method_count": 25, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PerformanceMonitor"], "functions": ["__init__", "start", "stop", "register_metric_collector", "unregister_metric_collector", "register_agent_metric_collector", "unregister_agent_metric_collector", "unregister_agent", "_monitor_loop", "_collect_metrics", "_collect_agent_metrics", "_update_system_state", "_collect_cpu_metrics", "_collect_memory_metrics", "_collect_disk_metrics", "_collect_network_metrics", "get_latest_metrics", "get_metrics_history", "get_agent_latest_metrics", "get_agent_metrics_history", "get_all_latest_metrics", "get_all_agent_latest_metrics", "get_metric_collectors", "get_agent_metric_collectors", "get_all_agent_metric_collectors"], "imports": ["logging", "threading", "time", "psutil", "os", "typing", "coordinator.core.system_state", "coordinator.communication.event_system"]}, "monitoring\\performance_tracker.py": {"path": "coordinator\\monitoring\\performance_tracker.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PerformanceTracker"], "functions": ["__init__", "track_performance"], "imports": ["logging"]}, "monitoring\\__init__.py": {"path": "coordinator\\monitoring\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "recovery\\auto_recovery.py": {"path": "coordinator\\recovery\\auto_recovery.py", "line_count": 498, "class_count": 1, "method_count": 17, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AutoRecovery"], "functions": ["__init__", "register_recovery_strategy", "unregister_recovery_strategy", "register_agent_recovery_strategy", "unregister_agent_recovery_strategy", "unregister_agent", "recover", "recover_agent", "_handle_system_error", "_handle_agent_error", "_restart_agent_strategy", "_add_recovery_history", "get_recovery_history", "get_agent_recovery_history", "get_recovery_strategies", "get_agent_recovery_strategies", "get_all_agent_recovery_strategies"], "imports": ["logging", "threading", "time", "traceback", "typing", "coordinator.core.system_state", "coordinator.core.lifecycle_manager", "coordinator.communication.event_system"]}, "recovery\\checkpoint_manager.py": {"path": "coordinator\\recovery\\checkpoint_manager.py", "line_count": 15, "class_count": 1, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["CheckpointManager"], "functions": ["__init__", "create_checkpoint", "restore_checkpoint"], "imports": ["logging"]}, "recovery\\error_handler.py": {"path": "coordinator\\recovery\\error_handler.py", "line_count": 336, "class_count": 1, "method_count": 13, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "functions": ["__init__", "register_error_handler", "unregister_error_handler", "register_agent_error_handler", "unregister_agent_error_handler", "unregister_agent", "handle_error", "handle_agent_error", "_handle_system_error", "_handle_agent_error", "get_error_handlers", "get_agent_error_handlers", "get_all_agent_error_handlers"], "imports": ["logging", "threading", "time", "traceback", "typing", "coordinator.core.system_state", "coordinator.communication.event_system"]}, "recovery\\fault_tolerance.py": {"path": "coordinator\\recovery\\fault_tolerance.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["FaultTolerance"], "functions": ["__init__", "handle_fault"], "imports": ["logging"]}, "recovery\\recovery_orchestrator.py": {"path": "coordinator\\recovery\\recovery_orchestrator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["RecoveryOrchestrator"], "functions": ["__init__", "orchestrate_recovery"], "imports": ["logging"]}, "recovery\\__init__.py": {"path": "coordinator\\recovery\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "resources\\load_balancer.py": {"path": "coordinator\\resources\\load_balancer.py", "line_count": 420, "class_count": 1, "method_count": 19, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["LoadBalancer"], "functions": ["__init__", "register_agent", "unregister_agent", "create_group", "delete_group", "add_agent_to_group", "remove_agent_from_group", "update_agent_load", "update_agent_capacity", "select_agent", "select_agents", "get_agent_load", "get_agent_capacity", "get_agent_load_ratio", "get_all_agent_loads", "get_all_agent_capacities", "get_all_agent_load_ratios", "get_group", "get_all_groups"], "imports": ["logging", "threading", "time", "random", "typing", "coordinator.core.agent_registry"]}, "resources\\resource_allocator.py": {"path": "coordinator\\resources\\resource_allocator.py", "line_count": 305, "class_count": 1, "method_count": 10, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ResourceAllocator"], "functions": ["__init__", "allocate", "release", "get_resource_status", "get_task_allocations", "add_resource", "remove_resource", "update_resource", "reserve_resource", "release_reserved_resource"], "imports": ["logging", "threading", "time", "typing"]}, "resources\\resource_monitor.py": {"path": "coordinator\\resources\\resource_monitor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ResourceMonitor"], "functions": ["__init__", "monitor_resources"], "imports": ["logging"]}, "resources\\throttle_manager.py": {"path": "coordinator\\resources\\throttle_manager.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ThrottleManager"], "functions": ["__init__", "apply_throttle"], "imports": ["logging"]}, "resources\\__init__.py": {"path": "coordinator\\resources\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "workflow\\pipeline_builder.py": {"path": "coordinator\\workflow\\pipeline_builder.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PipelineBuilder"], "functions": ["__init__", "build_pipeline"], "imports": ["logging"]}, "workflow\\task_orchestrator.py": {"path": "coordinator\\workflow\\task_orchestrator.py", "line_count": 550, "class_count": 1, "method_count": 18, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TaskOrchestrator"], "functions": ["__init__", "register_task_type", "unregister_task_type", "create_task", "start_task", "_assign_agents", "_allocate_resources", "_execute_task", "_execute_task_steps", "_update_task_progress", "_handle_task_timeout", "_release_resources", "cancel_task", "get_task", "get_all_tasks", "get_task_type", "get_all_task_types", "cleanup_tasks"], "imports": ["logging", "threading", "time", "uuid", "typing", "coordinator.core.agent_registry", "coordinator.communication.message_broker", "coordinator.communication.command_dispatcher", "coordinator.resources.resource_allocator"]}, "workflow\\workflow_manager.py": {"path": "coordinator\\workflow\\workflow_manager.py", "line_count": 556, "class_count": 1, "method_count": 15, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["WorkflowManager"], "functions": ["__init__", "register_workflow", "unregister_workflow", "execute", "execute_async", "_execute_workflow_async", "_execute_workflow_steps", "_execute_parallel_step", "_evaluate_condition", "get_workflow", "get_all_workflows", "get_workflow_status", "get_all_running_workflows", "cancel_workflow", "cleanup_workflows"], "imports": ["logging", "threading", "time", "uuid", "typing", "coordinator.core.agent_registry", "coordinator.communication.message_broker", "coordinator.resources.resource_allocator"]}, "workflow\\workflow_optimizer.py": {"path": "coordinator\\workflow\\workflow_optimizer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["WorkflowOptimizer"], "functions": ["__init__", "optimize_workflow"], "imports": ["logging"]}, "workflow\\__init__.py": {"path": "coordinator\\workflow\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}}, "main_agent_file": "coordinator/coordinator_agent.py", "completion_score": 90.0, "quality_indicators": {"has_docstrings": 32, "has_error_handling": 13, "has_logging": 32, "has_type_hints": 32, "has_async_methods": 1}}, "discovery": {"name": "discovery", "description": "项目发现智能体", "file_count": 34, "class_count": 39, "method_count": 160, "total_lines": 6656, "has_main_file": true, "has_init_file": true, "has_tests": false, "modules": {"discovery_agent.py": {"path": "discovery\\discovery_agent.py", "line_count": 489, "class_count": 1, "method_count": 14, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["DiscoveryAgent"], "functions": ["__init__", "start", "stop", "get_project", "get_projects", "add_project", "update_project", "remove_project", "get_stats", "_discovery_loop", "_discover_projects", "_save_projects", "_load_projects", "clear_projects"], "imports": ["logging", "threading", "time", "json", "os", "typing", "discovery.models.project", "discovery.sources.source_manager", "discovery.filters.filter_manager", "discovery.collectors.collector_manager"]}, "main.py": {"path": "discovery\\main.py", "line_count": 116, "class_count": 0, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": [], "functions": ["setup_logging", "load_config", "main"], "imports": ["os", "sys", "logging", "<PERSON><PERSON><PERSON><PERSON>", "json", "time", "typing", "discovery.discovery_agent"]}, "__init__.py": {"path": "discovery\\__init__.py", "line_count": 7, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "collectors\\base_collector.py": {"path": "discovery\\collectors\\base_collector.py", "line_count": 46, "class_count": 1, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BaseCollector"], "functions": ["__init__", "collect", "update_config"], "imports": ["logging", "abc", "typing"]}, "collectors\\blockchain_collector.py": {"path": "discovery\\collectors\\blockchain_collector.py", "line_count": 540, "class_count": 1, "method_count": 11, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BlockchainCollector"], "functions": ["__init__", "collect", "_extract_contract_address_from_url", "_collect_ethereum_info", "_collect_binance_info", "_collect_polygon_info", "_collect_solana_info", "_collect_avalanche_info", "_collect_arbitrum_info", "_collect_optimism_info", "_collect_base_info"], "imports": ["logging", "requests", "time", "random", "re", "typing", "discovery.collectors.base_collector"]}, "collectors\\collector_manager.py": {"path": "discovery\\collectors\\collector_manager.py", "line_count": 100, "class_count": 1, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["CollectorManager"], "functions": ["__init__", "collect_project_info", "update_config"], "imports": ["logging", "threading", "typing", "discovery.collectors.base_collector", "discovery.collectors.web_collector", "discovery.collectors.social_collector", "discovery.collectors.blockchain_collector"]}, "collectors\\condition_collector.py": {"path": "discovery\\collectors\\condition_collector.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ConditionCollector"], "functions": ["__init__", "collect_conditions"], "imports": ["logging"]}, "collectors\\project_collector.py": {"path": "discovery\\collectors\\project_collector.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProjectCollector"], "functions": ["__init__", "collect_project_info"], "imports": ["logging"]}, "collectors\\social_collector.py": {"path": "discovery\\collectors\\social_collector.py", "line_count": 680, "class_count": 1, "method_count": 16, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["SocialCollector"], "functions": ["__init__", "collect", "_collect_social_channel_info", "_collect_twitter_info", "_collect_twitter_info_via_api", "_collect_twitter_info_via_scraping", "_collect_telegram_info", "_collect_telegram_info_via_api", "_collect_telegram_info_via_scraping", "_collect_discord_info", "_collect_discord_info_via_api", "_collect_discord_info_via_scraping", "_collect_medium_info", "_collect_github_info", "get_channel_info", "get_invite_info"], "imports": ["logging", "requests", "time", "random", "re", "typing", "bs4", "discovery.collectors.base_collector", "tweepy", "telethon", "asyncio", "discord", "asyncio"]}, "collectors\\web_collector.py": {"path": "discovery\\collectors\\web_collector.py", "line_count": 674, "class_count": 1, "method_count": 11, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["WebCollector"], "functions": ["__init__", "collect", "_get_website_content", "_extract_title", "_extract_description", "_extract_social_links", "_extract_requirements", "_extract_token_info", "_extract_project_type", "_extract_blockchain", "_extract_time_info"], "imports": ["logging", "requests", "time", "random", "re", "typing", "bs4", "urllib.parse", "discovery.collectors.base_collector", "dateparser", "dateparser"]}, "collectors\\__init__.py": {"path": "discovery\\collectors\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "filters\\duplicate_filter.py": {"path": "discovery\\filters\\duplicate_filter.py", "line_count": 142, "class_count": 1, "method_count": 6, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["DuplicateFilter"], "functions": ["__init__", "filter", "_is_unique_project", "_calculate_content_hash", "reset", "update_config"], "imports": ["logging", "<PERSON><PERSON><PERSON>", "typing"]}, "filters\\filter_manager.py": {"path": "discovery\\filters\\filter_manager.py", "line_count": 96, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["FilterManager"], "functions": ["__init__", "filter_projects", "reset_filters", "update_config"], "imports": ["logging", "threading", "typing", "discovery.filters.keyword_filter", "discovery.filters.relevance_filter", "discovery.filters.duplicate_filter"]}, "filters\\keyword_filter.py": {"path": "discovery\\filters\\keyword_filter.py", "line_count": 143, "class_count": 1, "method_count": 5, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["KeywordFilter"], "functions": ["__init__", "filter", "_is_valid_project", "_extract_project_text", "update_config"], "imports": ["logging", "re", "typing"]}, "filters\\relevance_filter.py": {"path": "discovery\\filters\\relevance_filter.py", "line_count": 183, "class_count": 1, "method_count": 5, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["RelevanceFilter"], "functions": ["__init__", "filter", "_calculate_relevance_score", "_extract_project_text", "update_config"], "imports": ["logging", "re", "typing"]}, "filters\\__init__.py": {"path": "discovery\\filters\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "models\\enums.py": {"path": "discovery\\models\\enums.py", "line_count": 31, "class_count": 5, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": ["ProjectStatus", "ProjectType", "SourceType", "ProjectRisk", "VerificationStatus"], "functions": [], "imports": ["enum"]}, "models\\project.py": {"path": "discovery\\models\\project.py", "line_count": 449, "class_count": 7, "method_count": 10, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": false, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProjectStatus", "ProjectType", "BlockchainPlatform", "SocialChannel", "ProjectRequirement", "TokenInfo", "Project"], "functions": ["__post_init__", "update", "add_social_channel", "add_requirement", "set_token_info", "to_dict", "from_dict", "to_json", "from_json", "__str__"], "imports": ["time", "typing", "dataclasses", "enum", "json", "uuid"]}, "models\\__init__.py": {"path": "discovery\\models\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "sources\\base_source.py": {"path": "discovery\\sources\\base_source.py", "line_count": 35, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BaseProjectSource"], "functions": ["__init__", "get_projects"], "imports": ["logging", "abc", "typing"]}, "sources\\blockchain_scanner.py": {"path": "discovery\\sources\\blockchain_scanner.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BlockchainScanner"], "functions": ["__init__", "scan_blockchain"], "imports": ["logging"]}, "sources\\blockchain_source.py": {"path": "discovery\\sources\\blockchain_source.py", "line_count": 832, "class_count": 1, "method_count": 11, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BlockchainSource"], "functions": ["__init__", "get_projects", "_get_ethereum_projects", "_get_binance_projects", "_get_solana_projects", "_get_polygon_projects", "_get_avalanche_projects", "_get_arbitrum_projects", "_get_optimism_projects", "_get_base_projects", "_get_dappradar_projects"], "imports": ["logging", "requests", "time", "random", "re", "typing", "discovery.sources.base_source"]}, "sources\\discord_scanner.py": {"path": "discovery\\sources\\discord_scanner.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["DiscordScanner"], "functions": ["__init__", "scan_discord"], "imports": ["logging"]}, "sources\\discord_source.py": {"path": "discovery\\sources\\discord_source.py", "line_count": 323, "class_count": 2, "method_count": 9, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["DiscordSource", "DiscordClient"], "functions": ["__init__", "_init_api_client", "get_projects", "_get_projects_via_api", "_get_projects_via_simulation", "_extract_project_info_from_message", "fetch_messages", "__init__", "on_ready"], "imports": ["logging", "time", "random", "re", "typing", "discovery.sources.base_source", "discord", "discord", "asyncio"]}, "sources\\github_scanner.py": {"path": "discovery\\sources\\github_scanner.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["GitHubScanner"], "functions": ["__init__", "scan_github"], "imports": ["logging"]}, "sources\\github_source.py": {"path": "discovery\\sources\\github_source.py", "line_count": 428, "class_count": 1, "method_count": 6, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["GitHubSource"], "functions": ["__init__", "get_projects", "_get_projects_via_api", "_get_projects_via_scraping", "_extract_project_info_from_repo", "_extract_project_info_from_html"], "imports": ["logging", "requests", "time", "random", "re", "typing", "datetime", "discovery.sources.base_source"]}, "sources\\medium_scanner.py": {"path": "discovery\\sources\\medium_scanner.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["MediumScanner"], "functions": ["__init__", "scan_medium"], "imports": ["logging"]}, "sources\\medium_source.py": {"path": "discovery\\sources\\medium_source.py", "line_count": 345, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["MediumSource"], "functions": ["__init__", "get_projects", "_get_article_content", "_extract_project_info_from_article"], "imports": ["logging", "requests", "time", "random", "re", "typing", "bs4", "discovery.sources.base_source"]}, "sources\\source_manager.py": {"path": "discovery\\sources\\source_manager.py", "line_count": 251, "class_count": 1, "method_count": 8, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SourceManager"], "functions": ["__init__", "_init_sources", "get_sources", "get_source", "get_projects", "_calculate_source_counts", "get_status", "get_source_names"], "imports": ["logging", "threading", "time", "typing", "discovery.sources.base_source", "discovery.sources.twitter_source", "discovery.sources.discord_source", "discovery.sources.telegram_source", "discovery.sources.medium_source", "discovery.sources.github_source", "discovery.sources.blockchain_source"]}, "sources\\telegram_scanner.py": {"path": "discovery\\sources\\telegram_scanner.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TelegramScanner"], "functions": ["__init__", "scan_telegram"], "imports": ["logging"]}, "sources\\telegram_source.py": {"path": "discovery\\sources\\telegram_source.py", "line_count": 291, "class_count": 1, "method_count": 7, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["TelegramSource"], "functions": ["__init__", "_init_api_client", "get_projects", "_get_projects_via_api", "_get_projects_via_simulation", "_extract_project_info_from_message", "fetch_messages"], "imports": ["logging", "time", "random", "re", "typing", "discovery.sources.base_source", "telethon", "telethon", "asyncio"]}, "sources\\twitter_scanner.py": {"path": "discovery\\sources\\twitter_scanner.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TwitterScanner"], "functions": ["__init__", "scan_twitter"], "imports": ["logging"]}, "sources\\twitter_source.py": {"path": "discovery\\sources\\twitter_source.py", "line_count": 347, "class_count": 1, "method_count": 6, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TwitterSource"], "functions": ["__init__", "_init_api_client", "get_projects", "_get_projects_via_api", "_get_projects_via_scraping", "_extract_project_info_from_tweet"], "imports": ["logging", "requests", "time", "random", "re", "typing", "bs4", "discovery.sources.base_source", "tweepy", "tweepy"]}, "sources\\__init__.py": {"path": "discovery\\sources\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}}, "main_agent_file": "discovery/discovery_agent.py", "completion_score": 90.0, "quality_indicators": {"has_docstrings": 28, "has_error_handling": 13, "has_logging": 27, "has_type_hints": 28, "has_async_methods": 3}}, "assessment": {"name": "assessment", "description": "项目评估智能体", "file_count": 15, "class_count": 9, "method_count": 89, "total_lines": 6927, "has_main_file": true, "has_init_file": true, "has_tests": true, "modules": {"assessment_agent.py": {"path": "assessment\\assessment_agent.py", "line_count": 355, "class_count": 1, "method_count": 9, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AssessmentAgent"], "functions": ["__init__", "start", "stop", "assess_project", "assess_projects", "get_stats", "_assessment_loop", "_get_projects_to_assess", "_get_discovery_agent"], "imports": ["logging", "threading", "time", "typing", "assessment.verification.team_verifier", "assessment.verification.social_verifier", "assessment.verification.project_verifier", "assessment.security.contract_analyzer", "assessment.security.vulnerability_scanner", "assessment.security.permission_analyzer", "assessment.risk.risk_calculator", "assessment.risk.reward_estimator", "assessment.risk.score_generator", "discovery.models.project", "discovery.discovery_agent", "coordinator.core.agent_registry"]}, "main.py": {"path": "assessment\\main.py", "line_count": 259, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "__init__.py": {"path": "assessment\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "risk\\reward_estimator.py": {"path": "assessment\\risk\\reward_estimator.py", "line_count": 786, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "risk\\risk_calculator.py": {"path": "assessment\\risk\\risk_calculator.py", "line_count": 941, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "risk\\score_generator.py": {"path": "assessment\\risk\\score_generator.py", "line_count": 537, "class_count": 1, "method_count": 13, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ScoreGenerator"], "functions": ["__init__", "generate", "_generate_community_score", "_generate_team_score", "_calculate_social_activity_score", "_calculate_followers_count_score", "_calculate_engagement_level_score", "_calculate_growth_rate_score", "_calculate_experience_score", "_calculate_background_score", "_calculate_transparency_score", "_calculate_track_record_score", "get_stats"], "imports": ["logging", "typing", "discovery.models.project", "time"]}, "risk\\__init__.py": {"path": "assessment\\risk\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "security\\contract_analyzer.py": {"path": "assessment\\security\\contract_analyzer.py", "line_count": 788, "class_count": 1, "method_count": 12, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ContractAnalyzer"], "functions": ["__init__", "analyze", "_analyze_ethereum_contract", "_analyze_binance_contract", "_analyze_polygon_contract", "_analyze_arbitrum_contract", "_analyze_optimism_contract", "_analyze_base_contract", "_is_token_contract", "_detect_token_standard", "_analyze_source_code", "get_stats"], "imports": ["logging", "requests", "time", "re", "typing", "discovery.models.project", "json", "json"]}, "security\\permission_analyzer.py": {"path": "assessment\\security\\permission_analyzer.py", "line_count": 477, "class_count": 1, "method_count": 7, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PermissionAnalyzer"], "functions": ["__init__", "analyze", "_get_contract_source_code", "_analyze_permission_structure", "_analyze_dangerous_functions", "_determine_centralization_level", "get_stats"], "imports": ["logging", "requests", "time", "re", "json", "typing", "discovery.models.project"]}, "security\\vulnerability_scanner.py": {"path": "assessment\\security\\vulnerability_scanner.py", "line_count": 1075, "class_count": 2, "method_count": 16, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["VulnerabilityScanner", "VulnerabilityScanner"], "functions": ["__init__", "scan", "_get_contract_source_code", "_scan_with_patterns", "_scan_with_mythril", "_scan_with_slither", "_scan_with_mythx", "get_stats", "__init__", "scan", "_get_contract_source_code", "_scan_with_patterns", "_scan_with_mythril", "_scan_with_slither", "_scan_with_mythx", "get_stats"], "imports": ["logging", "requests", "time", "re", "json", "typing", "discovery.models.project", "logging", "requests", "time", "re", "json", "typing", "discovery.models.project", "subprocess", "subprocess", "tempfile", "os", "subprocess", "subprocess", "tempfile", "os"]}, "security\\__init__.py": {"path": "assessment\\security\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "verification\\project_verifier.py": {"path": "assessment\\verification\\project_verifier.py", "line_count": 524, "class_count": 1, "method_count": 10, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProjectVerifier"], "functions": ["__init__", "verify", "_verify_website", "_check_page_availability", "_evaluate_content_quality", "_extract_domain", "_extract_tld", "_get_whois_info", "_calculate_confidence", "get_stats"], "imports": ["logging", "requests", "time", "re", "typing", "urllib.parse", "discovery.models.project", "bs4", "bs4", "whois"]}, "verification\\social_verifier.py": {"path": "assessment\\verification\\social_verifier.py", "line_count": 606, "class_count": 1, "method_count": 11, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SocialVerifier"], "functions": ["__init__", "verify", "_verify_channel", "_verify_twitter", "_verify_telegram", "_verify_discord", "_verify_medium", "_verify_github", "_has_suspicious_content", "_calculate_confidence", "get_stats"], "imports": ["logging", "requests", "time", "re", "typing", "discovery.models.project", "bs4", "bs4", "bs4", "bs4", "bs4", "datetime", "datetime", "datetime", "datetime"]}, "verification\\team_verifier.py": {"path": "assessment\\verification\\team_verifier.py", "line_count": 559, "class_count": 1, "method_count": 11, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TeamVerifier"], "functions": ["__init__", "verify", "_extract_team_info", "_extract_team_from_text", "_find_team_page", "_extract_team_from_page", "_is_known_scammer", "_verify_member", "_estimate_experience", "_calculate_confidence", "get_stats"], "imports": ["logging", "requests", "time", "re", "typing", "discovery.models.project", "bs4", "bs4"]}, "verification\\__init__.py": {"path": "assessment\\verification\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}}, "main_agent_file": "assessment/assessment_agent.py", "completion_score": 94.0, "quality_indicators": {"has_docstrings": 8, "has_error_handling": 8, "has_logging": 8, "has_type_hints": 8, "has_async_methods": 0}}, "monitoring": {"name": "monitoring", "description": "项目监控智能体", "file_count": 15, "class_count": 10, "method_count": 78, "total_lines": 2146, "has_main_file": true, "has_init_file": true, "has_tests": false, "modules": {"main.py": {"path": "monitoring\\main.py", "line_count": 177, "class_count": 0, "method_count": 6, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": [], "functions": ["parse_arguments", "load_config", "load_project_data", "continuous_monitoring", "main", "monitor_thread"], "imports": ["os", "sys", "json", "logging", "<PERSON><PERSON><PERSON><PERSON>", "time", "threading", "typing", "monitoring.monitoring_agent"]}, "monitoring_agent.py": {"path": "monitoring\\monitoring_agent.py", "line_count": 503, "class_count": 1, "method_count": 21, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["MonitoringAgent"], "functions": ["__init__", "_load_config", "start", "stop", "_load_monitored_projects", "_save_monitored_projects", "add_project", "remove_project", "check_for_updates", "_check_twitter", "_check_discord", "_check_telegram", "_check_website", "_process_updates", "_is_priority_update", "_send_notification", "get_project_updates", "get_all_updates", "run_monitoring_cycle", "run_continuous_monitoring", "status"], "imports": ["logging", "os", "json", "time", "typing", "datetime"]}, "__init__.py": {"path": "monitoring\\__init__.py", "line_count": 8, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "analyzers\\announcement_analyzer.py": {"path": "monitoring\\analyzers\\announcement_analyzer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AnnouncementAnalyzer"], "functions": ["__init__", "analyze_announcement"], "imports": ["logging"]}, "analyzers\\milestone_tracker.py": {"path": "monitoring\\analyzers\\milestone_tracker.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["MilestoneTracker"], "functions": ["__init__", "track_milestone"], "imports": ["logging"]}, "analyzers\\update_detector.py": {"path": "monitoring\\analyzers\\update_detector.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["UpdateDetector"], "functions": ["__init__", "detect_updates"], "imports": ["logging"]}, "analyzers\\__init__.py": {"path": "monitoring\\analyzers\\__init__.py", "line_count": 6, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["announcement_analyzer", "update_detector", "milestone_tracker"]}, "channels\\discord_monitor.py": {"path": "monitoring\\channels\\discord_monitor.py", "line_count": 506, "class_count": 1, "method_count": 18, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["DiscordMonitor"], "functions": ["__init__", "_load_config", "_load_message_cache", "_save_message_cache", "_clean_old_cache", "_initialize_client", "_process_message", "_is_announcement", "_check_channel_history", "add_channel", "remove_channel", "start", "stop", "check_project", "run_client", "on_ready", "on_message", "check_channels"], "imports": ["os", "json", "logging", "time", "asyncio", "typing", "datetime", "discord", "discord.ext"]}, "channels\\telegram_monitor.py": {"path": "monitoring\\channels\\telegram_monitor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TelegramMonitor"], "functions": ["__init__", "monitor_telegram_channel"], "imports": ["logging"]}, "channels\\twitter_monitor.py": {"path": "monitoring\\channels\\twitter_monitor.py", "line_count": 416, "class_count": 1, "method_count": 9, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TwitterMonitor"], "functions": ["__init__", "_load_config", "_initialize_api", "_load_tweet_cache", "_save_tweet_cache", "_clean_old_cache", "check_account", "check_hashtag", "check_project"], "imports": ["os", "json", "logging", "time", "typing", "datetime", "tweepy"]}, "channels\\website_monitor.py": {"path": "monitoring\\channels\\website_monitor.py", "line_count": 453, "class_count": 1, "method_count": 12, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["WebsiteMonitor"], "functions": ["__init__", "_load_config", "_load_page_cache", "_save_page_cache", "_clean_old_cache", "_fetch_page", "_compute_hash", "_extract_text", "_extract_important_sections", "_compare_content", "check_website", "check_project"], "imports": ["os", "json", "logging", "time", "<PERSON><PERSON><PERSON>", "requests", "typing", "datetime", "bs4", "difflib"]}, "channels\\__init__.py": {"path": "monitoring\\channels\\__init__.py", "line_count": 6, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "notifiers\\alert_generator.py": {"path": "monitoring\\notifiers\\alert_generator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AlertGenerator"], "functions": ["__init__", "generate_alert"], "imports": ["logging"]}, "notifiers\\update_notifier.py": {"path": "monitoring\\notifiers\\update_notifier.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["UpdateNotifier"], "functions": ["__init__", "notify_update"], "imports": ["logging"]}, "notifiers\\__init__.py": {"path": "monitoring\\notifiers\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["alert_generator", "update_notifier"]}}, "main_agent_file": "monitoring/monitoring_agent.py", "completion_score": 90.0, "quality_indicators": {"has_docstrings": 11, "has_error_handling": 5, "has_logging": 11, "has_type_hints": 11, "has_async_methods": 1}}, "fund_management": {"name": "fund_management", "description": "资金管理智能体", "file_count": 39, "class_count": 32, "method_count": 268, "total_lines": 7864, "has_main_file": true, "has_init_file": true, "has_tests": false, "modules": {"fund_management_agent.py": {"path": "fund_management\\fund_management_agent.py", "line_count": 251, "class_count": 1, "method_count": 14, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["FundManagementAgent"], "functions": ["__init__", "_load_config", "start", "stop", "_load_existing_wallets", "_start_balance_monitoring", "_backup_wallets", "create_wallet", "get_wallet_balance", "transfer_funds", "optimize_gas", "get_transaction_history", "allocate_funds", "status"], "imports": ["logging", "os", "json", "typing"]}, "main.py": {"path": "fund_management\\main.py", "line_count": 102, "class_count": 0, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": [], "functions": ["parse_arguments", "load_config", "main"], "imports": ["os", "sys", "json", "logging", "<PERSON><PERSON><PERSON><PERSON>", "typing", "fund_management.fund_management_agent"]}, "__init__.py": {"path": "fund_management\\__init__.py", "line_count": 9, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "assets\\balance_tracker.py": {"path": "fund_management\\assets\\balance_tracker.py", "line_count": 413, "class_count": 1, "method_count": 17, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["BalanceTracker"], "functions": ["__init__", "initialize", "add_address", "remove_address", "get_current_balance", "get_balance_history", "start_tracking", "stop_tracking", "_tracking_loop", "_update_all_balances", "_update_address_balance", "_fetch_balances", "_check_balance_changes", "_calculate_balance_changes", "_cleanup_old_history", "_is_valid_address", "get_tracking_statistics"], "imports": ["logging", "asyncio", "typing", "datetime", "random"]}, "assets\\portfolio_manager.py": {"path": "fund_management\\assets\\portfolio_manager.py", "line_count": 190, "class_count": 1, "method_count": 5, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["PortfolioManager"], "functions": ["__init__", "initialize", "create_portfolio", "add_asset_to_portfolio", "remove_asset_from_portfolio"], "imports": ["logging", "typing", "datetime"]}, "assets\\token_detector.py": {"path": "fund_management\\assets\\token_detector.py", "line_count": 481, "class_count": 1, "method_count": 20, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["TokenDetector"], "functions": ["__init__", "initialize", "add_address_to_monitor", "remove_address_from_monitor", "start_detection", "stop_detection", "_detection_loop", "_scan_all_addresses", "_scan_address_for_new_tokens", "_get_current_tokens", "_process_new_token", "_get_token_balance", "_get_token_info", "_should_process_token", "_classify_detection", "_cleanup_old_detections", "_is_valid_address", "get_detected_tokens", "mark_token_processed", "get_detection_statistics"], "imports": ["logging", "asyncio", "typing", "datetime", "random", "random", "random"]}, "assets\\token_manager.py": {"path": "fund_management\\assets\\token_manager.py", "line_count": 415, "class_count": 1, "method_count": 15, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["TokenManager"], "functions": ["__init__", "initialize", "add_token", "get_token_info", "update_token_price", "_load_default_tokens", "_fetch_token_metadata", "remove_token", "get_all_tokens", "search_tokens", "get_token_price", "update_token_metadata", "_is_valid_address", "get_manager_statistics", "sync_token_data"], "imports": ["logging", "json", "typing", "datetime", "random"]}, "assets\\value_estimator.py": {"path": "fund_management\\assets\\value_estimator.py", "line_count": 388, "class_count": 1, "method_count": 12, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["ValueEstimator"], "functions": ["__init__", "initialize", "estimate_token_value", "estimate_portfolio_value", "_get_token_price", "_fetch_price_from_sources", "_preload_common_prices", "_calculate_portfolio_confidence", "get_price_history", "calculate_profit_loss", "get_estimation_statistics", "clear_cache"], "imports": ["logging", "typing", "datetime", "random"]}, "assets\\__init__.py": {"path": "fund_management\\assets\\__init__.py", "line_count": 19, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["balance_tracker", "token_manager", "token_detector", "value_estimator", "portfolio_manager"]}, "distribution\\balance_optimizer.py": {"path": "fund_management\\distribution\\balance_optimizer.py", "line_count": 283, "class_count": 1, "method_count": 9, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["BalanceOptimizer"], "functions": ["__init__", "initialize", "optimize_balances", "_analyze_current_distribution", "_generate_optimization_plan", "_generate_efficiency_plan", "_generate_risk_plan", "_generate_cost_plan", "_calculate_optimization_impact"], "imports": ["logging", "typing", "datetime"]}, "distribution\\fee_manager.py": {"path": "fund_management\\distribution\\fee_manager.py", "line_count": 128, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "functions": ["__init__", "initialize", "calculate_optimal_fee", "track_fee_payment"], "imports": ["logging", "typing", "datetime"]}, "distribution\\fund_allocator.py": {"path": "fund_management\\distribution\\fund_allocator.py", "line_count": 260, "class_count": 1, "method_count": 7, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["FundAllocator"], "functions": ["__init__", "initialize", "allocate_funds", "_equal_allocation", "_weighted_allocation", "_priority_allocation", "_risk_based_allocation"], "imports": ["logging", "typing", "datetime"]}, "distribution\\liquidity_manager.py": {"path": "fund_management\\distribution\\liquidity_manager.py", "line_count": 131, "class_count": 1, "method_count": 5, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["LiquidityManager"], "functions": ["__init__", "initialize", "check_liquidity", "reserve_liquidity", "release_liquidity"], "imports": ["logging", "typing", "datetime"]}, "distribution\\__init__.py": {"path": "fund_management\\distribution\\__init__.py", "line_count": 17, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["fund_allocator", "balance_optimizer", "fee_manager", "liquidity_manager"]}, "extensions\\extension_automator.py": {"path": "fund_management\\extensions\\extension_automator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ExtensionAutomator"], "functions": ["__init__", "automate_extension"], "imports": ["logging"]}, "extensions\\extension_installer.py": {"path": "fund_management\\extensions\\extension_installer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ExtensionInstaller"], "functions": ["__init__", "install_extension"], "imports": ["logging"]}, "extensions\\extension_monitor.py": {"path": "fund_management\\extensions\\extension_monitor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ExtensionMonitor"], "functions": ["__init__", "monitor_extension"], "imports": ["logging"]}, "extensions\\metamask_manager.py": {"path": "fund_management\\extensions\\metamask_manager.py", "line_count": 643, "class_count": 1, "method_count": 13, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["MetaMaskManager"], "functions": ["__init__", "_setup_driver", "start", "stop", "is_setup_required", "setup_new_wallet", "import_wallet", "unlock_wallet", "get_wallet_address", "switch_network", "add_custom_network", "sign_transaction", "get_balance"], "imports": ["os", "json", "time", "logging", "base64", "typing", "selenium", "selenium.webdriver.chrome.options", "selenium.webdriver.common.by", "selenium.webdriver.support.ui", "selenium.webdriver.support", "selenium.common.exceptions"]}, "extensions\\phantom_manager.py": {"path": "fund_management\\extensions\\phantom_manager.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PhantomManager"], "functions": ["__init__", "manage_phantom"], "imports": ["logging"]}, "extensions\\__init__.py": {"path": "fund_management\\extensions\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "security\\authorization_manager.py": {"path": "fund_management\\security\\authorization_manager.py", "line_count": 441, "class_count": 1, "method_count": 18, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["AuthorizationManager"], "functions": ["__init__", "initialize", "create_user", "authenticate_user", "check_permission", "_create_session", "_validate_session", "_get_user_permissions", "_is_user_locked", "_handle_failed_login", "_validate_password", "_hash_password", "_verify_password", "_verify_2fa", "_load_default_permissions", "_load_default_roles", "logout_user", "get_auth_statistics"], "imports": ["logging", "<PERSON><PERSON><PERSON>", "secrets", "typing", "datetime"]}, "security\\emergency_handler.py": {"path": "fund_management\\security\\emergency_handler.py", "line_count": 340, "class_count": 2, "method_count": 14, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["EmergencyType", "<PERSON><PERSON><PERSON><PERSON>"], "functions": ["__init__", "initialize", "trigger_emergency", "resolve_incident", "initiate_lockdown", "lift_lockdown", "_execute_emergency_response", "_check_auto_lockdown", "_auto_unlock_after_delay", "_send_emergency_notification", "_register_default_handlers", "_handle_fraud_detected", "_handle_security_breach", "_handle_unauthorized_access"], "imports": ["logging", "asyncio", "typing", "datetime", "enum"]}, "security\\fraud_detector.py": {"path": "fund_management\\security\\fraud_detector.py", "line_count": 218, "class_count": 1, "method_count": 6, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["<PERSON>aud<PERSON><PERSON><PERSON>"], "functions": ["__init__", "initialize", "detect_fraud", "_check_rapid_succession", "_check_amount_anomaly", "_check_unusual_timing"], "imports": ["logging", "typing", "datetime"]}, "security\\permission_scanner.py": {"path": "fund_management\\security\\permission_scanner.py", "line_count": 232, "class_count": 1, "method_count": 8, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["PermissionScanner"], "functions": ["__init__", "initialize", "scan_permissions", "_check_excessive_permissions", "_check_admin_2fa", "_check_inactive_users", "_calculate_risk_score", "_generate_recommendations"], "imports": ["logging", "typing", "datetime"]}, "security\\risk_analyzer.py": {"path": "fund_management\\security\\risk_analyzer.py", "line_count": 301, "class_count": 1, "method_count": 9, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["RiskAnalyzer"], "functions": ["__init__", "initialize", "analyze_transaction_risk", "_analyze_amount_risk", "_analyze_behavior_risk", "_analyze_timing_risk", "_analyze_address_risk", "_determine_risk_level", "_generate_risk_recommendations"], "imports": ["logging", "typing", "datetime", "random"]}, "security\\__init__.py": {"path": "fund_management\\security\\__init__.py", "line_count": 19, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["authorization_manager", "permission_scanner", "risk_analyzer", "fraud_detector", "emergency_handler"]}, "transactions\\gas_optimizer.py": {"path": "fund_management\\transactions\\gas_optimizer.py", "line_count": 266, "class_count": 1, "method_count": 8, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["GasOptimizer"], "functions": ["__init__", "initialize", "get_optimal_gas_settings", "_analyze_network_congestion", "_predict_gas_prices", "_calculate_optimal_settings", "_load_historical_data", "estimate_transaction_cost"], "imports": ["logging", "asyncio", "typing", "datetime"]}, "transactions\\transaction_builder.py": {"path": "fund_management\\transactions\\transaction_builder.py", "line_count": 335, "class_count": 1, "method_count": 8, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["TransactionBuilder"], "functions": ["__init__", "initialize", "build_transfer_transaction", "build_erc20_transfer_transaction", "build_contract_call_transaction", "build_approve_transaction", "validate_transaction", "_is_valid_address"], "imports": ["logging", "typing", "datetime"]}, "transactions\\transaction_manager.py": {"path": "fund_management\\transactions\\transaction_manager.py", "line_count": 200, "class_count": 1, "method_count": 7, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["TransactionManager"], "functions": ["__init__", "initialize", "create_transaction", "submit_transaction", "get_transaction_status", "cancel_transaction", "get_statistics"], "imports": ["logging", "asyncio", "uuid", "typing", "datetime"]}, "transactions\\transaction_monitor.py": {"path": "fund_management\\transactions\\transaction_monitor.py", "line_count": 333, "class_count": 1, "method_count": 14, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["TransactionMonitor"], "functions": ["__init__", "initialize", "start_monitoring", "stop_monitoring", "add_transaction", "_monitoring_loop", "_check_all_transactions", "_check_transaction_status", "_call_status_callbacks", "_is_transaction_timeout", "_handle_timeout_transaction", "get_transaction_info", "remove_transaction", "get_monitoring_statistics"], "imports": ["logging", "asyncio", "typing", "datetime", "random"]}, "transactions\\transaction_recovery.py": {"path": "fund_management\\transactions\\transaction_recovery.py", "line_count": 162, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["TransactionRecovery"], "functions": ["__init__", "initialize", "recover_failed_transaction", "_select_recovery_strategy"], "imports": ["logging", "asyncio", "typing", "datetime"]}, "transactions\\transaction_signer.py": {"path": "fund_management\\transactions\\transaction_signer.py", "line_count": 328, "class_count": 1, "method_count": 10, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["TransactionSigner"], "functions": ["__init__", "initialize", "sign_transaction", "_sign_eip1559_transaction", "_sign_eip155_transaction", "_sign_legacy_transaction", "_validate_private_key", "verify_signature", "get_signature_statistics", "create_message_signature"], "imports": ["logging", "<PERSON><PERSON><PERSON>", "typing", "datetime"]}, "transactions\\__init__.py": {"path": "fund_management\\transactions\\__init__.py", "line_count": 21, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["transaction_manager", "gas_optimizer", "transaction_builder", "transaction_signer", "transaction_monitor", "transaction_recovery"]}, "wallets\\key_manager.py": {"path": "fund_management\\wallets\\key_manager.py", "line_count": 260, "class_count": 1, "method_count": 9, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["KeyManager"], "functions": ["__init__", "_derive_key", "encrypt_key", "decrypt_key", "store_key", "retrieve_key", "rotate_key", "delete_key", "list_keys"], "imports": ["os", "json", "base64", "logging", "typing", "cryptography.fernet", "cryptography.hazmat.primitives", "cryptography.hazmat.primitives.kdf.pbkdf2"]}, "wallets\\wallet_generator.py": {"path": "fund_management\\wallets\\wallet_generator.py", "line_count": 191, "class_count": 1, "method_count": 6, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["WalletGenerator"], "functions": ["__init__", "generate_wallet", "_generate_ethereum_wallet", "_generate_solana_wallet", "_save_wallet", "list_wallets"], "imports": ["os", "json", "uuid", "logging", "typing", "time"]}, "wallets\\wallet_recovery.py": {"path": "fund_management\\wallets\\wallet_recovery.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["WalletRecovery"], "functions": ["__init__", "recover_wallet"], "imports": ["logging"]}, "wallets\\wallet_storage.py": {"path": "fund_management\\wallets\\wallet_storage.py", "line_count": 411, "class_count": 1, "method_count": 11, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["WalletStorage"], "functions": ["__init__", "store_wallet", "retrieve_wallet", "update_wallet", "delete_wallet", "list_wallets", "_list_wallets_in_dir", "backup_wallet", "backup_all_wallets", "restore_wallet", "clean_old_backups"], "imports": ["os", "json", "shutil", "logging", "time", "typing", "datetime"]}, "wallets\\wallet_validator.py": {"path": "fund_management\\wallets\\wallet_validator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["WalletValidator"], "functions": ["__init__", "validate_wallet"], "imports": ["logging"]}, "wallets\\__init__.py": {"path": "fund_management\\wallets\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}}, "main_agent_file": "fund_management/fund_management_agent.py", "completion_score": 90.0, "quality_indicators": {"has_docstrings": 32, "has_error_handling": 26, "has_logging": 32, "has_type_hints": 32, "has_async_methods": 20}}, "task_planning": {"name": "task_planning", "description": "任务规划智能体", "file_count": 19, "class_count": 12, "method_count": 64, "total_lines": 1938, "has_main_file": true, "has_init_file": true, "has_tests": false, "modules": {"main.py": {"path": "task_planning\\main.py", "line_count": 231, "class_count": 0, "method_count": 6, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": [], "functions": ["parse_arguments", "load_config", "load_data_file", "run_scheduler", "main", "scheduler_thread"], "imports": ["os", "sys", "json", "logging", "<PERSON><PERSON><PERSON><PERSON>", "time", "threading", "typing", "task_planning.task_planning_agent"]}, "task_planning_agent.py": {"path": "task_planning\\task_planning_agent.py", "line_count": 934, "class_count": 1, "method_count": 29, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TaskPlanningAgent"], "functions": ["__init__", "_load_config", "start", "stop", "_load_tasks", "_save_tasks", "_load_projects", "_save_projects", "_load_task_templates", "_create_default_templates", "create_task", "update_task", "delete_task", "get_task", "list_tasks", "create_project", "update_project", "delete_project", "get_project", "list_projects", "create_task_from_template", "create_task_sequence", "get_next_tasks", "schedule_tasks", "mark_task_completed", "mark_task_failed", "_schedule_recurring_task", "run_scheduler", "status"], "imports": ["logging", "os", "json", "time", "typing", "datetime", "uuid"]}, "__init__.py": {"path": "task_planning\\__init__.py", "line_count": 8, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "analyzers\\condition_analyzer.py": {"path": "task_planning\\analyzers\\condition_analyzer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ConditionAnalyzer"], "functions": ["__init__", "analyze_conditions"], "imports": ["logging"]}, "analyzers\\difficulty_estimator.py": {"path": "task_planning\\analyzers\\difficulty_estimator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["DifficultyEstimator"], "functions": ["__init__", "estimate_difficulty"], "imports": ["logging"]}, "analyzers\\requirement_parser.py": {"path": "task_planning\\analyzers\\requirement_parser.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["Requirement<PERSON>arser"], "functions": ["__init__", "parse_requirements"], "imports": ["logging"]}, "analyzers\\__init__.py": {"path": "task_planning\\analyzers\\__init__.py", "line_count": 6, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["condition_analyzer", "requirement_parser", "difficulty_estimator"]}, "optimizers\\resource_optimizer.py": {"path": "task_planning\\optimizers\\resource_optimizer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ResourceOptimizer"], "functions": ["__init__", "optimize_resources"], "imports": ["logging"]}, "optimizers\\strategy_adjuster.py": {"path": "task_planning\\optimizers\\strategy_adjuster.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["StrategyAdjuster"], "functions": ["__init__", "adjust_strategy"], "imports": ["logging"]}, "optimizers\\time_optimizer.py": {"path": "task_planning\\optimizers\\time_optimizer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TimeOptimizer"], "functions": ["__init__", "optimize_timing"], "imports": ["logging"]}, "optimizers\\__init__.py": {"path": "task_planning\\optimizers\\__init__.py", "line_count": 6, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["time_optimizer", "resource_optimizer", "strategy_adjuster"]}, "planners\\account_allocator.py": {"path": "task_planning\\planners\\account_allocator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AccountAllocator"], "functions": ["__init__", "allocate_accounts"], "imports": ["logging"]}, "planners\\path_designer.py": {"path": "task_planning\\planners\\path_designer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PathDesigner"], "functions": ["__init__", "design_execution_path"], "imports": ["logging"]}, "planners\\priority_scheduler.py": {"path": "task_planning\\planners\\priority_scheduler.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PriorityScheduler"], "functions": ["__init__", "schedule_tasks"], "imports": ["logging"]}, "planners\\__init__.py": {"path": "task_planning\\planners\\__init__.py", "line_count": 6, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["path_designer", "priority_scheduler", "account_allocator"]}, "scheduler\\priority_scheduler.py": {"path": "task_planning\\scheduler\\priority_scheduler.py", "line_count": 398, "class_count": 1, "method_count": 6, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PriorityScheduler"], "functions": ["__init__", "schedule", "_filter_ready_tasks", "_sort_tasks", "_can_schedule", "estimate_completion_time"], "imports": ["logging", "time", "typing", "datetime"]}, "scheduler\\__init__.py": {"path": "task_planning\\scheduler\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "templates\\social_templates.py": {"path": "task_planning\\templates\\social_templates.py", "line_count": 240, "class_count": 1, "method_count": 5, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SocialTemplates"], "functions": ["get_registration_template", "get_daily_engagement_template", "get_content_creation_template", "get_community_management_template", "get_all_templates"], "imports": ["logging", "typing", "json"]}, "templates\\__init__.py": {"path": "task_planning\\templates\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}}, "main_agent_file": "task_planning/task_planning_agent.py", "completion_score": 84.0, "quality_indicators": {"has_docstrings": 13, "has_error_handling": 3, "has_logging": 13, "has_type_hints": 13, "has_async_methods": 0}}, "task_execution": {"name": "task_execution", "description": "任务执行智能体", "file_count": 19, "class_count": 13, "method_count": 78, "total_lines": 2690, "has_main_file": true, "has_init_file": true, "has_tests": false, "modules": {"main.py": {"path": "task_execution\\main.py", "line_count": 131, "class_count": 0, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": [], "functions": ["parse_arguments", "load_config", "load_task_data", "main"], "imports": ["os", "sys", "json", "logging", "<PERSON><PERSON><PERSON><PERSON>", "time", "typing", "task_execution.task_execution_agent"]}, "task_execution_agent.py": {"path": "task_execution\\task_execution_agent.py", "line_count": 620, "class_count": 1, "method_count": 16, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TaskExecutionAgent"], "functions": ["__init__", "_load_config", "_load_task_history", "_save_task_history", "_load_platform_handler", "start", "stop", "execute_task", "_execute_step_task", "_get_execution_time", "_update_task_history", "abort_task", "get_task_status", "get_task_history", "list_running_tasks", "status"], "imports": ["logging", "os", "json", "time", "random", "typing", "datetime", "importlib", "traceback"]}, "__init__.py": {"path": "task_execution\\__init__.py", "line_count": 8, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "blockchain\\mainnet_interactor.py": {"path": "task_execution\\blockchain\\mainnet_interactor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["MainnetInteractor"], "functions": ["__init__", "interact_with_mainnet"], "imports": ["logging"]}, "blockchain\\testnet_interactor.py": {"path": "task_execution\\blockchain\\testnet_interactor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TestnetInteractor"], "functions": ["__init__", "interact_with_testnet"], "imports": ["logging"]}, "blockchain\\transaction_signer.py": {"path": "task_execution\\blockchain\\transaction_signer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TransactionSigner"], "functions": ["__init__", "sign_transaction"], "imports": ["logging"]}, "blockchain\\__init__.py": {"path": "task_execution\\blockchain\\__init__.py", "line_count": 6, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["testnet_interactor", "mainnet_interactor", "transaction_signer"]}, "monitors\\completion_verifier.py": {"path": "task_execution\\monitors\\completion_verifier.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["CompletionVerifier"], "functions": ["__init__", "verify_completion"], "imports": ["logging"]}, "monitors\\retry_handler.py": {"path": "task_execution\\monitors\\retry_handler.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "functions": ["__init__", "handle_retry"], "imports": ["logging"]}, "monitors\\task_monitor.py": {"path": "task_execution\\monitors\\task_monitor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TaskMonitor"], "functions": ["__init__", "monitor_task"], "imports": ["logging"]}, "monitors\\__init__.py": {"path": "task_execution\\monitors\\__init__.py", "line_count": 6, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["task_monitor", "completion_verifier", "retry_handler"]}, "platforms\\discord_handler.py": {"path": "task_execution\\platforms\\discord_handler.py", "line_count": 636, "class_count": 1, "method_count": 20, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["<PERSON>rd<PERSON><PERSON><PERSON>"], "functions": ["__init__", "_has_token", "_initialize_client", "_connect_client", "_run_coroutine", "register", "join_server", "send_message", "react", "get_messages", "engage", "close", "on_ready", "run_client", "join_server_async", "send_message_async", "react_async", "get_messages_async", "get_channels_async", "close_client"], "imports": ["logging", "os", "json", "time", "random", "asyncio", "typing", "datetime", "discord", "discord.ext", "threading"]}, "platforms\\twitter_handler.py": {"path": "task_execution\\platforms\\twitter_handler.py", "line_count": 571, "class_count": 1, "method_count": 12, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TwitterHandler"], "functions": ["__init__", "_has_credentials", "_initialize_api", "register", "post", "like", "retweet", "follow", "engage", "check_mentions", "search", "close"], "imports": ["logging", "os", "json", "time", "random", "typing", "datetime", "tweepy"]}, "platforms\\web_handler.py": {"path": "task_execution\\platforms\\web_handler.py", "line_count": 602, "class_count": 1, "method_count": 8, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["WebHandler"], "functions": ["__init__", "_initialize_browser", "visit", "fill_form", "extract_content", "search", "download", "close"], "imports": ["logging", "os", "json", "time", "random", "typing", "datetime", "requests", "bs4", "urllib.parse", "selenium", "selenium.webdriver.chrome.options", "selenium.webdriver.common.by", "selenium.webdriver.support.ui", "selenium.webdriver.support", "selenium.common.exceptions"]}, "platforms\\__init__.py": {"path": "task_execution\\platforms\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "social\\discord_executor.py": {"path": "task_execution\\social\\discord_executor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["DiscordExecutor"], "functions": ["__init__", "execute_discord_task"], "imports": ["logging"]}, "social\\telegram_executor.py": {"path": "task_execution\\social\\telegram_executor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TelegramExecutor"], "functions": ["__init__", "execute_telegram_task"], "imports": ["logging"]}, "social\\twitter_executor.py": {"path": "task_execution\\social\\twitter_executor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TwitterExecutor"], "functions": ["__init__", "execute_twitter_task"], "imports": ["logging"]}, "social\\__init__.py": {"path": "task_execution\\social\\__init__.py", "line_count": 6, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["twitter_executor", "discord_executor", "telegram_executor"]}}, "main_agent_file": "task_execution/task_execution_agent.py", "completion_score": 90.0, "quality_indicators": {"has_docstrings": 14, "has_error_handling": 5, "has_logging": 14, "has_type_hints": 14, "has_async_methods": 1}}, "proxy": {"name": "proxy", "description": "代理智能体", "file_count": 65, "class_count": 45, "method_count": 179, "total_lines": 3701, "has_main_file": true, "has_init_file": true, "has_tests": false, "modules": {"main.py": {"path": "proxy\\main.py", "line_count": 141, "class_count": 0, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": [], "functions": ["setup_logging", "load_config", "_merge_configs", "main"], "imports": ["logging", "<PERSON><PERSON><PERSON><PERSON>", "json", "os", "sys", "time", "typing", "proxy.proxy_agent", "proxy.api.proxy_api", "proxy.config.default_config"]}, "proxy_agent.py": {"path": "proxy\\proxy_agent.py", "line_count": 364, "class_count": 1, "method_count": 16, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyAgent"], "functions": ["__init__", "start", "stop", "get_proxy", "get_proxies", "report_proxy_status", "add_proxy", "remove_proxy", "get_pool_status", "get_source_status", "_acquisition_loop", "_verification_loop", "_cleanup_loop", "_acquire_proxies", "_verify_proxies", "_cleanup_proxies"], "imports": ["logging", "threading", "time", "typing", "proxy.acquisition.proxy_source_manager", "proxy.verification.proxy_verifier", "proxy.management.proxy_pool", "proxy.distribution.proxy_distributor", "proxy.models.proxy"]}, "__init__.py": {"path": "proxy\\__init__.py", "line_count": 7, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "acquisition\\proxy_source_manager.py": {"path": "proxy\\acquisition\\proxy_source_manager.py", "line_count": 224, "class_count": 1, "method_count": 6, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxySourceManager"], "functions": ["__init__", "_init_sources", "get_proxies", "_calculate_source_counts", "get_status", "get_source_names"], "imports": ["logging", "threading", "time", "random", "typing", "proxy.models.proxy", "proxy.acquisition.sources.base_source", "proxy.acquisition.sources.free_proxy_list", "proxy.acquisition.sources.proxy_scrape", "proxy.acquisition.sources.geonode", "proxy.acquisition.sources.proxy_nova"]}, "acquisition\\__init__.py": {"path": "proxy\\acquisition\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "acquisition\\sources\\base_source.py": {"path": "proxy\\acquisition\\sources\\base_source.py", "line_count": 38, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BaseProxySource"], "functions": ["__init__", "get_proxies"], "imports": ["logging", "abc", "typing", "proxy.models.proxy"]}, "acquisition\\sources\\free_proxy_list.py": {"path": "proxy\\acquisition\\sources\\free_proxy_list.py", "line_count": 118, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["FreeProxyListSource"], "functions": ["__init__", "get_proxies"], "imports": ["logging", "requests", "time", "random", "typing", "bs4", "proxy.models.proxy", "proxy.acquisition.sources.base_source"]}, "acquisition\\sources\\geonode.py": {"path": "proxy\\acquisition\\sources\\geonode.py", "line_count": 108, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["GeonodeSource"], "functions": ["__init__", "get_proxies"], "imports": ["logging", "requests", "time", "random", "typing", "proxy.models.proxy", "proxy.acquisition.sources.base_source"]}, "acquisition\\sources\\proxy_nova.py": {"path": "proxy\\acquisition\\sources\\proxy_nova.py", "line_count": 144, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyNovaSource"], "functions": ["__init__", "get_proxies"], "imports": ["logging", "requests", "time", "random", "re", "typing", "bs4", "proxy.models.proxy", "proxy.acquisition.sources.base_source"]}, "acquisition\\sources\\proxy_scrape.py": {"path": "proxy\\acquisition\\sources\\proxy_scrape.py", "line_count": 114, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyScrapeSource"], "functions": ["__init__", "get_proxies"], "imports": ["logging", "requests", "time", "random", "typing", "proxy.models.proxy", "proxy.acquisition.sources.base_source"]}, "acquisition\\sources\\__init__.py": {"path": "proxy\\acquisition\\sources\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "api\\proxy_api.py": {"path": "proxy\\api\\proxy_api.py", "line_count": 218, "class_count": 1, "method_count": 8, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyAPI"], "functions": ["__init__", "handle_request", "_handle_get_proxy", "_handle_get_proxies", "_handle_report_proxy_status", "_handle_get_pool_status", "_handle_get_source_status", "_error_response"], "imports": ["logging", "json", "typing", "proxy.models.proxy", "proxy.proxy_agent"]}, "api\\__init__.py": {"path": "proxy\\api\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "config\\default_config.py": {"path": "proxy\\config\\default_config.py", "line_count": 72, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "config\\__init__.py": {"path": "proxy\\config\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "distribution\\proxy_distributor.py": {"path": "proxy\\distribution\\proxy_distributor.py", "line_count": 270, "class_count": 1, "method_count": 9, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyDistributor"], "functions": ["__init__", "get_proxy", "get_proxies", "report_proxy_status", "rotate_proxy", "clear_sessions", "cleanup_sessions", "_generate_client_id", "get_status"], "imports": ["logging", "threading", "time", "typing", "proxy.models.proxy", "proxy.management.proxy_pool"]}, "distribution\\__init__.py": {"path": "proxy\\distribution\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "integrators\\agent_connector.py": {"path": "proxy\\integrators\\agent_connector.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AgentConnector"], "functions": ["__init__", "connect_agent"], "imports": ["logging"]}, "integrators\\browser_integrator.py": {"path": "proxy\\integrators\\browser_integrator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BrowserIntegrator"], "functions": ["__init__", "integrate_proxy"], "imports": ["logging"]}, "integrators\\proxy_distributor.py": {"path": "proxy\\integrators\\proxy_distributor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyDistributor"], "functions": ["__init__", "distribute_proxy"], "imports": ["logging"]}, "integrators\\request_interceptor.py": {"path": "proxy\\integrators\\request_interceptor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["RequestInterceptor"], "functions": ["__init__", "intercept_request"], "imports": ["logging"]}, "integrators\\__init__.py": {"path": "proxy\\integrators\\__init__.py", "line_count": 17, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["browser_integrator", "request_interceptor", "agent_connector", "proxy_distributor"]}, "maintenance\\auto_refresher.py": {"path": "proxy\\maintenance\\auto_refresher.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AutoRefresher"], "functions": ["__init__", "refresh_proxies"], "imports": ["logging"]}, "maintenance\\dead_proxy_cleaner.py": {"path": "proxy\\maintenance\\dead_proxy_cleaner.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["DeadProxyCleaner"], "functions": ["__init__", "clean_dead_proxies"], "imports": ["logging"]}, "maintenance\\health_monitor.py": {"path": "proxy\\maintenance\\health_monitor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["HealthMonitor"], "functions": ["__init__", "check_health"], "imports": ["logging"]}, "maintenance\\recovery_system.py": {"path": "proxy\\maintenance\\recovery_system.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["RecoverySystem"], "functions": ["__init__", "recover_proxy_service"], "imports": ["logging"]}, "maintenance\\source_evaluator.py": {"path": "proxy\\maintenance\\source_evaluator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SourceEvaluator"], "functions": ["__init__", "evaluate_source"], "imports": ["logging"]}, "maintenance\\__init__.py": {"path": "proxy\\maintenance\\__init__.py", "line_count": 19, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["health_monitor", "auto_refresher", "dead_proxy_cleaner", "source_evaluator", "recovery_system"]}, "management\\proxy_pool.py": {"path": "proxy\\management\\proxy_pool.py", "line_count": 411, "class_count": 1, "method_count": 14, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyPool"], "functions": ["__init__", "add_proxy", "remove_proxy", "get_proxy", "get_proxies", "get_all_proxies", "get_valid_proxies", "update_proxy_status", "cleanup_expired_proxies", "cleanup_failed_proxies", "get_status", "_save_proxies", "_load_proxies", "clear"], "imports": ["logging", "threading", "time", "json", "os", "typing", "proxy.models.proxy"]}, "management\\__init__.py": {"path": "proxy\\management\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "managers\\location_simulator.py": {"path": "proxy\\managers\\location_simulator.py", "line_count": 37, "class_count": 1, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["LocationSimulator"], "functions": ["__init__", "simulate_location", "get_location_by_proxy"], "imports": ["logging", "random"]}, "managers\\proxy_database.py": {"path": "proxy\\managers\\proxy_database.py", "line_count": 120, "class_count": 1, "method_count": 10, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyDatabase"], "functions": ["__init__", "add_proxy", "get_proxies", "update_proxy_status", "remove_proxy", "get_statistics", "_proxy_exists", "_matches_filters", "_load_database", "_save_database"], "imports": ["logging", "json", "os", "typing"]}, "managers\\proxy_pool.py": {"path": "proxy\\managers\\proxy_pool.py", "line_count": 45, "class_count": 1, "method_count": 5, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyPool"], "functions": ["__init__", "add_proxy", "get_proxy", "remove_proxy", "get_pool_status"], "imports": ["logging", "typing"]}, "managers\\proxy_scheduler.py": {"path": "proxy\\managers\\proxy_scheduler.py", "line_count": 50, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyScheduler"], "functions": ["__init__", "schedule_proxy", "release_proxy", "get_assignment_stats"], "imports": ["logging", "random", "typing"]}, "managers\\vpn_manager.py": {"path": "proxy\\managers\\vpn_manager.py", "line_count": 36, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["VPNManager"], "functions": ["__init__", "connect_vpn", "disconnect_vpn", "get_vpn_status"], "imports": ["logging"]}, "managers\\__init__.py": {"path": "proxy\\managers\\__init__.py", "line_count": 19, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["proxy_pool", "proxy_scheduler", "vpn_manager", "location_simulator", "proxy_database"]}, "models\\proxy.py": {"path": "proxy\\models\\proxy.py", "line_count": 190, "class_count": 1, "method_count": 10, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": false, "has_type_hints": true, "has_async_methods": false}, "classes": ["Proxy"], "functions": ["__post_init__", "url", "average_response_time", "success_rate", "age", "to_dict", "from_dict", "__eq__", "__hash__", "__str__"], "imports": ["time", "typing", "dataclasses"]}, "models\\__init__.py": {"path": "proxy\\models\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "optimizers\\resource_allocator.py": {"path": "proxy\\optimizers\\resource_allocator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ResourceAllocator"], "functions": ["__init__", "allocate_resources"], "imports": ["logging"]}, "optimizers\\speed_optimizer.py": {"path": "proxy\\optimizers\\speed_optimizer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SpeedOptimizer"], "functions": ["__init__", "optimize_speed"], "imports": ["logging"]}, "optimizers\\stability_enhancer.py": {"path": "proxy\\optimizers\\stability_enhancer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["StabilityEnhancer"], "functions": ["__init__", "enhance_stability"], "imports": ["logging"]}, "optimizers\\usage_balancer.py": {"path": "proxy\\optimizers\\usage_balancer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["UsageBalancer"], "functions": ["__init__", "balance_usage"], "imports": ["logging"]}, "optimizers\\__init__.py": {"path": "proxy\\optimizers\\__init__.py", "line_count": 17, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["speed_optimizer", "stability_enhancer", "resource_allocator", "usage_balancer"]}, "rotators\\auto_switcher.py": {"path": "proxy\\rotators\\auto_switcher.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AutoSwitcher"], "functions": ["__init__", "auto_switch"], "imports": ["logging"]}, "rotators\\connection_monitor.py": {"path": "proxy\\rotators\\connection_monitor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ConnectionMonitor"], "functions": ["__init__", "monitor_connection"], "imports": ["logging"]}, "rotators\\failure_detector.py": {"path": "proxy\\rotators\\failure_detector.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["FailureDetector"], "functions": ["__init__", "detect_failure"], "imports": ["logging"]}, "rotators\\identity_rotator.py": {"path": "proxy\\rotators\\identity_rotator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["IdentityRotator"], "functions": ["__init__", "rotate_identity"], "imports": ["logging"]}, "rotators\\session_manager.py": {"path": "proxy\\rotators\\session_manager.py", "line_count": 21, "class_count": 1, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["Session<PERSON>anager"], "functions": ["__init__", "create_session", "end_session"], "imports": ["logging"]}, "rotators\\__init__.py": {"path": "proxy\\rotators\\__init__.py", "line_count": 19, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["identity_rotator", "session_manager", "connection_monitor", "failure_detector", "auto_switcher"]}, "sources\\free_proxy_crawler.py": {"path": "proxy\\sources\\free_proxy_crawler.py", "line_count": 13, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["FreeProxyCrawler"], "functions": ["__init__", "crawl_proxies"], "imports": ["logging", "requests", "typing"]}, "sources\\proxy_api_client.py": {"path": "proxy\\sources\\proxy_api_client.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyAPIClient"], "functions": ["__init__", "fetch_proxies"], "imports": ["logging"]}, "sources\\proxy_list_parser.py": {"path": "proxy\\sources\\proxy_list_parser.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["Proxy<PERSON><PERSON><PERSON><PERSON><PERSON>"], "functions": ["__init__", "parse_proxy_list"], "imports": ["logging"]}, "sources\\residential_proxy_rotator.py": {"path": "proxy\\sources\\residential_proxy_rotator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ResidentialProxyRotator"], "functions": ["__init__", "rotate_proxy"], "imports": ["logging"]}, "sources\\tor_bridge_fetcher.py": {"path": "proxy\\sources\\tor_bridge_fetcher.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TorBridgeFetcher"], "functions": ["__init__", "fetch_bridges"], "imports": ["logging"]}, "sources\\__init__.py": {"path": "proxy\\sources\\__init__.py", "line_count": 19, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["free_proxy_crawler", "proxy_api_client", "proxy_list_parser", "tor_bridge_fetcher", "residential_proxy_rotator"]}, "utils\\proxy_utils.py": {"path": "proxy\\utils\\proxy_utils.py", "line_count": 226, "class_count": 0, "method_count": 10, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": [], "functions": ["is_valid_ip", "is_valid_port", "get_ip_info", "format_proxy_url", "parse_proxy_url", "ip_to_int", "int_to_ip", "calculate_proxy_score", "group_proxies_by_country", "group_proxies_by_protocol"], "imports": ["logging", "socket", "struct", "requests", "typing", "proxy.models.proxy"]}, "utils\\__init__.py": {"path": "proxy\\utils\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "validators\\anonymity_checker.py": {"path": "proxy\\validators\\anonymity_checker.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AnonymityChecker"], "functions": ["__init__", "check_anonymity"], "imports": ["logging"]}, "validators\\geo_validator.py": {"path": "proxy\\validators\\geo_validator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["GeoValidator"], "functions": ["__init__", "validate_location"], "imports": ["logging"]}, "validators\\protocol_tester.py": {"path": "proxy\\validators\\protocol_tester.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProtocolTester"], "functions": ["__init__", "test_protocol"], "imports": ["logging"]}, "validators\\proxy_validator.py": {"path": "proxy\\validators\\proxy_validator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyValidator"], "functions": ["__init__", "validate_proxy"], "imports": ["logging"]}, "validators\\speed_tester.py": {"path": "proxy\\validators\\speed_tester.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SpeedTester"], "functions": ["__init__", "test_speed"], "imports": ["logging"]}, "validators\\__init__.py": {"path": "proxy\\validators\\__init__.py", "line_count": 19, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["proxy_validator", "speed_tester", "anonymity_checker", "geo_validator", "protocol_tester"]}, "verification\\proxy_verifier.py": {"path": "proxy\\verification\\proxy_verifier.py", "line_count": 274, "class_count": 1, "method_count": 9, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProxyVerifier"], "functions": ["__init__", "verify_proxy", "verify_proxies", "_select_test_urls", "_test_proxy", "_enrich_proxy_info", "_get_country_info", "_get_anonymity_info", "get_config"], "imports": ["logging", "threading", "time", "requests", "concurrent.futures", "typing", "proxy.models.proxy"]}, "verification\\__init__.py": {"path": "proxy\\verification\\__init__.py", "line_count": 5, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}}, "main_agent_file": "proxy/proxy_agent.py", "completion_score": 84.0, "quality_indicators": {"has_docstrings": 47, "has_error_handling": 16, "has_logging": 46, "has_type_hints": 47, "has_async_methods": 0}}, "anti_sybil": {"name": "anti_sybil", "description": "防女巫智能体", "file_count": 44, "class_count": 37, "method_count": 141, "total_lines": 1849, "has_main_file": true, "has_init_file": true, "has_tests": true, "modules": {"anti_sybil_agent.py": {"path": "anti_sybil\\anti_sybil_agent.py", "line_count": 168, "class_count": 1, "method_count": 7, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["AntiSybilAgent"], "functions": ["__init__", "initialize", "create_identity", "start_session", "execute_task", "end_session", "get_statistics"], "imports": ["logging", "asyncio", "typing", "datetime", "uuid", "uuid"]}, "__init__.py": {"path": "anti_sybil\\__init__.py", "line_count": 11, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["anti_sybil_agent"]}, "analytics\\adaptation_engine.py": {"path": "anti_sybil\\analytics\\adaptation_engine.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AdaptationEngine"], "functions": ["__init__", "adapt_strategy"], "imports": ["logging"]}, "analytics\\behavior_analyzer.py": {"path": "anti_sybil\\analytics\\behavior_analyzer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BehaviorAnalyzer"], "functions": ["__init__", "analyze_behavior"], "imports": ["logging"]}, "analytics\\detection_risk_analyzer.py": {"path": "anti_sybil\\analytics\\detection_risk_analyzer.py", "line_count": 47, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["DetectionRiskAnalyzer"], "functions": ["__init__", "initialize", "analyze_risk", "get_statistics"], "imports": ["logging", "datetime"]}, "analytics\\pattern_optimizer.py": {"path": "anti_sybil\\analytics\\pattern_optimizer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PatternOptimizer"], "functions": ["__init__", "optimize_pattern"], "imports": ["logging"]}, "analytics\\success_rate_tracker.py": {"path": "anti_sybil\\analytics\\success_rate_tracker.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SuccessRateTracker"], "functions": ["__init__", "track_success"], "imports": ["logging"]}, "analytics\\__init__.py": {"path": "anti_sybil\\analytics\\__init__.py", "line_count": 19, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["detection_risk_analyzer", "behavior_analyzer", "pattern_optimizer", "success_rate_tracker", "adaptation_engine"]}, "behaviors\\behavior_designer.py": {"path": "anti_sybil\\behaviors\\behavior_designer.py", "line_count": 20, "class_count": 1, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BehaviorDesigner"], "functions": ["__init__", "design_behavior", "get_statistics"], "imports": ["logging", "random"]}, "behaviors\\browsing_pattern.py": {"path": "anti_sybil\\behaviors\\browsing_pattern.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BrowsingPattern"], "functions": ["__init__", "generate_browsing_pattern"], "imports": ["logging"]}, "behaviors\\habit_simulator.py": {"path": "anti_sybil\\behaviors\\habit_simulator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["HabitSimulator"], "functions": ["__init__", "simulate_habit"], "imports": ["logging"]}, "behaviors\\interaction_style.py": {"path": "anti_sybil\\behaviors\\interaction_style.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["InteractionStyle"], "functions": ["__init__", "generate_interaction_style"], "imports": ["logging"]}, "behaviors\\pattern_generator.py": {"path": "anti_sybil\\behaviors\\pattern_generator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PatternGenerator"], "functions": ["__init__", "generate_pattern"], "imports": ["logging"]}, "behaviors\\session_manager.py": {"path": "anti_sybil\\behaviors\\session_manager.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["Session<PERSON>anager"], "functions": ["__init__", "create_session"], "imports": ["logging"]}, "behaviors\\timing_controller.py": {"path": "anti_sybil\\behaviors\\timing_controller.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TimingController"], "functions": ["__init__", "control_timing"], "imports": ["logging"]}, "behaviors\\__init__.py": {"path": "anti_sybil\\behaviors\\__init__.py", "line_count": 23, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["behavior_designer", "pattern_generator", "timing_controller", "session_manager", "browsing_pattern", "interaction_style", "habit_simulator"]}, "detection_evasion\\behavioral_normalizer.py": {"path": "anti_sybil\\detection_evasion\\behavioral_normalizer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BehavioralNormalizer"], "functions": ["__init__", "normalize_behavior"], "imports": ["logging"]}, "detection_evasion\\bot_detector_analyzer.py": {"path": "anti_sybil\\detection_evasion\\bot_detector_analyzer.py", "line_count": 20, "class_count": 1, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["BotDetectorAnalyzer"], "functions": ["__init__", "initialize", "analyze_page"], "imports": ["logging"]}, "detection_evasion\\captcha_solver.py": {"path": "anti_sybil\\detection_evasion\\captcha_solver.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["CaptchaSolver"], "functions": ["__init__", "solve_captcha"], "imports": ["logging"]}, "detection_evasion\\honeypot_detector.py": {"path": "anti_sybil\\detection_evasion\\honeypot_detector.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["HoneypotDetector"], "functions": ["__init__", "detect_honeypot"], "imports": ["logging"]}, "detection_evasion\\tracking_evader.py": {"path": "anti_sybil\\detection_evasion\\tracking_evader.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TrackingEvader"], "functions": ["__init__", "evade_tracking"], "imports": ["logging"]}, "detection_evasion\\__init__.py": {"path": "anti_sybil\\detection_evasion\\__init__.py", "line_count": 19, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["bot_detector_analyzer", "captcha_solver", "honeypot_detector", "tracking_evader", "behavioral_normalizer"]}, "fingerprints\\browser_fingerprint.py": {"path": "anti_sybil\\fingerprints\\browser_fingerprint.py", "line_count": 289, "class_count": 1, "method_count": 27, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["BrowserFingerprint"], "functions": ["__init__", "generate_fingerprint", "apply_fingerprint", "get_fingerprint", "update_fingerprint", "get_statistics", "_generate_user_agent", "_generate_screen_resolution", "_generate_color_depth", "_generate_timezone", "_generate_language", "_generate_canvas_fingerprint", "_generate_webgl_fingerprint", "_generate_audio_fingerprint", "_generate_platform", "_generate_cpu_class", "_generate_hardware_concurrency", "_generate_device_memory", "_generate_font_list", "_generate_plugin_list", "_generate_mime_types", "_generate_do_not_track", "_generate_connection_type", "_generate_webrtc_ips", "_generate_touch_support", "_generate_java_enabled", "_generate_flash_version"], "imports": ["logging", "json", "uuid", "random", "typing", "datetime"]}, "fingerprints\\canvas_manager.py": {"path": "anti_sybil\\fingerprints\\canvas_manager.py", "line_count": 12, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["CanvasManager"], "functions": ["__init__", "generate_canvas_fingerprint"], "imports": ["logging", "random"]}, "fingerprints\\font_manager.py": {"path": "anti_sybil\\fingerprints\\font_manager.py", "line_count": 13, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["FontManager"], "functions": ["__init__", "generate_font_list"], "imports": ["logging", "random"]}, "fingerprints\\hardware_simulator.py": {"path": "anti_sybil\\fingerprints\\hardware_simulator.py", "line_count": 15, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["HardwareSimulator"], "functions": ["__init__", "generate_hardware_info"], "imports": ["logging", "random"]}, "fingerprints\\language_manager.py": {"path": "anti_sybil\\fingerprints\\language_manager.py", "line_count": 13, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["LanguageManager"], "functions": ["__init__", "generate_language"], "imports": ["logging", "random"]}, "fingerprints\\timezone_simulator.py": {"path": "anti_sybil\\fingerprints\\timezone_simulator.py", "line_count": 13, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TimezoneSimulator"], "functions": ["__init__", "generate_timezone"], "imports": ["logging", "random"]}, "fingerprints\\user_agent_manager.py": {"path": "anti_sybil\\fingerprints\\user_agent_manager.py", "line_count": 16, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["UserAgentManager"], "functions": ["__init__", "generate_user_agent"], "imports": ["logging", "random"]}, "fingerprints\\webrtc_masker.py": {"path": "anti_sybil\\fingerprints\\webrtc_masker.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["WebRTCMasker"], "functions": ["__init__", "mask_webrtc"], "imports": ["logging"]}, "fingerprints\\__init__.py": {"path": "anti_sybil\\fingerprints\\__init__.py", "line_count": 25, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["browser_fingerprint", "user_agent_manager", "canvas_manager", "webrtc_masker", "font_manager", "timezone_simulator", "language_manager", "hardware_simulator"]}, "identity\\consistency_tracker.py": {"path": "anti_sybil\\identity\\consistency_tracker.py", "line_count": 357, "class_count": 1, "method_count": 15, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ConsistencyTracker"], "functions": ["__init__", "record_behavior", "check_consistency", "get_consistency_score", "get_behavior_profile", "get_consistency_report", "_cleanup_old_records", "_update_consistency_score", "_calculate_consistency_score", "_calculate_average_behavior", "_calculate_behavior_similarity", "_generate_recommendations", "_analyze_consistency_trend", "_detect_anomalies", "_generate_improvement_recommendations"], "imports": ["logging", "typing", "datetime", "json"]}, "identity\\identity_manager.py": {"path": "anti_sybil\\identity\\identity_manager.py", "line_count": 52, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["IdentityManager"], "functions": ["__init__", "create_identity", "get_identity", "get_statistics"], "imports": ["logging", "json", "uuid", "typing", "datetime"]}, "identity\\identity_rotator.py": {"path": "anti_sybil\\identity\\identity_rotator.py", "line_count": 234, "class_count": 1, "method_count": 8, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["IdentityRotator"], "functions": ["__init__", "should_rotate", "calculate_rotation_priority", "get_rotation_schedule", "execute_rotation", "get_rotation_statistics", "_get_rotation_reason", "_calculate_rotation_time"], "imports": ["logging", "typing", "datetime"]}, "identity\\persona_generator.py": {"path": "anti_sybil\\identity\\persona_generator.py", "line_count": 177, "class_count": 1, "method_count": 11, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PersonaGenerator"], "functions": ["__init__", "generate_persona", "_generate_browsing_habits", "_generate_time_patterns", "_generate_device_preferences", "_generate_security_awareness", "_generate_communication_style", "_generate_content_preferences", "_generate_interaction_frequency", "get_persona_templates", "validate_persona"], "imports": ["logging", "random", "typing", "datetime"]}, "identity\\__init__.py": {"path": "anti_sybil\\identity\\__init__.py", "line_count": 17, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["identity_manager", "persona_generator", "identity_rotator", "consistency_tracker"]}, "simulators\\click_pattern.py": {"path": "anti_sybil\\simulators\\click_pattern.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ClickPattern"], "functions": ["__init__", "simulate_click"], "imports": ["logging"]}, "simulators\\form_filler.py": {"path": "anti_sybil\\simulators\\form_filler.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["FormFiller"], "functions": ["__init__", "fill_form"], "imports": ["logging"]}, "simulators\\human_simulator.py": {"path": "anti_sybil\\simulators\\human_simulator.py", "line_count": 35, "class_count": 1, "method_count": 5, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["HumanSimulator"], "functions": ["__init__", "initialize", "start_session", "execute_task", "end_session"], "imports": ["logging", "asyncio", "uuid"]}, "simulators\\mouse_movement.py": {"path": "anti_sybil\\simulators\\mouse_movement.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["MouseMovement"], "functions": ["__init__", "simulate_movement"], "imports": ["logging"]}, "simulators\\navigation_simulator.py": {"path": "anti_sybil\\simulators\\navigation_simulator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["NavigationSimulator"], "functions": ["__init__", "simulate_navigation"], "imports": ["logging"]}, "simulators\\scroll_behavior.py": {"path": "anti_sybil\\simulators\\scroll_behavior.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["<PERSON>roll<PERSON>eh<PERSON>or"], "functions": ["__init__", "simulate_scroll"], "imports": ["logging"]}, "simulators\\typing_simulator.py": {"path": "anti_sybil\\simulators\\typing_simulator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TypingSimulator"], "functions": ["__init__", "simulate_typing"], "imports": ["logging"]}, "simulators\\__init__.py": {"path": "anti_sybil\\simulators\\__init__.py", "line_count": 23, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["human_simulator", "mouse_movement", "typing_simulator", "scroll_behavior", "click_pattern", "form_filler", "navigation_simulator"]}}, "main_agent_file": "anti_sybil/anti_sybil_agent.py", "completion_score": 100.0, "quality_indicators": {"has_docstrings": 37, "has_error_handling": 6, "has_logging": 37, "has_type_hints": 37, "has_async_methods": 4}}, "profit_optimization": {"name": "profit_optimization", "description": "收益优化智能体", "file_count": 43, "class_count": 35, "method_count": 95, "total_lines": 1442, "has_main_file": true, "has_init_file": true, "has_tests": false, "modules": {"main.py": {"path": "profit_optimization\\main.py", "line_count": 755, "class_count": 0, "method_count": 12, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": [], "functions": ["parse_arguments", "load_config", "load_data_file", "main", "parse_arguments", "load_config", "load_data_file", "main", "parse_arguments", "load_config", "load_data_file", "main"], "imports": ["os", "sys", "json", "logging", "<PERSON><PERSON><PERSON><PERSON>", "time", "typing", "profit_optimization.profit_optimization_agent", "os", "sys", "json", "logging", "<PERSON><PERSON><PERSON><PERSON>", "time", "typing", "profit_optimization.profit_optimization_agent", "os", "sys", "json", "logging", "<PERSON><PERSON><PERSON><PERSON>", "time", "typing", "profit_optimization.profit_optimization_agent"]}, "profit_optimization_agent.py": {"path": "profit_optimization\\profit_optimization_agent.py", "line_count": 139, "class_count": 1, "method_count": 6, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": true}, "classes": ["ProfitOptimizationAgent"], "functions": ["__init__", "initialize", "track_token", "analyze_selling_opportunity", "execute_trade", "get_statistics"], "imports": ["logging", "asyncio", "typing", "datetime"]}, "__init__.py": {"path": "profit_optimization\\__init__.py", "line_count": 11, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["profit_optimization_agent"]}, "analyzers\\correlation_analyzer.py": {"path": "profit_optimization\\analyzers\\correlation_analyzer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["CorrelationAnalyzer"], "functions": ["__init__", "analyze_correlation"], "imports": ["logging"]}, "analyzers\\liquidity_analyzer.py": {"path": "profit_optimization\\analyzers\\liquidity_analyzer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["LiquidityAnalyzer"], "functions": ["__init__", "analyze_liquidity"], "imports": ["logging"]}, "analyzers\\risk_reward_calculator.py": {"path": "profit_optimization\\analyzers\\risk_reward_calculator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["RiskRewardCalculator"], "functions": ["__init__", "calculate_risk_reward"], "imports": ["logging"]}, "analyzers\\timing_analyzer.py": {"path": "profit_optimization\\analyzers\\timing_analyzer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["<PERSON>ing<PERSON><PERSON><PERSON><PERSON>"], "functions": ["__init__", "analyze_timing"], "imports": ["logging"]}, "analyzers\\trend_analyzer.py": {"path": "profit_optimization\\analyzers\\trend_analyzer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TrendAnalyzer"], "functions": ["__init__", "analyze_trend"], "imports": ["logging"]}, "analyzers\\volatility_analyzer.py": {"path": "profit_optimization\\analyzers\\volatility_analyzer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["VolatilityAnalyzer"], "functions": ["__init__", "analyze_volatility"], "imports": ["logging"]}, "analyzers\\__init__.py": {"path": "profit_optimization\\analyzers\\__init__.py", "line_count": 9, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["timing_analyzer", "liquidity_analyzer", "trend_analyzer", "volatility_analyzer", "correlation_analyzer", "risk_reward_calculator"]}, "data_sources\\cex_connector.py": {"path": "profit_optimization\\data_sources\\cex_connector.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["CEXConnector"], "functions": ["__init__", "get_cex_price"], "imports": ["logging"]}, "data_sources\\dex_connector.py": {"path": "profit_optimization\\data_sources\\dex_connector.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["DEXConnector"], "functions": ["__init__", "get_dex_price"], "imports": ["logging"]}, "data_sources\\news_analyzer.py": {"path": "profit_optimization\\data_sources\\news_analyzer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["NewsAnalyzer"], "functions": ["__init__", "analyze_news"], "imports": ["logging"]}, "data_sources\\price_feed_manager.py": {"path": "profit_optimization\\data_sources\\price_feed_manager.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PriceFeedManager"], "functions": ["__init__", "get_aggregated_price"], "imports": ["logging"]}, "data_sources\\social_sentiment.py": {"path": "profit_optimization\\data_sources\\social_sentiment.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SocialSentiment"], "functions": ["__init__", "analyze_sentiment"], "imports": ["logging"]}, "data_sources\\__init__.py": {"path": "profit_optimization\\data_sources\\__init__.py", "line_count": 8, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["dex_connector", "cex_connector", "price_feed_manager", "social_sentiment", "news_analyzer"]}, "execution\\execution_monitor.py": {"path": "profit_optimization\\execution\\execution_monitor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ExecutionMonitor"], "functions": ["__init__", "monitor_execution"], "imports": ["logging"]}, "execution\\gas_strategy.py": {"path": "profit_optimization\\execution\\gas_strategy.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["GasStrategy"], "functions": ["__init__", "optimize_gas"], "imports": ["logging"]}, "execution\\order_executor.py": {"path": "profit_optimization\\execution\\order_executor.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["OrderExecutor"], "functions": ["__init__", "execute_order"], "imports": ["logging"]}, "execution\\slippage_manager.py": {"path": "profit_optimization\\execution\\slippage_manager.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SlippageManager"], "functions": ["__init__", "calculate_slippage"], "imports": ["logging"]}, "execution\\swap_manager.py": {"path": "profit_optimization\\execution\\swap_manager.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SwapManager"], "functions": ["__init__", "execute_swap"], "imports": ["logging"]}, "execution\\__init__.py": {"path": "profit_optimization\\execution\\__init__.py", "line_count": 8, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["order_executor", "swap_manager", "gas_strategy", "slippage_manager", "execution_monitor"]}, "reporting\\performance_reporter.py": {"path": "profit_optimization\\reporting\\performance_reporter.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PerformanceReporter"], "functions": ["__init__", "generate_report"], "imports": ["logging"]}, "reporting\\portfolio_visualizer.py": {"path": "profit_optimization\\reporting\\portfolio_visualizer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PortfolioVisualizer"], "functions": ["__init__", "create_visualization"], "imports": ["logging"]}, "reporting\\profit_calculator.py": {"path": "profit_optimization\\reporting\\profit_calculator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ProfitCalculator"], "functions": ["__init__", "calculate_profit"], "imports": ["logging"]}, "reporting\\strategy_evaluator.py": {"path": "profit_optimization\\reporting\\strategy_evaluator.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["StrategyEvaluator"], "functions": ["__init__", "evaluate_strategy"], "imports": ["logging"]}, "reporting\\tax_reporter.py": {"path": "profit_optimization\\reporting\\tax_reporter.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TaxReporter"], "functions": ["__init__", "generate_tax_report"], "imports": ["logging"]}, "reporting\\__init__.py": {"path": "profit_optimization\\reporting\\__init__.py", "line_count": 8, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["profit_calculator", "performance_reporter", "tax_reporter", "portfolio_visualizer", "strategy_evaluator"]}, "strategies\\dca_strategy.py": {"path": "profit_optimization\\strategies\\dca_strategy.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["DCAStrategy"], "functions": ["__init__", "execute_dca"], "imports": ["logging"]}, "strategies\\holding_strategy.py": {"path": "profit_optimization\\strategies\\holding_strategy.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["HoldingStrategy"], "functions": ["__init__", "execute_hold"], "imports": ["logging"]}, "strategies\\limit_order_strategy.py": {"path": "profit_optimization\\strategies\\limit_order_strategy.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["LimitOrderStrategy"], "functions": ["__init__", "create_limit_order"], "imports": ["logging"]}, "strategies\\portfolio_optimizer.py": {"path": "profit_optimization\\strategies\\portfolio_optimizer.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PortfolioOptimizer"], "functions": ["__init__", "optimize_portfolio"], "imports": ["logging"]}, "strategies\\selling_strategy.py": {"path": "profit_optimization\\strategies\\selling_strategy.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["SellingStrategy"], "functions": ["__init__", "execute_sell"], "imports": ["logging"]}, "strategies\\strategy_backtester.py": {"path": "profit_optimization\\strategies\\strategy_backtester.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["StrategyBacktester"], "functions": ["__init__", "backtest_strategy"], "imports": ["logging"]}, "strategies\\strategy_factory.py": {"path": "profit_optimization\\strategies\\strategy_factory.py", "line_count": 11, "class_count": 1, "method_count": 2, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["StrategyFactory"], "functions": ["__init__", "create_strategy"], "imports": ["logging"]}, "strategies\\__init__.py": {"path": "profit_optimization\\strategies\\__init__.py", "line_count": 13, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": []}, "trackers\\airdrop_tracker.py": {"path": "profit_optimization\\trackers\\airdrop_tracker.py", "line_count": 30, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["AirdropTracker"], "functions": ["__init__", "track_airdrop", "get_airdrop_status", "get_all_airdrops"], "imports": ["logging", "typing"]}, "trackers\\exchange_monitor.py": {"path": "profit_optimization\\trackers\\exchange_monitor.py", "line_count": 15, "class_count": 1, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ExchangeMonitor"], "functions": ["__init__", "monitor_exchange_listings", "get_exchange_prices"], "imports": ["logging"]}, "trackers\\listing_detector.py": {"path": "profit_optimization\\trackers\\listing_detector.py", "line_count": 15, "class_count": 1, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["ListingDetector"], "functions": ["__init__", "detect_new_listings", "check_listing_status"], "imports": ["logging"]}, "trackers\\market_monitor.py": {"path": "profit_optimization\\trackers\\market_monitor.py", "line_count": 15, "class_count": 1, "method_count": 3, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": false, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["MarketMonitor"], "functions": ["__init__", "monitor_market_trends", "get_market_sentiment"], "imports": ["logging"]}, "trackers\\price_alert_system.py": {"path": "profit_optimization\\trackers\\price_alert_system.py", "line_count": 46, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["PriceAlertSystem"], "functions": ["__init__", "set_price_alert", "check_alerts", "_should_trigger_alert"], "imports": ["logging"]}, "trackers\\token_value_tracker.py": {"path": "profit_optimization\\trackers\\token_value_tracker.py", "line_count": 41, "class_count": 1, "method_count": 4, "function_count": 0, "quality": {"has_docstrings": true, "has_error_handling": true, "has_logging": true, "has_type_hints": true, "has_async_methods": false}, "classes": ["TokenValueTracker"], "functions": ["__init__", "track_token_value", "update_token_price", "get_token_value"], "imports": ["logging", "typing"]}, "trackers\\__init__.py": {"path": "profit_optimization\\trackers\\__init__.py", "line_count": 21, "class_count": 0, "method_count": 0, "function_count": 0, "quality": {"has_docstrings": false, "has_error_handling": false, "has_logging": false, "has_type_hints": false, "has_async_methods": false}, "classes": [], "functions": [], "imports": ["airdrop_tracker", "token_value_tracker", "market_monitor", "exchange_monitor", "listing_detector", "price_alert_system"]}}, "main_agent_file": "profit_optimization/profit_optimization_agent.py", "completion_score": 90.0, "quality_indicators": {"has_docstrings": 36, "has_error_handling": 5, "has_logging": 36, "has_type_hints": 36, "has_async_methods": 1}}}}