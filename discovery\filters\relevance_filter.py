"""
相关性过滤器

该模块实现了基于相关性的项目过滤器。
"""

import logging
import re
from typing import Dict, List, Any, Set


class RelevanceFilter:
    """相关性过滤器，基于相关性过滤项目"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化相关性过滤器
        
        Args:
            config: 配置字典，包含相关性过滤器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 相关性关键词及其权重
        self.relevance_keywords = config.get('relevance_keywords', {
            'airdrop': 10,
            'token': 8,
            'crypto': 7,
            'blockchain': 7,
            'defi': 6,
            'nft': 6,
            'free': 5,
            'claim': 5,
            'reward': 5,
            'earn': 4,
            'testnet': 4,
            'launch': 3,
            'project': 3,
            'community': 3,
            'ecosystem': 2,
            'protocol': 2,
            'platform': 2,
            'decentralized': 2,
            'finance': 1,
            'application': 1
        })
        
        # 最小相关性分数
        self.min_relevance_score = config.get('min_relevance_score', 10)
        
        # 是否区分大小写
        self.case_sensitive = config.get('case_sensitive', False)
        
        # 是否使用正则表达式匹配
        self.use_regex = config.get('use_regex', False)
        
        # 编译正则表达式
        if self.use_regex:
            self.regex_patterns = {}
            for keyword, weight in self.relevance_keywords.items():
                flags = 0 if self.case_sensitive else re.IGNORECASE
                self.regex_patterns[keyword] = re.compile(r'\b' + re.escape(keyword) + r'\b', flags)
    
    def filter(self, projects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤项目
        
        Args:
            projects: 项目列表
            
        Returns:
            过滤后的项目列表
        """
        filtered_projects = []
        
        for project in projects:
            relevance_score = self._calculate_relevance_score(project)
            
            # 添加相关性分数到项目
            project['relevance_score'] = relevance_score
            
            if relevance_score >= self.min_relevance_score:
                filtered_projects.append(project)
        
        # 按相关性分数降序排序
        filtered_projects.sort(key=lambda p: p.get('relevance_score', 0), reverse=True)
        
        self.logger.debug(f"相关性过滤: {len(projects)} -> {len(filtered_projects)}")
        return filtered_projects
    
    def _calculate_relevance_score(self, project: Dict[str, Any]) -> float:
        """
        计算项目的相关性分数
        
        Args:
            project: 项目字典
            
        Returns:
            相关性分数
        """
        # 提取项目文本
        project_text = self._extract_project_text(project)
        
        if not self.case_sensitive and not self.use_regex:
            project_text = project_text.lower()
        
        score = 0.0
        
        if self.use_regex:
            # 使用正则表达式匹配
            for keyword, weight in self.relevance_keywords.items():
                pattern = self.regex_patterns[keyword]
                matches = pattern.findall(project_text)
                if matches:
                    score += weight * len(matches)
        else:
            # 使用简单的字符串匹配
            for keyword, weight in self.relevance_keywords.items():
                keyword_to_match = keyword if self.case_sensitive else keyword.lower()
                count = project_text.count(keyword_to_match)
                if count > 0:
                    score += weight * count
        
        return score
    
    def _extract_project_text(self, project: Dict[str, Any]) -> str:
        """
        提取项目文本
        
        Args:
            project: 项目字典
            
        Returns:
            项目文本
        """
        text_parts = []
        
        # 添加项目名称
        if 'name' in project:
            text_parts.append(str(project['name']))
        
        # 添加项目描述
        if 'description' in project:
            text_parts.append(str(project['description']))
        
        # 添加项目类型
        if 'project_type' in project:
            text_parts.append(str(project['project_type']))
        
        # 添加区块链平台
        if 'blockchain' in project:
            text_parts.append(str(project['blockchain']))
        
        # 添加标签
        if 'tags' in project and isinstance(project['tags'], list):
            text_parts.extend([str(tag) for tag in project['tags']])
        
        return ' '.join(text_parts)
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            config: 新的配置字典
        """
        self.config = config
        
        # 更新配置参数
        self.relevance_keywords = config.get('relevance_keywords', self.relevance_keywords)
        self.min_relevance_score = config.get('min_relevance_score', self.min_relevance_score)
        self.case_sensitive = config.get('case_sensitive', self.case_sensitive)
        self.use_regex = config.get('use_regex', self.use_regex)
        
        # 重新编译正则表达式
        if self.use_regex:
            self.regex_patterns = {}
            for keyword, weight in self.relevance_keywords.items():
                flags = 0 if self.case_sensitive else re.IGNORECASE
                self.regex_patterns[keyword] = re.compile(r'\b' + re.escape(keyword) + r'\b', flags)
        
        self.logger.info("已更新相关性过滤器配置")