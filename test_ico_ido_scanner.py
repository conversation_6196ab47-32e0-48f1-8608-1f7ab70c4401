"""
测试 ICO/IDO 扫描器

该脚本用于测试 ICO/IDO 扫描器的功能。
"""

import json
import logging
import sys
from discovery.sources.ico_ido_scanner import ICOIDOScanner

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# 创建简化的配置，只测试CoinMarketCap（使用CoinGecko API）
scanner_config = {
    "enabled": True,
    "timeout": 30,
    "max_retries": 3,
    "icodrops": {
        "enabled": False
    },
    "cryptorank": {
        "enabled": False
    },
    "coinmarketcap": {
        "enabled": True
    },
    "binance_launchpad": {
        "enabled": False
    },
    "kucoin_spotlight": {
        "enabled": False
    },
    "gate_startup": {
        "enabled": False
    },
    "trustswap": {
        "enabled": False
    },
    "seedify": {
        "enabled": False
    },
    "polkastarter": {
        "enabled": False
    },
    "dao_maker": {
        "enabled": False
    }
}

# 创建 ICO/IDO 扫描器
scanner = ICOIDOScanner(scanner_config)

# 直接调用CoinMarketCap方法（使用CoinGecko API）
projects = scanner._get_coinmarketcap_projects()

# 打印项目信息
print(f"找到 {len(projects)} 个项目:")
for i, project in enumerate(projects):
    print(f"\n项目 {i+1}:")
    print(f"名称: {project.get('name')}")
    print(f"描述: {project.get('description')[:100] if project.get('description') else '无描述'}...")
    print(f"URL: {project.get('url')}")
    print(f"类型: {project.get('project_type')}")
    print(f"区块链: {project.get('blockchain')}")
    print(f"来源: {project.get('discovery_source')}")