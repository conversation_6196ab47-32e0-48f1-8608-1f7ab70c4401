"""
项目源管理器

该模块负责管理多个项目源，并从中获取项目。
"""

import logging
import threading
import time
from typing import Dict, List, Any, Optional, Set, Tuple

from discovery.sources.base_source import BaseProjectSource
from discovery.sources.twitter_source import TwitterSource
from discovery.sources.discord_source import DiscordSource
from discovery.sources.telegram_source import TelegramSource
from discovery.sources.medium_source import MediumSource
from discovery.sources.github_source import GitHubSource
from discovery.sources.blockchain_source import BlockchainSource
from discovery.sources.aggregator_source import AggregatorSource
from discovery.sources.enhanced_aggregator import EnhancedAggregatorSource
from discovery.sources.ico_ido_scanner import ICOIDOScanner
from discovery.sources.coingecko_scanner import CoinGeckoScanner


class SourceManager:
    """项目源管理器，负责管理多个项目源，并从中获取项目"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目源管理器
        
        Args:
            config: 配置字典，包含项目源的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        self._sources: Dict[str, BaseProjectSource] = {}
        self._source_stats: Dict[str, Dict[str, Any]] = {}
        
        # 初始化项目源
        self._init_sources()
    
    def _init_sources(self) -> None:
        """初始化项目源"""
        # 获取启用的源
        enabled_sources = self.config.get('enabled_sources', [])
        
        # 如果没有指定，默认启用所有源
        if not enabled_sources:
            enabled_sources = ['twitter', 'discord', 'telegram', 'medium', 'github', 'blockchain', 'aggregator', 'enhanced_aggregator', 'ico_ido_scanner', 'coingecko_scanner']
        
        # 初始化源
        for source_name in enabled_sources:
            source_config = self.config.get(source_name, {})
            
            if source_name == 'twitter':
                self._sources[source_name] = TwitterSource(source_config)
            elif source_name == 'discord':
                self._sources[source_name] = DiscordSource(source_config)
            elif source_name == 'telegram':
                self._sources[source_name] = TelegramSource(source_config)
            elif source_name == 'medium':
                self._sources[source_name] = MediumSource(source_config)
            elif source_name == 'github':
                self._sources[source_name] = GitHubSource(source_config)
            elif source_name == 'blockchain':
                self._sources[source_name] = BlockchainSource(source_config)
            elif source_name == 'aggregator':
                self._sources[source_name] = AggregatorSource(source_config)
            elif source_name == 'enhanced_aggregator':
                self._sources[source_name] = EnhancedAggregatorSource(source_config)
            elif source_name == 'ico_ido_scanner':
                self._sources[source_name] = ICOIDOScanner(source_config)
            elif source_name == 'coingecko_scanner':
                self._sources[source_name] = CoinGeckoScanner(source_config)
            else:
                self.logger.warning(f"未知的项目源: {source_name}")
                continue
            
            # 初始化源统计信息
            self._source_stats[source_name] = {
                'last_fetch_time': 0,
                'last_fetch_count': 0,
                'total_fetch_count': 0,
                'success_count': 0,
                'fail_count': 0
            }
            
            self.logger.info(f"已初始化项目源: {source_name}")
    
    def get_sources(self) -> Dict[str, BaseProjectSource]:
        """
        获取所有项目源
        
        Returns:
            项目源字典
        """
        with self._lock:
            return self._sources.copy()
    
    def get_source(self, source_name: str) -> Optional[BaseProjectSource]:
        """
        获取指定项目源
        
        Args:
            source_name: 项目源名称
            
        Returns:
            项目源对象，如果不存在则返回None
        """
        with self._lock:
            return self._sources.get(source_name)
    
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        with self._lock:
            if not self._sources:
                self.logger.warning("没有可用的项目源")
                return []
            
            # 计算每个源需要获取的项目数量
            source_counts = self._calculate_source_counts(count)
            
            # 从每个源获取项目
            all_projects = []
            for source_name, source_count in source_counts.items():
                if source_count <= 0:
                    continue
                
                source = self._sources.get(source_name)
                if not source:
                    continue
                
                try:
                    self.logger.info(f"从源 '{source_name}' 获取 {source_count} 个项目")
                    
                    # 记录开始时间
                    start_time = time.time()
                    
                    # 获取项目
                    projects = source.get_projects(source_count)
                    
                    # 更新统计信息
                    self._source_stats[source_name]['last_fetch_time'] = start_time
                    self._source_stats[source_name]['last_fetch_count'] = len(projects)
                    self._source_stats[source_name]['total_fetch_count'] += len(projects)
                    self._source_stats[source_name]['success_count'] += 1
                    
                    # 设置项目源
                    for project in projects:
                        project['discovery_source'] = source_name
                    
                    all_projects.extend(projects)
                    
                    self.logger.info(f"从源 '{source_name}' 获取到 {len(projects)} 个项目")
                
                except Exception as e:
                    self.logger.error(f"从源 '{source_name}' 获取项目时出错: {str(e)}")
                    self._source_stats[source_name]['fail_count'] += 1
            
            # 去重
            unique_projects = []
            seen_urls = set()
            for project in all_projects:
                url = project.get('url')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_projects.append(project)
            
            self.logger.info(f"总共获取到 {len(unique_projects)} 个唯一项目")
            
            return unique_projects
    
    def _calculate_source_counts(self, total_count: int) -> Dict[str, int]:
        """
        计算每个源需要获取的项目数量
        
        Args:
            total_count: 总共需要获取的项目数量
            
        Returns:
            每个源需要获取的项目数量字典
        """
        # 获取可用的源
        available_sources = list(self._sources.keys())
        
        if not available_sources:
            return {}
        
        # 获取源权重
        weights = {}
        for source_name in available_sources:
            # 默认权重为1
            weight = self.config.get(source_name, {}).get('weight', 1.0)
            
            # 如果源最近失败，降低权重
            stats = self._source_stats.get(source_name, {})
            if stats.get('fail_count', 0) > 0:
                last_fail_ratio = stats.get('fail_count', 0) / (stats.get('success_count', 0) + stats.get('fail_count', 0))
                weight *= (1.0 - last_fail_ratio)
            
            weights[source_name] = max(0.1, weight)  # 最小权重为0.1
        
        # 计算总权重
        total_weight = sum(weights.values())
        
        # 计算每个源的项目数量
        source_counts = {}
        remaining = total_count
        
        for source_name in available_sources:
            if total_weight <= 0:
                source_counts[source_name] = remaining // len(available_sources)
            else:
                source_counts[source_name] = int(total_count * weights[source_name] / total_weight)
            
            remaining -= source_counts[source_name]
        
        # 分配剩余的项目
        if remaining > 0:
            # 按权重排序
            sorted_sources = sorted(available_sources, key=lambda s: weights[s], reverse=True)
            
            for source_name in sorted_sources:
                if remaining <= 0:
                    break
                
                source_counts[source_name] += 1
                remaining -= 1
        
        return source_counts
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取项目源状态
        
        Returns:
            项目源状态字典
        """
        with self._lock:
            return {
                'sources': list(self._sources.keys()),
                'stats': self._source_stats.copy()
            }
    
    def get_source_names(self) -> List[str]:
        """
        获取所有项目源名称
        
        Returns:
            项目源名称列表
        """
        with self._lock:
            return list(self._sources.keys())