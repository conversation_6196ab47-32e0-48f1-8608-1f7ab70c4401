"""
命令分发器

该模块提供命令分发功能，允许协调控制智能体向其他智能体发送命令并接收响应。
"""

import logging
import threading
import uuid
import time
from typing import Dict, Any, Callable, Optional, List

from coordinator.communication.message_broker import MessageBroker


class CommandDispatcher:
    """命令分发器，负责向智能体发送命令并接收响应"""
    
    def __init__(self, message_broker: MessageBroker):
        """
        初始化命令分发器
        
        Args:
            message_broker: 消息代理实例
        """
        self.logger = logging.getLogger(__name__)
        self.message_broker = message_broker
        self._lock = threading.RLock()
        self._command_topic = "system.commands"
        self._response_topic = "system.responses"
        self._pending_commands: Dict[str, Dict[str, Any]] = {}  # command_id -> command_info
        self._command_handlers: Dict[str, Callable] = {}  # command_type -> handler
        self._command_subscriber_id = None
        self._response_subscriber_id = None
    
    def start(self) -> None:
        """启动命令分发器"""
        with self._lock:
            if self._command_subscriber_id is not None:
                self.logger.warning("命令分发器已经在运行")
                return
            
            # 创建命令和响应主题
            self.message_broker.create_topic(self._command_topic)
            self.message_broker.create_topic(self._response_topic)
            
            # 订阅命令主题
            self._command_subscriber_id = self.message_broker.subscribe(
                self._handle_command,
                self._command_topic
            )
            
            # 订阅响应主题
            self._response_subscriber_id = self.message_broker.subscribe(
                self._handle_response,
                self._response_topic
            )
            
            self.logger.info("命令分发器已启动")
    
    def stop(self) -> None:
        """停止命令分发器"""
        with self._lock:
            if self._command_subscriber_id is None:
                self.logger.warning("命令分发器未在运行")
                return
            
            # 取消订阅命令和响应主题
            self.message_broker.unsubscribe(self._command_subscriber_id, self._command_topic)
            self.message_broker.unsubscribe(self._response_subscriber_id, self._response_topic)
            
            self._command_subscriber_id = None
            self._response_subscriber_id = None
            
            self.logger.info("命令分发器已停止")
    
    def register_handler(self, command_type: str, handler: Callable[[Dict[str, Any]], Dict[str, Any]]) -> None:
        """
        注册命令处理器
        
        Args:
            command_type: 命令类型
            handler: 处理器函数，接收命令数据并返回响应数据
        """
        with self._lock:
            self._command_handlers[command_type] = handler
            self.logger.info(f"已注册命令类型 '{command_type}' 的处理器")
    
    def unregister_handler(self, command_type: str) -> bool:
        """
        注销命令处理器
        
        Args:
            command_type: 命令类型
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if command_type not in self._command_handlers:
                self.logger.warning(f"命令类型 '{command_type}' 没有注册处理器")
                return False
            
            del self._command_handlers[command_type]
            self.logger.info(f"已注销命令类型 '{command_type}' 的处理器")
            return True
    
    def send_command(self, target_agent: str, command_type: str, data: Dict[str, Any] = None,
                    timeout: float = 10.0) -> Optional[Dict[str, Any]]:
        """
        发送命令并等待响应
        
        Args:
            target_agent: 目标智能体名称
            command_type: 命令类型
            data: 命令数据
            timeout: 超时时间（秒）
            
        Returns:
            响应数据，如果超时或出错则返回None
        """
        # 生成命令ID
        command_id = str(uuid.uuid4())
        
        # 创建命令对象
        command = {
            "id": command_id,
            "type": command_type,
            "target": target_agent,
            "data": data or {},
            "timestamp": time.time()
        }
        
        # 创建事件和锁，用于等待响应
        response_event = threading.Event()
        response_data = [None]  # 使用列表作为可变容器
        
        # 注册挂起的命令
        with self._lock:
            self._pending_commands[command_id] = {
                "command": command,
                "event": response_event,
                "response": response_data,
                "timestamp": time.time()
            }
        
        # 发布命令
        result = self.message_broker.publish(self._command_topic, command)
        
        if not result:
            self.logger.error(f"发送命令 '{command_type}' 到智能体 '{target_agent}' 失败")
            with self._lock:
                if command_id in self._pending_commands:
                    del self._pending_commands[command_id]
            return None
        
        self.logger.debug(f"已发送命令 '{command_type}' 到智能体 '{target_agent}'")
        
        # 等待响应或超时
        if response_event.wait(timeout):
            # 收到响应
            with self._lock:
                if command_id in self._pending_commands:
                    del self._pending_commands[command_id]
            
            return response_data[0]
        else:
            # 超时
            self.logger.warning(f"等待命令 '{command_type}' 的响应超时")
            with self._lock:
                if command_id in self._pending_commands:
                    del self._pending_commands[command_id]
            
            return None
    
    def send_command_async(self, target_agent: str, command_type: str, data: Dict[str, Any] = None,
                          callback: Optional[Callable[[Optional[Dict[str, Any]]], None]] = None) -> str:
        """
        异步发送命令
        
        Args:
            target_agent: 目标智能体名称
            command_type: 命令类型
            data: 命令数据
            callback: 回调函数，接收响应数据作为参数
            
        Returns:
            命令ID
        """
        # 生成命令ID
        command_id = str(uuid.uuid4())
        
        # 创建命令对象
        command = {
            "id": command_id,
            "type": command_type,
            "target": target_agent,
            "data": data or {},
            "timestamp": time.time()
        }
        
        # 注册挂起的命令
        with self._lock:
            self._pending_commands[command_id] = {
                "command": command,
                "callback": callback,
                "timestamp": time.time()
            }
        
        # 发布命令
        result = self.message_broker.publish(self._command_topic, command)
        
        if not result:
            self.logger.error(f"发送命令 '{command_type}' 到智能体 '{target_agent}' 失败")
            with self._lock:
                if command_id in self._pending_commands:
                    del self._pending_commands[command_id]
            
            # 调用回调函数，传入None表示失败
            if callback:
                try:
                    callback(None)
                except Exception as e:
                    self.logger.error(f"调用命令回调函数时出错: {str(e)}")
        else:
            self.logger.debug(f"已异步发送命令 '{command_type}' 到智能体 '{target_agent}'")
        
        return command_id
    
    def _handle_command(self, topic: str, command: Dict[str, Any]) -> None:
        """
        处理接收到的命令
        
        Args:
            topic: 主题名称
            command: 命令对象
        """
        command_id = command.get("id")
        command_type = command.get("type")
        target_agent = command.get("target")
        command_data = command.get("data", {})
        
        if not command_id or not command_type or not target_agent:
            self.logger.warning("收到无效的命令")
            return
        
        # 检查是否是发给当前智能体的命令
        # 这里假设当前智能体的名称是"coordinator"
        if target_agent != "coordinator":
            return
        
        self.logger.debug(f"收到命令 '{command_type}'")
        
        # 查找命令处理器
        handler = None
        with self._lock:
            handler = self._command_handlers.get(command_type)
        
        if not handler:
            self.logger.warning(f"没有找到命令类型 '{command_type}' 的处理器")
            # 发送错误响应
            self._send_response(command_id, {"error": f"未知的命令类型: {command_type}"})
            return
        
        # 调用处理器
        try:
            response_data = handler(command_data)
            # 发送响应
            self._send_response(command_id, response_data or {})
        except Exception as e:
            self.logger.error(f"处理命令 '{command_type}' 时出错: {str(e)}")
            # 发送错误响应
            self._send_response(command_id, {"error": str(e)})
    
    def _handle_response(self, topic: str, response: Dict[str, Any]) -> None:
        """
        处理接收到的响应
        
        Args:
            topic: 主题名称
            response: 响应对象
        """
        command_id = response.get("command_id")
        response_data = response.get("data", {})
        
        if not command_id:
            self.logger.warning("收到无效的响应")
            return
        
        # 查找挂起的命令
        with self._lock:
            if command_id not in self._pending_commands:
                self.logger.warning(f"收到未知命令ID '{command_id}' 的响应")
                return
            
            command_info = self._pending_commands[command_id]
        
        # 处理响应
        if "event" in command_info:
            # 同步命令
            command_info["response"][0] = response_data
            command_info["event"].set()
        elif "callback" in command_info:
            # 异步命令
            callback = command_info["callback"]
            if callback:
                try:
                    callback(response_data)
                except Exception as e:
                    self.logger.error(f"调用命令回调函数时出错: {str(e)}")
            
            # 移除挂起的命令
            with self._lock:
                if command_id in self._pending_commands:
                    del self._pending_commands[command_id]
    
    def _send_response(self, command_id: str, data: Dict[str, Any]) -> bool:
        """
        发送响应
        
        Args:
            command_id: 命令ID
            data: 响应数据
            
        Returns:
            如果成功发送则返回True，否则返回False
        """
        # 创建响应对象
        response = {
            "command_id": command_id,
            "data": data,
            "timestamp": time.time()
        }
        
        # 发布响应
        return self.message_broker.publish(self._response_topic, response)
    
    def cleanup_pending_commands(self, max_age: float = 60.0) -> int:
        """
        清理超时的挂起命令
        
        Args:
            max_age: 最大命令年龄（秒）
            
        Returns:
            清理的命令数量
        """
        now = time.time()
        to_remove = []
        
        with self._lock:
            for command_id, command_info in self._pending_commands.items():
                if now - command_info["timestamp"] > max_age:
                    to_remove.append(command_id)
            
            for command_id in to_remove:
                command_info = self._pending_commands[command_id]
                
                # 如果是同步命令，设置事件以解除阻塞
                if "event" in command_info:
                    command_info["event"].set()
                
                # 如果是异步命令，调用回调函数
                elif "callback" in command_info and command_info["callback"]:
                    try:
                        command_info["callback"](None)  # 传入None表示超时
                    except Exception as e:
                        self.logger.error(f"调用命令回调函数时出错: {str(e)}")
                
                del self._pending_commands[command_id]
        
        if to_remove:
            self.logger.info(f"已清理 {len(to_remove)} 个超时的挂起命令")
        
        return len(to_remove)
    
    def get_pending_commands(self) -> List[Dict[str, Any]]:
        """
        获取所有挂起的命令
        
        Returns:
            挂起命令列表
        """
        with self._lock:
            return [info["command"] for info in self._pending_commands.values()]
    
    def get_registered_command_types(self) -> List[str]:
        """
        获取所有已注册的命令类型
        
        Returns:
            命令类型列表
        """
        with self._lock:
            return list(self._command_handlers.keys())