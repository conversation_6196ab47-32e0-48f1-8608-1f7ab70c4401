"""
Consistency Tracker

一致性跟踪器，负责确保同一身份在不同会话中保持行为一致性。
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import json


class ConsistencyTracker:
    """一致性跟踪器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 一致性配置
        self.consistency_config = {
            "tolerance_threshold": self.config.get("tolerance_threshold", 0.1),
            "tracking_window_days": self.config.get("tracking_window_days", 7),
            "min_samples": self.config.get("min_samples", 3)
        }
        
        # 行为历史记录
        self.behavior_history: Dict[str, List[Dict[str, Any]]] = {}
        
        # 一致性分数缓存
        self.consistency_scores: Dict[str, float] = {}
    
    def record_behavior(self, identity_id: str, behavior_data: Dict[str, Any]) -> bool:
        """
        记录身份行为数据
        
        Args:
            identity_id: 身份ID
            behavior_data: 行为数据
            
        Returns:
            bool: 记录是否成功
        """
        try:
            if identity_id not in self.behavior_history:
                self.behavior_history[identity_id] = []
            
            # 添加时间戳
            behavior_record = {
                "timestamp": datetime.utcnow().isoformat(),
                "data": behavior_data
            }
            
            self.behavior_history[identity_id].append(behavior_record)
            
            # 清理过期数据
            self._cleanup_old_records(identity_id)
            
            # 更新一致性分数
            self._update_consistency_score(identity_id)
            
            self.logger.debug(f"Recorded behavior for identity {identity_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error recording behavior: {e}")
            return False
    
    def check_consistency(self, identity_id: str, new_behavior: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查新行为与历史行为的一致性
        
        Args:
            identity_id: 身份ID
            new_behavior: 新的行为数据
            
        Returns:
            Dict[str, Any]: 一致性检查结果
        """
        try:
            if identity_id not in self.behavior_history:
                return {
                    "consistent": True,
                    "score": 1.0,
                    "reason": "no_history",
                    "recommendations": []
                }
            
            history = self.behavior_history[identity_id]
            
            if len(history) < self.consistency_config["min_samples"]:
                return {
                    "consistent": True,
                    "score": 0.8,
                    "reason": "insufficient_data",
                    "recommendations": ["collect_more_data"]
                }
            
            # 计算一致性分数
            consistency_score = self._calculate_consistency_score(identity_id, new_behavior)
            
            # 判断是否一致
            is_consistent = consistency_score >= (1.0 - self.consistency_config["tolerance_threshold"])
            
            # 生成建议
            recommendations = self._generate_recommendations(identity_id, new_behavior, consistency_score)
            
            result = {
                "consistent": is_consistent,
                "score": consistency_score,
                "reason": "analysis_complete",
                "recommendations": recommendations,
                "details": self._get_consistency_details(identity_id, new_behavior)
            }
            
            self.logger.info(f"Consistency check for {identity_id}: {consistency_score:.3f}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error checking consistency: {e}")
            return {
                "consistent": False,
                "score": 0.0,
                "reason": "error",
                "recommendations": ["retry_check"]
            }
    
    def get_consistency_score(self, identity_id: str) -> float:
        """
        获取身份的一致性分数
        
        Args:
            identity_id: 身份ID
            
        Returns:
            float: 一致性分数 (0-1)
        """
        return self.consistency_scores.get(identity_id, 1.0)
    
    def get_behavior_profile(self, identity_id: str) -> Dict[str, Any]:
        """
        获取身份的行为配置文件
        
        Args:
            identity_id: 身份ID
            
        Returns:
            Dict[str, Any]: 行为配置文件
        """
        try:
            if identity_id not in self.behavior_history:
                return {}
            
            history = self.behavior_history[identity_id]
            
            if not history:
                return {}
            
            # 计算平均行为特征
            profile = self._calculate_average_behavior(history)
            
            # 添加统计信息
            profile["statistics"] = {
                "total_sessions": len(history),
                "first_recorded": history[0]["timestamp"],
                "last_recorded": history[-1]["timestamp"],
                "consistency_score": self.get_consistency_score(identity_id)
            }
            
            return profile
            
        except Exception as e:
            self.logger.error(f"Error getting behavior profile: {e}")
            return {}
    
    def get_consistency_report(self, identity_id: str) -> Dict[str, Any]:
        """
        获取详细的一致性报告
        
        Args:
            identity_id: 身份ID
            
        Returns:
            Dict[str, Any]: 一致性报告
        """
        try:
            if identity_id not in self.behavior_history:
                return {"error": "no_data"}
            
            history = self.behavior_history[identity_id]
            consistency_score = self.get_consistency_score(identity_id)
            
            # 分析一致性趋势
            trend_analysis = self._analyze_consistency_trend(identity_id)
            
            # 识别异常行为
            anomalies = self._detect_anomalies(identity_id)
            
            report = {
                "identity_id": identity_id,
                "overall_score": consistency_score,
                "data_points": len(history),
                "trend_analysis": trend_analysis,
                "anomalies": anomalies,
                "recommendations": self._generate_improvement_recommendations(identity_id),
                "generated_at": datetime.utcnow().isoformat()
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"Error generating consistency report: {e}")
            return {"error": str(e)}
    
    def _cleanup_old_records(self, identity_id: str):
        """清理过期的行为记录"""
        try:
            if identity_id not in self.behavior_history:
                return
            
            cutoff_time = datetime.utcnow() - timedelta(days=self.consistency_config["tracking_window_days"])
            
            self.behavior_history[identity_id] = [
                record for record in self.behavior_history[identity_id]
                if datetime.fromisoformat(record["timestamp"]) >= cutoff_time
            ]
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old records: {e}")
    
    def _update_consistency_score(self, identity_id: str):
        """更新一致性分数"""
        try:
            if identity_id not in self.behavior_history:
                return
            
            history = self.behavior_history[identity_id]
            
            if len(history) < 2:
                self.consistency_scores[identity_id] = 1.0
                return
            
            # 计算最近行为与历史平均的一致性
            recent_behavior = history[-1]["data"]
            consistency_score = self._calculate_consistency_score(identity_id, recent_behavior)
            
            self.consistency_scores[identity_id] = consistency_score
            
        except Exception as e:
            self.logger.error(f"Error updating consistency score: {e}")
    
    def _calculate_consistency_score(self, identity_id: str, new_behavior: Dict[str, Any]) -> float:
        """计算一致性分数"""
        try:
            history = self.behavior_history[identity_id]
            
            if len(history) < 2:
                return 1.0
            
            # 获取历史行为数据（排除最新的）
            historical_behaviors = [record["data"] for record in history[:-1]]
            
            # 计算平均行为
            avg_behavior = self._calculate_average_behavior([{"data": b} for b in historical_behaviors])
            
            # 比较新行为与平均行为
            similarity = self._calculate_behavior_similarity(new_behavior, avg_behavior)
            
            return similarity
            
        except Exception as e:
            self.logger.error(f"Error calculating consistency score: {e}")
            return 0.5
    
    def _calculate_average_behavior(self, history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算平均行为特征"""
        if not history:
            return {}
        
        # 简化实现：计算数值字段的平均值
        numeric_fields = {}
        
        for record in history:
            data = record["data"]
            for key, value in data.items():
                if isinstance(value, (int, float)):
                    if key not in numeric_fields:
                        numeric_fields[key] = []
                    numeric_fields[key].append(value)
        
        avg_behavior = {}
        for key, values in numeric_fields.items():
            avg_behavior[key] = sum(values) / len(values)
        
        return avg_behavior
    
    def _calculate_behavior_similarity(self, behavior1: Dict[str, Any], behavior2: Dict[str, Any]) -> float:
        """计算两个行为的相似度"""
        if not behavior1 or not behavior2:
            return 0.0
        
        # 简化实现：比较共同的数值字段
        common_keys = set(behavior1.keys()) & set(behavior2.keys())
        
        if not common_keys:
            return 0.0
        
        similarities = []
        
        for key in common_keys:
            val1, val2 = behavior1[key], behavior2[key]
            
            if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                # 计算数值相似度
                if val1 == 0 and val2 == 0:
                    similarity = 1.0
                else:
                    max_val = max(abs(val1), abs(val2))
                    if max_val == 0:
                        similarity = 1.0
                    else:
                        similarity = 1.0 - abs(val1 - val2) / max_val
                
                similarities.append(similarity)
        
        return sum(similarities) / len(similarities) if similarities else 0.0
    
    def _generate_recommendations(self, identity_id: str, new_behavior: Dict[str, Any], score: float) -> List[str]:
        """生成一致性改进建议"""
        recommendations = []
        
        if score < 0.7:
            recommendations.append("adjust_behavior_parameters")
        
        if score < 0.5:
            recommendations.append("review_identity_profile")
            recommendations.append("consider_identity_rotation")
        
        return recommendations
    
    def _analyze_consistency_trend(self, identity_id: str) -> Dict[str, Any]:
        """分析一致性趋势"""
        # 简化实现
        return {
            "trend": "stable",
            "direction": "neutral",
            "confidence": 0.8
        }
    
    def _detect_anomalies(self, identity_id: str) -> List[Dict[str, Any]]:
        """检测异常行为"""
        # 简化实现
        return []
    
    def _generate_improvement_recommendations(self, identity_id: str) -> List[str]:
        """生成改进建议"""
        return ["maintain_current_behavior", "monitor_consistency"]
