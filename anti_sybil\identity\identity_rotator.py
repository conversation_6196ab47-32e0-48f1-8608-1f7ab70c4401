"""
Identity Rotator

身份轮换器，负责管理身份的轮换策略和时机。
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta


class IdentityRotator:
    """身份轮换器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 轮换配置
        self.rotation_config = {
            "max_usage_count": self.config.get("max_usage_count", 100),
            "max_age_days": self.config.get("max_age_days", 30),
            "risk_threshold": self.config.get("risk_threshold", 0.8),
            "cooldown_hours": self.config.get("cooldown_hours", 24)
        }
        
        # 轮换历史
        self.rotation_history: List[Dict[str, Any]] = []
    
    def should_rotate(self, identity: Dict[str, Any]) -> bool:
        """
        判断身份是否需要轮换
        
        Args:
            identity: 身份信息
            
        Returns:
            bool: 是否需要轮换
        """
        try:
            # 检查使用次数
            usage_count = identity.get("usage_count", 0)
            if usage_count >= self.rotation_config["max_usage_count"]:
                self.logger.info(f"Identity {identity.get('id')} needs rotation: usage count exceeded")
                return True
            
            # 检查年龄
            created_at = datetime.fromisoformat(identity.get("created_at", datetime.utcnow().isoformat()))
            age_days = (datetime.utcnow() - created_at).days
            if age_days >= self.rotation_config["max_age_days"]:
                self.logger.info(f"Identity {identity.get('id')} needs rotation: age exceeded")
                return True
            
            # 检查风险级别
            risk_level = identity.get("risk_score", 0.0)
            if risk_level >= self.rotation_config["risk_threshold"]:
                self.logger.info(f"Identity {identity.get('id')} needs rotation: risk threshold exceeded")
                return True
            
            # 检查状态
            if identity.get("status") == "compromised":
                self.logger.info(f"Identity {identity.get('id')} needs rotation: compromised")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking rotation need: {e}")
            return False
    
    def calculate_rotation_priority(self, identity: Dict[str, Any]) -> float:
        """
        计算轮换优先级
        
        Args:
            identity: 身份信息
            
        Returns:
            float: 优先级分数 (0-1, 越高越需要轮换)
        """
        try:
            priority = 0.0
            
            # 使用次数因子
            usage_count = identity.get("usage_count", 0)
            usage_factor = min(usage_count / self.rotation_config["max_usage_count"], 1.0)
            priority += usage_factor * 0.3
            
            # 年龄因子
            created_at = datetime.fromisoformat(identity.get("created_at", datetime.utcnow().isoformat()))
            age_days = (datetime.utcnow() - created_at).days
            age_factor = min(age_days / self.rotation_config["max_age_days"], 1.0)
            priority += age_factor * 0.3
            
            # 风险因子
            risk_score = identity.get("risk_score", 0.0)
            priority += risk_score * 0.4
            
            return min(priority, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating rotation priority: {e}")
            return 0.5
    
    def get_rotation_schedule(self, identities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        获取轮换计划
        
        Args:
            identities: 身份列表
            
        Returns:
            List[Dict[str, Any]]: 按优先级排序的轮换计划
        """
        try:
            rotation_schedule = []
            
            for identity in identities:
                if self.should_rotate(identity):
                    priority = self.calculate_rotation_priority(identity)
                    
                    rotation_schedule.append({
                        "identity_id": identity.get("id"),
                        "priority": priority,
                        "reason": self._get_rotation_reason(identity),
                        "recommended_time": self._calculate_rotation_time(identity)
                    })
            
            # 按优先级排序
            rotation_schedule.sort(key=lambda x: x["priority"], reverse=True)
            
            return rotation_schedule
            
        except Exception as e:
            self.logger.error(f"Error creating rotation schedule: {e}")
            return []
    
    def execute_rotation(self, old_identity_id: str, new_identity_id: str) -> bool:
        """
        执行身份轮换
        
        Args:
            old_identity_id: 旧身份ID
            new_identity_id: 新身份ID
            
        Returns:
            bool: 轮换是否成功
        """
        try:
            rotation_record = {
                "old_identity_id": old_identity_id,
                "new_identity_id": new_identity_id,
                "rotation_time": datetime.utcnow().isoformat(),
                "reason": "scheduled_rotation"
            }
            
            self.rotation_history.append(rotation_record)
            
            self.logger.info(f"Executed rotation: {old_identity_id} -> {new_identity_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error executing rotation: {e}")
            return False
    
    def get_rotation_statistics(self) -> Dict[str, Any]:
        """
        获取轮换统计信息
        
        Returns:
            Dict[str, Any]: 轮换统计
        """
        try:
            total_rotations = len(self.rotation_history)
            
            # 计算最近24小时的轮换次数
            recent_rotations = 0
            cutoff_time = datetime.utcnow() - timedelta(hours=24)
            
            for rotation in self.rotation_history:
                rotation_time = datetime.fromisoformat(rotation["rotation_time"])
                if rotation_time >= cutoff_time:
                    recent_rotations += 1
            
            return {
                "total_rotations": total_rotations,
                "recent_rotations_24h": recent_rotations,
                "average_rotations_per_day": total_rotations / max(1, len(set(
                    datetime.fromisoformat(r["rotation_time"]).date() 
                    for r in self.rotation_history
                ))),
                "rotation_config": self.rotation_config
            }
            
        except Exception as e:
            self.logger.error(f"Error getting rotation statistics: {e}")
            return {}
    
    def _get_rotation_reason(self, identity: Dict[str, Any]) -> str:
        """获取轮换原因"""
        reasons = []
        
        usage_count = identity.get("usage_count", 0)
        if usage_count >= self.rotation_config["max_usage_count"]:
            reasons.append("usage_limit")
        
        created_at = datetime.fromisoformat(identity.get("created_at", datetime.utcnow().isoformat()))
        age_days = (datetime.utcnow() - created_at).days
        if age_days >= self.rotation_config["max_age_days"]:
            reasons.append("age_limit")
        
        risk_score = identity.get("risk_score", 0.0)
        if risk_score >= self.rotation_config["risk_threshold"]:
            reasons.append("high_risk")
        
        if identity.get("status") == "compromised":
            reasons.append("compromised")
        
        return ",".join(reasons) if reasons else "scheduled"
    
    def _calculate_rotation_time(self, identity: Dict[str, Any]) -> str:
        """计算建议的轮换时间"""
        # 简单实现：立即轮换高风险身份，其他的在下一个维护窗口
        risk_score = identity.get("risk_score", 0.0)
        
        if risk_score >= 0.9 or identity.get("status") == "compromised":
            return datetime.utcnow().isoformat()
        else:
            # 安排在下一个维护窗口（例如：凌晨2点）
            next_maintenance = datetime.utcnow().replace(hour=2, minute=0, second=0, microsecond=0)
            if next_maintenance <= datetime.utcnow():
                next_maintenance += timedelta(days=1)
            
            return next_maintenance.isoformat()
