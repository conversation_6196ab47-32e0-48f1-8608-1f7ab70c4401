@echo off
echo ===================================
echo AirHunter 空投猎人系统启动脚本
echo ===================================

REM 检查Python是否安装
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo [错误] 未找到Python。请安装Python 3.8或更高版本。
    pause
    exit /b 1
)

REM 检查虚拟环境是否存在
if not exist .venv (
    echo [信息] 创建虚拟环境...
    python -m venv .venv
    if %ERRORLEVEL% neq 0 (
        echo [错误] 创建虚拟环境失败。
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
echo [信息] 激活虚拟环境...
call .venv\Scripts\activate

REM 检查依赖是否安装
if not exist .venv\Lib\site-packages\requests (
    echo [信息] 安装依赖...
    pip install -r requirements.txt
    if %ERRORLEVEL% neq 0 (
        echo [错误] 安装依赖失败。
        pause
        exit /b 1
    )
)

REM 初始化数据目录
echo [信息] 初始化数据目录...
python scripts\initialize_data_directories.py

REM 检查配置文件
if not exist config\config.json (
    echo [信息] 创建配置文件...
    if exist config\config.example.json (
        copy config\config.example.json config\config.json
    ) else (
        echo [错误] 示例配置文件不存在。
        pause
        exit /b 1
    )
)

REM 运行系统健康检查
echo [信息] 运行系统健康检查...
python scripts\system_check.py
if %ERRORLEVEL% neq 0 (
    echo [警告] 系统健康检查未通过。是否继续? (Y/N)
    set /p choice=
    if /i "%choice%" neq "Y" (
        echo [信息] 已取消启动。
        pause
        exit /b 1
    )
)

REM 启动系统
echo [信息] 启动AirHunter系统...
python main.py %*

pause