"""
Data Service

High-level data access service providing CRUD operations
and business logic for database entities.
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from ..core.db_manager import DatabaseManager
from ..models.project_model import ProjectModel
from ..models.wallet_model import WalletModel
from ..models.proxy_model import ProxyModel
from ..models.task_model import TaskModel
from ..models.account_model import AccountModel


class DataService:
    """
    High-level data access service.
    
    Provides business logic and CRUD operations for all database entities
    with proper error handling and validation.
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize data service.
        
        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    # Project operations
    def create_project(self, project: ProjectModel) -> Optional[int]:
        """Create new project."""
        try:
            data = project.to_dict()
            data.pop('id', None)  # Remove ID for insert
            
            query = """
            INSERT INTO projects (name, symbol, project_url, description, project_type,
                                blockchain, contract_address, status, discovered_at, updated_at,
                                requirements, social_requirements, technical_requirements,
                                risk_score, reward_score, difficulty_score, overall_score,
                                participants_count, estimated_reward, actual_reward,
                                source, tags, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                data['name'], data['symbol'], data['project_url'], data['description'],
                data['project_type'], data['blockchain'], data['contract_address'],
                data['status'], data['discovered_at'], data['updated_at'],
                data['requirements'], data['social_requirements'], data['technical_requirements'],
                data['risk_score'], data['reward_score'], data['difficulty_score'], data['overall_score'],
                data['participants_count'], data['estimated_reward'], data['actual_reward'],
                data['source'], data['tags'], data['notes']
            )
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.lastrowid
                
        except Exception as e:
            self.logger.error(f"Failed to create project: {e}")
            return None
    
    def get_project(self, project_id: int) -> Optional[ProjectModel]:
        """Get project by ID."""
        try:
            query = "SELECT * FROM projects WHERE id = ?"
            results = self.db_manager.execute_query(query, (project_id,))
            
            if results:
                return ProjectModel.from_dict(results[0])
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get project {project_id}: {e}")
            return None
    
    def get_projects_by_status(self, status: str) -> List[ProjectModel]:
        """Get projects by status."""
        try:
            query = "SELECT * FROM projects WHERE status = ? ORDER BY updated_at DESC"
            results = self.db_manager.execute_query(query, (status,))
            
            return [ProjectModel.from_dict(row) for row in results]
            
        except Exception as e:
            self.logger.error(f"Failed to get projects by status {status}: {e}")
            return []
    
    def update_project(self, project: ProjectModel) -> bool:
        """Update existing project."""
        try:
            data = project.to_dict()
            
            query = """
            UPDATE projects SET 
                name=?, symbol=?, project_url=?, description=?, project_type=?,
                blockchain=?, contract_address=?, status=?, updated_at=?,
                requirements=?, social_requirements=?, technical_requirements=?,
                risk_score=?, reward_score=?, difficulty_score=?, overall_score=?,
                participants_count=?, estimated_reward=?, actual_reward=?,
                source=?, tags=?, notes=?
            WHERE id=?
            """
            
            params = (
                data['name'], data['symbol'], data['project_url'], data['description'],
                data['project_type'], data['blockchain'], data['contract_address'],
                data['status'], data['updated_at'],
                data['requirements'], data['social_requirements'], data['technical_requirements'],
                data['risk_score'], data['reward_score'], data['difficulty_score'], data['overall_score'],
                data['participants_count'], data['estimated_reward'], data['actual_reward'],
                data['source'], data['tags'], data['notes'], data['id']
            )
            
            rows_affected = self.db_manager.execute_update(query, params)
            return rows_affected > 0
            
        except Exception as e:
            self.logger.error(f"Failed to update project: {e}")
            return False
    
    def delete_project(self, project_id: int) -> bool:
        """Delete project."""
        try:
            query = "DELETE FROM projects WHERE id = ?"
            rows_affected = self.db_manager.execute_update(query, (project_id,))
            return rows_affected > 0
            
        except Exception as e:
            self.logger.error(f"Failed to delete project {project_id}: {e}")
            return False
    
    # Wallet operations
    def create_wallet(self, wallet: WalletModel) -> Optional[int]:
        """Create new wallet."""
        try:
            query = """
            INSERT INTO wallets (address, private_key, blockchain, wallet_type, balance,
                               created_at, updated_at, is_active, tags, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                wallet.address, wallet.private_key, wallet.blockchain, wallet.wallet_type.value,
                wallet.balance, wallet.created_at.isoformat(), wallet.updated_at.isoformat(),
                wallet.is_active, str(wallet.tags), wallet.notes
            )
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.lastrowid
                
        except Exception as e:
            self.logger.error(f"Failed to create wallet: {e}")
            return None
    
    def get_wallets_by_blockchain(self, blockchain: str) -> List[WalletModel]:
        """Get wallets by blockchain."""
        try:
            query = "SELECT * FROM wallets WHERE blockchain = ? AND is_active = 1"
            results = self.db_manager.execute_query(query, (blockchain,))
            
            wallets = []
            for row in results:
                wallet = WalletModel(
                    id=row['id'],
                    address=row['address'],
                    private_key=row['private_key'],
                    blockchain=row['blockchain'],
                    wallet_type=row['wallet_type'],
                    balance=row['balance'],
                    is_active=row['is_active'],
                    notes=row['notes']
                )
                wallets.append(wallet)
            
            return wallets
            
        except Exception as e:
            self.logger.error(f"Failed to get wallets for blockchain {blockchain}: {e}")
            return []
    
    # Task operations
    def create_task(self, task: TaskModel) -> Optional[int]:
        """Create new task."""
        try:
            query = """
            INSERT INTO tasks (project_id, wallet_id, task_type, status, priority,
                             scheduled_at, task_data, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                task.project_id, task.wallet_id, task.task_type.value, task.status.value,
                task.priority, task.scheduled_at.isoformat() if task.scheduled_at else None,
                str(task.task_data), task.created_at.isoformat(), task.updated_at.isoformat()
            )
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.lastrowid
                
        except Exception as e:
            self.logger.error(f"Failed to create task: {e}")
            return None
    
    def get_pending_tasks(self, limit: int = 100) -> List[TaskModel]:
        """Get pending tasks."""
        try:
            query = """
            SELECT * FROM tasks 
            WHERE status IN ('pending', 'scheduled') 
            ORDER BY priority ASC, created_at ASC 
            LIMIT ?
            """
            results = self.db_manager.execute_query(query, (limit,))
            
            tasks = []
            for row in results:
                task = TaskModel(
                    id=row['id'],
                    project_id=row['project_id'],
                    wallet_id=row['wallet_id'],
                    task_type=row['task_type'],
                    status=row['status'],
                    priority=row['priority']
                )
                tasks.append(task)
            
            return tasks
            
        except Exception as e:
            self.logger.error(f"Failed to get pending tasks: {e}")
            return []
    
    def update_task_status(self, task_id: int, status: str, result_data: Dict[str, Any] = None,
                          error_message: str = "") -> bool:
        """Update task status."""
        try:
            query = """
            UPDATE tasks SET status=?, result_data=?, error_message=?, updated_at=?
            WHERE id=?
            """
            
            params = (
                status, str(result_data) if result_data else "",
                error_message, datetime.utcnow().isoformat(), task_id
            )
            
            rows_affected = self.db_manager.execute_update(query, params)
            return rows_affected > 0
            
        except Exception as e:
            self.logger.error(f"Failed to update task status: {e}")
            return False
    
    # Statistics and reporting
    def get_project_statistics(self) -> Dict[str, Any]:
        """Get project statistics."""
        try:
            stats = {}
            
            # Total projects by status
            query = "SELECT status, COUNT(*) as count FROM projects GROUP BY status"
            results = self.db_manager.execute_query(query)
            stats['projects_by_status'] = {row['status']: row['count'] for row in results}
            
            # Total projects
            query = "SELECT COUNT(*) as total FROM projects"
            results = self.db_manager.execute_query(query)
            stats['total_projects'] = results[0]['total'] if results else 0
            
            # Average scores
            query = """
            SELECT AVG(risk_score) as avg_risk, AVG(reward_score) as avg_reward,
                   AVG(difficulty_score) as avg_difficulty, AVG(overall_score) as avg_overall
            FROM projects WHERE status != 'blacklisted'
            """
            results = self.db_manager.execute_query(query)
            if results:
                stats['average_scores'] = {
                    'risk': results[0]['avg_risk'] or 0,
                    'reward': results[0]['avg_reward'] or 0,
                    'difficulty': results[0]['avg_difficulty'] or 0,
                    'overall': results[0]['avg_overall'] or 0
                }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get project statistics: {e}")
            return {}
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """Get task statistics."""
        try:
            stats = {}
            
            # Tasks by status
            query = "SELECT status, COUNT(*) as count FROM tasks GROUP BY status"
            results = self.db_manager.execute_query(query)
            stats['tasks_by_status'] = {row['status']: row['count'] for row in results}
            
            # Tasks by type
            query = "SELECT task_type, COUNT(*) as count FROM tasks GROUP BY task_type"
            results = self.db_manager.execute_query(query)
            stats['tasks_by_type'] = {row['task_type']: row['count'] for row in results}
            
            # Success rate
            query = """
            SELECT 
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                COUNT(*) as total
            FROM tasks
            """
            results = self.db_manager.execute_query(query)
            if results and results[0]['total'] > 0:
                completed = results[0]['completed'] or 0
                total = results[0]['total']
                stats['success_rate'] = completed / total
            else:
                stats['success_rate'] = 0
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get task statistics: {e}")
            return {}
