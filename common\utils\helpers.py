"""
Helper utilities for AirHunter

This module provides various helper functions used throughout the system.
"""

import os
import time
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Union

class Helpers:
    """Collection of helper methods"""
    
    @staticmethod
    def get_timestamp() -> int:
        """
        Get current Unix timestamp
        
        Returns:
            int: Current timestamp
        """
        return int(time.time())
    
    @staticmethod
    def format_datetime(dt: datetime = None, format: str = "%Y-%m-%d %H:%M:%S") -> str:
        """
        Format datetime object to string
        
        Args:
            dt: Datetime object (default: current time)
            format: DateTime format string
            
        Returns:
            str: Formatted datetime string
        """
        if dt is None:
            dt = datetime.now()
        return dt.strftime(format)
    
    @staticmethod
    def ensure_dir(directory: str) -> None:
        """
        Ensure directory exists, create if not
        
        Args:
            directory: Directory path to check/create
        """
        if not os.path.exists(directory):
            os.makedirs(directory)
    
    @staticmethod
    def read_json_file(file_path: str) -> Dict:
        """
        Read JSON file and return as dictionary
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            dict: JSON data as dictionary
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"Error reading JSON file {file_path}: {str(e)}")
            return {}
    
    @staticmethod
    def write_json_file(data: Dict, file_path: str) -> bool:
        """
        Write dictionary to JSON file
        
        Args:
            data: Dictionary to write
            file_path: Output file path
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4)
            return True
        except Exception as e:
            logging.error(f"Error writing JSON file {file_path}: {str(e)}")
            return False
    
    @staticmethod
    def chunk_list(lst: List, chunk_size: int) -> List[List]:
        """
        Split list into chunks of specified size
        
        Args:
            lst: List to split
            chunk_size: Size of each chunk
            
        Returns:
            List[List]: List of chunks
        """
        return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]
    
    @staticmethod
    def flatten_dict(d: Dict, separator: str = '.', prefix: str = '') -> Dict:
        """
        Flatten nested dictionary with custom separator
        
        Args:
            d: Dictionary to flatten
            separator: Key separator
            prefix: Current key prefix
            
        Returns:
            dict: Flattened dictionary
        """
        items = []
        for k, v in d.items():
            new_key = f"{prefix}{separator}{k}" if prefix else k
            if isinstance(v, dict):
                items.extend(Helpers.flatten_dict(v, separator, new_key).items())
            else:
                items.append((new_key, v))
        return dict(items)
    
    @staticmethod
    def safe_get(obj: Union[Dict, List], path: str, default: Any = None) -> Any:
        """
        Safely get nested value from dictionary or list using dot notation
        
        Args:
            obj: Object to get value from
            path: Path to value using dot notation
            default: Default value if path not found
            
        Returns:
            Any: Value at path or default
        """
        try:
            for key in path.split('.'):
                if isinstance(obj, (dict, list)):
                    obj = obj[int(key) if key.isdigit() else key]
                else:
                    return default
            return obj
        except (KeyError, IndexError, TypeError):
            return default
    
    @staticmethod
    def setup_logging(
        log_file: str = None,
        level: int = logging.INFO,
        format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ) -> None:
        """
        Setup logging configuration
        
        Args:
            log_file: Log file path (optional)
            level: Logging level
            format: Log message format
        """
        handlers = []
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(format))
        handlers.append(console_handler)
        
        # File handler if log_file specified
        if log_file:
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(logging.Formatter(format))
            handlers.append(file_handler)
        
        # Configure logging
        logging.basicConfig(
            level=level,
            handlers=handlers
        )

# Create global helpers instance
helpers = Helpers()
