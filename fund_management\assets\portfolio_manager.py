"""
Portfolio Manager

投资组合管理器，负责管理和分析投资组合。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta


class PortfolioManager:
    """
    投资组合管理器
    
    负责创建、管理和分析投资组合，包括资产分配、风险评估和收益跟踪。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化投资组合管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 投资组合存储
        self.portfolios: Dict[str, Dict] = {}
        
        # 管理统计
        self.manager_stats = {
            'total_portfolios': 0,
            'total_assets': 0,
            'rebalance_operations': 0,
            'last_update_time': None
        }
        
        # 风险配置
        self.risk_config = {
            "max_single_asset_weight": config.get("max_single_asset_weight", 0.3),
            "min_diversification_score": config.get("min_diversification_score", 0.5),
            "rebalance_threshold": config.get("rebalance_threshold", 0.05)
        }
    
    async def initialize(self) -> bool:
        """
        初始化投资组合管理器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("Portfolio Manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Portfolio Manager: {e}")
            return False
    
    async def create_portfolio(self, portfolio_id: str, name: str, 
                             description: str = "", initial_allocation: Dict = None) -> bool:
        """
        创建新的投资组合
        
        Args:
            portfolio_id: 投资组合ID
            name: 投资组合名称
            description: 描述
            initial_allocation: 初始资产分配
            
        Returns:
            bool: 创建是否成功
        """
        try:
            if portfolio_id in self.portfolios:
                self.logger.error(f"Portfolio {portfolio_id} already exists")
                return False
            
            portfolio = {
                "id": portfolio_id,
                "name": name,
                "description": description,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "holdings": initial_allocation or {},
                "target_allocation": {},
                "performance_history": [],
                "rebalance_history": [],
                "risk_metrics": {},
                "total_value_usd": 0.0,
                "total_value_eth": 0.0
            }
            
            self.portfolios[portfolio_id] = portfolio
            self.manager_stats['total_portfolios'] += 1
            
            self.logger.info(f"Created portfolio {name} with ID {portfolio_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating portfolio: {e}")
            return False
    
    async def add_asset_to_portfolio(self, portfolio_id: str, token_address: str,
                                   amount: float, token_info: Dict = None) -> bool:
        """
        向投资组合添加资产
        
        Args:
            portfolio_id: 投资组合ID
            token_address: 代币合约地址
            amount: 数量
            token_info: 代币信息
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if portfolio_id not in self.portfolios:
                self.logger.error(f"Portfolio {portfolio_id} not found")
                return False
            
            portfolio = self.portfolios[portfolio_id]
            
            # 添加或更新持仓
            if token_address in portfolio["holdings"]:
                portfolio["holdings"][token_address]["amount"] += amount
            else:
                portfolio["holdings"][token_address] = {
                    "amount": amount,
                    "token_info": token_info or {},
                    "added_at": datetime.utcnow().isoformat(),
                    "average_cost": 0.0,
                    "total_cost": 0.0
                }
                self.manager_stats['total_assets'] += 1
            
            portfolio["updated_at"] = datetime.utcnow().isoformat()
            
            self.logger.info(f"Added {amount} of {token_address} to portfolio {portfolio_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding asset to portfolio: {e}")
            return False
    
    async def remove_asset_from_portfolio(self, portfolio_id: str, token_address: str,
                                        amount: float = None) -> bool:
        """
        从投资组合移除资产
        
        Args:
            portfolio_id: 投资组合ID
            token_address: 代币合约地址
            amount: 移除数量，None表示全部移除
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if portfolio_id not in self.portfolios:
                self.logger.error(f"Portfolio {portfolio_id} not found")
                return False
            
            portfolio = self.portfolios[portfolio_id]
            
            if token_address not in portfolio["holdings"]:
                self.logger.error(f"Asset {token_address} not found in portfolio")
                return False
            
            current_amount = portfolio["holdings"][token_address]["amount"]
            
            if amount is None or amount >= current_amount:
                # 移除全部
                del portfolio["holdings"][token_address]
                self.manager_stats['total_assets'] -= 1
            else:
                # 部分移除
                portfolio["holdings"][token_address]["amount"] -= amount
            
            portfolio["updated_at"] = datetime.utcnow().isoformat()
            
            self.logger.info(f"Removed asset {token_address} from portfolio {portfolio_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error removing asset from portfolio: {e}")
            return False
