"""
自动恢复器

该模块提供自动恢复功能，负责在系统或智能体出现故障时自动恢复。
"""

import logging
import threading
import time
import traceback
from typing import Dict, List, Any, Optional, Set, Callable, Tuple

from coordinator.core.system_state import SystemState
from coordinator.core.lifecycle_manager import LifecycleManager
from coordinator.communication.event_system import EventSystem


class AutoRecovery:
    """自动恢复器，负责在系统或智能体出现故障时自动恢复"""
    
    def __init__(self, system_state: SystemState, lifecycle_manager: LifecycleManager, event_system: EventSystem):
        """
        初始化自动恢复器
        
        Args:
            system_state: 系统状态实例
            lifecycle_manager: 生命周期管理器实例
            event_system: 事件系统实例
        """
        self.logger = logging.getLogger(__name__)
        self.system_state = system_state
        self.lifecycle_manager = lifecycle_manager
        self.event_system = event_system
        self._lock = threading.RLock()
        self._recovery_strategies: Dict[str, List[Callable[[Dict[str, Any]], bool]]] = {}  # error_type -> [strategy_func]
        self._agent_recovery_strategies: Dict[str, Dict[str, List[Callable[[Dict[str, Any]], bool]]]] = {}  # agent_name -> {error_type -> [strategy_func]}
        self._max_retries = 3  # 最大重试次数
        self._retry_interval = 5.0  # 重试间隔（秒）
        self._recovery_history: List[Dict[str, Any]] = []  # 恢复历史
        self._history_size = 100  # 历史记录大小
        
        # 注册事件监听器
        self._error_subscriber_id = self.event_system.subscribe("system.health.error", self._handle_system_error)
        self._agent_error_subscriber_id = self.event_system.subscribe("agent.health.error", self._handle_agent_error)
        
        # 注册默认恢复策略
        self.register_recovery_strategy("agent.not_responding", self._restart_agent_strategy)
        self.register_recovery_strategy("agent.crashed", self._restart_agent_strategy)
    
    def register_recovery_strategy(self, error_type: str, strategy_func: Callable[[Dict[str, Any]], bool]) -> None:
        """
        注册恢复策略
        
        Args:
            error_type: 错误类型
            strategy_func: 策略函数，接收错误数据并返回是否成功恢复
        """
        with self._lock:
            if error_type not in self._recovery_strategies:
                self._recovery_strategies[error_type] = []
            
            self._recovery_strategies[error_type].append(strategy_func)
            self.logger.info(f"已注册恢复策略，错误类型: '{error_type}'")
    
    def unregister_recovery_strategy(self, error_type: str, strategy_func: Callable[[Dict[str, Any]], bool]) -> bool:
        """
        注销恢复策略
        
        Args:
            error_type: 错误类型
            strategy_func: 策略函数
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if error_type not in self._recovery_strategies:
                self.logger.warning(f"错误类型 '{error_type}' 没有注册恢复策略")
                return False
            
            if strategy_func not in self._recovery_strategies[error_type]:
                self.logger.warning(f"策略函数未注册到错误类型 '{error_type}'")
                return False
            
            self._recovery_strategies[error_type].remove(strategy_func)
            
            # 如果没有策略了，删除错误类型
            if not self._recovery_strategies[error_type]:
                del self._recovery_strategies[error_type]
            
            self.logger.info(f"已注销恢复策略，错误类型: '{error_type}'")
            return True
    
    def register_agent_recovery_strategy(self, agent_name: str, error_type: str, strategy_func: Callable[[Dict[str, Any]], bool]) -> None:
        """
        注册智能体恢复策略
        
        Args:
            agent_name: 智能体名称
            error_type: 错误类型
            strategy_func: 策略函数，接收错误数据并返回是否成功恢复
        """
        with self._lock:
            if agent_name not in self._agent_recovery_strategies:
                self._agent_recovery_strategies[agent_name] = {}
            
            if error_type not in self._agent_recovery_strategies[agent_name]:
                self._agent_recovery_strategies[agent_name][error_type] = []
            
            self._agent_recovery_strategies[agent_name][error_type].append(strategy_func)
            self.logger.info(f"已为智能体 '{agent_name}' 注册恢复策略，错误类型: '{error_type}'")
    
    def unregister_agent_recovery_strategy(self, agent_name: str, error_type: str, strategy_func: Callable[[Dict[str, Any]], bool]) -> bool:
        """
        注销智能体恢复策略
        
        Args:
            agent_name: 智能体名称
            error_type: 错误类型
            strategy_func: 策略函数
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_recovery_strategies:
                self.logger.warning(f"智能体 '{agent_name}' 没有注册恢复策略")
                return False
            
            if error_type not in self._agent_recovery_strategies[agent_name]:
                self.logger.warning(f"智能体 '{agent_name}' 的错误类型 '{error_type}' 没有注册恢复策略")
                return False
            
            if strategy_func not in self._agent_recovery_strategies[agent_name][error_type]:
                self.logger.warning(f"策略函数未注册到智能体 '{agent_name}' 的错误类型 '{error_type}'")
                return False
            
            self._agent_recovery_strategies[agent_name][error_type].remove(strategy_func)
            
            # 如果没有策略了，删除错误类型
            if not self._agent_recovery_strategies[agent_name][error_type]:
                del self._agent_recovery_strategies[agent_name][error_type]
            
            # 如果没有错误类型了，删除智能体
            if not self._agent_recovery_strategies[agent_name]:
                del self._agent_recovery_strategies[agent_name]
            
            self.logger.info(f"已注销智能体 '{agent_name}' 的恢复策略，错误类型: '{error_type}'")
            return True
    
    def unregister_agent(self, agent_name: str) -> bool:
        """
        注销智能体的所有恢复策略
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if agent_name not in self._agent_recovery_strategies:
                self.logger.warning(f"智能体 '{agent_name}' 没有注册恢复策略")
                return False
            
            del self._agent_recovery_strategies[agent_name]
            self.logger.info(f"已注销智能体 '{agent_name}' 的所有恢复策略")
            return True
    
    def recover(self, error_type: str, error_data: Dict[str, Any]) -> bool:
        """
        执行恢复
        
        Args:
            error_type: 错误类型
            error_data: 错误数据
            
        Returns:
            如果成功恢复则返回True，否则返回False
        """
        self.logger.info(f"执行恢复，错误类型: '{error_type}'")
        
        # 获取恢复策略
        strategies = []
        with self._lock:
            if error_type in self._recovery_strategies:
                strategies.extend(self._recovery_strategies[error_type])
            
            # 添加通用恢复策略
            if "*" in self._recovery_strategies:
                strategies.extend(self._recovery_strategies["*"])
        
        if not strategies:
            self.logger.warning(f"没有找到错误类型 '{error_type}' 的恢复策略")
            return False
        
        # 调用恢复策略
        success = False
        for strategy in strategies:
            try:
                if strategy(error_data):
                    success = True
                    self.logger.info(f"错误类型 '{error_type}' 已成功恢复")
                    
                    # 记录恢复历史
                    self._add_recovery_history(error_type, error_data, True)
                    
                    # 发布恢复成功事件
                    self.event_system.publish("system.recovery.success", {
                        "error_type": error_type,
                        "error_data": error_data
                    })
                    
                    break
            except Exception as e:
                self.logger.error(f"调用恢复策略时出错: {str(e)}")
                self.logger.error(traceback.format_exc())
        
        if not success:
            # 记录恢复历史
            self._add_recovery_history(error_type, error_data, False)
            
            # 发布恢复失败事件
            self.event_system.publish("system.recovery.failure", {
                "error_type": error_type,
                "error_data": error_data
            })
        
        return success
    
    def recover_agent(self, agent_name: str, error_type: str, error_data: Dict[str, Any]) -> bool:
        """
        执行智能体恢复
        
        Args:
            agent_name: 智能体名称
            error_type: 错误类型
            error_data: 错误数据
            
        Returns:
            如果成功恢复则返回True，否则返回False
        """
        self.logger.info(f"执行智能体 '{agent_name}' 的恢复，错误类型: '{error_type}'")
        
        # 获取恢复策略
        strategies = []
        with self._lock:
            # 添加智能体特定的恢复策略
            if agent_name in self._agent_recovery_strategies:
                if error_type in self._agent_recovery_strategies[agent_name]:
                    strategies.extend(self._agent_recovery_strategies[agent_name][error_type])
                
                # 添加智能体通用恢复策略
                if "*" in self._agent_recovery_strategies[agent_name]:
                    strategies.extend(self._agent_recovery_strategies[agent_name]["*"])
            
            # 添加全局恢复策略
            if error_type in self._recovery_strategies:
                strategies.extend(self._recovery_strategies[error_type])
            
            # 添加全局通用恢复策略
            if "*" in self._recovery_strategies:
                strategies.extend(self._recovery_strategies["*"])
        
        if not strategies:
            self.logger.warning(f"没有找到智能体 '{agent_name}' 的错误类型 '{error_type}' 的恢复策略")
            return False
        
        # 调用恢复策略
        success = False
        for strategy in strategies:
            try:
                # 添加智能体名称到错误数据
                error_data_with_agent = error_data.copy()
                error_data_with_agent["agent_name"] = agent_name
                
                if strategy(error_data_with_agent):
                    success = True
                    self.logger.info(f"智能体 '{agent_name}' 的错误类型 '{error_type}' 已成功恢复")
                    
                    # 记录恢复历史
                    self._add_recovery_history(error_type, error_data_with_agent, True, agent_name)
                    
                    # 发布恢复成功事件
                    self.event_system.publish("agent.recovery.success", {
                        "agent": agent_name,
                        "error_type": error_type,
                        "error_data": error_data
                    })
                    
                    break
            except Exception as e:
                self.logger.error(f"调用智能体 '{agent_name}' 的恢复策略时出错: {str(e)}")
                self.logger.error(traceback.format_exc())
        
        if not success:
            # 记录恢复历史
            self._add_recovery_history(error_type, error_data, False, agent_name)
            
            # 发布恢复失败事件
            self.event_system.publish("agent.recovery.failure", {
                "agent": agent_name,
                "error_type": error_type,
                "error_data": error_data
            })
        
        return success
    
    def _handle_system_error(self, error_data: Dict[str, Any]) -> None:
        """
        处理系统错误事件
        
        Args:
            error_data: 错误数据
        """
        check = error_data.get("check", "unknown")
        message = error_data.get("message", "未知错误")
        
        self.logger.error(f"收到系统错误事件: {message}")
        
        # 构造错误类型
        error_type = f"system.{check}"
        
        # 执行恢复
        self.recover(error_type, error_data)
    
    def _handle_agent_error(self, error_data: Dict[str, Any]) -> None:
        """
        处理智能体错误事件
        
        Args:
            error_data: 错误数据
        """
        agent = error_data.get("agent", "unknown")
        check = error_data.get("check", "unknown")
        message = error_data.get("message", "未知错误")
        
        self.logger.error(f"收到智能体 '{agent}' 的错误事件: {message}")
        
        # 构造错误类型
        error_type = f"agent.{check}"
        
        # 执行恢复
        self.recover_agent(agent, error_type, error_data)
    
    def _restart_agent_strategy(self, error_data: Dict[str, Any]) -> bool:
        """
        重启智能体恢复策略
        
        Args:
            error_data: 错误数据
            
        Returns:
            如果成功重启则返回True，否则返回False
        """
        agent_name = error_data.get("agent_name")
        if not agent_name:
            self.logger.warning("错误数据中没有智能体名称")
            return False
        
        self.logger.info(f"尝试重启智能体 '{agent_name}'")
        
        # 获取重试次数
        retry_count = error_data.get("retry_count", 0)
        
        # 检查是否超过最大重试次数
        if retry_count >= self._max_retries:
            self.logger.warning(f"智能体 '{agent_name}' 重启失败次数过多，放弃重试")
            return False
        
        # 更新重试次数
        error_data["retry_count"] = retry_count + 1
        
        # 重启智能体
        try:
            # 等待一段时间再重启
            time.sleep(self._retry_interval)
            
            # 重启智能体
            result = self.lifecycle_manager.restart_agent(agent_name)
            
            if result:
                self.logger.info(f"智能体 '{agent_name}' 重启成功")
                return True
            else:
                self.logger.error(f"智能体 '{agent_name}' 重启失败")
                return False
        
        except Exception as e:
            self.logger.error(f"重启智能体 '{agent_name}' 时出错: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False
    
    def _add_recovery_history(self, error_type: str, error_data: Dict[str, Any], success: bool, agent_name: str = None) -> None:
        """
        添加恢复历史记录
        
        Args:
            error_type: 错误类型
            error_data: 错误数据
            success: 是否成功恢复
            agent_name: 智能体名称
        """
        with self._lock:
            # 创建历史记录
            record = {
                "timestamp": time.time(),
                "error_type": error_type,
                "error_data": error_data,
                "success": success
            }
            
            if agent_name:
                record["agent_name"] = agent_name
            
            # 添加到历史记录
            self._recovery_history.append(record)
            
            # 限制历史记录大小
            if len(self._recovery_history) > self._history_size:
                self._recovery_history = self._recovery_history[-self._history_size:]
    
    def get_recovery_history(self, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取恢复历史记录
        
        Args:
            limit: 限制返回的记录数量
            
        Returns:
            恢复历史记录列表
        """
        with self._lock:
            history = self._recovery_history.copy()
            
            if limit:
                return history[-limit:]
            else:
                return history
    
    def get_agent_recovery_history(self, agent_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取智能体的恢复历史记录
        
        Args:
            agent_name: 智能体名称
            limit: 限制返回的记录数量
            
        Returns:
            恢复历史记录列表
        """
        with self._lock:
            # 过滤智能体的历史记录
            agent_history = [record for record in self._recovery_history if record.get("agent_name") == agent_name]
            
            if limit:
                return agent_history[-limit:]
            else:
                return agent_history
    
    def get_recovery_strategies(self) -> Dict[str, int]:
        """
        获取所有恢复策略
        
        Returns:
            恢复策略字典，键为错误类型，值为策略数量
        """
        with self._lock:
            return {error_type: len(strategies) for error_type, strategies in self._recovery_strategies.items()}
    
    def get_agent_recovery_strategies(self, agent_name: str) -> Dict[str, int]:
        """
        获取智能体的所有恢复策略
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            恢复策略字典，键为错误类型，值为策略数量
        """
        with self._lock:
            if agent_name not in self._agent_recovery_strategies:
                return {}
            
            return {error_type: len(strategies) for error_type, strategies in self._agent_recovery_strategies[agent_name].items()}
    
    def get_all_agent_recovery_strategies(self) -> Dict[str, Dict[str, int]]:
        """
        获取所有智能体的恢复策略
        
        Returns:
            恢复策略字典，键为智能体名称，值为错误类型字典
        """
        with self._lock:
            return {
                agent_name: {error_type: len(strategies) for error_type, strategies in error_types.items()}
                for agent_name, error_types in self._agent_recovery_strategies.items()
            }