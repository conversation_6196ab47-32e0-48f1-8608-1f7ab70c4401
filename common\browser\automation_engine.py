"""
Browser automation engine

This module provides browser automation utilities for web interactions.
"""

import time
import base64
import logging
from typing import Any, Dict, List, Optional, Tuple, Union
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hai<PERSON>
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import *

class AutomationEngine:
    """Engine for browser automation"""
    
    def __init__(self, driver):
        """
        Initialize automation engine
        
        Args:
            driver: WebDriver instance
        """
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        self.logger = logging.getLogger(__name__)
    
    def navigate(self, url: str, wait_load: bool = True) -> None:
        """
        Navigate to URL
        
        Args:
            url: Target URL
            wait_load: Whether to wait for page load
        """
        try:
            self.driver.get(url)
            if wait_load:
                self.wait_for_load()
        except Exception as e:
            self.logger.error(f"Navigation failed: {str(e)}")
            raise
    
    def find_element(
        self,
        locator: Tuple[str, str],
        timeout: int = 10,
        visible: bool = True
    ) -> Any:
        """
        Find element with wait
        
        Args:
            locator: Element locator tuple (By, value)
            timeout: Wait timeout in seconds
            visible: Whether element should be visible
            
        Returns:
            Any: Found element
        """
        try:
            if visible:
                return WebDriverWait(self.driver, timeout).until(
                    EC.visibility_of_element_located(locator)
                )
            return WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located(locator)
            )
        except Exception as e:
            self.logger.error(f"Element not found: {str(e)}")
            raise
    
    def click(
        self,
        locator: Tuple[str, str],
        timeout: int = 10,
        js_click: bool = False
    ) -> None:
        """
        Click element
        
        Args:
            locator: Element locator tuple
            timeout: Wait timeout
            js_click: Whether to use JavaScript click
        """
        try:
            element = self.find_element(locator, timeout)
            if js_click:
                self.driver.execute_script("arguments[0].click();", element)
            else:
                element.click()
        except Exception as e:
            self.logger.error(f"Click failed: {str(e)}")
            raise
    
    def input_text(
        self,
        locator: Tuple[str, str],
        text: str,
        clear: bool = True,
        timeout: int = 10
    ) -> None:
        """
        Input text to element
        
        Args:
            locator: Element locator tuple
            text: Text to input
            clear: Whether to clear existing text
            timeout: Wait timeout
        """
        try:
            element = self.find_element(locator, timeout)
            if clear:
                element.clear()
            element.send_keys(text)
        except Exception as e:
            self.logger.error(f"Text input failed: {str(e)}")
            raise
    
    def wait_for_load(self, timeout: int = 30) -> None:
        """
        Wait for page load
        
        Args:
            timeout: Wait timeout in seconds
        """
        try:
            WebDriverWait(self.driver, timeout).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
        except Exception as e:
            self.logger.error(f"Page load timeout: {str(e)}")
            raise
    
    def wait_for_element(
        self,
        locator: Tuple[str, str],
        timeout: int = 10,
        condition: str = "visible"
    ) -> Any:
        """
        Wait for element with condition
        
        Args:
            locator: Element locator tuple
            timeout: Wait timeout
            condition: Wait condition (visible, clickable, present)
            
        Returns:
            Any: Found element
        """
        try:
            if condition == "visible":
                return WebDriverWait(self.driver, timeout).until(
                    EC.visibility_of_element_located(locator)
                )
            elif condition == "clickable":
                return WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable(locator)
                )
            elif condition == "present":
                return WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located(locator)
                )
            else:
                raise ValueError(f"Unknown condition: {condition}")
        except Exception as e:
            self.logger.error(f"Wait failed: {str(e)}")
            raise
    
    def execute_script(
        self,
        script: str,
        *args,
        async_script: bool = False
    ) -> Any:
        """
        Execute JavaScript
        
        Args:
            script: JavaScript code
            *args: Script arguments
            async_script: Whether to execute asynchronously
            
        Returns:
            Any: Script result
        """
        try:
            if async_script:
                return self.driver.execute_async_script(script, *args)
            return self.driver.execute_script(script, *args)
        except Exception as e:
            self.logger.error(f"Script execution failed: {str(e)}")
            raise
    
    def switch_window(
        self,
        window_index: int = -1,
        title: str = None
    ) -> None:
        """
        Switch browser window
        
        Args:
            window_index: Window index (-1 for last)
            title: Window title to match
        """
        try:
            handles = self.driver.window_handles
            if title:
                for handle in handles:
                    self.driver.switch_to.window(handle)
                    if title in self.driver.title:
                        return
                raise ValueError(f"Window with title '{title}' not found")
            else:
                self.driver.switch_to.window(handles[window_index])
        except Exception as e:
            self.logger.error(f"Window switch failed: {str(e)}")
            raise
    
    def take_screenshot(
        self,
        filename: Optional[str] = None,
        element: Optional[Any] = None
    ) -> Optional[str]:
        """
        Take screenshot
        
        Args:
            filename: Output filename
            element: Specific element to capture
            
        Returns:
            Optional[str]: Base64 encoded image if no filename
        """
        try:
            if element:
                image = element.screenshot_as_base64
            else:
                image = self.driver.get_screenshot_as_base64()
                
            if filename:
                with open(filename, 'wb') as f:
                    f.write(base64.b64decode(image))
                return None
            return image
        except Exception as e:
            self.logger.error(f"Screenshot failed: {str(e)}")
            raise
    
    def scroll(
        self,
        direction: str = "down",
        amount: Optional[int] = None,
        element: Optional[Any] = None
    ) -> None:
        """
        Scroll page or element
        
        Args:
            direction: Scroll direction (up/down/left/right)
            amount: Scroll amount in pixels
            element: Element to scroll
        """
        try:
            target = element if element else self.driver.find_element(By.TAG_NAME, "body")
            
            if amount:
                if direction in ["down", "right"]:
                    amount = abs(amount)
                else:
                    amount = -abs(amount)
                    
                if direction in ["down", "up"]:
                    self.driver.execute_script(
                        "arguments[0].scrollTop += arguments[1];",
                        target, amount
                    )
                else:
                    self.driver.execute_script(
                        "arguments[0].scrollLeft += arguments[1];",
                        target, amount
                    )
            else:
                if direction == "down":
                    target.send_keys(Keys.PAGE_DOWN)
                elif direction == "up":
                    target.send_keys(Keys.PAGE_UP)
                elif direction == "right":
                    target.send_keys(Keys.ARROW_RIGHT)
                elif direction == "left":
                    target.send_keys(Keys.ARROW_LEFT)
                    
        except Exception as e:
            self.logger.error(f"Scroll failed: {str(e)}")
            raise
    
    def hover(self, element: Any) -> None:
        """
        Hover over element
        
        Args:
            element: Target element
        """
        try:
            ActionChains(self.driver).move_to_element(element).perform()
        except Exception as e:
            self.logger.error(f"Hover failed: {str(e)}")
            raise
    
    def drag_and_drop(
        self,
        source: Any,
        target: Any
    ) -> None:
        """
        Perform drag and drop
        
        Args:
            source: Source element
            target: Target element
        """
        try:
            ActionChains(self.driver).drag_and_drop(source, target).perform()
        except Exception as e:
            self.logger.error(f"Drag and drop failed: {str(e)}")
            raise

# Create global automation engine instance
automation_engine = None  # Will be initialized with WebDriver
