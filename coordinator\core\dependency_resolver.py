"""
依赖解析器

该模块提供智能体依赖关系解析功能，确保智能体按照正确的顺序启动和停止。
"""

import logging
from typing import Dict, List, Set, Any, Optional
import networkx as nx

from coordinator.core.agent_registry import AgentRegistry


class DependencyResolver:
    """依赖解析器，负责解析智能体之间的依赖关系"""
    
    def __init__(self, agent_registry: AgentRegistry):
        """
        初始化依赖解析器
        
        Args:
            agent_registry: 智能体注册表实例
        """
        self.agent_registry = agent_registry
        self.logger = logging.getLogger(__name__)
        self.dependencies: Dict[str, List[str]] = {}  # agent_name -> [dependency_names]
        
    def register_dependency(self, agent_name: str, dependency_name: str) -> None:
        """
        注册智能体依赖关系
        
        Args:
            agent_name: 依赖其他智能体的智能体名称
            dependency_name: 被依赖的智能体名称
        """
        if not self.agent_registry.exists(agent_name):
            self.logger.warning(f"尝试为不存在的智能体 '{agent_name}' 注册依赖")
            return
        
        if not self.agent_registry.exists(dependency_name):
            self.logger.warning(f"尝试注册不存在的依赖智能体 '{dependency_name}'")
            return
        
        if agent_name not in self.dependencies:
            self.dependencies[agent_name] = []
        
        if dependency_name not in self.dependencies[agent_name]:
            self.dependencies[agent_name].append(dependency_name)
            self.logger.info(f"已注册智能体 '{agent_name}' 对智能体 '{dependency_name}' 的依赖")
    
    def unregister_dependency(self, agent_name: str, dependency_name: str) -> None:
        """
        注销智能体依赖关系
        
        Args:
            agent_name: 依赖其他智能体的智能体名称
            dependency_name: 被依赖的智能体名称
        """
        if agent_name in self.dependencies and dependency_name in self.dependencies[agent_name]:
            self.dependencies[agent_name].remove(dependency_name)
            self.logger.info(f"已注销智能体 '{agent_name}' 对智能体 '{dependency_name}' 的依赖")
    
    def get_dependencies(self, agent_name: str) -> List[str]:
        """
        获取智能体的直接依赖
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            智能体直接依赖的智能体名称列表
        """
        return self.dependencies.get(agent_name, []).copy()
    
    def get_all_dependencies(self, agent_name: str) -> Set[str]:
        """
        获取智能体的所有依赖（包括间接依赖）
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            智能体所有依赖的智能体名称集合
        """
        result = set()
        self._collect_dependencies(agent_name, result)
        return result
    
    def _collect_dependencies(self, agent_name: str, result: Set[str]) -> None:
        """
        递归收集智能体的所有依赖
        
        Args:
            agent_name: 智能体名称
            result: 用于存储结果的集合
        """
        for dep in self.dependencies.get(agent_name, []):
            if dep not in result:
                result.add(dep)
                self._collect_dependencies(dep, result)
    
    def get_dependents(self, agent_name: str) -> List[str]:
        """
        获取依赖指定智能体的所有智能体
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            依赖指定智能体的智能体名称列表
        """
        dependents = []
        for agent, deps in self.dependencies.items():
            if agent_name in deps:
                dependents.append(agent)
        return dependents
    
    def get_startup_order(self) -> List[str]:
        """
        获取智能体的启动顺序
        
        Returns:
            按照依赖关系排序的智能体名称列表，先启动被依赖的智能体
            
        Raises:
            ValueError: 如果存在循环依赖
        """
        # 创建有向图
        G = nx.DiGraph()
        
        # 添加所有智能体作为节点
        for agent_name in self.agent_registry.get_names():
            G.add_node(agent_name)
        
        # 添加依赖关系作为边（从依赖方指向被依赖方）
        for agent_name, deps in self.dependencies.items():
            for dep in deps:
                G.add_edge(agent_name, dep)
        
        # 检查是否有循环依赖
        try:
            cycles = list(nx.simple_cycles(G))
            if cycles:
                cycle_str = ' -> '.join(cycles[0] + [cycles[0][0]])
                self.logger.error(f"检测到循环依赖: {cycle_str}")
                raise ValueError(f"检测到循环依赖: {cycle_str}")
        except nx.NetworkXNoCycle:
            pass
        
        # 拓扑排序
        try:
            # 反转顺序，因为我们需要先启动被依赖的智能体
            order = list(reversed(list(nx.topological_sort(G))))
            return order
        except nx.NetworkXUnfeasible:
            self.logger.error("无法确定启动顺序，可能存在循环依赖")
            raise ValueError("无法确定启动顺序，可能存在循环依赖")
    
    def get_shutdown_order(self) -> List[str]:
        """
        获取智能体的关闭顺序
        
        Returns:
            按照依赖关系排序的智能体名称列表，先关闭依赖其他智能体的智能体
            
        Raises:
            ValueError: 如果存在循环依赖
        """
        # 启动顺序的反转就是关闭顺序
        return list(reversed(self.get_startup_order()))
    
    def check_circular_dependencies(self) -> Optional[List[str]]:
        """
        检查是否存在循环依赖
        
        Returns:
            如果存在循环依赖，返回循环依赖的智能体名称列表；否则返回None
        """
        # 创建有向图
        G = nx.DiGraph()
        
        # 添加所有智能体作为节点
        for agent_name in self.agent_registry.get_names():
            G.add_node(agent_name)
        
        # 添加依赖关系作为边
        for agent_name, deps in self.dependencies.items():
            for dep in deps:
                G.add_edge(agent_name, dep)
        
        # 检查是否有循环依赖
        try:
            cycles = list(nx.simple_cycles(G))
            if cycles:
                return cycles[0]  # 返回第一个检测到的循环
            return None
        except nx.NetworkXNoCycle:
            return None