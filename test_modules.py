#!/usr/bin/env python3
"""
Test script to verify that all newly created modules can be imported successfully.
"""

import sys
import traceback
from pathlib import Path

def test_import(module_name, description=""):
    """Test importing a module."""
    try:
        __import__(module_name)
        print(f"✅ {module_name} - {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - Import Error: {e}")
        return False
    except Exception as e:
        print(f"⚠️  {module_name} - Other Error: {e}")
        return False

def main():
    """Test all newly created modules."""
    print("Testing AirHunter Module Imports")
    print("=" * 50)
    
    # Test database modules
    print("\n📁 Database Modules:")
    test_import("database", "Database system")
    test_import("database.core.db_manager", "Database manager")
    test_import("database.core.connection_pool", "Connection pool")
    test_import("database.core.query_builder", "Query builder")
    test_import("database.core.transaction_manager", "Transaction manager")
    test_import("database.models.project_model", "Project model")
    test_import("database.models.wallet_model", "Wallet model")
    test_import("database.models.proxy_model", "Proxy model")
    test_import("database.models.task_model", "Task model")
    test_import("database.models.account_model", "Account model")
    test_import("database.migrations.migration_manager", "Migration manager")
    test_import("database.services.data_service", "Data service")
    
    # Test services modules
    print("\n🔧 Services Modules:")
    test_import("services", "Services system")
    test_import("services.scheduler.task_scheduler", "Task scheduler")
    
    # Test health modules
    print("\n🏥 Health Modules:")
    test_import("health", "Health monitoring system")
    test_import("health.monitors.agent_monitor", "Agent monitor")
    
    # Test UI modules (may fail if PyQt6 not installed)
    print("\n🖥️  UI Modules:")
    test_import("ui", "UI system")
    test_import("ui.main_window", "Main window")
    test_import("ui.dashboard.overview", "Overview panel")
    
    # Test ML modules
    print("\n🤖 Machine Learning Modules:")
    test_import("common.ml", "ML system")
    test_import("common.ml.pattern_recognizer", "Pattern recognizer")
    test_import("common.ml.anomaly_detector", "Anomaly detector")
    test_import("common.ml.decision_maker", "Decision maker")
    test_import("common.ml.learning_module", "Learning module")
    
    print("\n" + "=" * 50)
    print("Module import testing completed!")
    
    # Test basic functionality
    print("\n🧪 Testing Basic Functionality:")
    
    try:
        from database.core.db_manager import DatabaseManager
        from database.models.project_model import ProjectModel, ProjectStatus, ProjectType
        from common.ml.pattern_recognizer import PatternRecognizer
        from common.ml.anomaly_detector import AnomalyDetector
        from common.ml.decision_maker import DecisionMaker, DecisionType
        from common.ml.learning_module import LearningModule
        
        # Test database manager initialization
        db_manager = DatabaseManager(":memory:")  # In-memory database for testing
        print("✅ Database manager created")
        
        # Test project model
        project = ProjectModel(
            name="Test Project",
            symbol="TEST",
            project_type=ProjectType.TESTNET,
            status=ProjectStatus.DISCOVERED
        )
        print("✅ Project model created")
        
        # Test ML components
        pattern_recognizer = PatternRecognizer()
        anomaly_detector = AnomalyDetector()
        decision_maker = DecisionMaker()
        learning_module = LearningModule()
        print("✅ ML components created")
        
        # Test pattern recognition
        test_data = {
            'team_size': 5,
            'social_followers': 1000,
            'github_activity': 50,
            'whitepaper_quality': 80
        }
        patterns = pattern_recognizer.analyze_data(test_data, 'project')
        print(f"✅ Pattern recognition test: {len(patterns)} patterns found")
        
        # Test anomaly detection
        anomaly_detector.add_data_point('cpu_usage', 75.0)
        print("✅ Anomaly detection test completed")
        
        # Test decision making
        decision = decision_maker.make_decision(
            DecisionType.PROJECT_APPROVAL,
            {
                'risk_score': 0.3,
                'reward_score': 0.8,
                'team_reputation': 0.7,
                'community_size': 5000
            }
        )
        if decision:
            print(f"✅ Decision making test: {decision.recommendation} (confidence: {decision.confidence:.2f})")
        else:
            print("✅ Decision making test: No decision made (low confidence)")
        
        # Test learning module
        learning_module.add_experience(
            'project_approval',
            {'risk_score': 0.3, 'reward_score': 0.8},
            'approve',
            {'success': True, 'profit': 100},
            True
        )
        print("✅ Learning module test completed")
        
        print("\n🎉 All basic functionality tests passed!")
        
    except Exception as e:
        print(f"\n❌ Functionality test failed: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
