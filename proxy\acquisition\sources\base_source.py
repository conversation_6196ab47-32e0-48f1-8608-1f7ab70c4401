"""
基础代理源

该模块定义了代理源的基类。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any

from proxy.models.proxy import Proxy


class BaseProxySource(ABC):
    """代理源基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化代理源
        
        Args:
            config: 配置字典，包含代理源的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def get_proxies(self, count: int) -> List[Proxy]:
        """
        获取代理
        
        Args:
            count: 要获取的代理数量
            
        Returns:
            代理列表
        """
        pass