"""
Main Window

The main application window for AirHunter providing the primary user interface
with navigation, dashboard, and system controls.
"""

import sys
import logging
from typing import Dict, Any, Optional
from datetime import datetime

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QWidget, Q<PERSON>oxLayout, QHBoxLayout,
        QTabWidget, QMenuBar, QStatusBar, QToolBar, QAction, QLabel,
        QSystemTrayIcon, QMenu, QMessageBox, QSplitter
    )
    from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
    from PyQt6.QtGui import QIcon, QPixmap, QFont
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    # Fallback classes for when PyQt6 is not available
    class QMainWindow:
        pass
    class QWidget:
        pass
    class pyqtSignal:
        def __init__(self, *args):
            pass

from .dashboard import (
    OverviewPanel, ProjectPanel, WalletPanel, 
    TaskPanel, ProxyPanel, AnalyticsPanel
)
from .settings import (
    GeneralSettings, AgentSettings, ProxySettings,
    SecuritySettings, AdvancedSettings
)
from .components import NotificationCenter
from .themes import ThemeManager


class SystemStatusThread(QThread if PYQT_AVAILABLE else object):
    """Background thread for updating system status."""
    
    status_updated = pyqtSignal(dict) if PYQT_AVAILABLE else None
    
    def __init__(self, parent=None):
        if PYQT_AVAILABLE:
            super().__init__(parent)
        self.running = False
        self.update_interval = 5000  # 5 seconds
    
    def run(self):
        """Run status update loop."""
        if not PYQT_AVAILABLE:
            return
            
        while self.running:
            try:
                # Collect system status information
                status = self._collect_system_status()
                if self.status_updated:
                    self.status_updated.emit(status)
                
                self.msleep(self.update_interval)
            except Exception as e:
                logging.error(f"Status thread error: {e}")
    
    def _collect_system_status(self) -> Dict[str, Any]:
        """Collect current system status."""
        # This would integrate with actual system components
        return {
            "timestamp": datetime.now(),
            "agents_running": 8,
            "agents_total": 10,
            "active_projects": 15,
            "pending_tasks": 42,
            "proxy_status": "connected",
            "system_health": "good"
        }
    
    def start_monitoring(self):
        """Start status monitoring."""
        self.running = True
        if PYQT_AVAILABLE:
            self.start()
    
    def stop_monitoring(self):
        """Stop status monitoring."""
        self.running = False
        if PYQT_AVAILABLE:
            self.quit()
            self.wait()


class MainWindow(QMainWindow if PYQT_AVAILABLE else object):
    """
    Main application window for AirHunter.
    
    Provides the primary user interface with dashboard, settings,
    and system monitoring capabilities.
    """
    
    def __init__(self, app_controller=None):
        """
        Initialize main window.
        
        Args:
            app_controller: Application controller instance
        """
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt6 is required for the GUI interface")
        
        super().__init__()
        self.app_controller = app_controller
        self.logger = logging.getLogger(__name__)
        
        # UI components
        self.central_widget = None
        self.tab_widget = None
        self.status_bar = None
        self.system_tray = None
        self.notification_center = None
        
        # Dashboard panels
        self.overview_panel = None
        self.project_panel = None
        self.wallet_panel = None
        self.task_panel = None
        self.proxy_panel = None
        self.analytics_panel = None
        
        # Settings panels
        self.general_settings = None
        self.agent_settings = None
        self.proxy_settings = None
        self.security_settings = None
        self.advanced_settings = None
        
        # Theme management
        self.theme_manager = ThemeManager()
        
        # Status monitoring
        self.status_thread = SystemStatusThread(self)
        self.status_thread.status_updated.connect(self._update_status_display)
        
        # Initialize UI
        self._init_ui()
        self._setup_system_tray()
        self._connect_signals()
        
        # Start status monitoring
        self.status_thread.start_monitoring()
        
        self.logger.info("Main window initialized")
    
    def _init_ui(self):
        """Initialize user interface."""
        self.setWindowTitle("AirHunter - Intelligent Airdrop Hunter")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)
        
        # Apply theme
        self.theme_manager.apply_theme(self, "dark")
        
        # Create central widget
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(self.central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Create tab widget for main content
        self.tab_widget = QTabWidget()
        splitter.addWidget(self.tab_widget)
        
        # Initialize dashboard panels
        self._init_dashboard_panels()
        
        # Initialize settings panels
        self._init_settings_panels()
        
        # Create notification center
        self.notification_center = NotificationCenter()
        splitter.addWidget(self.notification_center)
        
        # Set splitter proportions
        splitter.setSizes([1000, 400])
        
        # Create menu bar
        self._create_menu_bar()
        
        # Create toolbar
        self._create_toolbar()
        
        # Create status bar
        self._create_status_bar()
    
    def _init_dashboard_panels(self):
        """Initialize dashboard panels."""
        # Overview panel
        self.overview_panel = OverviewPanel()
        self.tab_widget.addTab(self.overview_panel, "Overview")
        
        # Project panel
        self.project_panel = ProjectPanel()
        self.tab_widget.addTab(self.project_panel, "Projects")
        
        # Wallet panel
        self.wallet_panel = WalletPanel()
        self.tab_widget.addTab(self.wallet_panel, "Wallets")
        
        # Task panel
        self.task_panel = TaskPanel()
        self.tab_widget.addTab(self.task_panel, "Tasks")
        
        # Proxy panel
        self.proxy_panel = ProxyPanel()
        self.tab_widget.addTab(self.proxy_panel, "Proxies")
        
        # Analytics panel
        self.analytics_panel = AnalyticsPanel()
        self.tab_widget.addTab(self.analytics_panel, "Analytics")
    
    def _init_settings_panels(self):
        """Initialize settings panels."""
        # Create settings tab widget
        settings_widget = QTabWidget()
        
        # General settings
        self.general_settings = GeneralSettings()
        settings_widget.addTab(self.general_settings, "General")
        
        # Agent settings
        self.agent_settings = AgentSettings()
        settings_widget.addTab(self.agent_settings, "Agents")
        
        # Proxy settings
        self.proxy_settings = ProxySettings()
        settings_widget.addTab(self.proxy_settings, "Proxies")
        
        # Security settings
        self.security_settings = SecuritySettings()
        settings_widget.addTab(self.security_settings, "Security")
        
        # Advanced settings
        self.advanced_settings = AdvancedSettings()
        settings_widget.addTab(self.advanced_settings, "Advanced")
        
        # Add settings to main tab widget
        self.tab_widget.addTab(settings_widget, "Settings")
    
    def _create_menu_bar(self):
        """Create application menu bar."""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("File")
        
        # Import/Export actions
        import_action = QAction("Import Configuration", self)
        import_action.triggered.connect(self._import_configuration)
        file_menu.addAction(import_action)
        
        export_action = QAction("Export Configuration", self)
        export_action.triggered.connect(self._export_configuration)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # System menu
        system_menu = menubar.addMenu("System")
        
        # Start/Stop actions
        start_action = QAction("Start System", self)
        start_action.triggered.connect(self._start_system)
        system_menu.addAction(start_action)
        
        stop_action = QAction("Stop System", self)
        stop_action.triggered.connect(self._stop_system)
        system_menu.addAction(stop_action)
        
        system_menu.addSeparator()
        
        # Health check action
        health_action = QAction("System Health Check", self)
        health_action.triggered.connect(self._run_health_check)
        system_menu.addAction(health_action)
        
        # View menu
        view_menu = menubar.addMenu("View")
        
        # Theme actions
        dark_theme_action = QAction("Dark Theme", self)
        dark_theme_action.triggered.connect(lambda: self._change_theme("dark"))
        view_menu.addAction(dark_theme_action)
        
        light_theme_action = QAction("Light Theme", self)
        light_theme_action.triggered.connect(lambda: self._change_theme("light"))
        view_menu.addAction(light_theme_action)
        
        # Help menu
        help_menu = menubar.addMenu("Help")
        
        # About action
        about_action = QAction("About AirHunter", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _create_toolbar(self):
        """Create application toolbar."""
        toolbar = self.addToolBar("Main")
        toolbar.setMovable(False)
        
        # Start/Stop buttons
        start_action = QAction("Start", self)
        start_action.setToolTip("Start AirHunter system")
        start_action.triggered.connect(self._start_system)
        toolbar.addAction(start_action)
        
        stop_action = QAction("Stop", self)
        stop_action.setToolTip("Stop AirHunter system")
        stop_action.triggered.connect(self._stop_system)
        toolbar.addAction(stop_action)
        
        toolbar.addSeparator()
        
        # Refresh action
        refresh_action = QAction("Refresh", self)
        refresh_action.setToolTip("Refresh all data")
        refresh_action.triggered.connect(self._refresh_data)
        toolbar.addAction(refresh_action)
    
    def _create_status_bar(self):
        """Create application status bar."""
        self.status_bar = self.statusBar()
        
        # System status label
        self.system_status_label = QLabel("System: Stopped")
        self.status_bar.addWidget(self.system_status_label)
        
        # Agent status label
        self.agent_status_label = QLabel("Agents: 0/0")
        self.status_bar.addPermanentWidget(self.agent_status_label)
        
        # Connection status label
        self.connection_status_label = QLabel("Connection: Disconnected")
        self.status_bar.addPermanentWidget(self.connection_status_label)
    
    def _setup_system_tray(self):
        """Setup system tray icon."""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            return
        
        self.system_tray = QSystemTrayIcon(self)
        
        # Create tray menu
        tray_menu = QMenu()
        
        show_action = QAction("Show AirHunter", self)
        show_action.triggered.connect(self.show)
        tray_menu.addAction(show_action)
        
        tray_menu.addSeparator()
        
        quit_action = QAction("Quit", self)
        quit_action.triggered.connect(QApplication.instance().quit)
        tray_menu.addAction(quit_action)
        
        self.system_tray.setContextMenu(tray_menu)
        self.system_tray.activated.connect(self._tray_icon_activated)
        
        # Set tray icon
        # self.system_tray.setIcon(QIcon("resources/icons/airhunter.png"))
        self.system_tray.show()
    
    def _connect_signals(self):
        """Connect UI signals."""
        # Tab change signal
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
        
        # Panel signals would be connected here
        # Example: self.project_panel.project_selected.connect(self._on_project_selected)
    
    def _update_status_display(self, status: Dict[str, Any]):
        """Update status display with new information."""
        # Update status bar
        if status.get("system_health") == "good":
            self.system_status_label.setText("System: Running")
        else:
            self.system_status_label.setText("System: Issues Detected")
        
        agents_running = status.get("agents_running", 0)
        agents_total = status.get("agents_total", 0)
        self.agent_status_label.setText(f"Agents: {agents_running}/{agents_total}")
        
        proxy_status = status.get("proxy_status", "disconnected")
        self.connection_status_label.setText(f"Connection: {proxy_status.title()}")
        
        # Update overview panel if visible
        if self.tab_widget.currentWidget() == self.overview_panel:
            self.overview_panel.update_status(status)
    
    def _on_tab_changed(self, index: int):
        """Handle tab change."""
        current_widget = self.tab_widget.widget(index)
        tab_name = self.tab_widget.tabText(index)
        
        self.logger.debug(f"Switched to tab: {tab_name}")
        
        # Refresh data for the current tab
        if hasattr(current_widget, 'refresh_data'):
            current_widget.refresh_data()
    
    def _tray_icon_activated(self, reason):
        """Handle system tray icon activation."""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show()
            self.raise_()
            self.activateWindow()
    
    def _start_system(self):
        """Start AirHunter system."""
        if self.app_controller:
            self.app_controller.start_system()
        self.logger.info("System start requested")
    
    def _stop_system(self):
        """Stop AirHunter system."""
        if self.app_controller:
            self.app_controller.stop_system()
        self.logger.info("System stop requested")
    
    def _refresh_data(self):
        """Refresh all data."""
        current_widget = self.tab_widget.currentWidget()
        if hasattr(current_widget, 'refresh_data'):
            current_widget.refresh_data()
        self.logger.info("Data refresh requested")
    
    def _change_theme(self, theme_name: str):
        """Change application theme."""
        self.theme_manager.apply_theme(self, theme_name)
        self.logger.info(f"Theme changed to: {theme_name}")
    
    def _import_configuration(self):
        """Import system configuration."""
        # Implementation would show file dialog and import config
        self.logger.info("Configuration import requested")
    
    def _export_configuration(self):
        """Export system configuration."""
        # Implementation would show file dialog and export config
        self.logger.info("Configuration export requested")
    
    def _run_health_check(self):
        """Run system health check."""
        # Implementation would trigger health check
        self.logger.info("Health check requested")
    
    def _show_about(self):
        """Show about dialog."""
        QMessageBox.about(
            self,
            "About AirHunter",
            "AirHunter v1.0.0\n\n"
            "Intelligent Airdrop Hunter System\n"
            "Multi-agent cryptocurrency airdrop automation\n\n"
            "© 2024 AirHunter Team"
        )
    
    def closeEvent(self, event):
        """Handle window close event."""
        if self.system_tray and self.system_tray.isVisible():
            # Minimize to tray instead of closing
            self.hide()
            event.ignore()
        else:
            # Stop status monitoring
            self.status_thread.stop_monitoring()
            
            # Stop system if running
            if self.app_controller:
                self.app_controller.stop_system()
            
            event.accept()
            self.logger.info("Main window closed")
