"""
项目评估智能体主程序

该模块提供了项目评估智能体的命令行接口。
"""

import argparse
import json
import logging
import os
import sys
import time
from typing import Dict, Any

from assessment.assessment_agent import AssessmentAgent


def setup_logging(log_level: str = 'INFO') -> None:
    """
    设置日志
    
    Args:
        log_level: 日志级别
    """
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    logging.basicConfig(level=getattr(logging, log_level), format=log_format)


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 获取评估智能体配置
        if 'assessment' in config:
            return config['assessment']
        else:
            return {}
    
    except Exception as e:
        logging.error(f"加载配置文件时出错: {str(e)}")
        return {}


def main() -> None:
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='项目评估智能体')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--log-level', type=str, default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'], help='日志级别')
    parser.add_argument('--project-id', type=str, help='要评估的项目ID')
    parser.add_argument('--assess-all', action='store_true', help='评估所有新项目')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建评估智能体
    agent = AssessmentAgent(config)
    
    if args.project_id:
        # 评估单个项目
        from discovery.discovery_agent import DiscoveryAgent
        from discovery.models.project import Project
        
        # 加载发现智能体配置
        discovery_config = {}
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                full_config = json.load(f)
                if 'discovery' in full_config:
                    discovery_config = full_config['discovery']
        except Exception as e:
            logging.error(f"加载发现智能体配置时出错: {str(e)}")
        
        # 创建发现智能体
        discovery_agent = DiscoveryAgent(discovery_config)
        
        # 获取项目
        project = discovery_agent.get_project(args.project_id)
        
        if project:
            # 评估项目
            assessed_project = agent.assess_project(project)
            
            # 更新项目
            discovery_agent.update_project(assessed_project)
            
            logging.info(f"项目 {project.name} (ID: {project.id}) 评估完成")
            logging.info(f"风险分数: {assessed_project.risk_score}")
            logging.info(f"潜在收益: {assessed_project.potential_reward}")
            logging.info(f"社区评分: {assessed_project.community_score}")
            logging.info(f"团队评分: {assessed_project.team_score}")
            logging.info(f"状态: {assessed_project.status.value}")
        else:
            logging.error(f"找不到项目: {args.project_id}")
    
    elif args.assess_all:
        # 启动评估智能体
        agent.start()
        
        try:
            # 保持程序运行
            while True:
                time.sleep(1)
        
        except KeyboardInterrupt:
            # 停止评估智能体
            agent.stop()
            logging.info("项目评估智能体已停止")
    
    else:
        parser.print_help()


if __name__ == '__main__':
    main()