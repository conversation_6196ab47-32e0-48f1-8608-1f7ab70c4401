"""
项目评估智能体测试

该模块包含项目评估智能体的单元测试。
"""

import unittest
import os
import sys
import json
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from assessment.assessment_agent import AssessmentAgent
from discovery.models.project import Project, ProjectStatus, TokenInfo, SocialChannel, TeamMember, Requirement
from discovery.models.enums import ProjectType, BlockchainPlatform, SocialChannelType, RequirementDifficulty


class TestAssessmentAgent(unittest.TestCase):
    """项目评估智能体测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试配置
        self.config = {
            'assessment_interval': 300,
            'max_projects_per_batch': 10,
            'min_confidence_score': 0.6,
            'verification': {
                'team': {},
                'social': {},
                'project': {}
            },
            'security': {
                'contract': {},
                'vulnerability': {},
                'permission': {}
            },
            'risk': {
                'risk': {},
                'reward': {},
                'score': {}
            }
        }
        
        # 创建测试项目
        self.project = self._create_test_project()
        
        # 创建模拟验证器和分析器
        self.mock_team_verifier = MagicMock()
        self.mock_social_verifier = MagicMock()
        self.mock_project_verifier = MagicMock()
        self.mock_contract_analyzer = MagicMock()
        self.mock_vulnerability_scanner = MagicMock()
        self.mock_permission_analyzer = MagicMock()
        self.mock_risk_calculator = MagicMock()
        self.mock_reward_estimator = MagicMock()
        self.mock_score_generator = MagicMock()
        
        # 设置模拟返回值
        self.mock_team_verifier.verify.return_value = {'verified': True, 'confidence': 0.8}
        self.mock_social_verifier.verify.return_value = {'verified': True, 'confidence': 0.7}
        self.mock_project_verifier.verify.return_value = {'verified': True, 'confidence': 0.9}
        self.mock_contract_analyzer.analyze.return_value = {'analyzed': True, 'risk_level': 'low'}
        self.mock_vulnerability_scanner.scan.return_value = {'scanned': True, 'critical_vulnerabilities': 0}
        self.mock_permission_analyzer.analyze.return_value = {'analyzed': True, 'centralization_level': 'low'}
        self.mock_risk_calculator.calculate.return_value = 30.0
        self.mock_reward_estimator.estimate.return_value = 70.0
        self.mock_score_generator.generate.return_value = (80.0, 75.0)
        
        # 创建模拟统计信息
        self.mock_stats = {'total_assessments': 1, 'successful_assessments': 1}
        self.mock_team_verifier.get_stats.return_value = self.mock_stats
        self.mock_social_verifier.get_stats.return_value = self.mock_stats
        self.mock_project_verifier.get_stats.return_value = self.mock_stats
        self.mock_contract_analyzer.get_stats.return_value = self.mock_stats
        self.mock_vulnerability_scanner.get_stats.return_value = self.mock_stats
        self.mock_permission_analyzer.get_stats.return_value = self.mock_stats
        self.mock_risk_calculator.get_stats.return_value = self.mock_stats
        self.mock_reward_estimator.get_stats.return_value = self.mock_stats
        self.mock_score_generator.get_stats.return_value = self.mock_stats
    
    def _create_test_project(self):
        """创建测试项目"""
        # 创建团队成员
        team_members = [
            TeamMember(
                name="John Doe",
                role="CEO & Founder",
                bio="Blockchain enthusiast with 5 years of experience in DeFi projects.",
                links=["https://linkedin.com/in/johndoe", "https://github.com/johndoe"]
            ),
            TeamMember(
                name="Jane Smith",
                role="CTO",
                bio="Software engineer with expertise in smart contract development.",
                links=["https://linkedin.com/in/janesmith", "https://github.com/janesmith"]
            )
        ]
        
        # 创建社交媒体渠道
        social_channels = [
            SocialChannel(
                channel_type=SocialChannelType.TWITTER,
                url="https://twitter.com/testproject",
                followers=1000
            ),
            SocialChannel(
                channel_type=SocialChannelType.TELEGRAM,
                url="https://t.me/testproject",
                followers=500
            )
        ]
        
        # 创建参与要求
        requirements = [
            Requirement(
                description="Complete testnet tasks",
                difficulty=RequirementDifficulty.MEDIUM,
                estimated_time=60
            ),
            Requirement(
                description="Join Discord and Telegram groups",
                difficulty=RequirementDifficulty.EASY,
                estimated_time=10
            )
        ]
        
        # 创建代币信息
        token_info = TokenInfo(
            name="Test Token",
            symbol="TEST",
            blockchain=BlockchainPlatform.ETHEREUM,
            contract_address="******************************************",
            total_supply=1000000000,
            airdrop_amount=1000000,
            estimated_value=1.0
        )
        
        # 创建项目
        project = Project(
            id="test-project-001",
            name="Test Project",
            description="Test project for unit testing",
            url="https://testproject.io",
            project_type=ProjectType.AIRDROP,
            blockchain=BlockchainPlatform.ETHEREUM,
            status=ProjectStatus.NEW,
            team=team_members,
            social_channels=social_channels,
            requirements=requirements,
            token_info=token_info,
            tags=["test", "airdrop"]
        )
        
        return project
    
    @patch('assessment.assessment_agent.TeamVerifier')
    @patch('assessment.assessment_agent.SocialVerifier')
    @patch('assessment.assessment_agent.ProjectVerifier')
    @patch('assessment.assessment_agent.ContractAnalyzer')
    @patch('assessment.assessment_agent.VulnerabilityScanner')
    @patch('assessment.assessment_agent.PermissionAnalyzer')
    @patch('assessment.assessment_agent.RiskCalculator')
    @patch('assessment.assessment_agent.RewardEstimator')
    @patch('assessment.assessment_agent.ScoreGenerator')
    def test_init(self, mock_score_gen, mock_reward_est, mock_risk_calc, 
                 mock_perm_ana, mock_vuln_scan, mock_contract_ana, 
                 mock_proj_ver, mock_social_ver, mock_team_ver):
        """测试初始化"""
        # 设置模拟对象
        mock_team_ver.return_value = self.mock_team_verifier
        mock_social_ver.return_value = self.mock_social_verifier
        mock_proj_ver.return_value = self.mock_project_verifier
        mock_contract_ana.return_value = self.mock_contract_analyzer
        mock_vuln_scan.return_value = self.mock_vulnerability_scanner
        mock_perm_ana.return_value = self.mock_permission_analyzer
        mock_risk_calc.return_value = self.mock_risk_calculator
        mock_reward_est.return_value = self.mock_reward_estimator
        mock_score_gen.return_value = self.mock_score_generator
        
        # 创建评估智能体
        agent = AssessmentAgent(self.config)
        
        # 验证初始化
        self.assertEqual(agent.config, self.config)
        self.assertEqual(agent.assessment_interval, 300)
        self.assertEqual(agent.max_projects_per_batch, 10)
        self.assertEqual(agent.min_confidence_score, 0.6)
        
        # 验证组件初始化
        mock_team_ver.assert_called_once()
        mock_social_ver.assert_called_once()
        mock_proj_ver.assert_called_once()
        mock_contract_ana.assert_called_once()
        mock_vuln_scan.assert_called_once()
        mock_perm_ana.assert_called_once()
        mock_risk_calc.assert_called_once()
        mock_reward_est.assert_called_once()
        mock_score_gen.assert_called_once()
    
    @patch('assessment.assessment_agent.TeamVerifier')
    @patch('assessment.assessment_agent.SocialVerifier')
    @patch('assessment.assessment_agent.ProjectVerifier')
    @patch('assessment.assessment_agent.ContractAnalyzer')
    @patch('assessment.assessment_agent.VulnerabilityScanner')
    @patch('assessment.assessment_agent.PermissionAnalyzer')
    @patch('assessment.assessment_agent.RiskCalculator')
    @patch('assessment.assessment_agent.RewardEstimator')
    @patch('assessment.assessment_agent.ScoreGenerator')
    def test_assess_project(self, mock_score_gen, mock_reward_est, mock_risk_calc, 
                           mock_perm_ana, mock_vuln_scan, mock_contract_ana, 
                           mock_proj_ver, mock_social_ver, mock_team_ver):
        """测试项目评估"""
        # 设置模拟对象
        mock_team_ver.return_value = self.mock_team_verifier
        mock_social_ver.return_value = self.mock_social_verifier
        mock_proj_ver.return_value = self.mock_project_verifier
        mock_contract_ana.return_value = self.mock_contract_analyzer
        mock_vuln_scan.return_value = self.mock_vulnerability_scanner
        mock_perm_ana.return_value = self.mock_permission_analyzer
        mock_risk_calc.return_value = self.mock_risk_calculator
        mock_reward_est.return_value = self.mock_reward_estimator
        mock_score_gen.return_value = self.mock_score_generator
        
        # 创建评估智能体
        agent = AssessmentAgent(self.config)
        
        # 评估项目
        assessed_project = agent.assess_project(self.project)
        
        # 验证评估结果
        self.assertEqual(assessed_project.risk_score, 30.0)
        self.assertEqual(assessed_project.potential_reward, 70.0)
        self.assertEqual(assessed_project.community_score, 80.0)
        self.assertEqual(assessed_project.team_score, 75.0)
        
        # 验证组件调用
        self.mock_team_verifier.verify.assert_called_once_with(self.project)
        self.mock_social_verifier.verify.assert_called_once_with(self.project)
        self.mock_project_verifier.verify.assert_called_once_with(self.project)
        self.mock_contract_analyzer.analyze.assert_called_once_with(self.project)
        self.mock_vulnerability_scanner.scan.assert_called_once_with(self.project)
        self.mock_permission_analyzer.analyze.assert_called_once_with(self.project)
        self.mock_risk_calculator.calculate.assert_called_once()
        self.mock_reward_estimator.estimate.assert_called_once_with(self.project)
        self.mock_score_generator.generate.assert_called_once()
    
    @patch('assessment.assessment_agent.TeamVerifier')
    @patch('assessment.assessment_agent.SocialVerifier')
    @patch('assessment.assessment_agent.ProjectVerifier')
    @patch('assessment.assessment_agent.ContractAnalyzer')
    @patch('assessment.assessment_agent.VulnerabilityScanner')
    @patch('assessment.assessment_agent.PermissionAnalyzer')
    @patch('assessment.assessment_agent.RiskCalculator')
    @patch('assessment.assessment_agent.RewardEstimator')
    @patch('assessment.assessment_agent.ScoreGenerator')
    def test_assess_project_with_error(self, mock_score_gen, mock_reward_est, mock_risk_calc, 
                                      mock_perm_ana, mock_vuln_scan, mock_contract_ana, 
                                      mock_proj_ver, mock_social_ver, mock_team_ver):
        """测试项目评估出错"""
        # 设置模拟对象
        mock_team_ver.return_value = self.mock_team_verifier
        mock_social_ver.return_value = self.mock_social_verifier
        mock_proj_ver.return_value = self.mock_project_verifier
        mock_contract_ana.return_value = self.mock_contract_analyzer
        mock_vuln_scan.return_value = self.mock_vulnerability_scanner
        mock_perm_ana.return_value = self.mock_permission_analyzer
        mock_risk_calc.return_value = self.mock_risk_calculator
        mock_reward_est.return_value = self.mock_reward_estimator
        mock_score_gen.return_value = self.mock_score_generator
        
        # 设置模拟错误
        self.mock_team_verifier.verify.side_effect = Exception("测试错误")
        
        # 创建评估智能体
        agent = AssessmentAgent(self.config)
        
        # 评估项目
        assessed_project = agent.assess_project(self.project)
        
        # 验证评估结果
        self.assertEqual(assessed_project.status, ProjectStatus.SUSPICIOUS)
        self.assertIn('assessment_error', assessed_project.tags)
    
    @patch('assessment.assessment_agent.TeamVerifier')
    @patch('assessment.assessment_agent.SocialVerifier')
    @patch('assessment.assessment_agent.ProjectVerifier')
    @patch('assessment.assessment_agent.ContractAnalyzer')
    @patch('assessment.assessment_agent.VulnerabilityScanner')
    @patch('assessment.assessment_agent.PermissionAnalyzer')
    @patch('assessment.assessment_agent.RiskCalculator')
    @patch('assessment.assessment_agent.RewardEstimator')
    @patch('assessment.assessment_agent.ScoreGenerator')
    def test_get_stats(self, mock_score_gen, mock_reward_est, mock_risk_calc, 
                      mock_perm_ana, mock_vuln_scan, mock_contract_ana, 
                      mock_proj_ver, mock_social_ver, mock_team_ver):
        """测试获取统计信息"""
        # 设置模拟对象
        mock_team_ver.return_value = self.mock_team_verifier
        mock_social_ver.return_value = self.mock_social_verifier
        mock_proj_ver.return_value = self.mock_project_verifier
        mock_contract_ana.return_value = self.mock_contract_analyzer
        mock_vuln_scan.return_value = self.mock_vulnerability_scanner
        mock_perm_ana.return_value = self.mock_permission_analyzer
        mock_risk_calc.return_value = self.mock_risk_calculator
        mock_reward_est.return_value = self.mock_reward_estimator
        mock_score_gen.return_value = self.mock_score_generator
        
        # 创建评估智能体
        agent = AssessmentAgent(self.config)
        
        # 获取统计信息
        stats = agent.get_stats()
        
        # 验证统计信息
        self.assertIn('running', stats)
        self.assertIn('assessment_interval', stats)
        self.assertIn('max_projects_per_batch', stats)
        self.assertIn('min_confidence_score', stats)
        self.assertIn('team_verifier', stats)
        self.assertIn('social_verifier', stats)
        self.assertIn('project_verifier', stats)
        self.assertIn('contract_analyzer', stats)
        self.assertIn('vulnerability_scanner', stats)
        self.assertIn('permission_analyzer', stats)
        self.assertIn('risk_calculator', stats)
        self.assertIn('reward_estimator', stats)
        self.assertIn('score_generator', stats)


if __name__ == '__main__':
    unittest.main()