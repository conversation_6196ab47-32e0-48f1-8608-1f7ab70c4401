"""
Profit Optimization Agent

This agent is responsible for optimizing profit generation from airdrops
and other reward mechanisms.
"""

import logging
import os
import json
import time
import random
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ProfitOptimizationAgent")

class ProfitOptimizationAgent:
    """
    Profit Optimization Agent for maximizing returns from airdrops.
    
    This agent handles:
    - Project profitability analysis
    - Resource allocation optimization
    - ROI tracking and forecasting
    - Strategy optimization
    - Risk-adjusted return calculation
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Profit Optimization Agent.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config = self._load_config(config_path)
        self.projects = {}
        self.strategies = {}
        self.roi_data = {}
        self.active = False
        
        # Initialize data storage
        os.makedirs(self.config["data_storage_path"], exist_ok=True)
        
        # Load project and strategy data
        self._load_projects()
        self._load_strategies()
        self._load_roi_data()
        
        logger.info("Profit Optimization Agent initialized")
    
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "data_storage_path": "data/profit_optimization",
            "min_roi_threshold": 1.5,  # Minimum ROI to consider a project profitable
            "max_concurrent_projects": 10,
            "resource_allocation": {
                "time_weight": 0.3,
                "capital_weight": 0.4,
                "risk_weight": 0.3
            },
            "risk_tolerance": 0.7,  # 0-1 scale, higher means more risk tolerance
            "rebalance_interval_hours": 24,
            "profit_taking_thresholds": {
                "low": 1.5,
                "medium": 3.0,
                "high": 5.0
            }
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    # Merge user config with defaults
                    for key, value in user_config.items():
                        if key == "resource_allocation" and isinstance(value, dict):
                            # Merge resource allocation
                            for subkey, subvalue in value.items():
                                default_config["resource_allocation"][subkey] = subvalue
                        elif key == "profit_taking_thresholds" and isinstance(value, dict):
                            # Merge profit taking thresholds
                            for subkey, subvalue in value.items():
                                default_config["profit_taking_thresholds"][subkey] = subvalue
                        else:
                            default_config[key] = value
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def _load_projects(self) -> None:
        """Load project data from storage."""
        projects_file = os.path.join(self.config["data_storage_path"], "projects.json")
        
        if os.path.exists(projects_file):
            try:
                with open(projects_file, 'r') as f:
                    self.projects = json.load(f)
                logger.info(f"Loaded {len(self.projects)} projects")
            except Exception as e:
                logger.error(f"Error loading projects: {e}")
                self.projects = {}
    
    def _save_projects(self) -> None:
        """Save project data to storage."""
        projects_file = os.path.join(self.config["data_storage_path"], "projects.json")
        
        try:
            with open(projects_file, 'w') as f:
                json.dump(self.projects, f, indent=2)
            logger.info(f"Saved {len(self.projects)} projects")
        except Exception as e:
            logger.error(f"Error saving projects: {e}")
    
    def _load_strategies(self) -> None:
        """Load strategy data from storage."""
        strategies_file = os.path.join(self.config["data_storage_path"], "strategies.json")
        
        if os.path.exists(strategies_file):
            try:
                with open(strategies_file, 'r') as f:
                    self.strategies = json.load(f)
                logger.info(f"Loaded {len(self.strategies)} strategies")
            except Exception as e:
                logger.error(f"Error loading strategies: {e}")
                self.strategies = {}
        else:
            # Initialize with default strategies
            self.strategies = self._get_default_strategies()
            self._save_strategies()
    
    def _save_strategies(self) -> None:
        """Save strategy data to storage."""
        strategies_file = os.path.join(self.config["data_storage_path"], "strategies.json")
        
        try:
            with open(strategies_file, 'w') as f:
                json.dump(self.strategies, f, indent=2)
            logger.info(f"Saved {len(self.strategies)} strategies")
        except Exception as e:
            logger.error(f"Error saving strategies: {e}")
    
    def _load_roi_data(self) -> None:
        """Load ROI data from storage."""
        roi_file = os.path.join(self.config["data_storage_path"], "roi_data.json")
        
        if os.path.exists(roi_file):
            try:
                with open(roi_file, 'r') as f:
                    self.roi_data = json.load(f)
                logger.info(f"Loaded ROI data for {len(self.roi_data)} projects")
            except Exception as e:
                logger.error(f"Error loading ROI data: {e}")
                self.roi_data = {}
    
    def _save_roi_data(self) -> None:
        """Save ROI data to storage."""
        roi_file = os.path.join(self.config["data_storage_path"], "roi_data.json")
        
        try:
            with open(roi_file, 'w') as f:
                json.dump(self.roi_data, f, indent=2)
            logger.info(f"Saved ROI data for {len(self.roi_data)} projects")
        except Exception as e:
            logger.error(f"Error saving ROI data: {e}")
    
    def _get_default_strategies(self) -> Dict[str, Any]:
        """
        Get default optimization strategies.
        
        Returns:
            Dict containing strategy data
        """
        return {
            "conservative": {
                "id": "conservative",
                "name": "Conservative Strategy",
                "description": "Focus on established projects with lower risk and moderate returns",
                "risk_tolerance": 0.3,
                "time_allocation": {
                    "research": 0.4,
                    "engagement": 0.3,
                    "monitoring": 0.3
                },
                "capital_allocation": {
                    "max_per_project": 0.1,  # Max 10% of capital per project
                    "min_per_project": 0.02  # Min 2% of capital per project
                },
                "project_criteria": {
                    "min_team_score": 0.7,
                    "min_tokenomics_score": 0.7,
                    "min_community_score": 0.6,
                    "max_risk_score": 0.4
                },
                "profit_taking": {
                    "target_roi": 2.0,
                    "stop_loss": 0.8,
                    "partial_take_profit": [
                        {"threshold": 1.5, "percentage": 0.3},
                        {"threshold": 2.0, "percentage": 0.3},
                        {"threshold": 3.0, "percentage": 0.4}
                    ]
                }
            },
            "balanced": {
                "id": "balanced",
                "name": "Balanced Strategy",
                "description": "Balance between risk and reward with a mix of established and promising new projects",
                "risk_tolerance": 0.5,
                "time_allocation": {
                    "research": 0.3,
                    "engagement": 0.4,
                    "monitoring": 0.3
                },
                "capital_allocation": {
                    "max_per_project": 0.15,  # Max 15% of capital per project
                    "min_per_project": 0.03  # Min 3% of capital per project
                },
                "project_criteria": {
                    "min_team_score": 0.6,
                    "min_tokenomics_score": 0.6,
                    "min_community_score": 0.5,
                    "max_risk_score": 0.6
                },
                "profit_taking": {
                    "target_roi": 3.0,
                    "stop_loss": 0.7,
                    "partial_take_profit": [
                        {"threshold": 2.0, "percentage": 0.3},
                        {"threshold": 3.0, "percentage": 0.3},
                        {"threshold": 4.0, "percentage": 0.4}
                    ]
                }
            },
            "aggressive": {
                "id": "aggressive",
                "name": "Aggressive Strategy",
                "description": "Focus on high-potential projects with higher risk and potentially higher returns",
                "risk_tolerance": 0.8,
                "time_allocation": {
                    "research": 0.2,
                    "engagement": 0.5,
                    "monitoring": 0.3
                },
                "capital_allocation": {
                    "max_per_project": 0.2,  # Max 20% of capital per project
                    "min_per_project": 0.05  # Min 5% of capital per project
                },
                "project_criteria": {
                    "min_team_score": 0.5,
                    "min_tokenomics_score": 0.5,
                    "min_community_score": 0.4,
                    "max_risk_score": 0.8
                },
                "profit_taking": {
                    "target_roi": 5.0,
                    "stop_loss": 0.6,
                    "partial_take_profit": [
                        {"threshold": 2.5, "percentage": 0.2},
                        {"threshold": 4.0, "percentage": 0.3},
                        {"threshold": 6.0, "percentage": 0.5}
                    ]
                }
            }
        }
    
    def start(self) -> bool:
        """
        Start the Profit Optimization Agent.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            # Perform initial analysis
            self._analyze_all_projects()
            
            self.active = True
            logger.info("Profit Optimization Agent started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start Profit Optimization Agent: {e}")
            return False
    
    def stop(self) -> bool:
        """
        Stop the Profit Optimization Agent.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        try:
            # Save current state
            self._save_projects()
            self._save_strategies()
            self._save_roi_data()
            
            self.active = False
            logger.info("Profit Optimization Agent stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Error stopping Profit Optimization Agent: {e}")
            return False
    
    def add_project(self, project_data: Dict[str, Any]) -> Optional[str]:
        """
        Add a new project for optimization.
        
        Args:
            project_data: Project data
            
        Returns:
            str: Project ID or None if failed
        """
        try:
            # Validate required fields
            required_fields = ["name", "description", "category", "estimated_airdrop_value"]
            for field in required_fields:
                if field not in project_data:
                    logger.error(f"Missing required field: {field}")
                    return None
            
            # Generate project ID if not provided
            if "id" not in project_data:
                project_id = f"project-{int(time.time())}-{random.randint(1000, 9999)}"
                project_data["id"] = project_id
            else:
                project_id = project_data["id"]
            
            # Add metadata
            project_data["added_at"] = datetime.now().isoformat()
            project_data["last_updated"] = datetime.now().isoformat()
            project_data["status"] = project_data.get("status", "pending_analysis")
            
            # Add default scores if not provided
            if "scores" not in project_data:
                project_data["scores"] = {
                    "team": 0.5,
                    "tokenomics": 0.5,
                    "community": 0.5,
                    "risk": 0.5,
                    "potential_roi": 0.5,
                    "engagement_requirement": 0.5
                }
            
            # Add default resource allocation if not provided
            if "resource_allocation" not in project_data:
                project_data["resource_allocation"] = {
                    "time": 0.0,
                    "capital": 0.0,
                    "identities": 0
                }
            
            # Store project
            self.projects[project_id] = project_data
            
            # Initialize ROI tracking
            if project_id not in self.roi_data:
                self.roi_data[project_id] = {
                    "initial_investment": 0.0,
                    "current_value": 0.0,
                    "roi": 0.0,
                    "last_updated": datetime.now().isoformat(),
                    "history": []
                }
            
            # Save projects
            self._save_projects()
            
            # Analyze project
            self._analyze_project(project_id)
            
            logger.info(f"Added project: {project_data['name']} (ID: {project_id})")
            return project_id
        except Exception as e:
            logger.error(f"Error adding project: {e}")
            return None
    
    def update_project(self, project_id: str, project_data: Dict[str, Any]) -> bool:
        """
        Update an existing project.
        
        Args:
            project_id: Project ID
            project_data: Updated project data
            
        Returns:
            bool: True if updated successfully, False otherwise
        """
        if project_id not in self.projects:
            logger.error(f"Project not found: {project_id}")
            return False
        
        try:
            # Get current project data
            current_data = self.projects[project_id]
            
            # Update fields
            for key, value in project_data.items():
                if key == "scores" and isinstance(value, dict):
                    # Merge scores
                    if "scores" not in current_data:
                        current_data["scores"] = {}
                    for score_key, score_value in value.items():
                        current_data["scores"][score_key] = score_value
                elif key == "resource_allocation" and isinstance(value, dict):
                    # Merge resource allocation
                    if "resource_allocation" not in current_data:
                        current_data["resource_allocation"] = {}
                    for res_key, res_value in value.items():
                        current_data["resource_allocation"][res_key] = res_value
                else:
                    current_data[key] = value
            
            # Update metadata
            current_data["last_updated"] = datetime.now().isoformat()
            
            # Save projects
            self._save_projects()
            
            # Re-analyze project
            self._analyze_project(project_id)
            
            logger.info(f"Updated project: {current_data['name']} (ID: {project_id})")
            return True
        except Exception as e:
            logger.error(f"Error updating project: {e}")
            return False
    
    def get_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a project by ID.
        
        Args:
            project_id: Project ID
            
        Returns:
            Dict containing project data or None if not found
        """
        if project_id not in self.projects:
            logger.warning(f"Project not found: {project_id}")
            return None
        
        return self.projects[project_id]
    
    def list_projects(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        List projects with optional filtering.
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            List of project data
        """
        projects = list(self.projects.values())
        
        if filters:
            filtered_projects = []
            for project in projects:
                match = True
                
                for key, value in filters.items():
                    if key == "min_roi":
                        project_roi = self.get_project_roi(project["id"])
                        if project_roi < value:
                            match = False
                            break
                    elif key == "status":
                        if project.get("status") != value:
                            match = False
                            break
                    elif key == "category":
                        if project.get("category") != value:
                            match = False
                            break
                    elif key == "min_score":
                        if key in filters and isinstance(value, dict):
                            for score_key, min_score in value.items():
                                if project.get("scores", {}).get(score_key, 0) < min_score:
                                    match = False
                                    break
                    elif key == "max_score":
                        if key in filters and isinstance(value, dict):
                            for score_key, max_score in value.items():
                                if project.get("scores", {}).get(score_key, 1) > max_score:
                                    match = False
                                    break
                
                if match:
                    filtered_projects.append(project)
            
            return filtered_projects
        
        return projects
    
    def _analyze_project(self, project_id: str) -> Dict[str, Any]:
        """
        Analyze a project for optimization.
        
        Args:
            project_id: Project ID
            
        Returns:
            Dict containing analysis results
        """
        if project_id not in self.projects:
            logger.warning(f"Project not found: {project_id}")
            return {}
        
        try:
            project = self.projects[project_id]
            
            # Calculate profitability score
            estimated_value = project.get("estimated_airdrop_value", 0)
            engagement_requirement = project.get("scores", {}).get("engagement_requirement", 0.5)
            team_score = project.get("scores", {}).get("team", 0.5)
            tokenomics_score = project.get("scores", {}).get("tokenomics", 0.5)
            community_score = project.get("scores", {}).get("community", 0.5)
            risk_score = project.get("scores", {}).get("risk", 0.5)
            
            # Calculate potential ROI
            time_cost = engagement_requirement * 100  # Convert to hours
            capital_cost = project.get("estimated_capital_required", 0)
            
            if time_cost > 0 or capital_cost > 0:
                # Calculate ROI based on time and capital costs
                time_value = time_cost * 20  # Assume $20 per hour value
                total_cost = time_value + capital_cost
                
                if total_cost > 0:
                    raw_roi = estimated_value / total_cost
                else:
                    raw_roi = estimated_value / 1  # Avoid division by zero
            else:
                raw_roi = 0
            
            # Adjust ROI based on risk and other factors
            adjusted_roi = raw_roi * (1 - risk_score * 0.5)  # Higher risk reduces ROI
            adjusted_roi = adjusted_roi * (team_score * 0.3 + tokenomics_score * 0.3 + community_score * 0.4)
            
            # Update project scores
            if "scores" not in project:
                project["scores"] = {}
            
            project["scores"]["potential_roi"] = min(1.0, adjusted_roi / 10)  # Normalize to 0-1
            project["scores"]["profitability"] = min(1.0, adjusted_roi / self.config["min_roi_threshold"])
            
            # Determine project status based on analysis
            if adjusted_roi >= self.config["min_roi_threshold"]:
                project["status"] = "approved"
                project["analysis_result"] = "profitable"
            else:
                project["status"] = "rejected"
                project["analysis_result"] = "unprofitable"
            
            # Save analysis results
            project["analysis"] = {
                "estimated_value": estimated_value,
                "time_cost": time_cost,
                "capital_cost": capital_cost,
                "raw_roi": raw_roi,
                "adjusted_roi": adjusted_roi,
                "analyzed_at": datetime.now().isoformat()
            }
            
            # Save projects
            self._save_projects()
            
            logger.info(f"Analyzed project {project['name']} (ID: {project_id}): ROI = {adjusted_roi:.2f}")
            
            return project["analysis"]
        except Exception as e:
            logger.error(f"Error analyzing project {project_id}: {e}")
            return {}
    
    def _analyze_all_projects(self) -> None:
        """Analyze all projects for optimization."""
        for project_id in self.projects:
            self._analyze_project(project_id)
    
    def optimize_resource_allocation(self, strategy_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Optimize resource allocation across projects.
        
        Args:
            strategy_id: Optional strategy ID to use
            
        Returns:
            Dict containing optimization results
        """
        try:
            # Get active strategy
            if strategy_id and strategy_id in self.strategies:
                strategy = self.strategies[strategy_id]
            else:
                # Use balanced strategy by default
                strategy = self.strategies.get("balanced", self._get_default_strategies()["balanced"])
            
            # Get approved projects
            approved_projects = [p for p in self.projects.values() if p.get("status") == "approved"]
            
            if not approved_projects:
                logger.warning("No approved projects found for optimization")
                return {"success": False, "error": "No approved projects found"}
            
            # Sort projects by adjusted ROI
            sorted_projects = sorted(approved_projects, 
                                    key=lambda p: p.get("analysis", {}).get("adjusted_roi", 0),
                                    reverse=True)
            
            # Limit to max concurrent projects
            max_projects = min(self.config["max_concurrent_projects"], len(sorted_projects))
            selected_projects = sorted_projects[:max_projects]
            
            # Calculate total scores for allocation
            total_roi_score = sum(p.get("analysis", {}).get("adjusted_roi", 0) for p in selected_projects)
            
            # Allocate resources
            allocations = {}
            
            for project in selected_projects:
                project_id = project["id"]
                roi_score = project.get("analysis", {}).get("adjusted_roi", 0)
                
                # Skip projects with zero ROI
                if roi_score <= 0:
                    continue
                
                # Calculate allocation ratio based on ROI
                if total_roi_score > 0:
                    allocation_ratio = roi_score / total_roi_score
                else:
                    allocation_ratio = 1.0 / len(selected_projects)
                
                # Apply strategy constraints
                min_allocation = strategy["capital_allocation"]["min_per_project"]
                max_allocation = strategy["capital_allocation"]["max_per_project"]
                
                # Adjust allocation to stay within bounds
                capital_allocation = max(min_allocation, min(max_allocation, allocation_ratio))
                
                # Calculate time allocation based on engagement requirement
                engagement_req = project.get("scores", {}).get("engagement_requirement", 0.5)
                time_allocation = capital_allocation * (1 + engagement_req) / 2  # Balance between capital and engagement
                
                # Calculate number of identities based on engagement
                identities = max(1, int(engagement_req * 5))  # 1-5 identities based on engagement
                
                # Store allocation
                allocations[project_id] = {
                    "capital": capital_allocation,
                    "time": time_allocation,
                    "identities": identities
                }
                
                # Update project resource allocation
                if "resource_allocation" not in project:
                    project["resource_allocation"] = {}
                
                project["resource_allocation"]["capital"] = capital_allocation
                project["resource_allocation"]["time"] = time_allocation
                project["resource_allocation"]["identities"] = identities
            
            # Save projects
            self._save_projects()
            
            logger.info(f"Optimized resource allocation for {len(allocations)} projects using {strategy['name']}")
            
            return {
                "success": True,
                "strategy": strategy["id"],
                "allocations": allocations,
                "total_projects": len(allocations)
            }
        except Exception as e:
            logger.error(f"Error optimizing resource allocation: {e}")
            return {"success": False, "error": str(e)}
    
    def update_roi(self, project_id: str, current_value: float, investment: Optional[float] = None) -> Dict[str, Any]:
        """
        Update ROI data for a project.
        
        Args:
            project_id: Project ID
            current_value: Current value of the investment
            investment: Optional initial investment amount
            
        Returns:
            Dict containing updated ROI data
        """
        if project_id not in self.projects:
            logger.warning(f"Project not found: {project_id}")
            return {"success": False, "error": "Project not found"}
        
        try:
            # Initialize ROI data if needed
            if project_id not in self.roi_data:
                self.roi_data[project_id] = {
                    "initial_investment": 0.0,
                    "current_value": 0.0,
                    "roi": 0.0,
                    "last_updated": datetime.now().isoformat(),
                    "history": []
                }
            
            roi_data = self.roi_data[project_id]
            
            # Update initial investment if provided
            if investment is not None:
                roi_data["initial_investment"] = investment
            
            # Update current value
            roi_data["current_value"] = current_value
            
            # Calculate ROI
            if roi_data["initial_investment"] > 0:
                roi_data["roi"] = (current_value - roi_data["initial_investment"]) / roi_data["initial_investment"]
            else:
                roi_data["roi"] = 0.0
            
            # Update timestamp
            roi_data["last_updated"] = datetime.now().isoformat()
            
            # Add to history
            history_entry = {
                "timestamp": datetime.now().isoformat(),
                "current_value": current_value,
                "roi": roi_data["roi"]
            }
            
            roi_data["history"].append(history_entry)
            
            # Save ROI data
            self._save_roi_data()
            
            logger.info(f"Updated ROI for project {project_id}: {roi_data['roi']:.2f}")
            
            return {
                "success": True,
                "project_id": project_id,
                "roi": roi_data["roi"],
                "current_value": current_value,
                "initial_investment": roi_data["initial_investment"]
            }
        except Exception as e:
            logger.error(f"Error updating ROI for project {project_id}: {e}")
            return {"success": False, "error": str(e)}
    
    def get_project_roi(self, project_id: str) -> float:
        """
        Get the current ROI for a project.
        
        Args:
            project_id: Project ID
            
        Returns:
            float: Current ROI value
        """
        if project_id not in self.roi_data:
            return 0.0
        
        return self.roi_data[project_id].get("roi", 0.0)
    
    def get_roi_history(self, project_id: str) -> List[Dict[str, Any]]:
        """
        Get ROI history for a project.
        
        Args:
            project_id: Project ID
            
        Returns:
            List of ROI history entries
        """
        if project_id not in self.roi_data:
            return []
        
        return self.roi_data[project_id].get("history", [])
    
    def get_profit_taking_recommendation(self, project_id: str) -> Dict[str, Any]:
        """
        Get profit taking recommendations for a project.
        
        Args:
            project_id: Project ID
            
        Returns:
            Dict containing profit taking recommendations
        """
        if project_id not in self.projects or project_id not in self.roi_data:
            logger.warning(f"Project not found: {project_id}")
            return {"success": False, "error": "Project not found"}
        
        try:
            project = self.projects[project_id]
            roi_data = self.roi_data[project_id]
            current_roi = roi_data.get("roi", 0.0)
            
            # Get thresholds from config
            thresholds = self.config["profit_taking_thresholds"]
            
            # Determine recommendation
            if current_roi <= 0:
                recommendation = {
                    "action": "hold",
                    "reason": "No profit to take yet",
                    "percentage": 0.0
                }
            elif current_roi >= thresholds["high"]:
                recommendation = {
                    "action": "take_profit",
                    "reason": f"ROI has reached high threshold ({thresholds['high']})",
                    "percentage": 0.75
                }
            elif current_roi >= thresholds["medium"]:
                recommendation = {
                    "action": "take_profit",
                    "reason": f"ROI has reached medium threshold ({thresholds['medium']})",
                    "percentage": 0.5
                }
            elif current_roi >= thresholds["low"]:
                recommendation = {
                    "action": "take_profit",
                    "reason": f"ROI has reached low threshold ({thresholds['low']})",
                    "percentage": 0.25
                }
            else:
                recommendation = {
                    "action": "hold",
                    "reason": "ROI below minimum threshold",
                    "percentage": 0.0
                }
            
            # Add project-specific context
            recommendation["project_id"] = project_id
            recommendation["project_name"] = project.get("name", "Unknown project")
            recommendation["current_roi"] = current_roi
            recommendation["current_value"] = roi_data.get("current_value", 0.0)
            recommendation["initial_investment"] = roi_data.get("initial_investment", 0.0)
            
            logger.info(f"Generated profit taking recommendation for {project_id}: {recommendation['action']}")
            
            return {
                "success": True,
                "recommendation": recommendation
            }
        except Exception as e:
            logger.error(f"Error generating profit taking recommendation for {project_id}: {e}")
            return {"success": False, "error": str(e)}
    
    def get_strategy(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a strategy by ID.
        
        Args:
            strategy_id: Strategy ID
            
        Returns:
            Dict containing strategy data or None if not found
        """
        return self.strategies.get(strategy_id)
    
    def list_strategies(self) -> List[Dict[str, Any]]:
        """
        List all available strategies.
        
        Returns:
            List of strategy data
        """
        return list(self.strategies.values())
    
    def create_strategy(self, strategy_data: Dict[str, Any]) -> Optional[str]:
        """
        Create a new optimization strategy.
        
        Args:
            strategy_data: Strategy data
            
        Returns:
            str: Strategy ID or None if failed
        """
        try:
            # Validate required fields
            required_fields = ["name", "description", "risk_tolerance"]
            for field in required_fields:
                if field not in strategy_data:
                    logger.error(f"Missing required field: {field}")
                    return None
            
            # Generate strategy ID if not provided
            if "id" not in strategy_data:
                strategy_id = f"strategy-{int(time.time())}-{random.randint(1000, 9999)}"
                strategy_data["id"] = strategy_id
            else:
                strategy_id = strategy_data["id"]
            
            # Add metadata
            strategy_data["created_at"] = datetime.now().isoformat()
            strategy_data["last_updated"] = datetime.now().isoformat()
            
            # Store strategy
            self.strategies[strategy_id] = strategy_data
            
            # Save strategies
            self._save_strategies()
            
            logger.info(f"Created strategy: {strategy_data['name']} (ID: {strategy_id})")
            return strategy_id
        except Exception as e:
            logger.error(f"Error creating strategy: {e}")
            return None
    
    def update_strategy(self, strategy_id: str, strategy_data: Dict[str, Any]) -> bool:
        """
        Update an existing strategy.
        
        Args:
            strategy_id: Strategy ID
            strategy_data: Updated strategy data
            
        Returns:
            bool: True if updated successfully, False otherwise
        """
        if strategy_id not in self.strategies:
            logger.error(f"Strategy not found: {strategy_id}")
            return False
        
        try:
            # Get current strategy data
            current_data = self.strategies[strategy_id]
            
            # Update fields
            for key, value in strategy_data.items():
                current_data[key] = value
            
            # Update metadata
            current_data["last_updated"] = datetime.now().isoformat()
            
            # Save strategies
            self._save_strategies()
            
            logger.info(f"Updated strategy: {current_data['name']} (ID: {strategy_id})")
            return True
        except Exception as e:
            logger.error(f"Error updating strategy: {e}")
            return False
    
    def delete_strategy(self, strategy_id: str) -> bool:
        """
        Delete a strategy.
        
        Args:
            strategy_id: Strategy ID
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        if strategy_id not in self.strategies:
            logger.error(f"Strategy not found: {strategy_id}")
            return False
        
        try:
            # Delete strategy
            del self.strategies[strategy_id]
            
            # Save strategies
            self._save_strategies()
            
            logger.info(f"Deleted strategy: {strategy_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting strategy: {e}")
            return False
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current portfolio.
        
        Returns:
            Dict containing portfolio summary
        """
        try:
            # Calculate portfolio metrics
            total_projects = len(self.projects)
            active_projects = len([p for p in self.projects.values() if p.get("status") == "approved"])
            
            total_investment = sum(data.get("initial_investment", 0) for data in self.roi_data.values())
            total_current_value = sum(data.get("current_value", 0) for data in self.roi_data.values())
            
            if total_investment > 0:
                portfolio_roi = (total_current_value - total_investment) / total_investment
            else:
                portfolio_roi = 0.0
            
            # Calculate project ROIs
            project_rois = []
            for project_id, project in self.projects.items():
                if project_id in self.roi_data:
                    roi_data = self.roi_data[project_id]
                    project_rois.append({
                        "project_id": project_id,
                        "name": project.get("name", "Unknown project"),
                        "roi": roi_data.get("roi", 0.0),
                        "current_value": roi_data.get("current_value", 0.0),
                        "initial_investment": roi_data.get("initial_investment", 0.0)
                    })
            
            # Sort by ROI (highest first)
            project_rois.sort(key=lambda x: x["roi"], reverse=True)
            
            return {
                "total_projects": total_projects,
                "active_projects": active_projects,
                "total_investment": total_investment,
                "total_current_value": total_current_value,
                "portfolio_roi": portfolio_roi,
                "best_performing_projects": project_rois[:3] if project_rois else [],
                "worst_performing_projects": project_rois[-3:] if project_rois else [],
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error generating portfolio summary: {e}")
            return {"error": str(e)}
    
    def status(self) -> Dict[str, Any]:
        """
        Get the current status of the Profit Optimization Agent.
        
        Returns:
            Dict containing status information
        """
        return {
            "active": self.active,
            "project_count": len(self.projects),
            "strategy_count": len(self.strategies),
            "approved_projects": len([p for p in self.projects.values() if p.get("status") == "approved"]),
            "rejected_projects": len([p for p in self.projects.values() if p.get("status") == "rejected"]),
            "pending_projects": len([p for p in self.projects.values() if p.get("status") == "pending_analysis"]),
            "average_roi": sum(data.get("roi", 0) for data in self.roi_data.values()) / len(self.roi_data) if self.roi_data else 0.0,
            "last_optimization": self.config.get("last_optimization_time")
        }


if __name__ == "__main__":
    # Simple test
    agent = ProfitOptimizationAgent()
    agent.start()
    
    # Add a test project
    project_data = {
        "name": "Test Project",
        "description": "A test project for profit optimization",
        "category": "defi",
        "estimated_airdrop_value": 1000,
        "estimated_capital_required": 100,
        "scores": {
            "team": 0.8,
            "tokenomics": 0.7,
            "community": 0.6,
            "risk": 0.4,
            "engagement_requirement": 0.5
        }
    }
    
    project_id = agent.add_project(project_data)
    
    if project_id:
        # Update ROI
        agent.update_roi(project_id, 200, 100)
        
        # Get profit taking recommendation
        recommendation = agent.get_profit_taking_recommendation(project_id)
        print(f"Profit taking recommendation: {recommendation}")
        
        # Optimize resource allocation
        optimization = agent.optimize_resource_allocation("balanced")
        print(f"Resource allocation: {optimization}")
    
    # Print portfolio summary
    summary = agent.get_portfolio_summary()
    print(f"Portfolio summary: {summary}")
    
    # Print status
    print(agent.status())
    agent.stop()