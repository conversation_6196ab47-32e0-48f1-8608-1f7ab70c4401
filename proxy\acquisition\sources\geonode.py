"""
Geonode 代理源

该模块实现了从 Geonode API 获取代理的功能。
"""

import logging
import requests
import time
import random
from typing import Dict, List, Any

from proxy.models.proxy import Proxy
from proxy.acquisition.sources.base_source import BaseProxySource


class GeonodeSource(BaseProxySource):
    """Geonode 代理源"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 Geonode 代理源
        
        Args:
            config: 配置字典，包含代理源的配置信息
        """
        super().__init__(config)
        self.api_url = config.get('api_url', 'https://proxylist.geonode.com/api/proxy-list')
        self.timeout = config.get('timeout', 10)
        self.api_key = config.get('api_key', '')  # 可选的 API 密钥
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
    
    def get_proxies(self, count: int) -> List[Proxy]:
        """
        从 Geonode 获取代理
        
        Args:
            count: 要获取的代理数量
            
        Returns:
            代理列表
        """
        self.logger.info(f"从 Geonode 获取 {count} 个代理")
        
        try:
            # 构建请求 URL
            url = f"{self.api_url}?limit={count}"
            
            # 如果有 API 密钥，添加到请求头
            headers = {
                'User-Agent': random.choice(self.user_agents)
            }
            
            if self.api_key:
                headers['Authorization'] = f"Bearer {self.api_key}"
            
            # 发送请求
            response = requests.get(url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析响应
            data = response.json()
            proxy_list = data.get('data', [])
            
            proxies = []
            for proxy_data in proxy_list:
                try:
                    host = proxy_data.get('ip')
                    port = int(proxy_data.get('port'))
                    protocol = proxy_data.get('protocols', ['http'])[0].lower()
                    country = proxy_data.get('country')
                    city = proxy_data.get('city')
                    anonymity = proxy_data.get('anonymityLevel', '').lower()
                    
                    # 映射匿名级别
                    if anonymity == 'elite' or anonymity == 'high anonymous':
                        anonymity_level = 'elite'
                    elif anonymity == 'anonymous':
                        anonymity_level = 'anonymous'
                    else:
                        anonymity_level = 'transparent'
                    
                    # 创建代理对象
                    proxy = Proxy(
                        host=host,
                        port=port,
                        protocol=protocol,
                        country=country,
                        city=city,
                        anonymity=anonymity_level,
                        source='geonode'
                    )
                    
                    proxies.append(proxy)
                
                except Exception as e:
                    self.logger.error(f"解析代理数据时出错: {str(e)}")
            
            self.logger.info(f"从 Geonode 获取到 {len(proxies)} 个代理")
            return proxies
        
        except Exception as e:
            self.logger.error(f"从 Geonode 获取代理时出错: {str(e)}")
            return []