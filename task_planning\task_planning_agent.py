"""
Task Planning Agent

This agent is responsible for creating and managing task sequences for different projects,
optimizing task scheduling based on priorities and dependencies.
"""

import logging
import os
import json
import time
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timedelta
import uuid

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("TaskPlanningAgent")

class TaskPlanningAgent:
    """
    Task Planning Agent for creating and managing task sequences.
    
    This agent handles:
    - Task creation and management
    - Task scheduling and prioritization
    - Dependency management
    - Resource allocation
    - Task optimization
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Task Planning Agent.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config = self._load_config(config_path)
        self.tasks = {}
        self.projects = {}
        self.task_templates = {}
        self.active = False
        self.scheduler_running = False
        logger.info("Task Planning Agent initialized")
    
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "data_storage_path": "data/task_planning",
            "max_concurrent_tasks": 10,
            "default_priority": 5,
            "priority_levels": 10,
            "scheduling_algorithm": "priority_first",
            "retry_failed_tasks": True,
            "max_retries": 3,
            "retry_delay_minutes": 15,
            "task_timeout_minutes": 60
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    # Merge user config with defaults
                    for key, value in user_config.items():
                        default_config[key] = value
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def start(self) -> bool:
        """
        Start the Task Planning Agent.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            # Initialize data storage
            os.makedirs(self.config["data_storage_path"], exist_ok=True)
            
            # Load tasks and projects
            self._load_tasks()
            self._load_projects()
            self._load_task_templates()
            
            self.active = True
            logger.info("Task Planning Agent started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start Task Planning Agent: {e}")
            return False
    
    def stop(self) -> bool:
        """
        Stop the Task Planning Agent.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        try:
            # Save current state
            self._save_tasks()
            self._save_projects()
            
            self.active = False
            self.scheduler_running = False
            logger.info("Task Planning Agent stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Error stopping Task Planning Agent: {e}")
            return False
    
    def _load_tasks(self) -> None:
        """Load tasks from storage."""
        tasks_file = os.path.join(self.config["data_storage_path"], "tasks.json")
        
        if os.path.exists(tasks_file):
            try:
                with open(tasks_file, 'r') as f:
                    self.tasks = json.load(f)
                logger.info(f"Loaded {len(self.tasks)} tasks")
            except Exception as e:
                logger.error(f"Error loading tasks: {e}")
                self.tasks = {}
    
    def _save_tasks(self) -> None:
        """Save tasks to storage."""
        tasks_file = os.path.join(self.config["data_storage_path"], "tasks.json")
        
        try:
            with open(tasks_file, 'w') as f:
                json.dump(self.tasks, f, indent=2)
            logger.info(f"Saved {len(self.tasks)} tasks")
        except Exception as e:
            logger.error(f"Error saving tasks: {e}")
    
    def _load_projects(self) -> None:
        """Load projects from storage."""
        projects_file = os.path.join(self.config["data_storage_path"], "projects.json")
        
        if os.path.exists(projects_file):
            try:
                with open(projects_file, 'r') as f:
                    self.projects = json.load(f)
                logger.info(f"Loaded {len(self.projects)} projects")
            except Exception as e:
                logger.error(f"Error loading projects: {e}")
                self.projects = {}
    
    def _save_projects(self) -> None:
        """Save projects to storage."""
        projects_file = os.path.join(self.config["data_storage_path"], "projects.json")
        
        try:
            with open(projects_file, 'w') as f:
                json.dump(self.projects, f, indent=2)
            logger.info(f"Saved {len(self.projects)} projects")
        except Exception as e:
            logger.error(f"Error saving projects: {e}")
    
    def _load_task_templates(self) -> None:
        """Load task templates from storage."""
        templates_file = os.path.join(self.config["data_storage_path"], "task_templates.json")
        
        if os.path.exists(templates_file):
            try:
                with open(templates_file, 'r') as f:
                    self.task_templates = json.load(f)
                logger.info(f"Loaded {len(self.task_templates)} task templates")
            except Exception as e:
                logger.error(f"Error loading task templates: {e}")
                self.task_templates = {}
        else:
            # Load default templates
            self._create_default_templates()
    
    def _create_default_templates(self) -> None:
        """Create default task templates."""
        default_templates = {
            "social_registration": {
                "name": "Social Media Registration",
                "description": "Register for social media accounts related to the project",
                "steps": [
                    {"name": "Twitter Registration", "agent": "task_execution", "params": {"platform": "twitter"}},
                    {"name": "Discord Registration", "agent": "task_execution", "params": {"platform": "discord"}},
                    {"name": "Telegram Registration", "agent": "task_execution", "params": {"platform": "telegram"}}
                ],
                "estimated_duration_minutes": 30,
                "priority": 8
            },
            "wallet_setup": {
                "name": "Wallet Setup",
                "description": "Set up wallet for the project",
                "steps": [
                    {"name": "Create Wallet", "agent": "fund_management", "params": {"chain": "ethereum"}},
                    {"name": "Fund Wallet", "agent": "fund_management", "params": {"amount": 0.05, "currency": "ETH"}}
                ],
                "estimated_duration_minutes": 15,
                "priority": 9
            },
            "project_monitoring": {
                "name": "Project Monitoring Setup",
                "description": "Set up monitoring for the project",
                "steps": [
                    {"name": "Add to Monitoring", "agent": "monitoring", "params": {"channels": ["twitter", "discord", "website"]}}
                ],
                "estimated_duration_minutes": 10,
                "priority": 7
            },
            "daily_engagement": {
                "name": "Daily Social Engagement",
                "description": "Perform daily social engagement tasks",
                "steps": [
                    {"name": "Twitter Engagement", "agent": "task_execution", "params": {"platform": "twitter", "action": "engage"}},
                    {"name": "Discord Engagement", "agent": "task_execution", "params": {"platform": "discord", "action": "engage"}}
                ],
                "estimated_duration_minutes": 20,
                "priority": 5,
                "recurring": True,
                "recurrence_hours": 24
            }
        }
        
        self.task_templates = default_templates
        
        # Save templates
        templates_file = os.path.join(self.config["data_storage_path"], "task_templates.json")
        try:
            with open(templates_file, 'w') as f:
                json.dump(self.task_templates, f, indent=2)
            logger.info(f"Created {len(self.task_templates)} default task templates")
        except Exception as e:
            logger.error(f"Error saving default task templates: {e}")
    
    def create_task(self, task_data: Dict[str, Any]) -> Optional[str]:
        """
        Create a new task.
        
        Args:
            task_data: Task information
            
        Returns:
            str: Task ID if created successfully, None otherwise
        """
        try:
            # Generate task ID
            task_id = str(uuid.uuid4())
            
            # Add metadata
            task_data["id"] = task_id
            task_data["created_at"] = datetime.now().isoformat()
            task_data["updated_at"] = task_data["created_at"]
            task_data["status"] = "pending"
            
            # Set default priority if not provided
            if "priority" not in task_data:
                task_data["priority"] = self.config["default_priority"]
            
            # Store task
            self.tasks[task_id] = task_data
            self._save_tasks()
            
            logger.info(f"Created task: {task_id} - {task_data.get('name', 'Unnamed task')}")
            return task_id
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            return None
    
    def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update a task.
        
        Args:
            task_id: Task ID
            updates: Data to update
            
        Returns:
            bool: True if updated successfully, False otherwise
        """
        try:
            if task_id not in self.tasks:
                logger.warning(f"Task not found: {task_id}")
                return False
            
            # Update task data
            task = self.tasks[task_id]
            for key, value in updates.items():
                task[key] = value
            
            # Update timestamp
            task["updated_at"] = datetime.now().isoformat()
            
            # Save tasks
            self._save_tasks()
            
            logger.info(f"Updated task: {task_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating task: {e}")
            return False
    
    def delete_task(self, task_id: str) -> bool:
        """
        Delete a task.
        
        Args:
            task_id: Task ID
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        try:
            if task_id not in self.tasks:
                logger.warning(f"Task not found: {task_id}")
                return False
            
            # Remove task
            del self.tasks[task_id]
            
            # Save tasks
            self._save_tasks()
            
            logger.info(f"Deleted task: {task_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting task: {e}")
            return False
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a task by ID.
        
        Args:
            task_id: Task ID
            
        Returns:
            Dict containing task data or None if not found
        """
        if task_id not in self.tasks:
            logger.warning(f"Task not found: {task_id}")
            return None
        
        return self.tasks[task_id]
    
    def list_tasks(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        List tasks with optional filtering.
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            List of task objects
        """
        result = []
        
        try:
            for task_id, task in self.tasks.items():
                # Apply filters if provided
                if filters:
                    match = True
                    for key, value in filters.items():
                        if key not in task or task[key] != value:
                            match = False
                            break
                    
                    if not match:
                        continue
                
                result.append(task)
            
            return result
        except Exception as e:
            logger.error(f"Error listing tasks: {e}")
            return []
    
    def create_project(self, project_data: Dict[str, Any]) -> Optional[str]:
        """
        Create a new project.
        
        Args:
            project_data: Project information
            
        Returns:
            str: Project ID if created successfully, None otherwise
        """
        try:
            # Generate project ID if not provided
            project_id = project_data.get("id", str(uuid.uuid4()))
            
            # Add metadata
            project_data["id"] = project_id
            project_data["created_at"] = datetime.now().isoformat()
            project_data["updated_at"] = project_data["created_at"]
            project_data["status"] = "active"
            
            # Initialize task lists if not present
            if "tasks" not in project_data:
                project_data["tasks"] = []
            
            # Store project
            self.projects[project_id] = project_data
            self._save_projects()
            
            logger.info(f"Created project: {project_id} - {project_data.get('name', 'Unnamed project')}")
            return project_id
        except Exception as e:
            logger.error(f"Error creating project: {e}")
            return None
    
    def update_project(self, project_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update a project.
        
        Args:
            project_id: Project ID
            updates: Data to update
            
        Returns:
            bool: True if updated successfully, False otherwise
        """
        try:
            if project_id not in self.projects:
                logger.warning(f"Project not found: {project_id}")
                return False
            
            # Update project data
            project = self.projects[project_id]
            for key, value in updates.items():
                project[key] = value
            
            # Update timestamp
            project["updated_at"] = datetime.now().isoformat()
            
            # Save projects
            self._save_projects()
            
            logger.info(f"Updated project: {project_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating project: {e}")
            return False
    
    def delete_project(self, project_id: str) -> bool:
        """
        Delete a project.
        
        Args:
            project_id: Project ID
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        try:
            if project_id not in self.projects:
                logger.warning(f"Project not found: {project_id}")
                return False
            
            # Remove project
            del self.projects[project_id]
            
            # Save projects
            self._save_projects()
            
            logger.info(f"Deleted project: {project_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting project: {e}")
            return False
    
    def get_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a project by ID.
        
        Args:
            project_id: Project ID
            
        Returns:
            Dict containing project data or None if not found
        """
        if project_id not in self.projects:
            logger.warning(f"Project not found: {project_id}")
            return None
        
        return self.projects[project_id]
    
    def list_projects(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        List projects with optional filtering.
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            List of project objects
        """
        result = []
        
        try:
            for project_id, project in self.projects.items():
                # Apply filters if provided
                if filters:
                    match = True
                    for key, value in filters.items():
                        if key not in project or project[key] != value:
                            match = False
                            break
                    
                    if not match:
                        continue
                
                result.append(project)
            
            return result
        except Exception as e:
            logger.error(f"Error listing projects: {e}")
            return []
    
    def create_task_from_template(self, template_id: str, project_id: str, params: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Create a task from a template.
        
        Args:
            template_id: Template ID
            project_id: Project ID
            params: Optional parameters to customize the task
            
        Returns:
            str: Task ID if created successfully, None otherwise
        """
        try:
            if template_id not in self.task_templates:
                logger.warning(f"Template not found: {template_id}")
                return None
            
            if project_id not in self.projects:
                logger.warning(f"Project not found: {project_id}")
                return None
            
            # Get template
            template = self.task_templates[template_id]
            
            # Create task data
            task_data = {
                "name": template["name"],
                "description": template["description"],
                "steps": template["steps"].copy(),
                "estimated_duration_minutes": template["estimated_duration_minutes"],
                "priority": template.get("priority", self.config["default_priority"]),
                "project_id": project_id,
                "template_id": template_id
            }
            
            # Add recurring settings if present in template
            if "recurring" in template and template["recurring"]:
                task_data["recurring"] = True
                task_data["recurrence_hours"] = template.get("recurrence_hours", 24)
            
            # Apply custom parameters if provided
            if params:
                for key, value in params.items():
                    if key == "steps":
                        # Merge steps with template steps
                        for i, step in enumerate(value):
                            if i < len(task_data["steps"]):
                                # Update existing step
                                for step_key, step_value in step.items():
                                    task_data["steps"][i][step_key] = step_value
                            else:
                                # Add new step
                                task_data["steps"].append(step)
                    else:
                        task_data[key] = value
            
            # Create the task
            task_id = self.create_task(task_data)
            
            if task_id:
                # Add task to project
                project = self.projects[project_id]
                project["tasks"].append(task_id)
                self._save_projects()
            
            return task_id
        except Exception as e:
            logger.error(f"Error creating task from template: {e}")
            return None
    
    def create_task_sequence(self, project_id: str, template_ids: List[str], params: Optional[Dict[str, Any]] = None) -> List[str]:
        """
        Create a sequence of tasks from templates.
        
        Args:
            project_id: Project ID
            template_ids: List of template IDs in sequence order
            params: Optional parameters to customize the tasks
            
        Returns:
            List of created task IDs
        """
        task_ids = []
        
        try:
            if project_id not in self.projects:
                logger.warning(f"Project not found: {project_id}")
                return []
            
            # Create tasks from templates
            for template_id in template_ids:
                # Get template-specific parameters if provided
                template_params = None
                if params and template_id in params:
                    template_params = params[template_id]
                
                # Create task
                task_id = self.create_task_from_template(template_id, project_id, template_params)
                if task_id:
                    task_ids.append(task_id)
            
            # Set up dependencies between tasks
            for i in range(1, len(task_ids)):
                prev_task_id = task_ids[i-1]
                curr_task_id = task_ids[i]
                
                # Add dependency
                curr_task = self.tasks[curr_task_id]
                if "dependencies" not in curr_task:
                    curr_task["dependencies"] = []
                curr_task["dependencies"].append(prev_task_id)
            
            # Save tasks
            self._save_tasks()
            
            logger.info(f"Created task sequence with {len(task_ids)} tasks for project {project_id}")
            return task_ids
        except Exception as e:
            logger.error(f"Error creating task sequence: {e}")
            return []
    
    def get_next_tasks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get the next tasks to execute based on priority and dependencies.
        
        Args:
            limit: Maximum number of tasks to return
            
        Returns:
            List of task objects
        """
        try:
            # Get all pending tasks
            pending_tasks = [task for task_id, task in self.tasks.items() if task["status"] == "pending"]
            
            # Filter out tasks with unmet dependencies
            ready_tasks = []
            for task in pending_tasks:
                dependencies_met = True
                
                if "dependencies" in task:
                    for dep_id in task["dependencies"]:
                        if dep_id in self.tasks and self.tasks[dep_id]["status"] != "completed":
                            dependencies_met = False
                            break
                
                if dependencies_met:
                    ready_tasks.append(task)
            
            # Sort by priority (higher first)
            sorted_tasks = sorted(ready_tasks, key=lambda x: x.get("priority", 0), reverse=True)
            
            # Return limited number of tasks
            return sorted_tasks[:limit]
        except Exception as e:
            logger.error(f"Error getting next tasks: {e}")
            return []
    
    def schedule_tasks(self) -> List[Dict[str, Any]]:
        """
        Schedule tasks for execution.
        
        Returns:
            List of scheduled task objects
        """
        try:
            # Get next tasks
            next_tasks = self.get_next_tasks(self.config["max_concurrent_tasks"])
            
            # Mark tasks as scheduled
            for task in next_tasks:
                task["status"] = "scheduled"
                task["scheduled_at"] = datetime.now().isoformat()
                self.tasks[task["id"]] = task
            
            # Save tasks
            if next_tasks:
                self._save_tasks()
            
            logger.info(f"Scheduled {len(next_tasks)} tasks for execution")
            return next_tasks
        except Exception as e:
            logger.error(f"Error scheduling tasks: {e}")
            return []
    
    def mark_task_completed(self, task_id: str, result: Optional[Dict[str, Any]] = None) -> bool:
        """
        Mark a task as completed.
        
        Args:
            task_id: Task ID
            result: Optional result data
            
        Returns:
            bool: True if marked successfully, False otherwise
        """
        try:
            if task_id not in self.tasks:
                logger.warning(f"Task not found: {task_id}")
                return False
            
            # Update task
            task = self.tasks[task_id]
            task["status"] = "completed"
            task["completed_at"] = datetime.now().isoformat()
            
            if result:
                task["result"] = result
            
            # Save tasks
            self._save_tasks()
            
            # Check if this is a recurring task
            if "recurring" in task and task["recurring"]:
                self._schedule_recurring_task(task)
            
            logger.info(f"Marked task as completed: {task_id}")
            return True
        except Exception as e:
            logger.error(f"Error marking task as completed: {e}")
            return False
    
    def mark_task_failed(self, task_id: str, error: Optional[str] = None) -> bool:
        """
        Mark a task as failed.
        
        Args:
            task_id: Task ID
            error: Optional error message
            
        Returns:
            bool: True if marked successfully, False otherwise
        """
        try:
            if task_id not in self.tasks:
                logger.warning(f"Task not found: {task_id}")
                return False
            
            # Update task
            task = self.tasks[task_id]
            
            # Check if we should retry
            retry = self.config["retry_failed_tasks"]
            retry_count = task.get("retry_count", 0)
            
            if retry and retry_count < self.config["max_retries"]:
                # Increment retry count
                task["retry_count"] = retry_count + 1
                task["status"] = "pending"
                task["retry_at"] = (datetime.now() + timedelta(minutes=self.config["retry_delay_minutes"])).isoformat()
                
                if error:
                    task["last_error"] = error
                
                logger.info(f"Scheduled task for retry: {task_id} (attempt {task['retry_count']})")
            else:
                # Mark as failed
                task["status"] = "failed"
                task["failed_at"] = datetime.now().isoformat()
                
                if error:
                    task["error"] = error
                
                logger.info(f"Marked task as failed: {task_id}")
            
            # Save tasks
            self._save_tasks()
            
            return True
        except Exception as e:
            logger.error(f"Error marking task as failed: {e}")
            return False
    
    def _schedule_recurring_task(self, task: Dict[str, Any]) -> Optional[str]:
        """
        Schedule a new instance of a recurring task.
        
        Args:
            task: Completed recurring task
            
        Returns:
            str: New task ID if scheduled successfully, None otherwise
        """
        try:
            # Create new task based on the completed one
            new_task = task.copy()
            
            # Remove fields that should not be copied
            for field in ["id", "created_at", "updated_at", "status", "completed_at", "scheduled_at", "result"]:
                if field in new_task:
                    del new_task[field]
            
            # Set next execution time
            recurrence_hours = new_task.get("recurrence_hours", 24)
            next_execution = datetime.now() + timedelta(hours=recurrence_hours)
            new_task["scheduled_for"] = next_execution.isoformat()
            
            # Create the new task
            new_task_id = self.create_task(new_task)
            
            if new_task_id:
                # Add to project if applicable
                if "project_id" in new_task:
                    project_id = new_task["project_id"]
                    if project_id in self.projects:
                        self.projects[project_id]["tasks"].append(new_task_id)
                        self._save_projects()
            
            logger.info(f"Scheduled recurring task: {new_task_id} (next execution: {next_execution})")
            return new_task_id
        except Exception as e:
            logger.error(f"Error scheduling recurring task: {e}")
            return None
    
    def run_scheduler(self, stop_event=None) -> None:
        """
        Run the task scheduler continuously.
        
        Args:
            stop_event: Optional event to signal stopping
        """
        if not self.active:
            logger.warning("Task Planning Agent is not active")
            return
        
        self.scheduler_running = True
        logger.info("Task scheduler started")
        
        try:
            while self.scheduler_running:
                if stop_event and stop_event.is_set():
                    logger.info("Stop event received, ending scheduler")
                    break
                
                # Schedule tasks
                scheduled_tasks = self.schedule_tasks()
                
                # Sleep for a bit
                time.sleep(10)
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received, ending scheduler")
        except Exception as e:
            logger.error(f"Error in task scheduler: {e}")
        finally:
            self.scheduler_running = False
            logger.info("Task scheduler stopped")
    
    def status(self) -> Dict[str, Any]:
        """
        Get the current status of the Task Planning Agent.
        
        Returns:
            Dict containing status information
        """
        pending_count = len([t for t in self.tasks.values() if t["status"] == "pending"])
        scheduled_count = len([t for t in self.tasks.values() if t["status"] == "scheduled"])
        completed_count = len([t for t in self.tasks.values() if t["status"] == "completed"])
        failed_count = len([t for t in self.tasks.values() if t["status"] == "failed"])
        
        return {
            "active": self.active,
            "scheduler_running": self.scheduler_running,
            "task_count": len(self.tasks),
            "project_count": len(self.projects),
            "template_count": len(self.task_templates),
            "task_status": {
                "pending": pending_count,
                "scheduled": scheduled_count,
                "completed": completed_count,
                "failed": failed_count
            },
            "max_concurrent_tasks": self.config["max_concurrent_tasks"]
        }


if __name__ == "__main__":
    # Simple test
    agent = TaskPlanningAgent()
    agent.start()
    
    # Create a test project
    test_project = {
        "name": "Test Project",
        "description": "A test project for the Task Planning Agent"
    }
    project_id = agent.create_project(test_project)
    
    # Create tasks from templates
    task_ids = agent.create_task_sequence(
        project_id,
        ["social_registration", "wallet_setup", "project_monitoring"]
    )
    
    # Print status
    print(agent.status())
    
    # Get next tasks
    next_tasks = agent.get_next_tasks()
    print(f"Next tasks: {[t['name'] for t in next_tasks]}")
    
    agent.stop()