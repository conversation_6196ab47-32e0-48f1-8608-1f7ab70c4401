#!/usr/bin/env python3
"""
协调控制智能体单元测试

测试协调控制智能体的核心功能
"""

import unittest
import asyncio
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from coordinator.coordinator_agent import CoordinatorAgent


class TestCoordinatorAgent(unittest.TestCase):
    """协调控制智能体测试类"""

    def setUp(self):
        """测试前准备"""
        self.config = {
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
            'resources': {
                'max_memory': 1024,
                'max_cpu': 80
            }
        }
        
        # 模拟依赖组件
        with patch.multiple(
            'coordinator.coordinator_agent',
            AgentRegistry=MagicMock(),
            LifecycleManager=MagicMock(),
            SystemState=MagicMock(),
            WorkflowManager=MagicMock(),
            ResourceAllocator=MagicMock(),
            MessageBroker=MagicMock(),
            EventSystem=MagicMock(),
            HealthMonitor=MagicMock(),
            ErrorHandler=MagicMock(),
            LoggingService=MagicMock()
        ):
            self.agent = CoordinatorAgent(self.config)

    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.agent.agent_name, "coordinator")
        self.assertEqual(self.agent.agent_type, "coordinator")
        self.assertEqual(self.agent.version, "1.0.0")
        self.assertEqual(self.agent.status, "initialized")

    def test_register_agent(self):
        """测试注册智能体"""
        mock_agent = Mock()
        mock_agent.name = "test_agent"
        
        # 模拟注册成功
        self.agent.agent_registry.register.return_value = True
        
        result = self.agent.register_agent("test_agent", mock_agent)
        
        self.assertTrue(result)
        self.agent.agent_registry.register.assert_called_once_with("test_agent", mock_agent)

    def test_register_agent_failure(self):
        """测试注册智能体失败"""
        mock_agent = Mock()
        
        # 模拟注册失败
        self.agent.agent_registry.register.side_effect = Exception("Registration failed")
        
        result = self.agent.register_agent("test_agent", mock_agent)
        
        self.assertFalse(result)

    def test_unregister_agent(self):
        """测试注销智能体"""
        # 模拟注销成功
        self.agent.agent_registry.unregister.return_value = True
        
        result = self.agent.unregister_agent("test_agent")
        
        self.assertTrue(result)
        self.agent.agent_registry.unregister.assert_called_once_with("test_agent")

    def test_get_agent(self):
        """测试获取智能体"""
        mock_agent = Mock()
        self.agent.agent_registry.get.return_value = mock_agent
        
        result = self.agent.get_agent("test_agent")
        
        self.assertEqual(result, mock_agent)
        self.agent.agent_registry.get.assert_called_once_with("test_agent")

    def test_get_all_agents(self):
        """测试获取所有智能体"""
        mock_agents = {"agent1": Mock(), "agent2": Mock()}
        self.agent.agent_registry.get_all.return_value = mock_agents
        
        result = self.agent.get_all_agents()
        
        self.assertEqual(result, mock_agents)

    def test_get_system_status(self):
        """测试获取系统状态"""
        # 模拟系统状态
        mock_agents = {
            "agent1": Mock(status="running", agent_type="discovery"),
            "agent2": Mock(status="stopped", agent_type="assessment")
        }
        self.agent.agent_registry.get_all.return_value = mock_agents
        self.agent.system_state.get_status.return_value = {"status": "healthy"}
        
        status = self.agent.get_system_status()
        
        self.assertIn("coordinator", status)
        self.assertIn("agents", status)
        self.assertIn("system", status)
        self.assertEqual(status["coordinator"]["name"], "coordinator")
        self.assertEqual(len(status["agents"]), 2)

    def test_get_agent_info(self):
        """测试获取智能体信息"""
        # 模拟注册的智能体
        self.agent.agent_registry.get_names.return_value = ["agent1", "agent2"]
        
        info = self.agent.get_agent_info()
        
        self.assertEqual(info["name"], "coordinator")
        self.assertEqual(info["type"], "coordinator")
        self.assertEqual(info["version"], "1.0.0")
        self.assertIn("capabilities", info)
        self.assertIn("registered_agents", info)
        self.assertEqual(len(info["capabilities"]), 6)

    @patch('asyncio.create_task')
    async def test_start(self, mock_create_task):
        """测试启动智能体"""
        # 模拟启动成功
        self.agent.message_broker.start = Mock()
        self.agent.event_system.start = Mock()
        self.agent.health_monitor.start = Mock()
        
        await self.agent.start()
        
        self.assertEqual(self.agent.status, "running")
        self.agent.message_broker.start.assert_called_once()
        self.agent.event_system.start.assert_called_once()
        self.agent.health_monitor.start.assert_called_once()

    async def test_start_failure(self):
        """测试启动失败"""
        # 模拟启动失败
        self.agent.message_broker.start = Mock(side_effect=Exception("Start failed"))
        
        with self.assertRaises(Exception):
            await self.agent.start()
        
        self.assertEqual(self.agent.status, "error")

    async def test_stop(self):
        """测试停止智能体"""
        # 模拟停止成功
        self.agent.health_monitor.stop = Mock()
        self.agent.event_system.stop = Mock()
        self.agent.message_broker.stop = Mock()
        
        await self.agent.stop()
        
        self.assertEqual(self.agent.status, "stopped")
        self.agent.health_monitor.stop.assert_called_once()
        self.agent.event_system.stop.assert_called_once()
        self.agent.message_broker.stop.assert_called_once()

    async def test_execute_workflow(self):
        """测试执行工作流"""
        # 模拟工作流执行
        expected_result = {"status": "success", "data": "test_data"}
        self.agent.workflow_manager.execute_async = Mock(return_value=expected_result)
        
        result = await self.agent.execute_workflow("test_workflow", {"param1": "value1"})
        
        self.assertEqual(result, expected_result)
        self.agent.workflow_manager.execute_async.assert_called_once_with(
            "test_workflow", {"param1": "value1"}
        )

    async def test_execute_workflow_failure(self):
        """测试工作流执行失败"""
        # 模拟工作流执行失败
        self.agent.workflow_manager.execute_async = Mock(
            side_effect=Exception("Workflow failed")
        )
        
        with self.assertRaises(Exception):
            await self.agent.execute_workflow("test_workflow")


class TestCoordinatorAgentIntegration(unittest.TestCase):
    """协调控制智能体集成测试"""

    def setUp(self):
        """测试前准备"""
        self.config = {
            'logging': {'level': 'ERROR'},  # 减少日志输出
            'resources': {'max_memory': 512}
        }

    @patch('coordinator.coordinator_agent.AgentRegistry')
    @patch('coordinator.coordinator_agent.LoggingService')
    def test_full_lifecycle(self, mock_logging_service, mock_agent_registry):
        """测试完整生命周期"""
        # 创建智能体
        agent = CoordinatorAgent(self.config)
        
        # 验证初始化
        self.assertEqual(agent.status, "initialized")
        
        # 验证组件创建
        mock_agent_registry.assert_called_once()
        mock_logging_service.assert_called_once()


def run_async_test(coro):
    """运行异步测试的辅助函数"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


if __name__ == '__main__':
    # 运行异步测试
    suite = unittest.TestSuite()
    
    # 添加同步测试
    suite.addTest(unittest.makeSuite(TestCoordinatorAgent))
    suite.addTest(unittest.makeSuite(TestCoordinatorAgentIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print(f"\n✅ 所有测试通过! 运行了 {result.testsRun} 个测试")
    else:
        print(f"\n❌ 测试失败! {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        
    exit(0 if result.wasSuccessful() else 1)
