"""
Website Monitor

This module is responsible for monitoring project websites for changes and updates.
"""

import os
import json
import logging
import time
import hashlib
import requests
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import difflib

logger = logging.getLogger("WebsiteMonitor")

class WebsiteMonitor:
    """
    Monitors websites for changes and updates.
    
    Features:
    - Content change detection
    - Announcement tracking
    - Blog post monitoring
    - Roadmap updates
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the website monitor.
        
        Args:
            config_path: Path to configuration file
        """
        self.config = self._load_config(config_path)
        self.page_cache = {}
        self.last_check_times = {}
        
        # Ensure cache directory exists
        os.makedirs(self.config["cache_dir"], exist_ok=True)
        
        # Load cached pages if available
        self._load_page_cache()
        
        logger.info("Website monitor initialized")
    
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "cache_dir": "data/monitoring/website_cache",
            "max_cache_age_days": 30,
            "check_interval_minutes": 60,
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "timeout_seconds": 30,
            "max_retries": 3,
            "retry_delay_seconds": 5,
            "important_sections": ["announcement", "news", "blog", "roadmap", "airdrop"],
            "change_threshold": 0.1  # 10% content change threshold
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    # Merge user config with defaults
                    for key, value in user_config.items():
                        default_config[key] = value
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def _load_page_cache(self) -> None:
        """Load cached pages from disk."""
        cache_file = os.path.join(self.config["cache_dir"], "page_cache.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    self.page_cache = json.load(f)
                logger.info(f"Loaded {len(self.page_cache)} cached pages")
            except Exception as e:
                logger.error(f"Error loading page cache: {e}")
                self.page_cache = {}
    
    def _save_page_cache(self) -> None:
        """Save cached pages to disk."""
        cache_file = os.path.join(self.config["cache_dir"], "page_cache.json")
        
        try:
            with open(cache_file, 'w') as f:
                json.dump(self.page_cache, f)
            logger.info(f"Saved {len(self.page_cache)} pages to cache")
        except Exception as e:
            logger.error(f"Error saving page cache: {e}")
    
    def _clean_old_cache(self) -> None:
        """Remove old entries from the page cache."""
        try:
            current_time = datetime.now()
            max_age = timedelta(days=self.config["max_cache_age_days"])
            
            urls_to_clean = []
            
            for url, page_data in self.page_cache.items():
                last_check = datetime.fromisoformat(page_data["last_check"])
                if current_time - last_check > max_age:
                    urls_to_clean.append(url)
            
            for url in urls_to_clean:
                del self.page_cache[url]
            
            logger.info(f"Cleaned page cache, removed {len(urls_to_clean)} URLs")
        except Exception as e:
            logger.error(f"Error cleaning page cache: {e}")
    
    def _fetch_page(self, url: str) -> Optional[str]:
        """
        Fetch a webpage.
        
        Args:
            url: URL to fetch
            
        Returns:
            str: Page content or None if failed
        """
        headers = {
            "User-Agent": self.config["user_agent"],
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0"
        }
        
        for attempt in range(self.config["max_retries"]):
            try:
                response = requests.get(
                    url,
                    headers=headers,
                    timeout=self.config["timeout_seconds"]
                )
                response.raise_for_status()
                return response.text
            except requests.RequestException as e:
                logger.warning(f"Attempt {attempt+1}/{self.config['max_retries']} failed for {url}: {e}")
                if attempt < self.config["max_retries"] - 1:
                    time.sleep(self.config["retry_delay_seconds"])
        
        logger.error(f"Failed to fetch page after {self.config['max_retries']} attempts: {url}")
        return None
    
    def _compute_hash(self, content: str) -> str:
        """
        Compute a hash of the content.
        
        Args:
            content: Content to hash
            
        Returns:
            str: Content hash
        """
        return hashlib.sha256(content.encode()).hexdigest()
    
    def _extract_text(self, html_content: str) -> str:
        """
        Extract readable text from HTML.
        
        Args:
            html_content: HTML content
            
        Returns:
            str: Extracted text
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.extract()
            
            # Get text
            text = soup.get_text()
            
            # Break into lines and remove leading and trailing space
            lines = (line.strip() for line in text.splitlines())
            # Break multi-headlines into a line each
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            # Remove blank lines
            text = '\n'.join(chunk for chunk in chunks if chunk)
            
            return text
        except Exception as e:
            logger.error(f"Error extracting text from HTML: {e}")
            return ""
    
    def _extract_important_sections(self, html_content: str) -> Dict[str, str]:
        """
        Extract important sections from HTML.
        
        Args:
            html_content: HTML content
            
        Returns:
            Dict mapping section names to content
        """
        sections = {}
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for sections with important keywords in their IDs or classes
            for keyword in self.config["important_sections"]:
                # Check elements with matching IDs
                elements = soup.find_all(id=lambda x: x and keyword.lower() in x.lower())
                for element in elements:
                    section_name = f"{keyword}_{len(sections)}"
                    sections[section_name] = element.get_text().strip()
                
                # Check elements with matching classes
                elements = soup.find_all(class_=lambda x: x and keyword.lower() in x.lower())
                for element in elements:
                    section_name = f"{keyword}_{len(sections)}"
                    sections[section_name] = element.get_text().strip()
            
            return sections
        except Exception as e:
            logger.error(f"Error extracting important sections: {e}")
            return {}
    
    def _compare_content(self, old_content: str, new_content: str) -> float:
        """
        Compare old and new content to determine change percentage.
        
        Args:
            old_content: Old content
            new_content: New content
            
        Returns:
            float: Change percentage (0.0 to 1.0)
        """
        if not old_content or not new_content:
            return 1.0 if new_content else 0.0
        
        # Use difflib to compute similarity
        matcher = difflib.SequenceMatcher(None, old_content, new_content)
        similarity = matcher.ratio()
        
        # Convert similarity to change percentage
        change_percentage = 1.0 - similarity
        
        return change_percentage
    
    def check_website(self, url: str, project_id: str) -> List[Dict[str, Any]]:
        """
        Check a website for changes.
        
        Args:
            url: Website URL
            project_id: Project identifier
            
        Returns:
            List of update objects
        """
        updates = []
        
        try:
            logger.info(f"Checking website: {url}")
            
            # Fetch the page
            html_content = self._fetch_page(url)
            if not html_content:
                return []
            
            # Extract text and compute hash
            text_content = self._extract_text(html_content)
            content_hash = self._compute_hash(text_content)
            
            # Extract important sections
            sections = self._extract_important_sections(html_content)
            
            # Check if we have a cached version
            is_new = False
            if url not in self.page_cache:
                is_new = True
                self.page_cache[url] = {
                    "hash": content_hash,
                    "text": text_content,
                    "sections": sections,
                    "last_check": datetime.now().isoformat()
                }
                logger.info(f"New website added to monitoring: {url}")
            else:
                # Compare with cached version
                cached = self.page_cache[url]
                old_hash = cached["hash"]
                old_text = cached["text"]
                old_sections = cached.get("sections", {})
                
                # Check if content has changed
                if old_hash != content_hash:
                    # Compute change percentage
                    change_percentage = self._compare_content(old_text, text_content)
                    
                    # Only report if change is significant
                    if change_percentage >= self.config["change_threshold"]:
                        logger.info(f"Website content changed ({change_percentage:.1%}): {url}")
                        
                        # Create update for overall content change
                        update = {
                            "project_id": project_id,
                            "source": "website",
                            "source_id": url,
                            "type": "content_change",
                            "title": f"Website content updated ({change_percentage:.1%} change)",
                            "content": f"The website at {url} has been updated.",
                            "url": url,
                            "timestamp": datetime.now().isoformat(),
                            "metadata": {
                                "change_percentage": change_percentage,
                                "previous_check": cached["last_check"]
                            }
                        }
                        updates.append(update)
                        
                        # Check for changes in important sections
                        for section_name, section_content in sections.items():
                            if section_name in old_sections:
                                section_change = self._compare_content(old_sections[section_name], section_content)
                                if section_change >= self.config["change_threshold"]:
                                    # Create update for section change
                                    section_update = {
                                        "project_id": project_id,
                                        "source": "website",
                                        "source_id": f"{url}#{section_name}",
                                        "type": "section_change",
                                        "title": f"Website section '{section_name}' updated ({section_change:.1%} change)",
                                        "content": section_content[:500] + ("..." if len(section_content) > 500 else ""),
                                        "url": url,
                                        "timestamp": datetime.now().isoformat(),
                                        "metadata": {
                                            "section": section_name,
                                            "change_percentage": section_change,
                                            "previous_check": cached["last_check"]
                                        }
                                    }
                                    updates.append(section_update)
                            else:
                                # New section
                                section_update = {
                                    "project_id": project_id,
                                    "source": "website",
                                    "source_id": f"{url}#{section_name}",
                                    "type": "new_section",
                                    "title": f"New website section '{section_name}' detected",
                                    "content": section_content[:500] + ("..." if len(section_content) > 500 else ""),
                                    "url": url,
                                    "timestamp": datetime.now().isoformat(),
                                    "metadata": {
                                        "section": section_name
                                    }
                                }
                                updates.append(section_update)
                    
                    # Update cache
                    self.page_cache[url] = {
                        "hash": content_hash,
                        "text": text_content,
                        "sections": sections,
                        "last_check": datetime.now().isoformat()
                    }
                else:
                    logger.info(f"No changes detected for website: {url}")
                    # Update last check time
                    self.page_cache[url]["last_check"] = datetime.now().isoformat()
            
            # Save updated cache
            self._save_page_cache()
            
            # Update last check time
            self.last_check_times[url] = datetime.now().isoformat()
            
            return updates
        except Exception as e:
            logger.error(f"Error checking website {url}: {e}")
            return []
    
    def check_project(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check all websites for a project.
        
        Args:
            project: Project data
            
        Returns:
            List of update objects
        """
        updates = []
        project_id = project["id"]
        
        # Check main website if available
        if "website" in project and project["website"]:
            website_updates = self.check_website(project["website"], project_id)
            updates.extend(website_updates)
        
        # Check additional websites if available
        if "additional_websites" in project and project["additional_websites"]:
            for url in project["additional_websites"]:
                website_updates = self.check_website(url, project_id)
                updates.extend(website_updates)
        
        # Clean old cache periodically
        if len(self.page_cache) > 100:
            self._clean_old_cache()
        
        return updates


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    
    # Initialize monitor
    monitor = WebsiteMonitor()
    
    # Test project
    test_project = {
        "id": "test-project-123",
        "name": "Test Project",
        "website": "https://ethereum.org",
        "additional_websites": ["https://ethereum.org/en/developers/"]
    }
    
    # Check for updates
    updates = monitor.check_project(test_project)
    
    print(f"Found {len(updates)} updates")
    for update in updates:
        print(f"- {update['title']}")
        print(f"  {update['content'][:100]}...")
        print()