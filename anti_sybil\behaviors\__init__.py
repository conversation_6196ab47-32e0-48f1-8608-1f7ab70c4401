"""
Behavior Simulation

行为模拟模块，负责设计和模拟人类行为模式。
"""

from .behavior_designer import BehaviorDesigner
from .pattern_generator import PatternGenerator
from .timing_controller import TimingController
from .session_manager import SessionManager
from .browsing_pattern import BrowsingPattern
from .interaction_style import InteractionStyle
from .habit_simulator import HabitSimulator

__all__ = [
    "BehaviorDesigner",
    "PatternGenerator",
    "TimingController",
    "SessionManager",
    "BrowsingPattern",
    "InteractionStyle",
    "HabitSimulator"
]
