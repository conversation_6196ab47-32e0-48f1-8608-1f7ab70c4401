"""
Notification Service

Provides notification management for system alerts, user notifications,
and external integrations with support for multiple channels and priorities.
"""

import logging
import smtplib
import json
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import requests
import threading
from collections import deque


class NotificationType(Enum):
    """Notification types."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    SUCCESS = "success"


class NotificationChannel(Enum):
    """Notification channels."""
    EMAIL = "email"
    WEBHOOK = "webhook"
    DISCORD = "discord"
    TELEGRAM = "telegram"
    SLACK = "slack"
    CONSOLE = "console"
    FILE = "file"


@dataclass
class Notification:
    """Notification data structure."""
    id: str
    type: NotificationType
    title: str
    message: str
    source: str
    channels: List[NotificationChannel]
    timestamp: datetime = None
    data: Dict[str, Any] = None
    sent_channels: Set[NotificationChannel] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.data is None:
            self.data = {}
        if self.sent_channels is None:
            self.sent_channels = set()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert notification to dictionary."""
        data = asdict(self)
        data['type'] = self.type.value
        data['channels'] = [c.value for c in self.channels]
        data['sent_channels'] = [c.value for c in self.sent_channels]
        data['timestamp'] = self.timestamp.isoformat()
        return data


class NotificationService:
    """
    Notification service for system alerts and user notifications.
    
    Supports multiple notification channels including email, webhooks,
    Discord, Telegram, and more with retry logic and rate limiting.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize notification service.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Notification queue
        self._notification_queue = deque()
        
        # Channel configurations
        self._channel_configs = config.get('channels', {})
        
        # Rate limiting
        self._rate_limits = config.get('rate_limits', {})
        self._rate_counters = {}
        
        # Statistics
        self._stats = {
            'notifications_sent': 0,
            'notifications_failed': 0,
            'notifications_queued': 0,
            'channel_stats': {}
        }
        
        # Threading
        self._lock = threading.RLock()
        self._running = False
        self._worker_thread = None
        
        # Initialize channel stats
        for channel in NotificationChannel:
            self._stats['channel_stats'][channel.value] = {
                'sent': 0,
                'failed': 0
            }
    
    def start(self):
        """Start the notification service."""
        with self._lock:
            if self._running:
                return
            
            self._running = True
            self._worker_thread = threading.Thread(
                target=self._notification_worker,
                name="NotificationService",
                daemon=True
            )
            self._worker_thread.start()
            
            self.logger.info("Notification service started")
    
    def stop(self):
        """Stop the notification service."""
        with self._lock:
            if not self._running:
                return
            
            self._running = False
            
            if self._worker_thread:
                self._worker_thread.join(timeout=5.0)
            
            self.logger.info("Notification service stopped")
    
    def send_notification(self, notification: Notification) -> bool:
        """
        Send a notification.
        
        Args:
            notification: Notification to send
            
        Returns:
            bool: True if notification was queued successfully
        """
        try:
            with self._lock:
                self._notification_queue.append(notification)
                self._stats['notifications_queued'] += 1
            
            self.logger.debug(f"Queued notification {notification.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error queueing notification: {e}")
            return False
    
    def send_info(self, title: str, message: str, source: str,
                 channels: List[NotificationChannel] = None) -> str:
        """Send info notification."""
        return self._send_simple_notification(
            NotificationType.INFO, title, message, source, channels
        )
    
    def send_warning(self, title: str, message: str, source: str,
                    channels: List[NotificationChannel] = None) -> str:
        """Send warning notification."""
        return self._send_simple_notification(
            NotificationType.WARNING, title, message, source, channels
        )
    
    def send_error(self, title: str, message: str, source: str,
                  channels: List[NotificationChannel] = None) -> str:
        """Send error notification."""
        return self._send_simple_notification(
            NotificationType.ERROR, title, message, source, channels
        )
    
    def send_critical(self, title: str, message: str, source: str,
                     channels: List[NotificationChannel] = None) -> str:
        """Send critical notification."""
        return self._send_simple_notification(
            NotificationType.CRITICAL, title, message, source, channels
        )
    
    def send_success(self, title: str, message: str, source: str,
                    channels: List[NotificationChannel] = None) -> str:
        """Send success notification."""
        return self._send_simple_notification(
            NotificationType.SUCCESS, title, message, source, channels
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get notification statistics."""
        with self._lock:
            stats = self._stats.copy()
            stats['queue_size'] = len(self._notification_queue)
            stats['running'] = self._running
            return stats
    
    def _send_simple_notification(self, notification_type: NotificationType,
                                 title: str, message: str, source: str,
                                 channels: List[NotificationChannel] = None) -> str:
        """Send a simple notification."""
        import uuid
        
        if channels is None:
            channels = [NotificationChannel.CONSOLE]
        
        notification = Notification(
            id=str(uuid.uuid4()),
            type=notification_type,
            title=title,
            message=message,
            source=source,
            channels=channels
        )
        
        self.send_notification(notification)
        return notification.id
    
    def _notification_worker(self):
        """Background notification worker."""
        self.logger.info("Notification worker started")
        
        while self._running:
            try:
                with self._lock:
                    if self._notification_queue:
                        notification = self._notification_queue.popleft()
                    else:
                        notification = None
                
                if notification:
                    self._process_notification(notification)
                else:
                    threading.Event().wait(1.0)
                    
            except Exception as e:
                self.logger.error(f"Error in notification worker: {e}")
                threading.Event().wait(5.0)
        
        self.logger.info("Notification worker stopped")
    
    def _process_notification(self, notification: Notification):
        """Process a notification by sending to all channels."""
        try:
            success_count = 0
            
            for channel in notification.channels:
                if channel in notification.sent_channels:
                    continue
                
                # Check rate limits
                if self._is_rate_limited(channel):
                    self.logger.warning(f"Rate limited for channel {channel.value}")
                    continue
                
                # Send to channel
                if self._send_to_channel(notification, channel):
                    notification.sent_channels.add(channel)
                    success_count += 1
                    
                    with self._lock:
                        self._stats['channel_stats'][channel.value]['sent'] += 1
                else:
                    with self._lock:
                        self._stats['channel_stats'][channel.value]['failed'] += 1
            
            # Update statistics
            with self._lock:
                if success_count > 0:
                    self._stats['notifications_sent'] += 1
                else:
                    self._stats['notifications_failed'] += 1
            
            # Retry if needed
            if len(notification.sent_channels) < len(notification.channels):
                notification.retry_count += 1
                if notification.retry_count <= notification.max_retries:
                    # Re-queue for retry
                    with self._lock:
                        self._notification_queue.append(notification)
                    self.logger.debug(f"Re-queued notification {notification.id} for retry")
                else:
                    self.logger.warning(f"Max retries exceeded for notification {notification.id}")
            
        except Exception as e:
            self.logger.error(f"Error processing notification: {e}")
    
    def _send_to_channel(self, notification: Notification, channel: NotificationChannel) -> bool:
        """Send notification to specific channel."""
        try:
            if channel == NotificationChannel.CONSOLE:
                return self._send_console(notification)
            elif channel == NotificationChannel.EMAIL:
                return self._send_email(notification)
            elif channel == NotificationChannel.WEBHOOK:
                return self._send_webhook(notification)
            elif channel == NotificationChannel.DISCORD:
                return self._send_discord(notification)
            elif channel == NotificationChannel.TELEGRAM:
                return self._send_telegram(notification)
            elif channel == NotificationChannel.SLACK:
                return self._send_slack(notification)
            elif channel == NotificationChannel.FILE:
                return self._send_file(notification)
            else:
                self.logger.warning(f"Unknown notification channel: {channel}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending to channel {channel.value}: {e}")
            return False
    
    def _send_console(self, notification: Notification) -> bool:
        """Send notification to console."""
        try:
            timestamp = notification.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            level_map = {
                NotificationType.INFO: "INFO",
                NotificationType.WARNING: "WARNING",
                NotificationType.ERROR: "ERROR",
                NotificationType.CRITICAL: "CRITICAL",
                NotificationType.SUCCESS: "SUCCESS"
            }
            
            level = level_map.get(notification.type, "INFO")
            print(f"[{timestamp}] [{level}] [{notification.source}] {notification.title}: {notification.message}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending console notification: {e}")
            return False
    
    def _send_email(self, notification: Notification) -> bool:
        """Send notification via email."""
        try:
            email_config = self._channel_configs.get('email', {})
            if not email_config:
                self.logger.warning("Email configuration not found")
                return False
            
            smtp_server = email_config.get('smtp_server')
            smtp_port = email_config.get('smtp_port', 587)
            username = email_config.get('username')
            password = email_config.get('password')
            from_email = email_config.get('from_email')
            to_emails = email_config.get('to_emails', [])
            
            if not all([smtp_server, username, password, from_email, to_emails]):
                self.logger.warning("Incomplete email configuration")
                return False
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = from_email
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = f"[{notification.type.value.upper()}] {notification.title}"
            
            body = f"""
Source: {notification.source}
Time: {notification.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
Type: {notification.type.value.upper()}

{notification.message}
"""
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(username, password)
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending email notification: {e}")
            return False
    
    def _send_webhook(self, notification: Notification) -> bool:
        """Send notification via webhook."""
        try:
            webhook_config = self._channel_configs.get('webhook', {})
            if not webhook_config:
                return False
            
            url = webhook_config.get('url')
            if not url:
                return False
            
            payload = {
                'id': notification.id,
                'type': notification.type.value,
                'title': notification.title,
                'message': notification.message,
                'source': notification.source,
                'timestamp': notification.timestamp.isoformat(),
                'data': notification.data
            }
            
            headers = webhook_config.get('headers', {})
            timeout = webhook_config.get('timeout', 10)
            
            response = requests.post(url, json=payload, headers=headers, timeout=timeout)
            response.raise_for_status()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending webhook notification: {e}")
            return False
    
    def _send_discord(self, notification: Notification) -> bool:
        """Send notification to Discord."""
        try:
            discord_config = self._channel_configs.get('discord', {})
            webhook_url = discord_config.get('webhook_url')
            
            if not webhook_url:
                return False
            
            # Discord color mapping
            color_map = {
                NotificationType.INFO: 0x3498db,      # Blue
                NotificationType.WARNING: 0xf39c12,   # Orange
                NotificationType.ERROR: 0xe74c3c,     # Red
                NotificationType.CRITICAL: 0x8e44ad,  # Purple
                NotificationType.SUCCESS: 0x2ecc71    # Green
            }
            
            embed = {
                'title': notification.title,
                'description': notification.message,
                'color': color_map.get(notification.type, 0x95a5a6),
                'timestamp': notification.timestamp.isoformat(),
                'fields': [
                    {'name': 'Source', 'value': notification.source, 'inline': True},
                    {'name': 'Type', 'value': notification.type.value.upper(), 'inline': True}
                ]
            }
            
            payload = {'embeds': [embed]}
            
            response = requests.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending Discord notification: {e}")
            return False
    
    def _send_telegram(self, notification: Notification) -> bool:
        """Send notification to Telegram."""
        try:
            telegram_config = self._channel_configs.get('telegram', {})
            bot_token = telegram_config.get('bot_token')
            chat_id = telegram_config.get('chat_id')
            
            if not bot_token or not chat_id:
                return False
            
            text = f"*{notification.title}*\n\n{notification.message}\n\n_Source: {notification.source}_\n_Type: {notification.type.value.upper()}_"
            
            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            payload = {
                'chat_id': chat_id,
                'text': text,
                'parse_mode': 'Markdown'
            }
            
            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending Telegram notification: {e}")
            return False
    
    def _send_slack(self, notification: Notification) -> bool:
        """Send notification to Slack."""
        try:
            slack_config = self._channel_configs.get('slack', {})
            webhook_url = slack_config.get('webhook_url')
            
            if not webhook_url:
                return False
            
            # Slack color mapping
            color_map = {
                NotificationType.INFO: 'good',
                NotificationType.WARNING: 'warning',
                NotificationType.ERROR: 'danger',
                NotificationType.CRITICAL: 'danger',
                NotificationType.SUCCESS: 'good'
            }
            
            attachment = {
                'color': color_map.get(notification.type, 'good'),
                'title': notification.title,
                'text': notification.message,
                'fields': [
                    {'title': 'Source', 'value': notification.source, 'short': True},
                    {'title': 'Type', 'value': notification.type.value.upper(), 'short': True}
                ],
                'ts': int(notification.timestamp.timestamp())
            }
            
            payload = {'attachments': [attachment]}
            
            response = requests.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending Slack notification: {e}")
            return False
    
    def _send_file(self, notification: Notification) -> bool:
        """Send notification to file."""
        try:
            file_config = self._channel_configs.get('file', {})
            file_path = file_config.get('path', 'notifications.log')
            
            timestamp = notification.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] [{notification.type.value.upper()}] [{notification.source}] {notification.title}: {notification.message}\n"
            
            with open(file_path, 'a', encoding='utf-8') as f:
                f.write(log_entry)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending file notification: {e}")
            return False
    
    def _is_rate_limited(self, channel: NotificationChannel) -> bool:
        """Check if channel is rate limited."""
        try:
            rate_limit = self._rate_limits.get(channel.value)
            if not rate_limit:
                return False
            
            max_per_minute = rate_limit.get('max_per_minute', 60)
            current_time = datetime.utcnow()
            
            # Initialize counter if needed
            if channel not in self._rate_counters:
                self._rate_counters[channel] = []
            
            # Clean old entries
            cutoff_time = current_time - timedelta(minutes=1)
            self._rate_counters[channel] = [
                t for t in self._rate_counters[channel] if t > cutoff_time
            ]
            
            # Check limit
            if len(self._rate_counters[channel]) >= max_per_minute:
                return True
            
            # Add current time
            self._rate_counters[channel].append(current_time)
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking rate limit: {e}")
            return False
