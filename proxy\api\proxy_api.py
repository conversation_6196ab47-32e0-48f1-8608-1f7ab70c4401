"""
代理 API

该模块提供代理 API 接口，用于与其他组件交互。
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union

from proxy.models.proxy import Proxy
from proxy.proxy_agent import ProxyAgent


class ProxyAPI:
    """代理 API，用于与其他组件交互"""
    
    def __init__(self, proxy_agent: ProxyAgent):
        """
        初始化代理 API
        
        Args:
            proxy_agent: 代理智能体实例
        """
        self.proxy_agent = proxy_agent
        self.logger = logging.getLogger(__name__)
    
    def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理请求
        
        Args:
            request: 请求字典
            
        Returns:
            响应字典
        """
        action = request.get('action')
        
        if not action:
            return self._error_response("Missing 'action' field")
        
        # 根据动作分发请求
        if action == 'get_proxy':
            return self._handle_get_proxy(request)
        elif action == 'get_proxies':
            return self._handle_get_proxies(request)
        elif action == 'report_proxy_status':
            return self._handle_report_proxy_status(request)
        elif action == 'get_pool_status':
            return self._handle_get_pool_status(request)
        elif action == 'get_source_status':
            return self._handle_get_source_status(request)
        else:
            return self._error_response(f"Unknown action: {action}")
    
    def _handle_get_proxy(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理获取代理请求
        
        Args:
            request: 请求字典
            
        Returns:
            响应字典
        """
        protocol = request.get('protocol')
        country = request.get('country')
        anonymity = request.get('anonymity')
        
        try:
            proxy = self.proxy_agent.get_proxy(protocol, country, anonymity)
            
            if not proxy:
                return self._error_response("No proxy available", 404)
            
            return {
                'status': 'success',
                'proxy': proxy.to_dict()
            }
        
        except Exception as e:
            self.logger.error(f"获取代理时出错: {str(e)}")
            return self._error_response(f"Error getting proxy: {str(e)}")
    
    def _handle_get_proxies(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理获取多个代理请求
        
        Args:
            request: 请求字典
            
        Returns:
            响应字典
        """
        count = request.get('count', 1)
        protocol = request.get('protocol')
        country = request.get('country')
        anonymity = request.get('anonymity')
        
        try:
            # 验证计数
            try:
                count = int(count)
                if count <= 0:
                    return self._error_response("Count must be a positive integer")
            except ValueError:
                return self._error_response("Count must be a valid integer")
            
            proxies = self.proxy_agent.get_proxies(count, protocol, country, anonymity)
            
            if not proxies:
                return self._error_response("No proxies available", 404)
            
            return {
                'status': 'success',
                'proxies': [proxy.to_dict() for proxy in proxies]
            }
        
        except Exception as e:
            self.logger.error(f"获取多个代理时出错: {str(e)}")
            return self._error_response(f"Error getting proxies: {str(e)}")
    
    def _handle_report_proxy_status(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理报告代理状态请求
        
        Args:
            request: 请求字典
            
        Returns:
            响应字典
        """
        proxy_data = request.get('proxy')
        success = request.get('success')
        response_time = request.get('response_time')
        
        if not proxy_data:
            return self._error_response("Missing 'proxy' field")
        
        if success is None:
            return self._error_response("Missing 'success' field")
        
        try:
            # 创建代理对象
            proxy = Proxy.from_dict(proxy_data)
            
            # 报告状态
            self.proxy_agent.report_proxy_status(proxy, success, response_time)
            
            return {
                'status': 'success'
            }
        
        except Exception as e:
            self.logger.error(f"报告代理状态时出错: {str(e)}")
            return self._error_response(f"Error reporting proxy status: {str(e)}")
    
    def _handle_get_pool_status(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理获取代理池状态请求
        
        Args:
            request: 请求字典
            
        Returns:
            响应字典
        """
        try:
            status = self.proxy_agent.get_pool_status()
            
            return {
                'status': 'success',
                'pool_status': status
            }
        
        except Exception as e:
            self.logger.error(f"获取代理池状态时出错: {str(e)}")
            return self._error_response(f"Error getting pool status: {str(e)}")
    
    def _handle_get_source_status(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理获取代理源状态请求
        
        Args:
            request: 请求字典
            
        Returns:
            响应字典
        """
        try:
            status = self.proxy_agent.get_source_status()
            
            return {
                'status': 'success',
                'source_status': status
            }
        
        except Exception as e:
            self.logger.error(f"获取代理源状态时出错: {str(e)}")
            return self._error_response(f"Error getting source status: {str(e)}")
    
    def _error_response(self, message: str, code: int = 400) -> Dict[str, Any]:
        """
        创建错误响应
        
        Args:
            message: 错误消息
            code: 错误代码
            
        Returns:
            错误响应字典
        """
        return {
            'status': 'error',
            'code': code,
            'message': message
        }