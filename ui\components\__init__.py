"""UI Components - Placeholder"""

class ProjectCard: pass
class TaskList: pass
class WalletCard: pass
class ProxyStatus: pass
class ProgressTracker: pass

try:
    from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel
    class NotificationCenter(QWidget):
        def __init__(self, parent=None):
            super().__init__(parent)
            layout = QVBoxLayout(self)
            layout.addWidget(QLabel("Notifications"))
except ImportError:
    class NotificationCenter: pass
