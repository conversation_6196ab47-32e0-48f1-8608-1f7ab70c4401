"""
工作流管理器

该模块提供工作流管理功能，负责定义、执行和监控工作流。
"""

import logging
import threading
import time
import uuid
from typing import Dict, List, Any, Callable, Optional, Tuple

from coordinator.core.agent_registry import AgentRegistry
from coordinator.communication.message_broker import MessageBroker
from coordinator.resources.resource_allocator import ResourceAllocator


class WorkflowManager:
    """工作流管理器，负责定义、执行和监控工作流"""
    
    def __init__(self, agent_registry: AgentRegistry, message_broker: MessageBroker,
                resource_allocator: ResourceAllocator):
        """
        初始化工作流管理器
        
        Args:
            agent_registry: 智能体注册表实例
            message_broker: 消息代理实例
            resource_allocator: 资源分配器实例
        """
        self.logger = logging.getLogger(__name__)
        self.agent_registry = agent_registry
        self.message_broker = message_broker
        self.resource_allocator = resource_allocator
        self._lock = threading.RLock()
        self._workflows: Dict[str, Dict[str, Any]] = {}  # workflow_name -> workflow_definition
        self._running_workflows: Dict[str, Dict[str, Any]] = {}  # workflow_id -> workflow_state
    
    def register_workflow(self, name: str, steps: List[Dict[str, Any]], description: str = "") -> None:
        """
        注册工作流
        
        Args:
            name: 工作流名称
            steps: 工作流步骤列表
            description: 工作流描述
        """
        with self._lock:
            if name in self._workflows:
                self.logger.warning(f"工作流 '{name}' 已存在，将被覆盖")
            
            self._workflows[name] = {
                "name": name,
                "description": description,
                "steps": steps,
                "created_at": time.time()
            }
            
            self.logger.info(f"已注册工作流 '{name}'")
    
    def unregister_workflow(self, name: str) -> bool:
        """
        注销工作流
        
        Args:
            name: 工作流名称
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if name not in self._workflows:
                self.logger.warning(f"工作流 '{name}' 不存在")
                return False
            
            del self._workflows[name]
            self.logger.info(f"已注销工作流 '{name}'")
            return True
    
    def execute(self, workflow_name: str, params: Dict[str, Any] = None) -> Any:
        """
        执行工作流
        
        Args:
            workflow_name: 工作流名称
            params: 工作流参数
            
        Returns:
            工作流执行结果
            
        Raises:
            ValueError: 如果工作流不存在
        """
        # 检查工作流是否存在
        with self._lock:
            if workflow_name not in self._workflows:
                raise ValueError(f"工作流 '{workflow_name}' 不存在")
            
            workflow = self._workflows[workflow_name].copy()
        
        # 生成工作流ID
        workflow_id = str(uuid.uuid4())
        
        # 初始化工作流状态
        workflow_state = {
            "id": workflow_id,
            "name": workflow_name,
            "params": params or {},
            "current_step": 0,
            "steps": workflow["steps"],
            "results": {},
            "status": "running",
            "start_time": time.time(),
            "end_time": None,
            "error": None
        }
        
        # 注册运行中的工作流
        with self._lock:
            self._running_workflows[workflow_id] = workflow_state
        
        try:
            # 执行工作流步骤
            result = self._execute_workflow_steps(workflow_state)
            
            # 更新工作流状态
            with self._lock:
                if workflow_id in self._running_workflows:
                    self._running_workflows[workflow_id]["status"] = "completed"
                    self._running_workflows[workflow_id]["end_time"] = time.time()
            
            return result
        
        except Exception as e:
            # 更新工作流状态
            with self._lock:
                if workflow_id in self._running_workflows:
                    self._running_workflows[workflow_id]["status"] = "failed"
                    self._running_workflows[workflow_id]["end_time"] = time.time()
                    self._running_workflows[workflow_id]["error"] = str(e)
            
            self.logger.error(f"执行工作流 '{workflow_name}' 时出错: {str(e)}")
            raise
    
    def execute_async(self, workflow_name: str, params: Dict[str, Any] = None,
                     callback: Optional[Callable[[str, Any, Optional[Exception]], None]] = None) -> str:
        """
        异步执行工作流
        
        Args:
            workflow_name: 工作流名称
            params: 工作流参数
            callback: 回调函数，接收工作流ID、结果和异常作为参数
            
        Returns:
            工作流ID
            
        Raises:
            ValueError: 如果工作流不存在
        """
        # 检查工作流是否存在
        with self._lock:
            if workflow_name not in self._workflows:
                raise ValueError(f"工作流 '{workflow_name}' 不存在")
        
        # 生成工作流ID
        workflow_id = str(uuid.uuid4())
        
        # 创建并启动工作流线程
        thread = threading.Thread(
            target=self._execute_workflow_async,
            args=(workflow_id, workflow_name, params, callback),
            name=f"Workflow-{workflow_id}",
            daemon=True
        )
        thread.start()
        
        return workflow_id
    
    def _execute_workflow_async(self, workflow_id: str, workflow_name: str, params: Dict[str, Any],
                              callback: Optional[Callable[[str, Any, Optional[Exception]], None]]) -> None:
        """
        在单独的线程中执行工作流
        
        Args:
            workflow_id: 工作流ID
            workflow_name: 工作流名称
            params: 工作流参数
            callback: 回调函数
        """
        try:
            # 获取工作流定义
            with self._lock:
                workflow = self._workflows[workflow_name].copy()
            
            # 初始化工作流状态
            workflow_state = {
                "id": workflow_id,
                "name": workflow_name,
                "params": params or {},
                "current_step": 0,
                "steps": workflow["steps"],
                "results": {},
                "status": "running",
                "start_time": time.time(),
                "end_time": None,
                "error": None
            }
            
            # 注册运行中的工作流
            with self._lock:
                self._running_workflows[workflow_id] = workflow_state
            
            # 执行工作流步骤
            result = self._execute_workflow_steps(workflow_state)
            
            # 更新工作流状态
            with self._lock:
                if workflow_id in self._running_workflows:
                    self._running_workflows[workflow_id]["status"] = "completed"
                    self._running_workflows[workflow_id]["end_time"] = time.time()
            
            # 调用回调函数
            if callback:
                callback(workflow_id, result, None)
        
        except Exception as e:
            # 更新工作流状态
            with self._lock:
                if workflow_id in self._running_workflows:
                    self._running_workflows[workflow_id]["status"] = "failed"
                    self._running_workflows[workflow_id]["end_time"] = time.time()
                    self._running_workflows[workflow_id]["error"] = str(e)
            
            self.logger.error(f"执行工作流 '{workflow_name}' 时出错: {str(e)}")
            
            # 调用回调函数
            if callback:
                callback(workflow_id, None, e)
    
    def _execute_workflow_steps(self, workflow_state: Dict[str, Any]) -> Any:
        """
        执行工作流步骤
        
        Args:
            workflow_state: 工作流状态
            
        Returns:
            工作流执行结果
        """
        steps = workflow_state["steps"]
        params = workflow_state["params"]
        results = workflow_state["results"]
        
        # 执行每个步骤
        for i, step in enumerate(steps):
            # 更新当前步骤
            workflow_state["current_step"] = i
            
            # 获取步骤信息
            step_type = step.get("type")
            step_params = step.get("params", {})
            step_agent = step.get("agent")
            step_method = step.get("method")
            step_id = step.get("id", f"step_{i}")
            
            # 合并工作流参数和步骤参数
            merged_params = {**params, **step_params}
            
            # 根据步骤类型执行不同的操作
            if step_type == "agent_call":
                # 调用智能体方法
                if not step_agent or not step_method:
                    raise ValueError(f"步骤 {i} 缺少必要的参数: agent={step_agent}, method={step_method}")
                
                agent = self.agent_registry.get(step_agent)
                if not agent:
                    raise ValueError(f"智能体 '{step_agent}' 不存在")
                
                method = getattr(agent, step_method, None)
                if not method or not callable(method):
                    raise ValueError(f"智能体 '{step_agent}' 没有方法 '{step_method}'")
                
                # 调用方法
                result = method(**merged_params)
                results[step_id] = result
            
            elif step_type == "conditional":
                # 条件分支
                condition = step.get("condition")
                if not condition:
                    raise ValueError(f"步骤 {i} 缺少必要的参数: condition")
                
                # 评估条件
                condition_result = self._evaluate_condition(condition, params, results)
                
                # 根据条件结果选择分支
                if condition_result:
                    branch = step.get("then", [])
                else:
                    branch = step.get("else", [])
                
                # 创建分支的工作流状态
                branch_state = {
                    "id": workflow_state["id"],
                    "name": workflow_state["name"],
                    "params": params,
                    "current_step": 0,
                    "steps": branch,
                    "results": results,
                    "status": "running",
                    "start_time": workflow_state["start_time"],
                    "end_time": None,
                    "error": None
                }
                
                # 执行分支
                branch_result = self._execute_workflow_steps(branch_state)
                results[step_id] = branch_result
            
            elif step_type == "parallel":
                # 并行执行
                parallel_steps = step.get("steps", [])
                if not parallel_steps:
                    raise ValueError(f"步骤 {i} 缺少必要的参数: steps")
                
                # 创建并执行每个并行步骤
                parallel_results = []
                threads = []
                
                for j, parallel_step in enumerate(parallel_steps):
                    # 创建并行步骤的工作流状态
                    parallel_state = {
                        "id": f"{workflow_state['id']}_parallel_{j}",
                        "name": workflow_state["name"],
                        "params": params,
                        "current_step": 0,
                        "steps": [parallel_step],
                        "results": {},
                        "status": "running",
                        "start_time": time.time(),
                        "end_time": None,
                        "error": None
                    }
                    
                    # 创建线程
                    thread = threading.Thread(
                        target=self._execute_parallel_step,
                        args=(parallel_state, parallel_results, j),
                        name=f"Workflow-{workflow_state['id']}-Parallel-{j}",
                        daemon=True
                    )
                    threads.append(thread)
                    thread.start()
                
                # 等待所有线程完成
                for thread in threads:
                    thread.join()
                
                # 排序结果（因为线程可能以不同的顺序完成）
                parallel_results.sort(key=lambda x: x[0])
                results[step_id] = [result for _, result in parallel_results]
            
            elif step_type == "loop":
                # 循环执行
                loop_steps = step.get("steps", [])
                loop_condition = step.get("condition")
                max_iterations = step.get("max_iterations", 100)
                
                if not loop_steps:
                    raise ValueError(f"步骤 {i} 缺少必要的参数: steps")
                
                # 执行循环
                loop_results = []
                iteration = 0
                
                while iteration < max_iterations:
                    # 检查循环条件
                    if loop_condition and not self._evaluate_condition(loop_condition, params, results):
                        break
                    
                    # 创建循环步骤的工作流状态
                    loop_state = {
                        "id": f"{workflow_state['id']}_loop_{iteration}",
                        "name": workflow_state["name"],
                        "params": params,
                        "current_step": 0,
                        "steps": loop_steps,
                        "results": results.copy(),
                        "status": "running",
                        "start_time": time.time(),
                        "end_time": None,
                        "error": None
                    }
                    
                    # 执行循环步骤
                    loop_result = self._execute_workflow_steps(loop_state)
                    loop_results.append(loop_result)
                    
                    # 更新结果
                    results.update(loop_state["results"])
                    
                    # 增加迭代计数
                    iteration += 1
                
                results[step_id] = loop_results
            
            else:
                raise ValueError(f"未知的步骤类型: {step_type}")
        
        # 返回最后一个步骤的结果，如果没有步骤则返回None
        if steps:
            last_step_id = steps[-1].get("id", f"step_{len(steps) - 1}")
            return results.get(last_step_id)
        else:
            return None
    
    def _execute_parallel_step(self, parallel_state: Dict[str, Any], results: List[Tuple[int, Any]], index: int) -> None:
        """
        执行并行步骤
        
        Args:
            parallel_state: 并行步骤的工作流状态
            results: 用于存储结果的列表
            index: 并行步骤的索引
        """
        try:
            result = self._execute_workflow_steps(parallel_state)
            results.append((index, result))
        except Exception as e:
            self.logger.error(f"执行并行步骤 {index} 时出错: {str(e)}")
            results.append((index, None))
    
    def _evaluate_condition(self, condition: str, params: Dict[str, Any], results: Dict[str, Any]) -> bool:
        """
        评估条件表达式
        
        Args:
            condition: 条件表达式
            params: 工作流参数
            results: 步骤结果
            
        Returns:
            条件评估结果
        """
        # 创建局部变量
        locals_dict = {
            "params": params,
            "results": results
        }
        
        # 评估条件
        try:
            return bool(eval(condition, {"__builtins__": {}}, locals_dict))
        except Exception as e:
            self.logger.error(f"评估条件 '{condition}' 时出错: {str(e)}")
            return False
    
    def get_workflow(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取工作流定义
        
        Args:
            name: 工作流名称
            
        Returns:
            工作流定义，如果不存在则返回None
        """
        with self._lock:
            return self._workflows.get(name)
    
    def get_all_workflows(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有工作流定义
        
        Returns:
            工作流定义字典，键为工作流名称，值为工作流定义
        """
        with self._lock:
            return self._workflows.copy()
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        获取工作流状态
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            工作流状态，如果不存在则返回None
        """
        with self._lock:
            return self._running_workflows.get(workflow_id)
    
    def get_all_running_workflows(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有运行中的工作流
        
        Returns:
            运行中的工作流字典，键为工作流ID，值为工作流状态
        """
        with self._lock:
            return {k: v for k, v in self._running_workflows.items() if v["status"] == "running"}
    
    def cancel_workflow(self, workflow_id: str) -> bool:
        """
        取消工作流
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            如果成功取消则返回True，否则返回False
        """
        with self._lock:
            if workflow_id not in self._running_workflows:
                self.logger.warning(f"工作流ID '{workflow_id}' 不存在")
                return False
            
            workflow_state = self._running_workflows[workflow_id]
            if workflow_state["status"] != "running":
                self.logger.warning(f"工作流ID '{workflow_id}' 不在运行中")
                return False
            
            workflow_state["status"] = "cancelled"
            workflow_state["end_time"] = time.time()
            
            self.logger.info(f"已取消工作流ID '{workflow_id}'")
            return True
    
    def cleanup_workflows(self, max_age: float = 3600.0) -> int:
        """
        清理已完成的工作流
        
        Args:
            max_age: 最大工作流年龄（秒）
            
        Returns:
            清理的工作流数量
        """
        now = time.time()
        to_remove = []
        
        with self._lock:
            for workflow_id, workflow_state in self._running_workflows.items():
                if workflow_state["status"] in ["completed", "failed", "cancelled"]:
                    if now - workflow_state["end_time"] > max_age:
                        to_remove.append(workflow_id)
            
            for workflow_id in to_remove:
                del self._running_workflows[workflow_id]
        
        if to_remove:
            self.logger.info(f"已清理 {len(to_remove)} 个已完成的工作流")
        
        return len(to_remove)