"""
关键词过滤器

该模块实现了基于关键词的项目过滤器。
"""

import logging
import re
from typing import Dict, List, Any, Set


class KeywordFilter:
    """关键词过滤器，基于关键词过滤项目"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化关键词过滤器
        
        Args:
            config: 配置字典，包含关键词过滤器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 必须包含的关键词
        self.required_keywords = set(config.get('required_keywords', [
            'airdrop', 'token', 'crypto', 'blockchain', 'defi', 'nft'
        ]))
        
        # 必须排除的关键词
        self.excluded_keywords = set(config.get('excluded_keywords', [
            'scam', 'fake', 'phishing', 'spam', 'virus', 'malware'
        ]))
        
        # 最小关键词匹配数
        self.min_required_matches = config.get('min_required_matches', 1)
        
        # 是否区分大小写
        self.case_sensitive = config.get('case_sensitive', False)
    
    def filter(self, projects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤项目
        
        Args:
            projects: 项目列表
            
        Returns:
            过滤后的项目列表
        """
        filtered_projects = []
        
        for project in projects:
            if self._is_valid_project(project):
                filtered_projects.append(project)
        
        self.logger.debug(f"关键词过滤: {len(projects)} -> {len(filtered_projects)}")
        return filtered_projects
    
    def _is_valid_project(self, project: Dict[str, Any]) -> bool:
        """
        检查项目是否有效
        
        Args:
            project: 项目字典
            
        Returns:
            如果项目有效则返回True，否则返回False
        """
        # 提取项目文本
        project_text = self._extract_project_text(project)
        
        if not self.case_sensitive:
            project_text = project_text.lower()
            required_keywords = {k.lower() for k in self.required_keywords}
            excluded_keywords = {k.lower() for k in self.excluded_keywords}
        else:
            required_keywords = self.required_keywords
            excluded_keywords = self.excluded_keywords
        
        # 检查排除的关键词
        for keyword in excluded_keywords:
            if keyword in project_text:
                return False
        
        # 检查必须包含的关键词
        matches = 0
        for keyword in required_keywords:
            if keyword in project_text:
                matches += 1
        
        return matches >= self.min_required_matches
    
    def _extract_project_text(self, project: Dict[str, Any]) -> str:
        """
        提取项目文本
        
        Args:
            project: 项目字典
            
        Returns:
            项目文本
        """
        text_parts = []
        
        # 添加项目名称
        if 'name' in project:
            text_parts.append(str(project['name']))
        
        # 添加项目描述
        if 'description' in project:
            text_parts.append(str(project['description']))
        
        # 添加项目类型
        if 'project_type' in project:
            text_parts.append(str(project['project_type']))
        
        # 添加区块链平台
        if 'blockchain' in project:
            text_parts.append(str(project['blockchain']))
        
        # 添加标签
        if 'tags' in project and isinstance(project['tags'], list):
            text_parts.extend([str(tag) for tag in project['tags']])
        
        return ' '.join(text_parts)
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            config: 新的配置字典
        """
        self.config = config
        
        # 更新配置参数
        self.required_keywords = set(config.get('required_keywords', self.required_keywords))
        self.excluded_keywords = set(config.get('excluded_keywords', self.excluded_keywords))
        self.min_required_matches = config.get('min_required_matches', self.min_required_matches)
        self.case_sensitive = config.get('case_sensitive', self.case_sensitive)
        
        self.logger.info("已更新关键词过滤器配置")