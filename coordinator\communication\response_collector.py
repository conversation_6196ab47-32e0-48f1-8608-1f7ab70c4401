"""
响应收集器

该模块提供响应收集功能，用于收集和聚合来自多个智能体的响应。
"""

import logging
import threading
import time
import uuid
from typing import Dict, List, Any, Callable, Optional, Set

from coordinator.communication.message_broker import MessageBroker
from coordinator.communication.command_dispatcher import CommandDispatcher


class ResponseCollector:
    """响应收集器，负责收集和聚合来自多个智能体的响应"""
    
    def __init__(self, message_broker: MessageBroker, command_dispatcher: CommandDispatcher):
        """
        初始化响应收集器
        
        Args:
            message_broker: 消息代理实例
            command_dispatcher: 命令分发器实例
        """
        self.logger = logging.getLogger(__name__)
        self.message_broker = message_broker
        self.command_dispatcher = command_dispatcher
        self._lock = threading.RLock()
        self._collection_tasks: Dict[str, Dict[str, Any]] = {}  # task_id -> task_info
    
    def collect_responses(self, command_type: str, data: Dict[str, Any], target_agents: List[str],
                         timeout: float = 30.0, aggregator: Optional[Callable[[List[Dict[str, Any]]], Any]] = None) -> Any:
        """
        向多个智能体发送命令并收集响应
        
        Args:
            command_type: 命令类型
            data: 命令数据
            target_agents: 目标智能体列表
            timeout: 超时时间（秒）
            aggregator: 聚合函数，接收响应列表并返回聚合结果
            
        Returns:
            聚合后的响应，如果没有提供聚合函数则返回响应列表
        """
        if not target_agents:
            self.logger.warning("目标智能体列表为空")
            return [] if aggregator is None else aggregator([])
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建事件，用于等待所有响应
        completion_event = threading.Event()
        
        # 初始化任务信息
        with self._lock:
            self._collection_tasks[task_id] = {
                "command_type": command_type,
                "data": data,
                "target_agents": set(target_agents),
                "pending_agents": set(target_agents),
                "responses": {},
                "completion_event": completion_event,
                "start_time": time.time()
            }
        
        # 向每个目标智能体发送命令
        for agent in target_agents:
            self._send_command_to_agent(task_id, agent, command_type, data)
        
        # 等待所有响应或超时
        completion_event.wait(timeout)
        
        # 获取结果
        with self._lock:
            if task_id in self._collection_tasks:
                task_info = self._collection_tasks[task_id]
                responses = [task_info["responses"].get(agent) for agent in target_agents]
                # 过滤掉None值（表示没有收到响应）
                responses = [r for r in responses if r is not None]
                
                # 清理任务
                del self._collection_tasks[task_id]
                
                # 应用聚合函数
                if aggregator:
                    return aggregator(responses)
                else:
                    return responses
            else:
                self.logger.error(f"任务ID '{task_id}' 不存在")
                return [] if aggregator is None else aggregator([])
    
    def collect_responses_async(self, command_type: str, data: Dict[str, Any], target_agents: List[str],
                              callback: Callable[[List[Dict[str, Any]]], None], timeout: float = 30.0,
                              aggregator: Optional[Callable[[List[Dict[str, Any]]], Any]] = None) -> str:
        """
        异步向多个智能体发送命令并收集响应
        
        Args:
            command_type: 命令类型
            data: 命令数据
            target_agents: 目标智能体列表
            callback: 回调函数，接收响应列表作为参数
            timeout: 超时时间（秒）
            aggregator: 聚合函数，接收响应列表并返回聚合结果
            
        Returns:
            任务ID
        """
        if not target_agents:
            self.logger.warning("目标智能体列表为空")
            if callback:
                result = [] if aggregator is None else aggregator([])
                callback(result)
            return str(uuid.uuid4())  # 返回一个虚拟的任务ID
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 初始化任务信息
        with self._lock:
            self._collection_tasks[task_id] = {
                "command_type": command_type,
                "data": data,
                "target_agents": set(target_agents),
                "pending_agents": set(target_agents),
                "responses": {},
                "callback": callback,
                "aggregator": aggregator,
                "start_time": time.time(),
                "timeout": timeout
            }
        
        # 向每个目标智能体发送命令
        for agent in target_agents:
            self._send_command_to_agent(task_id, agent, command_type, data)
        
        # 启动超时检查
        threading.Timer(timeout, self._check_timeout, args=(task_id,)).start()
        
        return task_id
    
    def _send_command_to_agent(self, task_id: str, agent: str, command_type: str, data: Dict[str, Any]) -> None:
        """
        向单个智能体发送命令
        
        Args:
            task_id: 任务ID
            agent: 目标智能体
            command_type: 命令类型
            data: 命令数据
        """
        # 创建回调函数
        def response_callback(response: Optional[Dict[str, Any]]) -> None:
            self._handle_agent_response(task_id, agent, response)
        
        # 发送命令
        self.command_dispatcher.send_command_async(agent, command_type, data, response_callback)
    
    def _handle_agent_response(self, task_id: str, agent: str, response: Optional[Dict[str, Any]]) -> None:
        """
        处理智能体响应
        
        Args:
            task_id: 任务ID
            agent: 智能体名称
            response: 响应数据
        """
        with self._lock:
            if task_id not in self._collection_tasks:
                self.logger.warning(f"收到未知任务ID '{task_id}' 的响应")
                return
            
            task_info = self._collection_tasks[task_id]
            
            # 记录响应
            if response is not None:
                task_info["responses"][agent] = response
            
            # 从待处理列表中移除智能体
            if agent in task_info["pending_agents"]:
                task_info["pending_agents"].remove(agent)
            
            # 检查是否所有智能体都已响应
            if not task_info["pending_agents"]:
                self._complete_task(task_id)
    
    def _complete_task(self, task_id: str) -> None:
        """
        完成任务
        
        Args:
            task_id: 任务ID
        """
        with self._lock:
            if task_id not in self._collection_tasks:
                return
            
            task_info = self._collection_tasks[task_id]
            
            # 如果有完成事件，设置它
            if "completion_event" in task_info:
                task_info["completion_event"].set()
            
            # 如果有回调函数，调用它
            if "callback" in task_info and task_info["callback"]:
                target_agents = list(task_info["target_agents"])
                responses = [task_info["responses"].get(agent) for agent in target_agents]
                # 过滤掉None值
                responses = [r for r in responses if r is not None]
                
                # 应用聚合函数
                result = responses
                if "aggregator" in task_info and task_info["aggregator"]:
                    result = task_info["aggregator"](responses)
                
                try:
                    task_info["callback"](result)
                except Exception as e:
                    self.logger.error(f"调用任务回调函数时出错: {str(e)}")
            
            # 清理任务
            del self._collection_tasks[task_id]
    
    def _check_timeout(self, task_id: str) -> None:
        """
        检查任务是否超时
        
        Args:
            task_id: 任务ID
        """
        with self._lock:
            if task_id not in self._collection_tasks:
                return
            
            task_info = self._collection_tasks[task_id]
            
            # 检查是否已经超时
            if time.time() - task_info["start_time"] >= task_info.get("timeout", 30.0):
                self.logger.warning(f"任务ID '{task_id}' 超时")
                self._complete_task(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            如果成功取消则返回True，否则返回False
        """
        with self._lock:
            if task_id not in self._collection_tasks:
                self.logger.warning(f"尝试取消不存在的任务ID '{task_id}'")
                return False
            
            self.logger.info(f"取消任务ID '{task_id}'")
            self._complete_task(task_id)
            return True
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有活动任务
        
        Returns:
            活动任务列表
        """
        with self._lock:
            result = []
            for task_id, task_info in self._collection_tasks.items():
                # 创建任务信息的副本，排除内部字段
                task_copy = {
                    "task_id": task_id,
                    "command_type": task_info["command_type"],
                    "target_agents": list(task_info["target_agents"]),
                    "pending_agents": list(task_info["pending_agents"]),
                    "response_count": len(task_info["responses"]),
                    "start_time": task_info["start_time"],
                    "elapsed_time": time.time() - task_info["start_time"]
                }
                result.append(task_copy)
            
            return result
    
    def cleanup_tasks(self, max_age: float = 60.0) -> int:
        """
        清理超时的任务
        
        Args:
            max_age: 最大任务年龄（秒）
            
        Returns:
            清理的任务数量
        """
        now = time.time()
        to_complete = []
        
        with self._lock:
            for task_id, task_info in self._collection_tasks.items():
                if now - task_info["start_time"] > max_age:
                    to_complete.append(task_id)
        
        for task_id in to_complete:
            self._complete_task(task_id)
        
        if to_complete:
            self.logger.info(f"已清理 {len(to_complete)} 个超时的任务")
        
        return len(to_complete)