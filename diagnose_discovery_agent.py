#!/usr/bin/env python3
"""
诊断项目发现智能体

检查项目发现智能体无法发现项目的原因
"""

import asyncio
import logging
import json
import os
import time
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def diagnose_discovery_agent():
    """诊断项目发现智能体"""
    logger.info("🔍 开始诊断项目发现智能体...")
    
    try:
        # 1. 检查配置
        await check_configuration()
        
        # 2. 检查源管理器
        await check_source_manager()
        
        # 3. 检查具体的源
        await check_individual_sources()
        
        # 4. 检查过滤器
        await check_filters()
        
        # 5. 检查收集器
        await check_collectors()
        
        # 6. 测试完整的发现流程
        await test_full_discovery_process()
        
        logger.info("🎉 诊断完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def check_configuration():
    """检查配置"""
    logger.info("📋 检查配置...")
    
    try:
        # 创建测试配置
        test_config = {
            'discovery_interval': 60,  # 1分钟
            'max_projects_per_source': 5,
            'storage_file': 'test_projects.json',
            'storage_enabled': True,
            'sources': {
                'enabled_sources': ['aggregator', 'enhanced_aggregator'],
                'aggregator': {
                    'enabled': True,
                    'timeout': 30,
                    'airdrops_io': {'enabled': True},
                    'coinmarketcap': {'enabled': True},
                    'cryptorank': {'enabled': True},
                    'defillama': {'enabled': True}
                },
                'enhanced_aggregator': {
                    'enabled': True,
                    'timeout': 30
                }
            },
            'filters': {
                'enabled': True,
                'min_score': 0.1
            },
            'collectors': {
                'enabled': True,
                'timeout': 30
            }
        }
        
        # 保存测试配置
        os.makedirs("test_data", exist_ok=True)
        with open("test_data/discovery_config.json", "w") as f:
            json.dump(test_config, f, indent=2)
        
        logger.info("  ✅ 配置创建成功")
        logger.info(f"  📊 启用的源: {test_config['sources']['enabled_sources']}")
        
        return test_config
        
    except Exception as e:
        logger.error(f"  ❌ 配置检查失败: {e}")
        return None


async def check_source_manager():
    """检查源管理器"""
    logger.info("🔧 检查源管理器...")
    
    try:
        from discovery.sources.source_manager import SourceManager
        
        # 创建测试配置
        sources_config = {
            'enabled_sources': ['aggregator'],
            'aggregator': {
                'enabled': True,
                'timeout': 30,
                'airdrops_io': {'enabled': True},
                'coinmarketcap': {'enabled': True}
            }
        }
        
        # 创建源管理器
        source_manager = SourceManager(sources_config)
        logger.info("  ✅ 源管理器创建成功")
        
        # 检查可用源
        sources = source_manager.get_sources()
        logger.info(f"  📊 可用源数量: {len(sources)}")
        logger.info(f"  📋 源列表: {list(sources.keys())}")
        
        # 测试获取项目
        logger.info("  🔍 测试获取项目...")
        projects = source_manager.get_projects(3)
        logger.info(f"  📰 获取到项目数量: {len(projects)}")
        
        if projects:
            logger.info("  ✅ 源管理器工作正常")
            for i, project in enumerate(projects[:2]):
                logger.info(f"    项目 {i+1}: {project.get('name', 'Unknown')} - {project.get('url', 'No URL')}")
        else:
            logger.warning("  ⚠️ 源管理器未获取到任何项目")
        
        return source_manager
        
    except Exception as e:
        logger.error(f"  ❌ 源管理器检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None


async def check_individual_sources():
    """检查具体的源"""
    logger.info("🌐 检查具体的源...")
    
    try:
        # 测试聚合器源
        await test_aggregator_source()
        
        # 测试增强聚合器源
        await test_enhanced_aggregator_source()
        
        logger.info("  ✅ 源检查完成")
        
    except Exception as e:
        logger.error(f"  ❌ 源检查失败: {e}")


async def test_aggregator_source():
    """测试聚合器源"""
    logger.info("  🔍 测试聚合器源...")
    
    try:
        from discovery.sources.aggregator_source import AggregatorSource
        
        config = {
            'timeout': 30,
            'airdrops_io': {'enabled': True},
            'coinmarketcap': {'enabled': True},
            'cryptorank': {'enabled': True},
            'defillama': {'enabled': True}
        }
        
        source = AggregatorSource(config)
        logger.info("    ✅ 聚合器源创建成功")
        
        # 测试获取项目
        projects = source.get_projects(3)
        logger.info(f"    📰 聚合器源获取到 {len(projects)} 个项目")
        
        if projects:
            for i, project in enumerate(projects[:2]):
                logger.info(f"      项目 {i+1}: {project.get('name', 'Unknown')} - {project.get('discovery_source', 'Unknown')}")
        else:
            logger.warning("    ⚠️ 聚合器源未获取到任何项目")
        
    except Exception as e:
        logger.error(f"    ❌ 聚合器源测试失败: {e}")


async def test_enhanced_aggregator_source():
    """测试增强聚合器源"""
    logger.info("  🔍 测试增强聚合器源...")
    
    try:
        from discovery.sources.enhanced_aggregator import EnhancedAggregatorSource
        
        config = {
            'timeout': 30,
            'use_mock_data': True  # 使用模拟数据进行测试
        }
        
        source = EnhancedAggregatorSource(config)
        logger.info("    ✅ 增强聚合器源创建成功")
        
        # 测试获取项目
        projects = source.get_projects(5)
        logger.info(f"    📰 增强聚合器源获取到 {len(projects)} 个项目")
        
        if projects:
            for i, project in enumerate(projects[:3]):
                logger.info(f"      项目 {i+1}: {project.get('name', 'Unknown')} - {project.get('blockchain', 'Unknown')}")
        else:
            logger.warning("    ⚠️ 增强聚合器源未获取到任何项目")
        
    except Exception as e:
        logger.error(f"    ❌ 增强聚合器源测试失败: {e}")


async def check_filters():
    """检查过滤器"""
    logger.info("🔧 检查过滤器...")
    
    try:
        from discovery.filters.filter_manager import FilterManager
        
        config = {
            'enabled': True,
            'min_score': 0.1
        }
        
        filter_manager = FilterManager(config)
        logger.info("  ✅ 过滤器管理器创建成功")
        
        # 创建测试项目
        test_projects = [
            {
                'name': 'Test Airdrop 1',
                'description': 'A great airdrop project',
                'url': 'https://example1.com',
                'project_type': 'airdrop'
            },
            {
                'name': 'Test Airdrop 2',
                'description': 'Another airdrop project',
                'url': 'https://example2.com',
                'project_type': 'airdrop'
            }
        ]
        
        # 测试过滤
        filtered_projects = filter_manager.filter_projects(test_projects)
        logger.info(f"  📊 过滤前: {len(test_projects)} 个项目")
        logger.info(f"  📊 过滤后: {len(filtered_projects)} 个项目")
        
        if len(filtered_projects) > 0:
            logger.info("  ✅ 过滤器工作正常")
        else:
            logger.warning("  ⚠️ 过滤器过滤掉了所有项目")
        
    except Exception as e:
        logger.error(f"  ❌ 过滤器检查失败: {e}")


async def check_collectors():
    """检查收集器"""
    logger.info("🔧 检查收集器...")
    
    try:
        from discovery.collectors.collector_manager import CollectorManager
        
        config = {
            'enabled': True,
            'timeout': 30
        }
        
        collector_manager = CollectorManager(config)
        logger.info("  ✅ 收集器管理器创建成功")
        
        # 创建测试项目
        test_project = {
            'name': 'Test Project',
            'description': 'A test project',
            'url': 'https://example.com',
            'project_type': 'airdrop'
        }
        
        # 测试收集
        collected_info = collector_manager.collect_project_info(test_project)
        logger.info(f"  📊 收集到的信息: {bool(collected_info)}")
        
        if collected_info:
            logger.info("  ✅ 收集器工作正常")
            logger.info(f"    项目名称: {collected_info.get('name', 'Unknown')}")
        else:
            logger.warning("  ⚠️ 收集器未收集到任何信息")
        
    except Exception as e:
        logger.error(f"  ❌ 收集器检查失败: {e}")


async def test_full_discovery_process():
    """测试完整的发现流程"""
    logger.info("🚀 测试完整的发现流程...")
    
    try:
        from discovery.discovery_agent import DiscoveryAgent
        
        # 创建测试配置
        config = {
            'discovery_interval': 10,  # 10秒
            'max_projects_per_source': 3,
            'storage_file': 'test_data/test_projects.json',
            'storage_enabled': True,
            'sources': {
                'enabled_sources': ['enhanced_aggregator'],
                'enhanced_aggregator': {
                    'enabled': True,
                    'timeout': 30,
                    'use_mock_data': True
                }
            },
            'filters': {
                'enabled': True,
                'min_score': 0.1
            },
            'collectors': {
                'enabled': True,
                'timeout': 30
            }
        }
        
        # 创建发现智能体
        discovery_agent = DiscoveryAgent(config)
        logger.info("  ✅ 发现智能体创建成功")
        
        # 启动发现智能体
        discovery_agent.start()
        logger.info("  ✅ 发现智能体启动成功")
        
        # 等待一段时间让它发现项目
        logger.info("  ⏳ 等待项目发现...")
        await asyncio.sleep(15)  # 等待15秒
        
        # 检查发现的项目
        projects = discovery_agent.get_projects()
        logger.info(f"  📊 发现的项目数量: {len(projects)}")
        
        if projects:
            logger.info("  ✅ 发现智能体工作正常！")
            for i, project in enumerate(projects[:3]):
                logger.info(f"    项目 {i+1}: {project.name} - {project.blockchain.value}")
        else:
            logger.warning("  ⚠️ 发现智能体未发现任何项目")
        
        # 获取统计信息
        stats = discovery_agent.get_stats()
        logger.info(f"  📊 统计信息: {stats}")
        
        # 停止发现智能体
        discovery_agent.stop()
        logger.info("  ✅ 发现智能体停止成功")
        
        return len(projects) > 0
        
    except Exception as e:
        logger.error(f"  ❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def create_mock_enhanced_aggregator():
    """创建模拟的增强聚合器源"""
    logger.info("🔧 创建模拟的增强聚合器源...")
    
    mock_content = '''"""
增强聚合器源 - 模拟版本

用于测试的模拟数据源
"""

import logging
import time
import random
from typing import Dict, List, Any

from discovery.sources.base_source import BaseProjectSource
from discovery.models.project import ProjectType, BlockchainPlatform


class EnhancedAggregatorSource(BaseProjectSource):
    """增强聚合器源 - 模拟版本"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.use_mock_data = config.get('use_mock_data', True)
        self.logger.info("增强聚合器源初始化完成（模拟模式）")
    
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """获取项目（模拟数据）"""
        if not self.use_mock_data:
            return []
        
        self.logger.info(f"生成 {count} 个模拟项目")
        
        projects = []
        
        # 模拟项目数据
        mock_projects = [
            {
                'name': 'LayerZero Airdrop',
                'description': 'Cross-chain interoperability protocol airdrop',
                'blockchain': 'ethereum',
                'project_type': 'airdrop'
            },
            {
                'name': 'zkSync Era Testnet',
                'description': 'Layer 2 scaling solution testnet',
                'blockchain': 'ethereum',
                'project_type': 'testnet'
            },
            {
                'name': 'Arbitrum Odyssey',
                'description': 'Arbitrum ecosystem exploration campaign',
                'blockchain': 'arbitrum',
                'project_type': 'campaign'
            },
            {
                'name': 'Optimism Quests',
                'description': 'Optimism network quests and rewards',
                'blockchain': 'optimism',
                'project_type': 'quest'
            },
            {
                'name': 'Polygon zkEVM',
                'description': 'Polygon zero-knowledge Ethereum Virtual Machine',
                'blockchain': 'polygon',
                'project_type': 'testnet'
            },
            {
                'name': 'Starknet Alpha',
                'description': 'StarkNet decentralized ZK-Rollup',
                'blockchain': 'starknet',
                'project_type': 'testnet'
            },
            {
                'name': 'Aptos Incentivized Testnet',
                'description': 'Aptos blockchain testnet with rewards',
                'blockchain': 'aptos',
                'project_type': 'testnet'
            },
            {
                'name': 'Sui Devnet',
                'description': 'Sui blockchain development network',
                'blockchain': 'sui',
                'project_type': 'testnet'
            }
        ]
        
        # 随机选择项目
        selected_projects = random.sample(mock_projects, min(count, len(mock_projects)))
        
        for i, project_data in enumerate(selected_projects):
            project = {
                'id': f"enhanced_aggregator_{int(time.time())}_{i}",
                'name': project_data['name'],
                'description': project_data['description'],
                'url': f"https://example-{i}.com",
                'project_type': project_data['project_type'],
                'blockchain': project_data['blockchain'],
                'discovery_source': 'enhanced_aggregator',
                'source_url': 'https://mock-aggregator.com',
                'discovery_time': time.time()
            }
            
            projects.append(project)
        
        self.logger.info(f"生成了 {len(projects)} 个模拟项目")
        return projects
'''
    
    # 确保目录存在
    os.makedirs('discovery/sources', exist_ok=True)
    
    # 写入模拟文件
    with open('discovery/sources/enhanced_aggregator.py', 'w', encoding='utf-8') as f:
        f.write(mock_content)
    
    logger.info("  ✅ 模拟增强聚合器源创建成功")


async def main():
    """主函数"""
    logger.info("🚀 开始项目发现智能体诊断")
    
    try:
        # 创建模拟数据源
        await create_mock_enhanced_aggregator()
        
        # 运行诊断
        success = await diagnose_discovery_agent()
        
        if success:
            logger.info("🎉 诊断完成，发现智能体工作正常！")
            return 0
        else:
            logger.error("❌ 诊断发现问题")
            return 1
            
    except Exception as e:
        logger.error(f"诊断过程中出错: {e}")
        return 1
    finally:
        # 清理测试数据
        import shutil
        if os.path.exists("test_data"):
            shutil.rmtree("test_data")
            logger.info("🧹 清理测试数据完成")


if __name__ == "__main__":
    exit(asyncio.run(main()))
