"""
ProxyScrape 代理源

该模块实现了从 ProxyScrape API 获取代理的功能。
"""

import logging
import requests
import time
import random
from typing import Dict, List, Any

from proxy.models.proxy import Proxy
from proxy.acquisition.sources.base_source import BaseProxySource


class ProxyScrapeSource(BaseProxySource):
    """ProxyScrape 代理源"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 ProxyScrape 代理源
        
        Args:
            config: 配置字典，包含代理源的配置信息
        """
        super().__init__(config)
        self.base_url = config.get('base_url', 'https://api.proxyscrape.com/v2/')
        self.timeout = config.get('timeout', 10)
        self.api_key = config.get('api_key', '')  # 可选的 API 密钥
        self.request_types = config.get('request_types', ['http', 'socks4', 'socks5'])
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
    
    def get_proxies(self, count: int) -> List[Proxy]:
        """
        从 ProxyScrape 获取代理
        
        Args:
            count: 要获取的代理数量
            
        Returns:
            代理列表
        """
        self.logger.info(f"从 ProxyScrape 获取 {count} 个代理")
        
        proxies = []
        
        # 计算每种类型需要获取的代理数量
        type_count = max(1, count // len(self.request_types))
        
        for request_type in self.request_types:
            try:
                # 构建请求 URL
                url = f"{self.base_url}?request=getproxies&protocol={request_type}&timeout=10000&country=all&ssl=all&anonymity=all"
                
                # 如果有 API 密钥，添加到 URL
                if self.api_key:
                    url += f"&key={self.api_key}"
                
                # 随机选择一个 User-Agent
                headers = {
                    'User-Agent': random.choice(self.user_agents)
                }
                
                # 发送请求
                response = requests.get(url, headers=headers, timeout=self.timeout)
                response.raise_for_status()
                
                # 解析响应
                proxy_list = response.text.strip().split('\r\n')
                
                for proxy_str in proxy_list:
                    try:
                        if not proxy_str:
                            continue
                        
                        # 解析代理
                        parts = proxy_str.split(':')
                        if len(parts) < 2:
                            continue
                        
                        host = parts[0]
                        port = int(parts[1])
                        
                        # 创建代理对象
                        proxy = Proxy(
                            host=host,
                            port=port,
                            protocol=request_type,
                            source='proxy_scrape'
                        )
                        
                        proxies.append(proxy)
                        
                        # 如果已经获取到足够的代理，就停止
                        if len(proxies) >= count:
                            break
                    
                    except Exception as e:
                        self.logger.error(f"解析代理时出错: {str(e)}")
                
                # 如果已经获取到足够的代理，就停止
                if len(proxies) >= count:
                    break
            
            except Exception as e:
                self.logger.error(f"从 ProxyScrape 获取 {request_type} 代理时出错: {str(e)}")
        
        self.logger.info(f"从 ProxyScrape 获取到 {len(proxies)} 个代理")
        return proxies[:count]