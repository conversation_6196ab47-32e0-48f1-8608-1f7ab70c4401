"""
Sybil Defense Agent

This agent is responsible for implementing anti-Sybil measures to protect against
project restrictions and bans.
"""

import logging
import os
import json
import time
import random
import hashlib
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("SybilDefenseAgent")

class SybilDefenseAgent:
    """
    Sybil Defense Agent for implementing anti-Sybil measures.
    
    This agent handles:
    - Identity management
    - Activity pattern analysis
    - Risk assessment
    - Behavior randomization
    - Fingerprint management
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Sybil Defense Agent.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config = self._load_config(config_path)
        self.identities = {}
        self.activity_logs = {}
        self.risk_assessments = {}
        self.active = False
        
        # Initialize data storage
        os.makedirs(self.config["data_storage_path"], exist_ok=True)
        
        # Load identities and activity logs
        self._load_identities()
        self._load_activity_logs()
        
        logger.info("Sybil Defense Agent initialized")
    
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "data_storage_path": "data/sybil_defense",
            "max_identities_per_project": 5,
            "min_activity_interval_minutes": 30,
            "max_activity_interval_minutes": 240,
            "activity_randomization_factor": 0.3,
            "high_risk_threshold": 0.7,
            "medium_risk_threshold": 0.4,
            "fingerprint_rotation_days": 7,
            "ip_rotation_enabled": True,
            "browser_fingerprint_rotation_enabled": True,
            "behavior_randomization_enabled": True,
            "proxy_providers": []
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    # Merge user config with defaults
                    for key, value in user_config.items():
                        default_config[key] = value
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def _load_identities(self) -> None:
        """Load identities from storage."""
        identities_file = os.path.join(self.config["data_storage_path"], "identities.json")
        
        if os.path.exists(identities_file):
            try:
                with open(identities_file, 'r') as f:
                    self.identities = json.load(f)
                logger.info(f"Loaded {len(self.identities)} identities")
            except Exception as e:
                logger.error(f"Error loading identities: {e}")
                self.identities = {}
    
    def _save_identities(self) -> None:
        """Save identities to storage."""
        identities_file = os.path.join(self.config["data_storage_path"], "identities.json")
        
        try:
            with open(identities_file, 'w') as f:
                json.dump(self.identities, f, indent=2)
            logger.info(f"Saved {len(self.identities)} identities")
        except Exception as e:
            logger.error(f"Error saving identities: {e}")
    
    def _load_activity_logs(self) -> None:
        """Load activity logs from storage."""
        logs_file = os.path.join(self.config["data_storage_path"], "activity_logs.json")
        
        if os.path.exists(logs_file):
            try:
                with open(logs_file, 'r') as f:
                    self.activity_logs = json.load(f)
                logger.info(f"Loaded activity logs for {len(self.activity_logs)} identities")
            except Exception as e:
                logger.error(f"Error loading activity logs: {e}")
                self.activity_logs = {}
    
    def _save_activity_logs(self) -> None:
        """Save activity logs to storage."""
        logs_file = os.path.join(self.config["data_storage_path"], "activity_logs.json")
        
        try:
            with open(logs_file, 'w') as f:
                json.dump(self.activity_logs, f, indent=2)
            logger.info(f"Saved activity logs for {len(self.activity_logs)} identities")
        except Exception as e:
            logger.error(f"Error saving activity logs: {e}")
    
    def start(self) -> bool:
        """
        Start the Sybil Defense Agent.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            # Perform initial risk assessment
            self._assess_all_risks()
            
            self.active = True
            logger.info("Sybil Defense Agent started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start Sybil Defense Agent: {e}")
            return False
    
    def stop(self) -> bool:
        """
        Stop the Sybil Defense Agent.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        try:
            # Save current state
            self._save_identities()
            self._save_activity_logs()
            
            self.active = False
            logger.info("Sybil Defense Agent stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Error stopping Sybil Defense Agent: {e}")
            return False
    
    def create_identity(self, project_id: str, identity_type: str = "default") -> Optional[Dict[str, Any]]:
        """
        Create a new identity for a project.
        
        Args:
            project_id: Project ID
            identity_type: Type of identity to create
            
        Returns:
            Dict containing identity data or None if creation failed
        """
        try:
            # Check if we've reached the maximum number of identities for this project
            project_identities = [id_data for id_data in self.identities.values() 
                                if id_data["project_id"] == project_id]
            
            if len(project_identities) >= self.config["max_identities_per_project"]:
                logger.warning(f"Maximum number of identities reached for project {project_id}")
                return None
            
            # Generate identity ID
            identity_id = hashlib.sha256(f"{project_id}_{identity_type}_{time.time()}_{random.random()}".encode()).hexdigest()[:16]
            
            # Create identity data
            identity_data = {
                "id": identity_id,
                "project_id": project_id,
                "type": identity_type,
                "created_at": datetime.now().isoformat(),
                "last_used": None,
                "usage_count": 0,
                "status": "active",
                "risk_level": "low",
                "fingerprint": self._generate_fingerprint(),
                "proxy": self._assign_proxy()
            }
            
            # Add identity-type specific data
            if identity_type == "social":
                identity_data["social_accounts"] = {
                    "twitter": None,
                    "discord": None,
                    "telegram": None
                }
            elif identity_type == "wallet":
                identity_data["wallet_addresses"] = {}
            
            # Store identity
            self.identities[identity_id] = identity_data
            
            # Initialize activity log
            self.activity_logs[identity_id] = []
            
            # Save identities
            self._save_identities()
            
            logger.info(f"Created identity {identity_id} for project {project_id}")
            return identity_data
        except Exception as e:
            logger.error(f"Error creating identity for project {project_id}: {e}")
            return None
    
    def _generate_fingerprint(self) -> Dict[str, Any]:
        """
        Generate a browser fingerprint.
        
        Returns:
            Dict containing fingerprint data
        """
        # Generate random user agent
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
        ]
        
        # Generate random screen resolution
        screen_resolutions = [
            "1920x1080",
            "1366x768",
            "1440x900",
            "1536x864",
            "2560x1440",
            "1280x720"
        ]
        
        # Generate random time zone
        time_zones = [
            "America/New_York",
            "America/Los_Angeles",
            "America/Chicago",
            "Europe/London",
            "Europe/Berlin",
            "Asia/Tokyo",
            "Asia/Singapore",
            "Australia/Sydney"
        ]
        
        # Generate random language
        languages = [
            "en-US",
            "en-GB",
            "fr-FR",
            "de-DE",
            "es-ES",
            "it-IT",
            "ja-JP",
            "ko-KR",
            "zh-CN"
        ]
        
        return {
            "user_agent": random.choice(user_agents),
            "screen_resolution": random.choice(screen_resolutions),
            "color_depth": random.choice([24, 32]),
            "time_zone": random.choice(time_zones),
            "language": random.choice(languages),
            "platform": random.choice(["Win32", "MacIntel", "Linux x86_64"]),
            "do_not_track": random.choice([None, "1", "0"]),
            "plugins": random.randint(3, 10),
            "fonts": random.randint(10, 30),
            "canvas_hash": hashlib.md5(str(random.random()).encode()).hexdigest(),
            "webgl_hash": hashlib.md5(str(random.random()).encode()).hexdigest(),
            "audio_hash": hashlib.md5(str(random.random()).encode()).hexdigest(),
            "created_at": datetime.now().isoformat(),
            "last_rotated": None
        }
    
    def _assign_proxy(self) -> Optional[Dict[str, Any]]:
        """
        Assign a proxy to an identity.
        
        Returns:
            Dict containing proxy data or None if no proxy available
        """
        if not self.config["ip_rotation_enabled"] or not self.config["proxy_providers"]:
            return None
        
        # This is a placeholder for actual proxy assignment logic
        # In a real implementation, this would interact with proxy providers
        
        return {
            "type": "http",
            "host": f"proxy{random.randint(1, 100)}.example.com",
            "port": random.choice([8080, 3128, 8118]),
            "username": f"user{random.randint(1, 1000)}",
            "password": f"pass{random.randint(1000, 9999)}",
            "country": random.choice(["US", "UK", "DE", "JP", "SG", "CA"]),
            "last_rotated": datetime.now().isoformat()
        }
    
    def get_identity(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get an identity by ID.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            Dict containing identity data or None if not found
        """
        if identity_id not in self.identities:
            logger.warning(f"Identity not found: {identity_id}")
            return None
        
        return self.identities[identity_id]
    
    def get_project_identities(self, project_id: str) -> List[Dict[str, Any]]:
        """
        Get all identities for a project.
        
        Args:
            project_id: Project ID
            
        Returns:
            List of identity data
        """
        return [id_data for id_data in self.identities.values() if id_data["project_id"] == project_id]
    
    def select_identity(self, project_id: str, identity_type: Optional[str] = None, risk_level: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Select an appropriate identity for a task.
        
        Args:
            project_id: Project ID
            identity_type: Optional type of identity to select
            risk_level: Optional maximum risk level to allow
            
        Returns:
            Dict containing identity data or None if no suitable identity found
        """
        try:
            # Get all identities for the project
            project_identities = self.get_project_identities(project_id)
            
            if not project_identities:
                logger.warning(f"No identities found for project {project_id}")
                return None
            
            # Filter by type if specified
            if identity_type:
                project_identities = [id_data for id_data in project_identities if id_data["type"] == identity_type]
                
                if not project_identities:
                    logger.warning(f"No identities of type {identity_type} found for project {project_id}")
                    return None
            
            # Filter by risk level if specified
            if risk_level:
                risk_levels = {"low": 0, "medium": 1, "high": 2}
                max_risk = risk_levels.get(risk_level.lower(), 2)
                
                project_identities = [id_data for id_data in project_identities 
                                    if risk_levels.get(id_data["risk_level"].lower(), 0) <= max_risk]
                
                if not project_identities:
                    logger.warning(f"No identities with risk level <= {risk_level} found for project {project_id}")
                    return None
            
            # Filter out inactive identities
            project_identities = [id_data for id_data in project_identities if id_data["status"] == "active"]
            
            if not project_identities:
                logger.warning(f"No active identities found for project {project_id}")
                return None
            
            # Select the least recently used identity
            selected_identity = min(project_identities, 
                                   key=lambda x: (x["usage_count"], x["last_used"] or "0000-00-00"))
            
            return selected_identity
        except Exception as e:
            logger.error(f"Error selecting identity for project {project_id}: {e}")
            return None
    
    def use_identity(self, identity_id: str, activity_type: str, details: Optional[Dict[str, Any]] = None) -> bool:
        """
        Record usage of an identity.
        
        Args:
            identity_id: Identity ID
            activity_type: Type of activity performed
            details: Optional details about the activity
            
        Returns:
            bool: True if recorded successfully, False otherwise
        """
        if identity_id not in self.identities:
            logger.warning(f"Identity not found: {identity_id}")
            return False
        
        try:
            # Update identity usage
            identity = self.identities[identity_id]
            identity["last_used"] = datetime.now().isoformat()
            identity["usage_count"] += 1
            
            # Record activity
            activity = {
                "timestamp": datetime.now().isoformat(),
                "type": activity_type,
                "details": details or {}
            }
            
            if identity_id not in self.activity_logs:
                self.activity_logs[identity_id] = []
            
            self.activity_logs[identity_id].append(activity)
            
            # Save periodically
            if identity["usage_count"] % 10 == 0:
                self._save_identities()
                self._save_activity_logs()
            
            # Assess risk after activity
            self._assess_identity_risk(identity_id)
            
            logger.info(f"Recorded {activity_type} activity for identity {identity_id}")
            return True
        except Exception as e:
            logger.error(f"Error recording activity for identity {identity_id}: {e}")
            return False
    
    def _assess_identity_risk(self, identity_id: str) -> str:
        """
        Assess the risk level of an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            str: Risk level (low, medium, high)
        """
        if identity_id not in self.identities:
            logger.warning(f"Identity not found: {identity_id}")
            return "high"
        
        try:
            identity = self.identities[identity_id]
            
            # Get activity logs
            logs = self.activity_logs.get(identity_id, [])
            
            # Calculate risk factors
            risk_score = 0.0
            
            # Factor 1: Activity frequency
            if logs:
                # Calculate average time between activities
                timestamps = [datetime.fromisoformat(log["timestamp"]) for log in logs]
                timestamps.sort()
                
                if len(timestamps) > 1:
                    time_diffs = [(timestamps[i] - timestamps[i-1]).total_seconds() / 60 
                                for i in range(1, len(timestamps))]
                    avg_time_diff = sum(time_diffs) / len(time_diffs)
                    
                    # If average time is too short, increase risk
                    if avg_time_diff < self.config["min_activity_interval_minutes"]:
                        risk_score += 0.3
                    
                    # Check for suspicious patterns (too regular)
                    if time_diffs:
                        std_dev = (sum((x - avg_time_diff) ** 2 for x in time_diffs) / len(time_diffs)) ** 0.5
                        if std_dev < avg_time_diff * 0.1:  # Too regular
                            risk_score += 0.2
            
            # Factor 2: Usage count
            if identity["usage_count"] > 100:
                risk_score += 0.2
            elif identity["usage_count"] > 50:
                risk_score += 0.1
            
            # Factor 3: Age of identity
            if identity["created_at"]:
                created_at = datetime.fromisoformat(identity["created_at"])
                age_days = (datetime.now() - created_at).days
                
                if age_days < 1:
                    risk_score += 0.2
                elif age_days < 7:
                    risk_score += 0.1
            
            # Factor 4: Fingerprint age
            if identity["fingerprint"] and identity["fingerprint"].get("last_rotated"):
                last_rotated = datetime.fromisoformat(identity["fingerprint"]["last_rotated"])
                rotation_age_days = (datetime.now() - last_rotated).days
                
                if rotation_age_days > self.config["fingerprint_rotation_days"]:
                    risk_score += 0.2
            
            # Determine risk level
            if risk_score >= self.config["high_risk_threshold"]:
                risk_level = "high"
            elif risk_score >= self.config["medium_risk_threshold"]:
                risk_level = "medium"
            else:
                risk_level = "low"
            
            # Update identity risk level
            identity["risk_level"] = risk_level
            
            # Take action based on risk level
            if risk_level == "high":
                self._mitigate_high_risk(identity_id)
            elif risk_level == "medium":
                self._mitigate_medium_risk(identity_id)
            
            logger.info(f"Assessed risk for identity {identity_id}: {risk_level} (score: {risk_score:.2f})")
            return risk_level
        except Exception as e:
            logger.error(f"Error assessing risk for identity {identity_id}: {e}")
            return "high"
    
    def _assess_all_risks(self) -> None:
        """Assess risk levels for all identities."""
        for identity_id in self.identities:
            self._assess_identity_risk(identity_id)
    
    def _mitigate_high_risk(self, identity_id: str) -> None:
        """
        Apply mitigation measures for high-risk identity.
        
        Args:
            identity_id: Identity ID
        """
        if identity_id not in self.identities:
            return
        
        identity = self.identities[identity_id]
        
        # Rotate fingerprint
        if self.config["browser_fingerprint_rotation_enabled"]:
            identity["fingerprint"] = self._generate_fingerprint()
            identity["fingerprint"]["last_rotated"] = datetime.now().isoformat()
        
        # Rotate proxy
        if self.config["ip_rotation_enabled"]:
            identity["proxy"] = self._assign_proxy()
        
        # Reduce activity frequency
        # This is implemented by setting a cooldown period
        identity["cooldown_until"] = (datetime.now() + timedelta(hours=random.randint(12, 24))).isoformat()
        
        logger.info(f"Applied high-risk mitigation for identity {identity_id}")
    
    def _mitigate_medium_risk(self, identity_id: str) -> None:
        """
        Apply mitigation measures for medium-risk identity.
        
        Args:
            identity_id: Identity ID
        """
        if identity_id not in self.identities:
            return
        
        identity = self.identities[identity_id]
        
        # Rotate fingerprint if it's old
        if self.config["browser_fingerprint_rotation_enabled"]:
            if identity["fingerprint"].get("last_rotated"):
                last_rotated = datetime.fromisoformat(identity["fingerprint"]["last_rotated"])
                rotation_age_days = (datetime.now() - last_rotated).days
                
                if rotation_age_days > self.config["fingerprint_rotation_days"] / 2:
                    identity["fingerprint"] = self._generate_fingerprint()
                    identity["fingerprint"]["last_rotated"] = datetime.now().isoformat()
        
        # Rotate proxy if it's old
        if self.config["ip_rotation_enabled"] and identity["proxy"] and identity["proxy"].get("last_rotated"):
            last_rotated = datetime.fromisoformat(identity["proxy"]["last_rotated"])
            rotation_age_days = (datetime.now() - last_rotated).days
            
            if rotation_age_days > 3:  # Rotate proxy every 3 days for medium risk
                identity["proxy"] = self._assign_proxy()
        
        # Add some randomness to activity timing
        # This is implemented by setting a short cooldown
        identity["cooldown_until"] = (datetime.now() + timedelta(hours=random.randint(2, 6))).isoformat()
        
        logger.info(f"Applied medium-risk mitigation for identity {identity_id}")
    
    def can_use_identity(self, identity_id: str) -> bool:
        """
        Check if an identity can be used now.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            bool: True if the identity can be used, False otherwise
        """
        if identity_id not in self.identities:
            logger.warning(f"Identity not found: {identity_id}")
            return False
        
        identity = self.identities[identity_id]
        
        # Check if identity is active
        if identity["status"] != "active":
            return False
        
        # Check if identity is in cooldown
        if "cooldown_until" in identity and identity["cooldown_until"]:
            cooldown_until = datetime.fromisoformat(identity["cooldown_until"])
            if datetime.now() < cooldown_until:
                return False
        
        return True
    
    def get_next_activity_time(self, identity_id: str) -> Optional[datetime]:
        """
        Get the recommended time for the next activity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            datetime: Recommended time for next activity or None if not applicable
        """
        if identity_id not in self.identities:
            logger.warning(f"Identity not found: {identity_id}")
            return None
        
        identity = self.identities[identity_id]
        
        # Check if identity is in cooldown
        if "cooldown_until" in identity and identity["cooldown_until"]:
            cooldown_until = datetime.fromisoformat(identity["cooldown_until"])
            if datetime.now() < cooldown_until:
                return cooldown_until
        
        # Get last activity time
        if identity["last_used"]:
            last_used = datetime.fromisoformat(identity["last_used"])
        else:
            last_used = datetime.fromisoformat(identity["created_at"])
        
        # Calculate next activity time based on risk level
        min_interval = self.config["min_activity_interval_minutes"]
        max_interval = self.config["max_activity_interval_minutes"]
        
        if identity["risk_level"] == "high":
            # Longer interval for high risk
            interval_minutes = random.randint(max_interval // 2, max_interval)
        elif identity["risk_level"] == "medium":
            # Medium interval for medium risk
            interval_minutes = random.randint(min_interval * 2, max_interval // 2)
        else:
            # Shorter interval for low risk
            interval_minutes = random.randint(min_interval, min_interval * 2)
        
        # Add randomization
        randomization = self.config["activity_randomization_factor"]
        interval_minutes = int(interval_minutes * (1 + random.uniform(-randomization, randomization)))
        
        # Calculate next time
        next_time = last_used + timedelta(minutes=interval_minutes)
        
        return next_time
    
    def rotate_fingerprint(self, identity_id: str) -> bool:
        """
        Rotate the browser fingerprint for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            bool: True if rotated successfully, False otherwise
        """
        if identity_id not in self.identities:
            logger.warning(f"Identity not found: {identity_id}")
            return False
        
        try:
            identity = self.identities[identity_id]
            
            # Generate new fingerprint
            identity["fingerprint"] = self._generate_fingerprint()
            identity["fingerprint"]["last_rotated"] = datetime.now().isoformat()
            
            # Save identities
            self._save_identities()
            
            logger.info(f"Rotated fingerprint for identity {identity_id}")
            return True
        except Exception as e:
            logger.error(f"Error rotating fingerprint for identity {identity_id}: {e}")
            return False
    
    def rotate_proxy(self, identity_id: str) -> bool:
        """
        Rotate the proxy for an identity.
        
        Args:
            identity_id: Identity ID
            
        Returns:
            bool: True if rotated successfully, False otherwise
        """
        if identity_id not in self.identities:
            logger.warning(f"Identity not found: {identity_id}")
            return False
        
        if not self.config["ip_rotation_enabled"]:
            logger.warning("IP rotation is disabled")
            return False
        
        try:
            identity = self.identities[identity_id]
            
            # Assign new proxy
            identity["proxy"] = self._assign_proxy()
            
            # Save identities
            self._save_identities()
            
            logger.info(f"Rotated proxy for identity {identity_id}")
            return True
        except Exception as e:
            logger.error(f"Error rotating proxy for identity {identity_id}: {e}")
            return False
    
    def deactivate_identity(self, identity_id: str, reason: str) -> bool:
        """
        Deactivate an identity.
        
        Args:
            identity_id: Identity ID
            reason: Reason for deactivation
            
        Returns:
            bool: True if deactivated successfully, False otherwise
        """
        if identity_id not in self.identities:
            logger.warning(f"Identity not found: {identity_id}")
            return False
        
        try:
            identity = self.identities[identity_id]
            
            # Update status
            identity["status"] = "inactive"
            identity["deactivated_at"] = datetime.now().isoformat()
            identity["deactivation_reason"] = reason
            
            # Save identities
            self._save_identities()
            
            logger.info(f"Deactivated identity {identity_id}: {reason}")
            return True
        except Exception as e:
            logger.error(f"Error deactivating identity {identity_id}: {e}")
            return False
    
    def get_identity_activity(self, identity_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent activity for an identity.
        
        Args:
            identity_id: Identity ID
            limit: Maximum number of activities to return
            
        Returns:
            List of activity data
        """
        if identity_id not in self.activity_logs:
            return []
        
        # Sort by timestamp (newest first) and limit
        activities = sorted(self.activity_logs[identity_id], 
                           key=lambda x: x["timestamp"], 
                           reverse=True)
        
        return activities[:limit]
    
    def get_recommended_behavior(self, identity_id: str, activity_type: str) -> Dict[str, Any]:
        """
        Get recommended behavior parameters for an activity.
        
        Args:
            identity_id: Identity ID
            activity_type: Type of activity
            
        Returns:
            Dict containing behavior recommendations
        """
        if not self.config["behavior_randomization_enabled"]:
            return {}
        
        try:
            identity = self.get_identity(identity_id)
            
            if not identity:
                return {}
            
            # Base recommendations
            recommendations = {
                "timing": {
                    "delay_before_seconds": random.randint(1, 5),
                    "delay_after_seconds": random.randint(1, 5),
                    "typing_speed_variance": random.uniform(0.8, 1.2)
                },
                "mouse": {
                    "movement_style": random.choice(["direct", "natural", "hesitant"]),
                    "click_style": random.choice(["precise", "sloppy"]),
                    "scroll_style": random.choice(["smooth", "jumpy", "careful"])
                },
                "session": {
                    "max_duration_minutes": random.randint(10, 60),
                    "page_view_count": random.randint(3, 10)
                }
            }
            
            # Adjust based on activity type
            if activity_type == "registration":
                recommendations["timing"]["delay_before_seconds"] = random.randint(3, 10)
                recommendations["session"]["max_duration_minutes"] = random.randint(5, 15)
            elif activity_type == "engagement":
                recommendations["timing"]["typing_speed_variance"] = random.uniform(0.9, 1.1)
                recommendations["session"]["max_duration_minutes"] = random.randint(5, 30)
            elif activity_type == "browsing":
                recommendations["mouse"]["movement_style"] = random.choice(["natural", "curious"])
                recommendations["session"]["page_view_count"] = random.randint(5, 15)
            
            # Adjust based on risk level
            if identity["risk_level"] == "high":
                recommendations["timing"]["delay_before_seconds"] = random.randint(5, 15)
                recommendations["timing"]["delay_after_seconds"] = random.randint(5, 15)
                recommendations["session"]["max_duration_minutes"] = random.randint(3, 10)
            
            return recommendations
        except Exception as e:
            logger.error(f"Error getting behavior recommendations for identity {identity_id}: {e}")
            return {}
    
    def status(self) -> Dict[str, Any]:
        """
        Get the current status of the Sybil Defense Agent.
        
        Returns:
            Dict containing status information
        """
        # Count identities by risk level
        risk_counts = {"low": 0, "medium": 0, "high": 0}
        status_counts = {"active": 0, "inactive": 0}
        
        for identity in self.identities.values():
            risk_level = identity.get("risk_level", "low")
            status = identity.get("status", "active")
            
            if risk_level in risk_counts:
                risk_counts[risk_level] += 1
            
            if status in status_counts:
                status_counts[status] += 1
        
        return {
            "active": self.active,
            "identity_count": len(self.identities),
            "risk_levels": risk_counts,
            "status_counts": status_counts,
            "fingerprint_rotation_enabled": self.config["browser_fingerprint_rotation_enabled"],
            "ip_rotation_enabled": self.config["ip_rotation_enabled"],
            "behavior_randomization_enabled": self.config["behavior_randomization_enabled"]
        }


if __name__ == "__main__":
    # Simple test
    agent = SybilDefenseAgent()
    agent.start()
    
    # Create a test identity
    project_id = "test-project-123"
    identity = agent.create_identity(project_id, "social")
    
    if identity:
        identity_id = identity["id"]
        
        # Record some activity
        agent.use_identity(identity_id, "login", {"platform": "twitter"})
        time.sleep(1)
        agent.use_identity(identity_id, "post", {"platform": "twitter", "content_type": "text"})
        time.sleep(1)
        agent.use_identity(identity_id, "engagement", {"platform": "twitter", "action": "like"})
        
        # Get activity
        activities = agent.get_identity_activity(identity_id)
        print(f"Activities for identity {identity_id}:")
        for activity in activities:
            print(f"- {activity['timestamp']}: {activity['type']}")
        
        # Get behavior recommendations
        recommendations = agent.get_recommended_behavior(identity_id, "engagement")
        print(f"Behavior recommendations: {recommendations}")
        
        # Get next activity time
        next_time = agent.get_next_activity_time(identity_id)
        if next_time:
            print(f"Next recommended activity time: {next_time}")
    
    # Print status
    print(agent.status())
    
    agent.stop()