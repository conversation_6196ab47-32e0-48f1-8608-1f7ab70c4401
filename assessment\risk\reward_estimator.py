"""
收益估算器

该模块实现了估算项目潜在收益的功能。
"""

import logging
import time
import math
from typing import Dict, List, Any, Optional

from discovery.models.project import Project


class RewardEstimator:
    """收益估算器，负责估算项目潜在收益"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化收益估算器
        
        Args:
            config: 配置字典，包含收益估算器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.weights = config.get('weights', {
            'token_value': 0.3,
            'project_quality': 0.2,
            'community_size': 0.15,
            'requirements_difficulty': 0.1,
            'competition_level': 0.15,
            'historical_performance': 0.1
        })
        
        self.token_value_multiplier = config.get('token_value_multiplier', 1.0)
        self.min_reward = config.get('min_reward', 0.0)
        self.max_reward = config.get('max_reward', 100.0)
        
        # 统计信息
        self._stats = {
            'total_estimations': 0,
            'high_reward': 0,
            'medium_reward': 0,
            'low_reward': 0
        }
    
    def estimate(self, project: Project) -> float:
        """
        估算项目潜在收益
        
        Args:
            project: 项目对象
            
        Returns:
            潜在收益分数 (0-100)，分数越高收益越大
        """
        self.logger.info(f"开始估算项目 {project.name} (ID: {project.id}) 的潜在收益")
        
        try:
            # 更新统计信息
            self._stats['total_estimations'] += 1
            
            # 计算各部分收益分数
            token_value_score = self._calculate_token_value_score(project)
            project_quality_score = self._calculate_project_quality_score(project)
            community_size_score = self._calculate_community_size_score(project)
            requirements_difficulty_score = self._calculate_requirements_difficulty_score(project)
            competition_level_score = self._calculate_competition_level_score(project)
            historical_performance_score = self._calculate_historical_performance_score(project)
            
            # 计算加权收益分数
            reward_score = (
                token_value_score * self.weights.get('token_value', 0.3) +
                project_quality_score * self.weights.get('project_quality', 0.2) +
                community_size_score * self.weights.get('community_size', 0.15) +
                requirements_difficulty_score * self.weights.get('requirements_difficulty', 0.1) +
                competition_level_score * self.weights.get('competition_level', 0.15) +
                historical_performance_score * self.weights.get('historical_performance', 0.1)
            )
            
            # 确保分数在min_reward-max_reward范围内
            reward_score = max(self.min_reward, min(self.max_reward, reward_score))
            
            # 更新收益统计
            if reward_score >= 70:
                self._stats['high_reward'] += 1
            elif reward_score >= 40:
                self._stats['medium_reward'] += 1
            else:
                self._stats['low_reward'] += 1
            
            self.logger.info(f"项目 {project.name} 潜在收益估算完成: {reward_score:.2f}")
            
            return reward_score
        
        except Exception as e:
            self.logger.error(f"估算项目 {project.name} 潜在收益时出错: {str(e)}")
            return 50.0  # 出错时返回中等收益
    
    def _calculate_token_value_score(self, project: Project) -> float:
        """
        计算代币价值分数
        
        Args:
            project: 项目对象
            
        Returns:
            代币价值分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 检查代币信息
        if not project.token_info:
            return score
        
        # 根据代币估计价值调整分数
        estimated_value = project.token_info.estimated_value
        if estimated_value is not None:
            # 应用乘数
            adjusted_value = estimated_value * self.token_value_multiplier
            
            # 使用对数函数映射价值到分数
            # 假设价值在0-1000美元范围内
            if adjusted_value > 0:
                value_score = min(100, 20 * math.log10(adjusted_value + 1))
                score = value_score
        
        # 根据空投数量调整分数
        airdrop_amount = project.token_info.airdrop_amount
        if airdrop_amount is not None and airdrop_amount > 0:
            # 假设空投数量在0-1000000范围内
            amount_score = min(30, 5 * math.log10(airdrop_amount + 1))
            score += amount_score
        
        # 根据代币标准调整分数
        if project.token_info.blockchain:
            blockchain = project.token_info.blockchain.lower()
            
            if blockchain in ['ethereum', 'arbitrum', 'optimism', 'base']:
                score += 10.0
            elif blockchain in ['binance', 'polygon']:
                score += 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_project_quality_score(self, project: Project) -> float:
        """
        计算项目质量分数
        
        Args:
            project: 项目对象
            
        Returns:
            项目质量分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 根据项目类型调整分数
        project_type = project.project_type.value
        
        if project_type == 'airdrop':
            score += 10.0
        elif project_type == 'testnet':
            score += 5.0
        
        # 根据项目描述长度调整分数
        description_length = len(project.description) if project.description else 0
        if description_length > 1000:
            score += 10.0
        elif description_length > 500:
            score += 5.0
        elif description_length < 100:
            score -= 10.0
        
        # 根据项目标签调整分数
        for tag in project.tags:
            if tag in ['high_quality', 'promising', 'innovative']:
                score += 5.0
            elif tag in ['suspicious', 'high_risk', 'scam']:
                score -= 20.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_community_size_score(self, project: Project) -> float:
        """
        计算社区规模分数
        
        Args:
            project: 项目对象
            
        Returns:
            社区规模分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 计算总粉丝数
        total_followers = 0
        
        for channel in project.social_channels:
            if channel.followers is not None:
                total_followers += channel.followers
        
        # 根据总粉丝数调整分数
        if total_followers > 100000:
            score += 30.0
        elif total_followers > 50000:
            score += 25.0
        elif total_followers > 10000:
            score += 20.0
        elif total_followers > 5000:
            score += 15.0
        elif total_followers > 1000:
            score += 10.0
        elif total_followers < 100:
            score -= 10.0
        
        # 根据社交媒体渠道数量调整分数
        channel_count = len(project.social_channels)
        if channel_count >= 4:
            score += 10.0
        elif channel_count >= 3:
            score += 5.0
        elif channel_count <= 1:
            score -= 10.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_requirements_difficulty_score(self, project: Project) -> float:
        """
        计算参与要求难度分数
        
        Args:
            project: 项目对象
            
        Returns:
            参与要求难度分数 (0-100)，分数越高表示要求越简单
        """
        # 初始分数
        score = 50.0
        
        # 获取参与要求
        requirements = project.requirements
        
        if not requirements:
            # 没有要求，简单
            return 80.0
        
        # 计算要求数量
        req_count = len(requirements)
        
        # 根据要求数量调整分数
        if req_count > 10:
            score -= 30.0
        elif req_count > 5:
            score -= 20.0
        elif req_count > 3:
            score -= 10.0
        elif req_count <= 1:
            score += 20.0
        
        # 计算难度级别
        difficulty_levels = {'easy': 0, 'medium': 0, 'hard': 0}
        
        for req in requirements:
            difficulty = req.difficulty
            if difficulty:
                difficulty = difficulty.lower()
                if difficulty in difficulty_levels:
                    difficulty_levels[difficulty] += 1
        
        # 根据难度级别调整分数
        score -= difficulty_levels['hard'] * 10.0
        score -= difficulty_levels['medium'] * 5.0
        score += difficulty_levels['easy'] * 2.0
        
        # 计算估计完成时间
        total_time = 0
        time_count = 0
        
        for req in requirements:
            if req.estimated_time is not None:
                total_time += req.estimated_time
                time_count += 1
        
        # 根据估计完成时间调整分数
        if time_count > 0:
            avg_time = total_time / time_count
            
            if avg_time > 120:  # 超过2小时
                score -= 20.0
            elif avg_time > 60:  # 超过1小时
                score -= 10.0
            elif avg_time < 15:  # 少于15分钟
                score += 10.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_competition_level_score(self, project: Project) -> float:
        """
        计算竞争级别分数
        
        Args:
            project: 项目对象
            
        Returns:
            竞争级别分数 (0-100)，分数越高表示竞争越小
        """
        # 初始分数
        score = 50.0
        
        # 根据项目发现时间调整分数
        # 假设越早发现的项目竞争越小
        current_time = time.time()
        discovery_time = project.discovery_time
        
        if discovery_time:
            time_diff = current_time - discovery_time
            
            if time_diff < 3600:  # 1小时内
                score += 30.0
            elif time_diff < 86400:  # 1天内
                score += 20.0
            elif time_diff < 259200:  # 3天内
                score += 10.0
            elif time_diff > 2592000:  # 30天以上
                score -= 20.0
        
        # 根据项目类型调整分数
        project_type = project.project_type.value
        
        if project_type == 'testnet':
            score += 10.0  # 测试网项目通常竞争较小
        
        # 根据区块链平台调整分数
        blockchain = project.blockchain.value
        
        if blockchain in ['arbitrum', 'optimism', 'base']:
            score += 10.0  # 较新的L2平台竞争较小
        elif blockchain in ['ethereum']:
            score -= 10.0  # 以太坊主网竞争激烈
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_historical_performance_score(self, project: Project) -> float:
        """
        计算历史表现分数
        
        Args:
            project: 项目对象
            
        Returns:
            历史表现分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 根据项目标签调整分数
        for tag in project.tags:
            if tag in ['successful_airdrop', 'high_return']:
                score += 20.0
            elif tag in ['low_return', 'failed_airdrop']:
                score -= 20.0
        
        # 根据项目备注调整分数
        notes = project.notes.lower() if project.notes else ''
        
        if 'successful' in notes or 'high return' in notes:
            score += 10.0
        elif 'failed' in notes or 'low return' in notes:
            score -= 10.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()下
"""
收益估算器

该模块实现了估算项目潜在收益的功能。
"""

import logging
import time
import math
from typing import Dict, List, Any, Optional

from discovery.models.project import Project


class RewardEstimator:
    """收益估算器，负责估算项目潜在收益"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化收益估算器
        
        Args:
            config: 配置字典，包含收益估算器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.weights = config.get('weights', {
            'token_value': 0.3,
            'project_quality': 0.2,
            'community_size': 0.15,
            'requirements_difficulty': 0.1,
            'competition_level': 0.15,
            'historical_performance': 0.1
        })
        
        self.token_value_multiplier = config.get('token_value_multiplier', 1.0)
        self.min_reward = config.get('min_reward', 0.0)
        self.max_reward = config.get('max_reward', 100.0)
        
        # 统计信息
        self._stats = {
            'total_estimations': 0,
            'high_reward': 0,
            'medium_reward': 0,
            'low_reward': 0
        }
    
    def estimate(self, project: Project) -> float:
        """
        估算项目潜在收益
        
        Args:
            project: 项目对象
            
        Returns:
            潜在收益分数 (0-100)，分数越高收益越大
        """
        self.logger.info(f"开始估算项目 {project.name} (ID: {project.id}) 的潜在收益")
        
        try:
            # 更新统计信息
            self._stats['total_estimations'] += 1
            
            # 计算各部分收益分数
            token_value_score = self._calculate_token_value_score(project)
            project_quality_score = self._calculate_project_quality_score(project)
            community_size_score = self._calculate_community_size_score(project)
            requirements_difficulty_score = self._calculate_requirements_difficulty_score(project)
            competition_level_score = self._calculate_competition_level_score(project)
            historical_performance_score = self._calculate_historical_performance_score(project)
            
            # 计算加权收益分数
            reward_score = (
                token_value_score * self.weights.get('token_value', 0.3) +
                project_quality_score * self.weights.get('project_quality', 0.2) +
                community_size_score * self.weights.get('community_size', 0.15) +
                requirements_difficulty_score * self.weights.get('requirements_difficulty', 0.1) +
                competition_level_score * self.weights.get('competition_level', 0.15) +
                historical_performance_score * self.weights.get('historical_performance', 0.1)
            )
            
            # 确保分数在min_reward-max_reward范围内
            reward_score = max(self.min_reward, min(self.max_reward, reward_score))
            
            # 更新收益统计
            if reward_score >= 70:
                self._stats['high_reward'] += 1
            elif reward_score >= 40:
                self._stats['medium_reward'] += 1
            else:
                self._stats['low_reward'] += 1
            
            self.logger.info(f"项目 {project.name} 潜在收益估算完成: {reward_score:.2f}")
            
            return reward_score
        
        except Exception as e:
            self.logger.error(f"估算项目 {project.name} 潜在收益时出错: {str(e)}")
            return 50.0  # 出错时返回中等收益
    
    def _calculate_token_value_score(self, project: Project) -> float:
        """
        计算代币价值分数
        
        Args:
            project: 项目对象
            
        Returns:
            代币价值分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 检查代币信息
        if not project.token_info:
            return score
        
        # 根据代币估计价值调整分数
        estimated_value = project.token_info.estimated_value
        if estimated_value is not None:
            # 应用乘数
            adjusted_value = estimated_value * self.token_value_multiplier
            
            # 使用对数函数映射价值到分数
            # 假设价值在0-1000美元范围内
            if adjusted_value > 0:
                value_score = min(100, 20 * math.log10(adjusted_value + 1))
                score = value_score
        
        # 根据空投数量调整分数
        airdrop_amount = project.token_info.airdrop_amount
        if airdrop_amount is not None and airdrop_amount > 0:
            # 假设空投数量在0-1000000范围内
            amount_score = min(30, 5 * math.log10(airdrop_amount + 1))
            score += amount_score
        
        # 根据代币标准调整分数
        if project.token_info.blockchain:
            blockchain = project.token_info.blockchain.lower()
            
            if blockchain in ['ethereum', 'arbitrum', 'optimism', 'base']:
                score += 10.0
            elif blockchain in ['binance', 'polygon']:
                score += 5.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_project_quality_score(self, project: Project) -> float:
        """
        计算项目质量分数
        
        Args:
            project: 项目对象
            
        Returns:
            项目质量分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 根据项目类型调整分数
        project_type = project.project_type.value
        
        if project_type == 'airdrop':
            score += 10.0
        elif project_type == 'testnet':
            score += 5.0
        
        # 根据项目描述长度调整分数
        description_length = len(project.description) if project.description else 0
        if description_length > 1000:
            score += 10.0
        elif description_length > 500:
            score += 5.0
        elif description_length < 100:
            score -= 10.0
        
        # 根据项目标签调整分数
        for tag in project.tags:
            if tag in ['high_quality', 'promising', 'innovative']:
                score += 5.0
            elif tag in ['suspicious', 'high_risk', 'scam']:
                score -= 20.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_community_size_score(self, project: Project) -> float:
        """
        计算社区规模分数
        
        Args:
            project: 项目对象
            
        Returns:
            社区规模分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 计算总粉丝数
        total_followers = 0
        
        for channel in project.social_channels:
            if channel.followers is not None:
                total_followers += channel.followers
        
        # 根据总粉丝数调整分数
        if total_followers > 100000:
            score += 30.0
        elif total_followers > 50000:
            score += 25.0
        elif total_followers > 10000:
            score += 20.0
        elif total_followers > 5000:
            score += 15.0
        elif total_followers > 1000:
            score += 10.0
        elif total_followers < 100:
            score -= 10.0
        
        # 根据社交媒体渠道数量调整分数
        channel_count = len(project.social_channels)
        if channel_count >= 4:
            score += 10.0
        elif channel_count >= 3:
            score += 5.0
        elif channel_count <= 1:
            score -= 10.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_requirements_difficulty_score(self, project: Project) -> float:
        """
        计算参与要求难度分数
        
        Args:
            project: 项目对象
            
        Returns:
            参与要求难度分数 (0-100)，分数越高表示要求越简单
        """
        # 初始分数
        score = 50.0
        
        # 获取参与要求
        requirements = project.requirements
        
        if not requirements:
            # 没有要求，简单
            return 80.0
        
        # 计算要求数量
        req_count = len(requirements)
        
        # 根据要求数量调整分数
        if req_count > 10:
            score -= 30.0
        elif req_count > 5:
            score -= 20.0
        elif req_count > 3:
            score -= 10.0
        elif req_count <= 1:
            score += 20.0
        
        # 计算难度级别
        difficulty_levels = {'easy': 0, 'medium': 0, 'hard': 0}
        
        for req in requirements:
            difficulty = req.difficulty
            if difficulty:
                difficulty = difficulty.lower()
                if difficulty in difficulty_levels:
                    difficulty_levels[difficulty] += 1
        
        # 根据难度级别调整分数
        score -= difficulty_levels['hard'] * 10.0
        score -= difficulty_levels['medium'] * 5.0
        score += difficulty_levels['easy'] * 2.0
        
        # 计算估计完成时间
        total_time = 0
        time_count = 0
        
        for req in requirements:
            if req.estimated_time is not None:
                total_time += req.estimated_time
                time_count += 1
        
        # 根据估计完成时间调整分数
        if time_count > 0:
            avg_time = total_time / time_count
            
            if avg_time > 120:  # 超过2小时
                score -= 20.0
            elif avg_time > 60:  # 超过1小时
                score -= 10.0
            elif avg_time < 15:  # 少于15分钟
                score += 10.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_competition_level_score(self, project: Project) -> float:
        """
        计算竞争级别分数
        
        Args:
            project: 项目对象
            
        Returns:
            竞争级别分数 (0-100)，分数越高表示竞争越小
        """
        # 初始分数
        score = 50.0
        
        # 根据项目发现时间调整分数
        # 假设越早发现的项目竞争越小
        current_time = time.time()
        discovery_time = project.discovery_time
        
        if discovery_time:
            time_diff = current_time - discovery_time
            
            if time_diff < 3600:  # 1小时内
                score += 30.0
            elif time_diff < 86400:  # 1天内
                score += 20.0
            elif time_diff < 259200:  # 3天内
                score += 10.0
            elif time_diff > 2592000:  # 30天以上
                score -= 20.0
        
        # 根据项目类型调整分数
        project_type = project.project_type.value
        
        if project_type == 'testnet':
            score += 10.0  # 测试网项目通常竞争较小
        
        # 根据区块链平台调整分数
        blockchain = project.blockchain.value
        
        if blockchain in ['arbitrum', 'optimism', 'base']:
            score += 10.0  # 较新的L2平台竞争较小
        elif blockchain in ['ethereum']:
            score -= 10.0  # 以太坊主网竞争激烈
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _calculate_historical_performance_score(self, project: Project) -> float:
        """
        计算历史表现分数
        
        Args:
            project: 项目对象
            
        Returns:
            历史表现分数 (0-100)
        """
        # 初始分数
        score = 50.0
        
        # 根据项目标签调整分数
        for tag in project.tags:
            if tag in ['successful_airdrop', 'high_return']:
                score += 20.0
            elif tag in ['low_return', 'failed_airdrop']:
                score -= 20.0
        
        # 根据项目备注调整分数
        notes = project.notes.lower() if project.notes else ''
        
        if 'successful' in notes or 'high return' in notes:
            score += 10.0
        elif 'failed' in notes or 'low return' in notes:
            score -= 10.0
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()