"""
Browser management package

This package provides browser automation and management utilities.
"""

from .chrome_manager import ChromeManager
from .extension_manager import ExtensionManager
from .profile_manager import ProfileManager
from .automation_engine import AutomationEngine
from .page_analyzer import PageAnalyzer

__all__ = [
    'ChromeManager',
    'ExtensionManager',
    'ProfileManager',
    'AutomationEngine',
    'PageAnalyzer'
]
