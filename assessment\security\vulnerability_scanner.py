"""
漏洞扫描器

该模块实现了扫描智能合约漏洞的功能。
"""

import logging
import requests
import time
import re
import json
from typing import Dict, List, Any, Optional

from discovery.models.project import Project


class VulnerabilityScanner:
    """漏洞扫描器，负责扫描智能合约漏洞"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化漏洞扫描器
        
        Args:
            config: 配置字典，包含漏洞扫描器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.timeout = config.get('timeout', 30)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        self.mythril_enabled = config.get('mythril_enabled', False)
        self.slither_enabled = config.get('slither_enabled', False)
        self.mythx_api_key = config.get('mythx_api_key', '')
        self.mythx_enabled = config.get('mythx_enabled', False) and bool(self.mythx_api_key)
        
        # 漏洞模式
        self.vulnerability_patterns = {
            'reentrancy': {
                'pattern': r'(call\.value|send|transfer).*\(.*\).*\n.*[^;]',
                'description': '重入漏洞',
                'severity': 'critical'
            },
            'integer_overflow': {
                'pattern': r'(\+\+|\+=|=\s*\+|\*=|=\s*\*)',
                'description': '整数溢出',
                'severity': 'high'
            },
            'unchecked_external_call': {
                'pattern': r'(call|send|transfer).*\(.*\)(?!\s*returns|\s*revert)',
                'description': '未检查的外部调用',
                'severity': 'high'
            },
            'tx_origin': {
                'pattern': r'tx\.origin\s*==',
                'description': '使用tx.origin进行身份验证',
                'severity': 'high'
            },
            'timestamp_dependence': {
                'pattern': r'block\.timestamp',
                'description': '时间戳依赖',
                'severity': 'medium'
            },
            'assembly_usage': {
                'pattern': r'assembly\s*{',
                'description': '使用内联汇编',
                'severity': 'medium'
            },
            'deprecated_functions': {
                'pattern': r'(suicide|sha3|throw)',
                'description': '使用已弃用的函数',
                'severity': 'medium'
            },
            'multiple_sends': {
                'pattern': r'(send|transfer).*\n.*(send|transfer)',
                'description': '多次发送',
                'severity': 'medium'
            },
            'unprotected_functions': {
                'pattern': r'function\s+(\w+)\s*\([^)]*\)\s*public(?!\s+view|\s+pure|\s+constant)(?!\s+onlyOwner)(?!.*require\s*\(\s*msg\.sender\s*==)',
                'description': '未受保护的公共函数',
                'severity': 'low'
            }
        }
        
        # 统计信息
        self._stats = {
            'total_scans': 0,
            'successful_scans': 0,
            'failed_scans': 0,
            'critical_vulnerabilities': 0,
            'high_vulnerabilities': 0,
            'medium_vulnerabilities': 0,
            'low_vulnerabilities': 0
        }
    
    def scan(self, project: Project) -> Dict[str, Any]:
        """
        扫描项目智能合约漏洞
        
        Args:
            project: 要扫描的项目
            
        Returns:
            扫描结果字典
        """
        self.logger.info(f"开始扫描项目 {project.name} (ID: {project.id}) 的智能合约漏洞")
        
        try:
            # 更新统计信息
            self._stats['total_scans'] += 1
            
            # 初始化结果
            result = {
                'scanned': False,
                'contract_address': None,
                'blockchain': None,
                'vulnerabilities': [],
                'critical_vulnerabilities': 0,
                'high_vulnerabilities': 0,
                'medium_vulnerabilities': 0,
                'low_vulnerabilities': 0,
                'scan_time': time.time(),
                'warnings': []
            }
            
            # 获取合约地址和区块链
            if not project.token_info or not project.token_info.contract_address:
                self.logger.warning(f"项目 {project.name} 没有合约地址")
                result['warnings'].append("没有合约地址")
                self._stats['failed_scans'] += 1
                return result
            
            contract_address = project.token_info.contract_address
            blockchain = project.token_info.blockchain or project.blockchain.value
            
            result['contract_address'] = contract_address
            result['blockchain'] = blockchain
            
            # 获取合约源代码
            source_code = self._get_contract_source_code(contract_address, blockchain)
            
            if not source_code:
                self.logger.warning(f"无法获取合约源代码: {contract_address}")
                result['warnings'].append("无法获取合约源代码")
                self._stats['failed_scans'] += 1
                return result
            
            # 使用模式匹配扫描漏洞
            pattern_vulnerabilities = self._scan_with_patterns(source_code)
            result['vulnerabilities'].extend(pattern_vulnerabilities)
            
            # 使用Mythril扫描（如果启用）
            if self.mythril_enabled:
                mythril_vulnerabilities = self._scan_with_mythril(contract_address, blockchain)
                if mythril_vulnerabilities:
                    result['vulnerabilities'].extend(mythril_vulnerabilities)
            
            # 使用Slither扫描（如果启用）
            if self.slither_enabled:
                slither_vulnerabilities = self._scan_with_slither(source_code)
                if slither_vulnerabilities:
                    result['vulnerabilities'].extend(slither_vulnerabilities)
            
            # 使用MythX扫描（如果启用）
            if self.mythx_enabled:
                mythx_vulnerabilities = self._scan_with_mythx(contract_address, blockchain)
                if mythx_vulnerabilities:
                    result['vulnerabilities'].extend(mythx_vulnerabilities)
            
            # 统计漏洞数量
            for vulnerability in result['vulnerabilities']:
                severity = vulnerability.get('severity', 'low').lower()
                
                if severity == 'critical':
                    result['critical_vulnerabilities'] += 1
                    self._stats['critical_vulnerabilities'] += 1
                elif severity == 'high':
                    result['high_vulnerabilities'] += 1
                    self._stats['high_vulnerabilities'] += 1
                elif severity == 'medium':
                    result['medium_vulnerabilities'] += 1
                    self._stats['medium_vulnerabilities'] += 1
                elif severity == 'low':
                    result['low_vulnerabilities'] += 1
                    self._stats['low_vulnerabilities'] += 1
            
            # 更新扫描状态
            result['scanned'] = True
            self._stats['successful_scans'] += 1
            
            self.logger.info(f"项目 {project.name} 智能合约漏洞扫描完成，发现 {len(result['vulnerabilities'])} 个漏洞")
            
            return result
        
        except Exception as e:
            self.logger.error(f"扫描项目 {project.name} 智能合约漏洞时出错: {str(e)}")
            self._stats['failed_scans'] += 1
            
            return {
                'scanned': False,
                'error': str(e),
                'warnings': ["扫描过程中出错"]
            }
    
    def _get_contract_source_code(self, contract_address: str, blockchain: str) -> Optional[str]:
        """
        获取合约源代码
        
        Args:
            contract_address: 合约地址
            blockchain: 区块链平台
            
        Returns:
            合约源代码，如果无法获取则返回None
        """
        try:
            # 根据区块链选择API
            if blockchain == 'ethereum':
                api_key = self.config.get('etherscan_api_key', '')
                api_url = f"https://api.etherscan.io/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            elif blockchain == 'binance':
                api_key = self.config.get('bscscan_api_key', '')
                api_url = f"https://api.bscscan.com/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            elif blockchain == 'polygon':
                api_key = self.config.get('polygonscan_api_key', '')
                api_url = f"https://api.polygonscan.com/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            elif blockchain == 'arbitrum':
                api_key = self.config.get('arbiscan_api_key', '')
                api_url = f"https://api.arbiscan.io/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            elif blockchain == 'optimism':
                api_key = self.config.get('optimism_api_key', '')
                api_url = f"https://api-optimistic.etherscan.io/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            elif blockchain == 'base':
                api_key = self.config.get('basescan_api_key', '')
                api_url = f"https://api.basescan.org/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            else:
                self.logger.warning(f"不支持的区块链: {blockchain}")
                return None
            
            # 发送请求
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return None
            
            contract_data = data['result'][0]
            
            # 检查源代码是否存在
            if not contract_data.get('SourceCode'):
                return None
            
            return contract_data.get('SourceCode')
        
        except Exception as e:
            self.logger.error(f"获取合约源代码时出错: {str(e)}")
            return None
    
    def _scan_with_patterns(self, source_code: str) -> List[Dict[str, Any]]:
        """
        使用模式匹配扫描漏洞
        
        Args:
            source_code: 合约源代码
            
        Returns:
            漏洞列表
        """
        vulnerabilities = []
        
        # 扫描每种漏洞模式
        for vuln_id, vuln_info in self.vulnerability_patterns.items():
            pattern = vuln_info['pattern']
            description = vuln_info['description']
            severity = vuln_info['severity']
            
            # 查找匹配
            matches = re.finditer(pattern, source_code)
            
            for match in matches:
                # 获取匹配行号
                line_number = source_code[:match.start()].count('\n') + 1
                
                # 获取匹配代码
                lines = source_code.split('\n')
                code_context = '\n'.join(lines[max(0, line_number - 2):min(len(lines), line_number + 2)])
                
                # 添加漏洞
                vulnerabilities.append({
                    'id': vuln_id,
                    'description': description,
                    'severity': severity,
                    'line': line_number,
                    'code': code_context,
                    'detector': 'pattern_matching'
                })
        
        return vulnerabilities
    
    def _scan_with_mythril(self, contract_address: str, blockchain: str) -> Optional[List[Dict[str, Any]]]:
        """
        使用Mythril扫描漏洞
        
        Args:
            contract_address: 合约地址
            blockchain: 区块链平台
            
        Returns:
            漏洞列表，如果无法扫描则返回None
        """
        try:
            # 检查是否安装了Mythril
            import subprocess
            
            # 构建命令
            cmd = [
                'myth', 'analyze',
                '-a', contract_address,
                '--infura-id', self.config.get('infura_id', ''),
                '-o', 'json'
            ]
            
            # 执行命令
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = process.communicate(timeout=self.timeout)
            
            if process.returncode != 0:
                self.logger.warning(f"Mythril扫描失败: {stderr.decode('utf-8')}")
                return None
            
            # 解析结果
            result = json.loads(stdout.decode('utf-8'))
            
            # 转换为标准格式
            vulnerabilities = []
            
            for issue in result.get('issues', []):
                severity = issue.get('severity', 'low').lower()
                
                vulnerabilities.append({
                    'id': issue.get('swc-id', 'unknown'),
                    'description': issue.get('description', {}).get('head', 'Unknown vulnerability'),
                    'severity': severity,
                    'line': issue.get('lineno', 0),
                    'code': issue.get('code', ''),
                    'detector': 'mythril'
                })
            
            return vulnerabilities
        
        except ImportError:
            self.logger.warning("未安装Mythril")
            return None
        
        except Exception as e:
            self.logger.error(f"使用Mythril扫描时出错: {str(e)}")
            return None
    
    def _scan_with_slither(self, source_code: str) -> Optional[List[Dict[str, Any]]]:
        """
        使用Slither扫描漏洞
        
        Args:
            source_code: 合约源代码
            
        Returns:
            漏洞列表，如果无法扫描则返回None
        """
        try:
            # 检查是否安装了Slither
            import subprocess
            import tempfile
            import os
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.sol', delete=False) as temp_file:
                temp_file.write(source_code.encode('utf-8'))
                temp_file_path = temp_file.name
            
            try:
                # 构建命令
                cmd = [
                    'slither',
                    temp_file_path,
                    '--json', '-'
                ]
                
                # 执行命令
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                stdout, stderr = process.communicate(timeout=self.timeout)
                
                if process.returncode != 0:
                    self.logger.warning(f"Slither扫描失败: {stderr.decode('utf-8')}")
                    return None
                
                # 解析结果
                result = json.loads(stdout.decode('utf-8'))
                
                # 转换为标准格式
                vulnerabilities = []
                
                for detector in result.get('detectors', []):
                    for issue in detector.get('elements', []):
                        severity = detector.get('impact', 'low').lower()
                        
                        vulnerabilities.append({
                            'id': detector.get('check', 'unknown'),
                            'description': detector.get('description', 'Unknown vulnerability'),
                            'severity': severity,
                            'line': issue.get('source_mapping', {}).get('lines', [0])[0],
                            'code': issue.get('source_mapping', {}).get('content', ''),
                            'detector': 'slither'
                        })
                
                return vulnerabilities
            
            finally:
                # 删除临时文件
                os.unlink(temp_file_path)
        
        except ImportError:
            self.logger.warning("未安装Slither")
            return None
        
        except Exception as e:
            self.logger.error(f"使用Slither扫描时出错: {str(e)}")
            return None
    
    def _scan_with_mythx(self, contract_address: str, blockchain: str) -> Optional[List[Dict[str, Any]]]:
        """
        使用MythX扫描漏洞
        
        Args:
            contract_address: 合约地址
            blockchain: 区块链平台
            
        Returns:
            漏洞列表，如果无法扫描则返回None
        """
        try:
            # 检查是否有API密钥
            if not self.mythx_api_key:
                return None
            
            # 获取合约源代码
            source_code = self._get_contract_source_code(contract_address, blockchain)
            
            if not source_code:
                return None
            
            # 构建请求
            api_url = "https://api.mythx.io/v1/analyses"
            
            headers = {
                "Authorization": f"Bearer {self.mythx_api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "data": {
                    "type": "analyses",
                    "attributes": {
                        "sources": {
                            "main.sol": {
                                "source": source_code
                            }
                        },
                        "mainSource": "main.sol"
                    }
                }
            }
            
            # 发送请求
            response = requests.post(api_url, headers=headers, json=data, timeout=self.timeout)
            response.raise_for_status()
            
            analysis_data = response.json()
            analysis_id = analysis_data.get('data', {}).get('id')
            
            if not analysis_id:
                return None
            
            # 等待分析完成
            status_url = f"https://api.mythx.io/v1/analyses/{analysis_id}"
            
            for _ in range(10):  # 最多等待10次
                time.sleep(5)  # 等待5秒
                
                status_response = requests.get(status_url, headers=headers, timeout=self.timeout)
                status_response.raise_for_status()
                
                status_data = status_response.json()
                status = status_data.get('data', {}).get('attributes', {}).get('status')
                
                if status == 'Finished':
                    break
            
            # 获取分析结果
            issues_url = f"https://api.mythx.io/v1/analyses/{analysis_id}/issues"
            
            issues_response = requests.get(issues_url, headers=headers, timeout=self.timeout)
            issues_response.raise_for_status()
            
            issues_data = issues_response.json()
            
            # 转换为标准格式
            vulnerabilities = []
            
            for issue in issues_data.get('data', []):
                attributes = issue.get('attributes', {})
                severity = attributes.get('severity', 'low').lower()
                
                vulnerabilities.append({
                    'id': attributes.get('swcID', 'unknown'),
                    'description': attributes.get('description', 'Unknown vulnerability'),
                    'severity': severity,
                    'line': attributes.get('sourceLocation', {}).get('line', 0),
                    'code': attributes.get('sourceLocation', {}).get('source', ''),
                    'detector': 'mythx'
                })
            
            return vulnerabilities
        
        except Exception as e:
            self.logger.error(f"使用MythX扫描时出错: {str(e)}")
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()

import logging
import requests
import time
import re
import json
from typing import Dict, List, Any, Optional

from discovery.models.project import Project


class VulnerabilityScanner:
    """漏洞扫描器，负责扫描智能合约漏洞"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化漏洞扫描器
        
        Args:
            config: 配置字典，包含漏洞扫描器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.timeout = config.get('timeout', 30)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        self.mythril_enabled = config.get('mythril_enabled', False)
        self.slither_enabled = config.get('slither_enabled', False)
        self.mythx_api_key = config.get('mythx_api_key', '')
        self.mythx_enabled = config.get('mythx_enabled', False) and bool(self.mythx_api_key)
        
        # 漏洞模式
        self.vulnerability_patterns = {
            'reentrancy': {
                'pattern': r'(call\.value|send|transfer).*\(.*\).*\n.*[^;]',
                'description': '重入漏洞',
                'severity': 'critical'
            },
            'integer_overflow': {
                'pattern': r'(\+\+|\+=|=\s*\+|\*=|=\s*\*)',
                'description': '整数溢出',
                'severity': 'high'
            },
            'unchecked_external_call': {
                'pattern': r'(call|send|transfer).*\(.*\)(?!\s*returns|\s*revert)',
                'description': '未检查的外部调用',
                'severity': 'high'
            },
            'tx_origin': {
                'pattern': r'tx\.origin\s*==',
                'description': '使用tx.origin进行身份验证',
                'severity': 'high'
            },
            'timestamp_dependence': {
                'pattern': r'block\.timestamp',
                'description': '时间戳依赖',
                'severity': 'medium'
            },
            'assembly_usage': {
                'pattern': r'assembly\s*{',
                'description': '使用内联汇编',
                'severity': 'medium'
            },
            'deprecated_functions': {
                'pattern': r'(suicide|sha3|throw)',
                'description': '使用已弃用的函数',
                'severity': 'medium'
            },
            'multiple_sends': {
                'pattern': r'(send|transfer).*\n.*(send|transfer)',
                'description': '多次发送',
                'severity': 'medium'
            },
            'unprotected_functions': {
                'pattern': r'function\s+(\w+)\s*\([^)]*\)\s*public(?!\s+view|\s+pure|\s+constant)(?!\s+onlyOwner)(?!.*require\s*\(\s*msg\.sender\s*==)',
                'description': '未受保护的公共函数',
                'severity': 'low'
            }
        }
        
        # 统计信息
        self._stats = {
            'total_scans': 0,
            'successful_scans': 0,
            'failed_scans': 0,
            'critical_vulnerabilities': 0,
            'high_vulnerabilities': 0,
            'medium_vulnerabilities': 0,
            'low_vulnerabilities': 0
        }
    
    def scan(self, project: Project) -> Dict[str, Any]:
        """
        扫描项目智能合约漏洞
        
        Args:
            project: 要扫描的项目
            
        Returns:
            扫描结果字典
        """
        self.logger.info(f"开始扫描项目 {project.name} (ID: {project.id}) 的智能合约漏洞")
        
        try:
            # 更新统计信息
            self._stats['total_scans'] += 1
            
            # 初始化结果
            result = {
                'scanned': False,
                'contract_address': None,
                'blockchain': None,
                'vulnerabilities': [],
                'critical_vulnerabilities': 0,
                'high_vulnerabilities': 0,
                'medium_vulnerabilities': 0,
                'low_vulnerabilities': 0,
                'scan_time': time.time(),
                'warnings': []
            }
            
            # 获取合约地址和区块链
            if not project.token_info or not project.token_info.contract_address:
                self.logger.warning(f"项目 {project.name} 没有合约地址")
                result['warnings'].append("没有合约地址")
                self._stats['failed_scans'] += 1
                return result
            
            contract_address = project.token_info.contract_address
            blockchain = project.token_info.blockchain or project.blockchain.value
            
            result['contract_address'] = contract_address
            result['blockchain'] = blockchain
            
            # 获取合约源代码
            source_code = self._get_contract_source_code(contract_address, blockchain)
            
            if not source_code:
                self.logger.warning(f"无法获取合约源代码: {contract_address}")
                result['warnings'].append("无法获取合约源代码")
                self._stats['failed_scans'] += 1
                return result
            
            # 使用模式匹配扫描漏洞
            pattern_vulnerabilities = self._scan_with_patterns(source_code)
            result['vulnerabilities'].extend(pattern_vulnerabilities)
            
            # 使用Mythril扫描（如果启用）
            if self.mythril_enabled:
                mythril_vulnerabilities = self._scan_with_mythril(contract_address, blockchain)
                if mythril_vulnerabilities:
                    result['vulnerabilities'].extend(mythril_vulnerabilities)
            
            # 使用Slither扫描（如果启用）
            if self.slither_enabled:
                slither_vulnerabilities = self._scan_with_slither(source_code)
                if slither_vulnerabilities:
                    result['vulnerabilities'].extend(slither_vulnerabilities)
            
            # 使用MythX扫描（如果启用）
            if self.mythx_enabled:
                mythx_vulnerabilities = self._scan_with_mythx(contract_address, blockchain)
                if mythx_vulnerabilities:
                    result['vulnerabilities'].extend(mythx_vulnerabilities)
            
            # 统计漏洞数量
            for vulnerability in result['vulnerabilities']:
                severity = vulnerability.get('severity', 'low').lower()
                
                if severity == 'critical':
                    result['critical_vulnerabilities'] += 1
                    self._stats['critical_vulnerabilities'] += 1
                elif severity == 'high':
                    result['high_vulnerabilities'] += 1
                    self._stats['high_vulnerabilities'] += 1
                elif severity == 'medium':
                    result['medium_vulnerabilities'] += 1
                    self._stats['medium_vulnerabilities'] += 1
                elif severity == 'low':
                    result['low_vulnerabilities'] += 1
                    self._stats['low_vulnerabilities'] += 1
            
            # 更新扫描状态
            result['scanned'] = True
            self._stats['successful_scans'] += 1
            
            self.logger.info(f"项目 {project.name} 智能合约漏洞扫描完成，发现 {len(result['vulnerabilities'])} 个漏洞")
            
            return result
        
        except Exception as e:
            self.logger.error(f"扫描项目 {project.name} 智能合约漏洞时出错: {str(e)}")
            self._stats['failed_scans'] += 1
            
            return {
                'scanned': False,
                'error': str(e),
                'warnings': ["扫描过程中出错"]
            }
    
    def _get_contract_source_code(self, contract_address: str, blockchain: str) -> Optional[str]:
        """
        获取合约源代码
        
        Args:
            contract_address: 合约地址
            blockchain: 区块链平台
            
        Returns:
            合约源代码，如果无法获取则返回None
        """
        try:
            # 根据区块链选择API
            if blockchain == 'ethereum':
                api_key = self.config.get('etherscan_api_key', '')
                api_url = f"https://api.etherscan.io/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            elif blockchain == 'binance':
                api_key = self.config.get('bscscan_api_key', '')
                api_url = f"https://api.bscscan.com/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            elif blockchain == 'polygon':
                api_key = self.config.get('polygonscan_api_key', '')
                api_url = f"https://api.polygonscan.com/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            elif blockchain == 'arbitrum':
                api_key = self.config.get('arbiscan_api_key', '')
                api_url = f"https://api.arbiscan.io/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            elif blockchain == 'optimism':
                api_key = self.config.get('optimism_api_key', '')
                api_url = f"https://api-optimistic.etherscan.io/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            elif blockchain == 'base':
                api_key = self.config.get('basescan_api_key', '')
                api_url = f"https://api.basescan.org/api?module=contract&action=getsourcecode&address={contract_address}&apikey={api_key}"
            else:
                self.logger.warning(f"不支持的区块链: {blockchain}")
                return None
            
            # 发送请求
            response = requests.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != '1' or 'result' not in data:
                return None
            
            contract_data = data['result'][0]
            
            # 检查源代码是否存在
            if not contract_data.get('SourceCode'):
                return None
            
            return contract_data.get('SourceCode')
        
        except Exception as e:
            self.logger.error(f"获取合约源代码时出错: {str(e)}")
            return None
    
    def _scan_with_patterns(self, source_code: str) -> List[Dict[str, Any]]:
        """
        使用模式匹配扫描漏洞
        
        Args:
            source_code: 合约源代码
            
        Returns:
            漏洞列表
        """
        vulnerabilities = []
        
        # 扫描每种漏洞模式
        for vuln_id, vuln_info in self.vulnerability_patterns.items():
            pattern = vuln_info['pattern']
            description = vuln_info['description']
            severity = vuln_info['severity']
            
            # 查找匹配
            matches = re.finditer(pattern, source_code)
            
            for match in matches:
                # 获取匹配行号
                line_number = source_code[:match.start()].count('\n') + 1
                
                # 获取匹配代码
                lines = source_code.split('\n')
                code_context = '\n'.join(lines[max(0, line_number - 2):min(len(lines), line_number + 2)])
                
                # 添加漏洞
                vulnerabilities.append({
                    'id': vuln_id,
                    'description': description,
                    'severity': severity,
                    'line': line_number,
                    'code': code_context,
                    'detector': 'pattern_matching'
                })
        
        return vulnerabilities
    
    def _scan_with_mythril(self, contract_address: str, blockchain: str) -> Optional[List[Dict[str, Any]]]:
        """
        使用Mythril扫描漏洞
        
        Args:
            contract_address: 合约地址
            blockchain: 区块链平台
            
        Returns:
            漏洞列表，如果无法扫描则返回None
        """
        try:
            # 检查是否安装了Mythril
            import subprocess
            
            # 构建命令
            cmd = [
                'myth', 'analyze',
                '-a', contract_address,
                '--infura-id', self.config.get('infura_id', ''),
                '-o', 'json'
            ]
            
            # 执行命令
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = process.communicate(timeout=self.timeout)
            
            if process.returncode != 0:
                self.logger.warning(f"Mythril扫描失败: {stderr.decode('utf-8')}")
                return None
            
            # 解析结果
            result = json.loads(stdout.decode('utf-8'))
            
            # 转换为标准格式
            vulnerabilities = []
            
            for issue in result.get('issues', []):
                severity = issue.get('severity', 'low').lower()
                
                vulnerabilities.append({
                    'id': issue.get('swc-id', 'unknown'),
                    'description': issue.get('description', {}).get('head', 'Unknown vulnerability'),
                    'severity': severity,
                    'line': issue.get('lineno', 0),
                    'code': issue.get('code', ''),
                    'detector': 'mythril'
                })
            
            return vulnerabilities
        
        except ImportError:
            self.logger.warning("未安装Mythril")
            return None
        
        except Exception as e:
            self.logger.error(f"使用Mythril扫描时出错: {str(e)}")
            return None
    
    def _scan_with_slither(self, source_code: str) -> Optional[List[Dict[str, Any]]]:
        """
        使用Slither扫描漏洞
        
        Args:
            source_code: 合约源代码
            
        Returns:
            漏洞列表，如果无法扫描则返回None
        """
        try:
            # 检查是否安装了Slither
            import subprocess
            import tempfile
            import os
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.sol', delete=False) as temp_file:
                temp_file.write(source_code.encode('utf-8'))
                temp_file_path = temp_file.name
            
            try:
                # 构建命令
                cmd = [
                    'slither',
                    temp_file_path,
                    '--json', '-'
                ]
                
                # 执行命令
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                stdout, stderr = process.communicate(timeout=self.timeout)
                
                if process.returncode != 0:
                    self.logger.warning(f"Slither扫描失败: {stderr.decode('utf-8')}")
                    return None
                
                # 解析结果
                result = json.loads(stdout.decode('utf-8'))
                
                # 转换为标准格式
                vulnerabilities = []
                
                for detector in result.get('detectors', []):
                    for issue in detector.get('elements', []):
                        severity = detector.get('impact', 'low').lower()
                        
                        vulnerabilities.append({
                            'id': detector.get('check', 'unknown'),
                            'description': detector.get('description', 'Unknown vulnerability'),
                            'severity': severity,
                            'line': issue.get('source_mapping', {}).get('lines', [0])[0],
                            'code': issue.get('source_mapping', {}).get('content', ''),
                            'detector': 'slither'
                        })
                
                return vulnerabilities
            
            finally:
                # 删除临时文件
                os.unlink(temp_file_path)
        
        except ImportError:
            self.logger.warning("未安装Slither")
            return None
        
        except Exception as e:
            self.logger.error(f"使用Slither扫描时出错: {str(e)}")
            return None
    
    def _scan_with_mythx(self, contract_address: str, blockchain: str) -> Optional[List[Dict[str, Any]]]:
        """
        使用MythX扫描漏洞
        
        Args:
            contract_address: 合约地址
            blockchain: 区块链平台
            
        Returns:
            漏洞列表，如果无法扫描则返回None
        """
        try:
            # 检查是否有API密钥
            if not self.mythx_api_key:
                return None
            
            # 获取合约源代码
            source_code = self._get_contract_source_code(contract_address, blockchain)
            
            if not source_code:
                return None
            
            # 构建请求
            api_url = "https://api.mythx.io/v1/analyses"
            
            headers = {
                "Authorization": f"Bearer {self.mythx_api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "data": {
                    "type": "analyses",
                    "attributes": {
                        "sources": {
                            "main.sol": {
                                "source": source_code
                            }
                        },
                        "mainSource": "main.sol"
                    }
                }
            }
            
            # 发送请求
            response = requests.post(api_url, headers=headers, json=data, timeout=self.timeout)
            response.raise_for_status()
            
            analysis_data = response.json()
            analysis_id = analysis_data.get('data', {}).get('id')
            
            if not analysis_id:
                return None
            
            # 等待分析完成
            status_url = f"https://api.mythx.io/v1/analyses/{analysis_id}"
            
            for _ in range(10):  # 最多等待10次
                time.sleep(5)  # 等待5秒
                
                status_response = requests.get(status_url, headers=headers, timeout=self.timeout)
                status_response.raise_for_status()
                
                status_data = status_response.json()
                status = status_data.get('data', {}).get('attributes', {}).get('status')
                
                if status == 'Finished':
                    break
            
            # 获取分析结果
            issues_url = f"https://api.mythx.io/v1/analyses/{analysis_id}/issues"
            
            issues_response = requests.get(issues_url, headers=headers, timeout=self.timeout)
            issues_response.raise_for_status()
            
            issues_data = issues_response.json()
            
            # 转换为标准格式
            vulnerabilities = []
            
            for issue in issues_data.get('data', []):
                attributes = issue.get('attributes', {})
                severity = attributes.get('severity', 'low').lower()
                
                vulnerabilities.append({
                    'id': attributes.get('swcID', 'unknown'),
                    'description': attributes.get('description', 'Unknown vulnerability'),
                    'severity': severity,
                    'line': attributes.get('sourceLocation', {}).get('line', 0),
                    'code': attributes.get('sourceLocation', {}).get('source', ''),
                    'detector': 'mythx'
                })
            
            return vulnerabilities
        
        except Exception as e:
            self.logger.error(f"使用MythX扫描时出错: {str(e)}")
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()