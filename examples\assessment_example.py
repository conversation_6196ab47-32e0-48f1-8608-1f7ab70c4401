"""
项目评估智能体示例

该脚本演示了如何使用项目评估智能体评估项目。
"""

import json
import logging
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from assessment.assessment_agent import AssessmentAgent
from discovery.models.project import Project, ProjectStatus, TokenInfo, SocialChannel, TeamMember, Requirement
from discovery.models.enums import ProjectType, BlockchainPlatform, SocialChannelType, RequirementDifficulty


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def load_config():
    """加载配置"""
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'assessment_config.json')
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    return config.get('assessment', {})


def create_sample_project():
    """创建示例项目"""
    # 创建团队成员
    team_members = [
        TeamMember(
            name="John Doe",
            role="CEO & Founder",
            bio="Blockchain enthusiast with 5 years of experience in DeFi projects.",
            links=["https://linkedin.com/in/johndoe", "https://github.com/johndoe"]
        ),
        TeamMember(
            name="Jane Smith",
            role="CTO",
            bio="Software engineer with expertise in smart contract development.",
            links=["https://linkedin.com/in/janesmith", "https://github.com/janesmith"]
        ),
        TeamMember(
            name="Mike Johnson",
            role="CMO",
            bio="Marketing specialist with experience in crypto projects.",
            links=["https://linkedin.com/in/mikejohnson"]
        )
    ]
    
    # 创建社交媒体渠道
    social_channels = [
        SocialChannel(
            channel_type=SocialChannelType.TWITTER,
            url="https://twitter.com/sampleproject",
            followers=5000
        ),
        SocialChannel(
            channel_type=SocialChannelType.TELEGRAM,
            url="https://t.me/sampleproject",
            followers=3000
        ),
        SocialChannel(
            channel_type=SocialChannelType.DISCORD,
            url="https://discord.gg/sampleproject",
            followers=2000
        ),
        SocialChannel(
            channel_type=SocialChannelType.MEDIUM,
            url="https://medium.com/@sampleproject",
            followers=1000
        )
    ]
    
    # 创建参与要求
    requirements = [
        Requirement(
            description="Complete testnet tasks",
            difficulty=RequirementDifficulty.MEDIUM,
            estimated_time=60
        ),
        Requirement(
            description="Join Discord and Telegram groups",
            difficulty=RequirementDifficulty.EASY,
            estimated_time=10
        ),
        Requirement(
            description="Follow Twitter and retweet announcement",
            difficulty=RequirementDifficulty.EASY,
            estimated_time=5
        )
    ]
    
    # 创建代币信息
    token_info = TokenInfo(
        name="Sample Token",
        symbol="SMPL",
        blockchain=BlockchainPlatform.ETHEREUM,
        contract_address="******************************************",
        total_supply=1000000000,
        airdrop_amount=1000000,
        estimated_value=10.0
    )
    
    # 创建项目
    project = Project(
        id="sample-project-001",
        name="Sample Project",
        description="""
        Sample Project is a decentralized finance platform that enables users to trade, stake, and earn rewards.
        
        Our mission is to provide a secure and user-friendly DeFi experience for everyone.
        
        ## Features
        - Decentralized exchange
        - Yield farming
        - NFT marketplace
        - Cross-chain bridge
        
        ## Team
        Our team consists of experienced blockchain developers and finance professionals.
        
        John Doe - CEO & Founder
        Jane Smith - CTO
        Mike Johnson - CMO
        
        ## Roadmap
        Q1 2023: Testnet launch
        Q2 2023: Mainnet launch
        Q3 2023: Mobile app release
        Q4 2023: Cross-chain integration
        
        ## Tokenomics
        Total supply: 1,000,000,000 SMPL
        Airdrop allocation: 10%
        Team allocation: 20%
        Community rewards: 30%
        Liquidity: 40%
        """,
        url="https://sampleproject.io",
        project_type=ProjectType.AIRDROP,
        blockchain=BlockchainPlatform.ETHEREUM,
        status=ProjectStatus.NEW,
        discovery_time=time.time() - 86400,  # 1 day ago
        team=team_members,
        social_channels=social_channels,
        requirements=requirements,
        token_info=token_info,
        tags=["defi", "airdrop", "ethereum"],
        notes="Promising project with experienced team"
    )
    
    return project


def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 加载配置
    config = load_config()
    
    # 创建项目评估智能体
    assessment_agent = AssessmentAgent(config)
    
    # 创建示例项目
    project = create_sample_project()
    
    # 评估项目
    print(f"开始评估项目: {project.name}")
    assessed_project = assessment_agent.assess_project(project)
    
    # 打印评估结果
    print("\n评估结果:")
    print(f"项目名称: {assessed_project.name}")
    print(f"项目状态: {assessed_project.status.value}")
    print(f"风险分数: {assessed_project.risk_score}")
    print(f"潜在收益: {assessed_project.potential_reward}")
    print(f"社区评分: {assessed_project.community_score}")
    print(f"团队评分: {assessed_project.team_score}")
    print(f"标签: {', '.join(assessed_project.tags)}")
    
    # 打印详细评估结果
    if hasattr(assessed_project, 'assessment_results'):
        print("\n详细评估结果:")
        
        # 团队验证结果
        team_result = assessed_project.assessment_results.get('team_verification', {})
        print(f"\n团队验证:")
        print(f"验证结果: {'通过' if team_result.get('verified', False) else '未通过'}")
        print(f"置信度: {team_result.get('confidence', 0.0):.2f}")
        print(f"团队规模: {team_result.get('team_size', 0)}")
        print(f"已知成员: {team_result.get('known_members', 0)}")
        print(f"警告: {', '.join(team_result.get('warnings', []))}")
        
        # 社交媒体验证结果
        social_result = assessed_project.assessment_results.get('social_verification', {})
        print(f"\n社交媒体验证:")
        print(f"验证结果: {'通过' if social_result.get('verified', False) else '未通过'}")
        print(f"置信度: {social_result.get('confidence', 0.0):.2f}")
        print(f"渠道数量: {social_result.get('channel_count', 0)}")
        print(f"已验证渠道: {social_result.get('verified_channels', 0)}")
        print(f"总粉丝数: {social_result.get('total_followers', 0)}")
        print(f"活动级别: {social_result.get('activity_level', 'unknown')}")
        print(f"警告: {', '.join(social_result.get('warnings', []))}")
        
        # 项目真实性验证结果
        project_result = assessed_project.assessment_results.get('project_verification', {})
        print(f"\n项目真实性验证:")
        print(f"验证结果: {'通过' if project_result.get('verified', False) else '未通过'}")
        print(f"置信度: {project_result.get('confidence', 0.0):.2f}")
        print(f"网站可用: {'是' if project_result.get('website_available', False) else '否'}")
        print(f"内容质量: {project_result.get('content_quality', 'unknown')}")
        print(f"警告: {', '.join(project_result.get('warnings', []))}")
        
        # 智能合约分析结果
        contract_result = assessed_project.assessment_results.get('contract_analysis', {})
        if contract_result:
            print(f"\n智能合约分析:")
            print(f"分析结果: {'成功' if contract_result.get('analyzed', False) else '失败'}")
            print(f"合约已验证: {'是' if contract_result.get('contract_verified', False) else '否'}")
            print(f"风险级别: {contract_result.get('risk_level', 'unknown')}")
            print(f"警告: {', '.join(contract_result.get('warnings', []))}")
        
        # 漏洞扫描结果
        vulnerability_result = assessed_project.assessment_results.get('vulnerability_scan', {})
        if vulnerability_result:
            print(f"\n漏洞扫描:")
            print(f"扫描结果: {'成功' if vulnerability_result.get('scanned', False) else '失败'}")
            print(f"严重漏洞: {vulnerability_result.get('critical_vulnerabilities', 0)}")
            print(f"高风险漏洞: {vulnerability_result.get('high_vulnerabilities', 0)}")
            print(f"中风险漏洞: {vulnerability_result.get('medium_vulnerabilities', 0)}")
            print(f"低风险漏洞: {vulnerability_result.get('low_vulnerabilities', 0)}")
            print(f"警告: {', '.join(vulnerability_result.get('warnings', []))}")
        
        # 权限分析结果
        permission_result = assessed_project.assessment_results.get('permission_analysis', {})
        if permission_result:
            print(f"\n权限分析:")
            print(f"分析结果: {'成功' if permission_result.get('analyzed', False) else '失败'}")
            print(f"集中化级别: {permission_result.get('centralization_level', 'unknown')}")
            print(f"警告: {', '.join(permission_result.get('warnings', []))}")


if __name__ == '__main__':
    main()