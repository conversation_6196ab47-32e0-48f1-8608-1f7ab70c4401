"""
Transaction Manager

Provides transaction management capabilities with support for nested transactions,
savepoints, and automatic rollback on errors.
"""

import logging
import threading
from contextlib import contextmanager
from typing import Optional, Any
from .connection_pool import ConnectionPool


class TransactionContext:
    """
    Transaction context manager.
    
    Handles transaction lifecycle including commit, rollback, and cleanup.
    """
    
    def __init__(self, connection, transaction_manager):
        """
        Initialize transaction context.
        
        Args:
            connection: Database connection
            transaction_manager: Parent transaction manager
        """
        self.connection = connection
        self.transaction_manager = transaction_manager
        self.logger = logging.getLogger(__name__)
        self._committed = False
        self._rolled_back = False
        self._savepoint_name = None
    
    def __enter__(self):
        """Enter transaction context."""
        try:
            # Begin transaction or create savepoint
            if self.transaction_manager._get_transaction_depth() == 0:
                self.connection.execute("BEGIN")
                self.logger.debug("Transaction started")
            else:
                # Nested transaction - use savepoint
                self._savepoint_name = f"sp_{self.transaction_manager._get_transaction_depth()}"
                self.connection.execute(f"SAVEPOINT {self._savepoint_name}")
                self.logger.debug(f"Savepoint created: {self._savepoint_name}")
            
            self.transaction_manager._increment_depth()
            return self
            
        except Exception as e:
            self.logger.error(f"Failed to start transaction: {e}")
            raise
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit transaction context."""
        try:
            self.transaction_manager._decrement_depth()
            
            if exc_type is not None:
                # Exception occurred - rollback
                self.rollback()
                return False
            else:
                # No exception - commit if not already done
                if not self._committed and not self._rolled_back:
                    self.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error in transaction exit: {e}")
            try:
                self.rollback()
            except:
                pass
            raise
        finally:
            # Return connection to pool
            self.transaction_manager.connection_pool.return_connection(self.connection)
    
    def commit(self):
        """Commit transaction or release savepoint."""
        if self._committed or self._rolled_back:
            return
        
        try:
            if self._savepoint_name:
                # Release savepoint
                self.connection.execute(f"RELEASE SAVEPOINT {self._savepoint_name}")
                self.logger.debug(f"Savepoint released: {self._savepoint_name}")
            else:
                # Commit transaction
                self.connection.execute("COMMIT")
                self.logger.debug("Transaction committed")
            
            self._committed = True
            
        except Exception as e:
            self.logger.error(f"Failed to commit transaction: {e}")
            self.rollback()
            raise
    
    def rollback(self):
        """Rollback transaction or savepoint."""
        if self._rolled_back:
            return
        
        try:
            if self._savepoint_name:
                # Rollback to savepoint
                self.connection.execute(f"ROLLBACK TO SAVEPOINT {self._savepoint_name}")
                self.logger.debug(f"Rolled back to savepoint: {self._savepoint_name}")
            else:
                # Rollback transaction
                self.connection.execute("ROLLBACK")
                self.logger.debug("Transaction rolled back")
            
            self._rolled_back = True
            
        except Exception as e:
            self.logger.error(f"Failed to rollback transaction: {e}")
            raise
    
    def execute(self, query: str, params: tuple = ()) -> Any:
        """
        Execute query within transaction.
        
        Args:
            query: SQL query
            params: Query parameters
            
        Returns:
            Cursor result
        """
        cursor = self.connection.cursor()
        cursor.execute(query, params)
        return cursor
    
    def executemany(self, query: str, params_list: list) -> Any:
        """
        Execute query with multiple parameter sets.
        
        Args:
            query: SQL query
            params_list: List of parameter tuples
            
        Returns:
            Cursor result
        """
        cursor = self.connection.cursor()
        cursor.executemany(query, params_list)
        return cursor


class TransactionManager:
    """
    Database transaction manager.
    
    Manages database transactions with support for nested transactions
    using savepoints and automatic cleanup.
    """
    
    def __init__(self, connection_pool: ConnectionPool):
        """
        Initialize transaction manager.
        
        Args:
            connection_pool: Database connection pool
        """
        self.connection_pool = connection_pool
        self.logger = logging.getLogger(__name__)
        self._local = threading.local()
    
    def _get_transaction_depth(self) -> int:
        """Get current transaction depth for thread."""
        if not hasattr(self._local, 'depth'):
            self._local.depth = 0
        return self._local.depth
    
    def _increment_depth(self):
        """Increment transaction depth."""
        if not hasattr(self._local, 'depth'):
            self._local.depth = 0
        self._local.depth += 1
    
    def _decrement_depth(self):
        """Decrement transaction depth."""
        if hasattr(self._local, 'depth') and self._local.depth > 0:
            self._local.depth -= 1
    
    def begin_transaction(self) -> TransactionContext:
        """
        Begin new transaction.
        
        Returns:
            TransactionContext: Transaction context manager
        """
        connection = self.connection_pool.get_connection()
        return TransactionContext(connection, self)
    
    @contextmanager
    def transaction(self):
        """
        Context manager for transactions.
        
        Yields:
            TransactionContext: Transaction context
        """
        with self.begin_transaction() as tx:
            yield tx
    
    def execute_in_transaction(self, func, *args, **kwargs):
        """
        Execute function within transaction.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
        """
        with self.transaction() as tx:
            return func(tx, *args, **kwargs)
    
    def is_in_transaction(self) -> bool:
        """
        Check if currently in transaction.
        
        Returns:
            bool: True if in transaction
        """
        return self._get_transaction_depth() > 0
