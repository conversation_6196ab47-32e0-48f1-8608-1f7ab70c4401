"""
Discord 项目源

该模块实现了从 Discord 获取项目信息的功能。
"""

import logging
import time
import random
import re
from typing import Dict, List, Any, Optional

from discovery.sources.base_source import BaseProjectSource


class DiscordSource(BaseProjectSource):
    """Discord 项目源"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 Discord 项目源
        
        Args:
            config: 配置字典，包含项目源的配置信息
        """
        super().__init__(config)
        self.token = config.get('token', '')
        self.use_api = config.get('use_api', False) and bool(self.token)
        self.servers = config.get('servers', [])
        self.channels = config.get('channels', [])
        self.search_terms = config.get('search_terms', [
            'airdrop', 'crypto airdrop', 'token airdrop', 'free tokens',
            'blockchain airdrop', 'defi airdrop', 'nft airdrop'
        ])
        
        # 初始化 Discord API 客户端
        if self.use_api:
            self._init_api_client()
    
    def _init_api_client(self) -> None:
        """初始化 Discord API 客户端"""
        try:
            import discord
            
            class DiscordClient(discord.Client):
                def __init__(self, logger, *args, **kwargs):
                    super().__init__(*args, **kwargs)
                    self.logger = logger
                    self.messages = []
                
                async def on_ready(self):
                    self.logger.info(f"Discord 客户端已登录: {self.user}")
            
            intents = discord.Intents.default()
            intents.message_content = True
            
            self.api_client = DiscordClient(self.logger, intents=intents)
            self.logger.info("Discord API 客户端初始化成功")
        
        except ImportError:
            self.logger.warning("无法导入 discord.py 库，将使用模拟数据")
            self.use_api = False
        except Exception as e:
            self.logger.error(f"初始化 Discord API 客户端时出错: {str(e)}")
            self.use_api = False
    
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        从 Discord 获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        self.logger.info(f"从 Discord 获取 {count} 个项目")
        
        if self.use_api:
            return self._get_projects_via_api(count)
        else:
            return self._get_projects_via_simulation(count)
    
    def _get_projects_via_api(self, count: int) -> List[Dict[str, Any]]:
        """
        通过 API 获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            import discord
            import asyncio
            
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 定义异步函数
            async def fetch_messages():
                # 登录
                await self.api_client.login(self.token)
                
                messages = []
                
                # 获取指定服务器的消息
                for guild in self.api_client.guilds:
                    if not self.servers or guild.name in self.servers or str(guild.id) in self.servers:
                        for channel in guild.text_channels:
                            if not self.channels or channel.name in self.channels or str(channel.id) in self.channels:
                                try:
                                    async for message in channel.history(limit=50):
                                        # 检查消息是否包含关键词
                                        if any(term.lower() in message.content.lower() for term in self.search_terms):
                                            messages.append({
                                                'content': message.content,
                                                'author': str(message.author),
                                                'channel': channel.name,
                                                'guild': guild.name,
                                                'created_at': message.created_at.timestamp(),
                                                'jump_url': message.jump_url,
                                                'attachments': [a.url for a in message.attachments],
                                                'embeds': [e.to_dict() for e in message.embeds]
                                            })
                                            
                                            # 如果已经获取到足够的消息，就停止
                                            if len(messages) >= count * 2:  # 获取更多消息，以便过滤后仍有足够的项目
                                                break
                                
                                except Exception as e:
                                    self.logger.error(f"获取频道 {channel.name} 的消息时出错: {str(e)}")
                            
                            # 如果已经获取到足够的消息，就停止
                            if len(messages) >= count * 2:
                                break
                    
                    # 如果已经获取到足够的消息，就停止
                    if len(messages) >= count * 2:
                        break
                
                # 关闭客户端
                await self.api_client.close()
                
                return messages
            
            # 运行异步函数
            messages = loop.run_until_complete(fetch_messages())
            
            # 关闭事件循环
            loop.close()
            
            # 提取项目信息
            projects = []
            for message in messages:
                project_info = self._extract_project_info_from_message(message)
                if project_info:
                    projects.append(project_info)
                    
                    # 如果已经获取到足够的项目，就停止
                    if len(projects) >= count:
                        break
            
            self.logger.info(f"从 Discord API 获取到 {len(projects)} 个项目")
            return projects[:count]
        
        except Exception as e:
            self.logger.error(f"通过 API 获取项目时出错: {str(e)}")
            return []
    
    def _get_projects_via_simulation(self, count: int) -> List[Dict[str, Any]]:
        """
        通过模拟数据获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        self.logger.info("使用模拟数据获取 Discord 项目")
        
        # 模拟数据
        simulated_messages = [
            {
                'content': '🚀 New Airdrop Alert! 🚀\n\nProject: CryptoMoon\nBlockchain: Ethereum\nAirdrop: 1000 MOON tokens\n\nJoin now: https://cryptomoon.io',
                'author': 'AirdropFinder#1234',
                'channel': 'airdrops',
                'guild': 'Crypto Community',
                'created_at': time.time() - 3600,
                'jump_url': 'https://discord.com/channels/123456789/123456789/123456789',
                'attachments': [],
                'embeds': []
            },
            {
                'content': '💰 Solana Airdrop! 💰\n\nEarn free SOL tokens by completing simple tasks.\n\nVisit: https://solanaproject.com',
                'author': 'CryptoHunter#5678',
                'channel': 'announcements',
                'guild': 'Solana Community',
                'created_at': time.time() - 7200,
                'jump_url': 'https://discord.com/channels/123456789/123456789/123456790',
                'attachments': [],
                'embeds': []
            },
            {
                'content': '🔥 DeFi Revolution Airdrop 🔥\n\nJoin the future of finance!\nPlatform: Binance Smart Chain\nToken: DEFR\n\nRegister: https://defirevolution.finance',
                'author': 'DeFiExplorer#9012',
                'channel': 'defi-projects',
                'guild': 'DeFi Hub',
                'created_at': time.time() - 10800,
                'jump_url': 'https://discord.com/channels/123456789/123456789/123456791',
                'attachments': [],
                'embeds': []
            },
            {
                'content': '🎮 GameFi Airdrop: PlayToEarn 🎮\n\nEarn while you play!\nPlatform: Polygon\nAirdrop: 500 P2E tokens\n\nJoin: https://playtoearn.game',
                'author': 'GameMaster#3456',
                'channel': 'gaming',
                'guild': 'GameFi Alliance',
                'created_at': time.time() - 14400,
                'jump_url': 'https://discord.com/channels/123456789/123456789/123456792',
                'attachments': [],
                'embeds': []
            },
            {
                'content': '🌟 Arbitrum Ecosystem Airdrop 🌟\n\nProject: ArbiFinance\nAirdrop: 2000 ARFI tokens\n\nClaim now: https://arbifinance.io',
                'author': 'ArbitrumFan#7890',
                'channel': 'layer2',
                'guild': 'L2 Solutions',
                'created_at': time.time() - 18000,
                'jump_url': 'https://discord.com/channels/123456789/123456789/123456793',
                'attachments': [],
                'embeds': []
            }
        ]
        
        # 随机选择消息
        selected_messages = random.sample(simulated_messages, min(count, len(simulated_messages)))
        
        # 提取项目信息
        projects = []
        for message in selected_messages:
            project_info = self._extract_project_info_from_message(message)
            if project_info:
                projects.append(project_info)
        
        self.logger.info(f"从 Discord 模拟数据获取到 {len(projects)} 个项目")
        return projects
    
    def _extract_project_info_from_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        从消息中提取项目信息
        
        Args:
            message: 消息字典
            
        Returns:
            项目信息字典，如果无法提取则返回None
        """
        content = message.get('content', '')
        
        # 检查消息是否包含关键词
        keywords = ['airdrop', 'token', 'crypto', 'blockchain', 'defi', 'nft']
        if not any(keyword in content.lower() for keyword in keywords):
            return None
        
        # 提取项目名称
        name_match = re.search(r'(?i)(?:project|token|airdrop)[\s:]+([A-Za-z0-9\s]+)', content)
        name = name_match.group(1).strip() if name_match else "Unknown Project"
        
        # 提取项目URL
        url_match = re.search(r'(?i)(?:website|site|join|register|claim|visit)[\s:]+\s*(https?://[^\s]+)', content)
        if not url_match:
            url_match = re.search(r'(https?://[^\s]+)', content)
        
        project_url = url_match.group(1) if url_match else None
        
        if not project_url:
            return None
        
        # 创建项目信息
        project_info = {
            'name': name,
            'description': content[:200] + ('...' if len(content) > 200 else ''),
            'url': project_url,
            'project_type': 'airdrop',
            'blockchain': 'other',
            'source_url': message.get('jump_url', ''),
            'discovery_source': 'discord',
            'social_channels': [
                {
                    'platform': 'discord',
                    'url': message.get('jump_url', '').split('/channels/')[0] if 'jump_url' in message else '',
                    'followers': None
                }
            ]
        }
        
        # 尝试识别区块链平台
        blockchain_keywords = {
            'ethereum': ['ethereum', 'eth', 'erc20', 'erc721'],
            'binance': ['binance', 'bsc', 'bnb', 'bep20'],
            'solana': ['solana', 'sol'],
            'polygon': ['polygon', 'matic'],
            'avalanche': ['avalanche', 'avax'],
            'arbitrum': ['arbitrum', 'arb'],
            'optimism': ['optimism', 'op'],
            'base': ['base'],
            'cosmos': ['cosmos', 'atom'],
            'polkadot': ['polkadot', 'dot'],
            'near': ['near'],
            'aptos': ['aptos', 'apt'],
            'sui': ['sui']
        }
        
        for blockchain, keywords in blockchain_keywords.items():
            if any(keyword in content.lower() for keyword in keywords):
                project_info['blockchain'] = blockchain
                break
        
        return project_info