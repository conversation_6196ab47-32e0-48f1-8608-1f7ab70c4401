"""
Task Execution Agent

This agent is responsible for executing tasks assigned by the Task Planning Agent,
handling interactions with various platforms and services.
"""

import logging
import os
import json
import time
import random
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timedelta
import importlib
import traceback

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("TaskExecutionAgent")

class TaskExecutionAgent:
    """
    Task Execution Agent for executing assigned tasks.
    
    This agent handles:
    - Task execution
    - Platform interactions
    - Error handling and recovery
    - Task result reporting
    - Execution monitoring
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Task Execution Agent.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config = self._load_config(config_path)
        self.active = False
        self.running_tasks = {}
        self.task_history = {}
        self.platform_handlers = {}
        self.max_concurrent_tasks = self.config.get("max_concurrent_tasks", 5)
        
        # Initialize data storage
        os.makedirs(self.config["data_storage_path"], exist_ok=True)
        
        # Load task history
        self._load_task_history()
        
        logger.info("Task Execution Agent initialized")
    
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "data_storage_path": "data/task_execution",
            "max_concurrent_tasks": 5,
            "task_timeout_minutes": 60,
            "retry_failed_tasks": True,
            "max_retries": 3,
            "retry_delay_minutes": 5,
            "platforms": {
                "twitter": {
                    "enabled": True,
                    "handler_module": "task_execution.platforms.twitter_handler",
                    "handler_class": "TwitterHandler",
                    "config_file": "config/platforms/twitter_config.json"
                },
                "discord": {
                    "enabled": True,
                    "handler_module": "task_execution.platforms.discord_handler",
                    "handler_class": "DiscordHandler",
                    "config_file": "config/platforms/discord_config.json"
                },
                "telegram": {
                    "enabled": True,
                    "handler_module": "task_execution.platforms.telegram_handler",
                    "handler_class": "TelegramHandler",
                    "config_file": "config/platforms/telegram_config.json"
                },
                "web": {
                    "enabled": True,
                    "handler_module": "task_execution.platforms.web_handler",
                    "handler_class": "WebHandler",
                    "config_file": "config/platforms/web_config.json"
                }
            }
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    # Merge user config with defaults
                    for key, value in user_config.items():
                        if key == "platforms" and isinstance(value, dict):
                            # Merge platform configs
                            for platform, platform_config in value.items():
                                if platform in default_config["platforms"]:
                                    default_config["platforms"][platform].update(platform_config)
                                else:
                                    default_config["platforms"][platform] = platform_config
                        else:
                            default_config[key] = value
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def _load_task_history(self) -> None:
        """Load task execution history from storage."""
        history_file = os.path.join(self.config["data_storage_path"], "task_history.json")
        
        if os.path.exists(history_file):
            try:
                with open(history_file, 'r') as f:
                    self.task_history = json.load(f)
                logger.info(f"Loaded history for {len(self.task_history)} tasks")
            except Exception as e:
                logger.error(f"Error loading task history: {e}")
                self.task_history = {}
    
    def _save_task_history(self) -> None:
        """Save task execution history to storage."""
        history_file = os.path.join(self.config["data_storage_path"], "task_history.json")
        
        try:
            with open(history_file, 'w') as f:
                json.dump(self.task_history, f, indent=2)
            logger.info(f"Saved history for {len(self.task_history)} tasks")
        except Exception as e:
            logger.error(f"Error saving task history: {e}")
    
    def _load_platform_handler(self, platform: str) -> bool:
        """
        Load a platform handler.
        
        Args:
            platform: Platform name
            
        Returns:
            bool: True if loaded successfully, False otherwise
        """
        try:
            if platform not in self.config["platforms"]:
                logger.error(f"Platform not configured: {platform}")
                return False
            
            platform_config = self.config["platforms"][platform]
            
            if not platform_config.get("enabled", True):
                logger.warning(f"Platform {platform} is disabled")
                return False
            
            if platform in self.platform_handlers:
                logger.info(f"Platform handler already loaded: {platform}")
                return True
            
            # Import the handler module
            module_name = platform_config["handler_module"]
            class_name = platform_config["handler_class"]
            
            module = importlib.import_module(module_name)
            handler_class = getattr(module, class_name)
            
            # Load platform-specific config if available
            config_file = platform_config.get("config_file")
            platform_specific_config = None
            
            if config_file and os.path.exists(config_file):
                try:
                    with open(config_file, 'r') as f:
                        platform_specific_config = json.load(f)
                except Exception as e:
                    logger.error(f"Error loading platform config from {config_file}: {e}")
            
            # Initialize handler
            handler = handler_class(platform_specific_config)
            self.platform_handlers[platform] = handler
            
            logger.info(f"Loaded platform handler: {platform}")
            return True
        except Exception as e:
            logger.error(f"Error loading platform handler for {platform}: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def start(self) -> bool:
        """
        Start the Task Execution Agent.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            # Load platform handlers
            for platform in self.config["platforms"]:
                self._load_platform_handler(platform)
            
            self.active = True
            logger.info("Task Execution Agent started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start Task Execution Agent: {e}")
            return False
    
    def stop(self) -> bool:
        """
        Stop the Task Execution Agent.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        try:
            # Stop any running tasks
            for task_id in list(self.running_tasks.keys()):
                self.abort_task(task_id, "Agent shutting down")
            
            # Save task history
            self._save_task_history()
            
            # Close platform handlers
            for platform, handler in self.platform_handlers.items():
                if hasattr(handler, "close") and callable(handler.close):
                    try:
                        handler.close()
                    except Exception as e:
                        logger.error(f"Error closing platform handler {platform}: {e}")
            
            self.active = False
            logger.info("Task Execution Agent stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Error stopping Task Execution Agent: {e}")
            return False
    
    def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task.
        
        Args:
            task: Task data
            
        Returns:
            Dict containing execution result
        """
        task_id = task["id"]
        
        try:
            # Check if we're already at max concurrent tasks
            if len(self.running_tasks) >= self.max_concurrent_tasks:
                return {
                    "success": False,
                    "error": "Maximum concurrent tasks reached",
                    "status": "deferred"
                }
            
            # Check if task is already running
            if task_id in self.running_tasks:
                return {
                    "success": False,
                    "error": "Task is already running",
                    "status": "running"
                }
            
            # Mark task as running
            self.running_tasks[task_id] = {
                "task": task,
                "start_time": datetime.now().isoformat(),
                "status": "running"
            }
            
            # Log task start
            logger.info(f"Executing task: {task_id} - {task.get('name', 'Unnamed task')}")
            
            # Determine the platform and action
            platform = task.get("platform")
            action = task.get("action")
            
            if not platform or not action:
                # Check if this is a step-based task
                if "steps" in task and task["steps"]:
                    return self._execute_step_task(task)
                else:
                    raise ValueError("Task must specify platform and action or contain steps")
            
            # Load platform handler if needed
            if platform not in self.platform_handlers:
                success = self._load_platform_handler(platform)
                if not success:
                    raise ValueError(f"Failed to load platform handler: {platform}")
            
            # Get handler
            handler = self.platform_handlers[platform]
            
            # Check if handler supports the action
            if not hasattr(handler, action) or not callable(getattr(handler, action)):
                raise ValueError(f"Platform handler {platform} does not support action: {action}")
            
            # Execute the action
            action_method = getattr(handler, action)
            params = task.get("params", {})
            
            result = action_method(**params)
            
            # Process result
            execution_result = {
                "success": True,
                "result": result,
                "status": "completed",
                "execution_time": self._get_execution_time(task_id)
            }
            
            # Update task history
            self._update_task_history(task_id, execution_result)
            
            # Remove from running tasks
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            logger.info(f"Task completed successfully: {task_id}")
            return execution_result
        except Exception as e:
            logger.error(f"Error executing task {task_id}: {e}")
            logger.error(traceback.format_exc())
            
            # Update task history with error
            execution_result = {
                "success": False,
                "error": str(e),
                "status": "failed",
                "execution_time": self._get_execution_time(task_id)
            }
            self._update_task_history(task_id, execution_result)
            
            # Remove from running tasks
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            return execution_result
    
    def _execute_step_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task with multiple steps.
        
        Args:
            task: Task data with steps
            
        Returns:
            Dict containing execution result
        """
        task_id = task["id"]
        steps = task["steps"]
        step_results = []
        
        try:
            # Execute each step
            for i, step in enumerate(steps):
                logger.info(f"Executing step {i+1}/{len(steps)} of task {task_id}: {step.get('name', 'Unnamed step')}")
                
                # Create a sub-task for this step
                step_task = {
                    "id": f"{task_id}_step{i+1}",
                    "name": step.get("name", f"Step {i+1}"),
                    "platform": step.get("platform"),
                    "action": step.get("action"),
                    "params": step.get("params", {})
                }
                
                # Execute the step
                step_result = self.execute_task(step_task)
                step_results.append(step_result)
                
                # If step failed and it's not the last step, abort the task
                if not step_result["success"] and i < len(steps) - 1:
                    logger.warning(f"Step {i+1} failed, aborting task {task_id}")
                    break
            
            # Determine overall success
            success = all(result["success"] for result in step_results)
            
            # Process result
            execution_result = {
                "success": success,
                "status": "completed" if success else "failed",
                "step_results": step_results,
                "execution_time": self._get_execution_time(task_id)
            }
            
            # Update task history
            self._update_task_history(task_id, execution_result)
            
            # Remove from running tasks
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            logger.info(f"Task completed with status {execution_result['status']}: {task_id}")
            return execution_result
        except Exception as e:
            logger.error(f"Error executing step task {task_id}: {e}")
            logger.error(traceback.format_exc())
            
            # Update task history with error
            execution_result = {
                "success": False,
                "error": str(e),
                "status": "failed",
                "step_results": step_results,
                "execution_time": self._get_execution_time(task_id)
            }
            self._update_task_history(task_id, execution_result)
            
            # Remove from running tasks
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            return execution_result
    
    def _get_execution_time(self, task_id: str) -> float:
        """
        Get the execution time for a task in seconds.
        
        Args:
            task_id: Task ID
            
        Returns:
            float: Execution time in seconds
        """
        if task_id not in self.running_tasks:
            return 0.0
        
        start_time = datetime.fromisoformat(self.running_tasks[task_id]["start_time"])
        end_time = datetime.now()
        
        return (end_time - start_time).total_seconds()
    
    def _update_task_history(self, task_id: str, result: Dict[str, Any]) -> None:
        """
        Update task execution history.
        
        Args:
            task_id: Task ID
            result: Execution result
        """
        # Create history entry
        history_entry = {
            "task_id": task_id,
            "timestamp": datetime.now().isoformat(),
            "result": result
        }
        
        # Add to history
        if task_id not in self.task_history:
            self.task_history[task_id] = []
        
        self.task_history[task_id].append(history_entry)
        
        # Save history periodically
        if len(self.task_history) % 10 == 0:
            self._save_task_history()
    
    def abort_task(self, task_id: str, reason: str) -> bool:
        """
        Abort a running task.
        
        Args:
            task_id: Task ID
            reason: Reason for aborting
            
        Returns:
            bool: True if aborted successfully, False otherwise
        """
        if task_id not in self.running_tasks:
            logger.warning(f"Task not running: {task_id}")
            return False
        
        try:
            logger.info(f"Aborting task {task_id}: {reason}")
            
            # Update task history
            execution_result = {
                "success": False,
                "error": f"Task aborted: {reason}",
                "status": "aborted",
                "execution_time": self._get_execution_time(task_id)
            }
            self._update_task_history(task_id, execution_result)
            
            # Remove from running tasks
            del self.running_tasks[task_id]
            
            return True
        except Exception as e:
            logger.error(f"Error aborting task {task_id}: {e}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of a task.
        
        Args:
            task_id: Task ID
            
        Returns:
            Dict containing task status or None if not found
        """
        # Check if task is currently running
        if task_id in self.running_tasks:
            running_task = self.running_tasks[task_id]
            return {
                "status": "running",
                "start_time": running_task["start_time"],
                "execution_time": self._get_execution_time(task_id)
            }
        
        # Check task history
        if task_id in self.task_history:
            history = self.task_history[task_id]
            if history:
                latest = history[-1]
                return {
                    "status": latest["result"]["status"],
                    "timestamp": latest["timestamp"],
                    "success": latest["result"]["success"],
                    "execution_time": latest["result"].get("execution_time", 0)
                }
        
        return None
    
    def get_task_history(self, task_id: str) -> List[Dict[str, Any]]:
        """
        Get the execution history for a task.
        
        Args:
            task_id: Task ID
            
        Returns:
            List of history entries
        """
        if task_id in self.task_history:
            return self.task_history[task_id]
        
        return []
    
    def list_running_tasks(self) -> List[Dict[str, Any]]:
        """
        Get a list of currently running tasks.
        
        Returns:
            List of running task data
        """
        return [
            {
                "task_id": task_id,
                "name": task_data["task"].get("name", "Unnamed task"),
                "start_time": task_data["start_time"],
                "execution_time": self._get_execution_time(task_id)
            }
            for task_id, task_data in self.running_tasks.items()
        ]
    
    def status(self) -> Dict[str, Any]:
        """
        Get the current status of the Task Execution Agent.
        
        Returns:
            Dict containing status information
        """
        return {
            "active": self.active,
            "running_tasks": len(self.running_tasks),
            "task_history_count": len(self.task_history),
            "loaded_platforms": list(self.platform_handlers.keys()),
            "max_concurrent_tasks": self.max_concurrent_tasks
        }


if __name__ == "__main__":
    # Simple test
    agent = TaskExecutionAgent()
    agent.start()
    
    # Create a test task
    test_task = {
        "id": "test-task-123",
        "name": "Test Task",
        "platform": "web",
        "action": "visit",
        "params": {
            "url": "https://example.com"
        }
    }
    
    # Execute the task
    result = agent.execute_task(test_task)
    print(f"Task execution result: {result}")
    
    # Get task status
    status = agent.get_task_status("test-task-123")
    print(f"Task status: {status}")
    
    # Print agent status
    print(agent.status())
    
    agent.stop()