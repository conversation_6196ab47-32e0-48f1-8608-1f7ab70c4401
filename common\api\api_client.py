"""
API Client for making HTTP requests

This module provides a general-purpose API client with retry mechanism,
error handling, and proxy support.
"""

import json
import logging
from typing import Any, Dict, Optional, Union
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

from ..utils.retry_mechanism import retry_mechanism
from ..utils.error_handlers import error_handler

class APIClient:
    """Generic API client for making HTTP requests"""
    
    def __init__(
        self,
        base_url: str = None,
        timeout: int = 30,
        max_retries: int = 3,
        verify_ssl: bool = True
    ):
        """
        Initialize API client
        
        Args:
            base_url: Base URL for all requests
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries
            verify_ssl: Whether to verify SSL certificates
        """
        self.base_url = base_url
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        self.session = self._create_session(max_retries)
        self.logger = logging.getLogger(__name__)
    
    def _create_session(self, max_retries: int) -> requests.Session:
        """
        Create session with retry strategy
        
        Args:
            max_retries: Maximum number of retries
            
        Returns:
            requests.Session: Configured session
        """
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=0.5,
            status_forcelist=[500, 502, 503, 504]
        )
        
        # Mount adapter with retry strategy
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _build_url(self, endpoint: str) -> str:
        """
        Build full URL from endpoint
        
        Args:
            endpoint: API endpoint
            
        Returns:
            str: Full URL
        """
        if self.base_url:
            return f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        return endpoint
    
    def _handle_response(self, response: requests.Response) -> Any:
        """
        Handle API response
        
        Args:
            response: Response object
            
        Returns:
            Any: Parsed response data
            
        Raises:
            requests.exceptions.RequestException: If response indicates an error
        """
        try:
            response.raise_for_status()
            return response.json()
        except ValueError:
            return response.text
    
    @retry_mechanism.retry(max_attempts=3, delay=1, backoff=2)
    def request(
        self,
        method: str,
        endpoint: str,
        params: Dict = None,
        data: Dict = None,
        json_data: Dict = None,
        headers: Dict = None,
        proxy: Dict = None,
        **kwargs
    ) -> Any:
        """
        Make HTTP request
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            params: Query parameters
            data: Form data
            json_data: JSON data
            headers: Request headers
            proxy: Proxy configuration
            **kwargs: Additional request parameters
            
        Returns:
            Any: Response data
            
        Raises:
            requests.exceptions.RequestException: If request fails
        """
        url = self._build_url(endpoint)
        
        request_kwargs = {
            'params': params,
            'data': data,
            'json': json_data,
            'headers': headers,
            'proxies': proxy,
            'timeout': self.timeout,
            'verify': self.verify_ssl,
            **kwargs
        }
        
        try:
            response = self.session.request(method, url, **request_kwargs)
            return self._handle_response(response)
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {str(e)}")
            raise
    
    def get(self, endpoint: str, **kwargs) -> Any:
        """Make GET request"""
        return self.request('GET', endpoint, **kwargs)
    
    def post(self, endpoint: str, **kwargs) -> Any:
        """Make POST request"""
        return self.request('POST', endpoint, **kwargs)
    
    def put(self, endpoint: str, **kwargs) -> Any:
        """Make PUT request"""
        return self.request('PUT', endpoint, **kwargs)
    
    def delete(self, endpoint: str, **kwargs) -> Any:
        """Make DELETE request"""
        return self.request('DELETE', endpoint, **kwargs)
    
    def patch(self, endpoint: str, **kwargs) -> Any:
        """Make PATCH request"""
        return self.request('PATCH', endpoint, **kwargs)
    
    def head(self, endpoint: str, **kwargs) -> Any:
        """Make HEAD request"""
        return self.request('HEAD', endpoint, **kwargs)
    
    def options(self, endpoint: str, **kwargs) -> Any:
        """Make OPTIONS request"""
        return self.request('OPTIONS', endpoint, **kwargs)
    
    def set_default_headers(self, headers: Dict[str, str]) -> None:
        """
        Set default headers for all requests
        
        Args:
            headers: Headers to set
        """
        self.session.headers.update(headers)
    
    def set_proxy(self, proxy: Dict[str, str]) -> None:
        """
        Set proxy for all requests
        
        Args:
            proxy: Proxy configuration
        """
        self.session.proxies.update(proxy)
    
    def close(self) -> None:
        """Close session"""
        self.session.close()
    
    def __enter__(self):
        """Context manager enter"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
