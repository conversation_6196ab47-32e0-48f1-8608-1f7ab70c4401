from enum import Enum

class ProjectStatus(Enum):
    NEW = "new"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class ProjectType(Enum):
    AIRDROP = "airdrop"
    PRESALE = "presale"
    NFT = "nft"
    OTHER = "other"

class SourceType(Enum):
    TWITTER = "twitter"
    DISCORD = "discord"
    TELEGRAM = "telegram"
    WEBSITE = "website"
    BLOCKCHAIN = "blockchain"

class ProjectRisk(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class VerificationStatus(Enum):
    PENDING = "pending"
    VERIFIED = "verified"
    REJECTED = "rejected"
