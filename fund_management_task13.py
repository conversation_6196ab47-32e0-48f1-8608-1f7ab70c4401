#!/usr/bin/env python3
"""
Fund Management Agent - Task 13

完成Authorization Manager并创建其他Security模块文件
"""

from pathlib import Path

def create_file(path: str, content: str):
    """Create a file with the given content."""
    file_path = Path(path)
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Created: {path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create {path}: {e}")
        return False

def complete_authorization_manager():
    """完成Authorization Manager的剩余方法"""
    
    additional_content = '''
    async def check_permission(self, session_token: str, permission: str) -> bool:
        """
        检查用户权限
        
        Args:
            session_token: 会话令牌
            permission: 权限名称
            
        Returns:
            bool: 是否有权限
        """
        try:
            self.auth_stats['permission_checks'] += 1
            
            # 验证会话
            session = await self._validate_session(session_token)
            if not session:
                self.auth_stats['permission_denials'] += 1
                return False
            
            username = session["username"]
            user = self.users[username]
            
            # 检查用户角色权限
            user_permissions = await self._get_user_permissions(username)
            
            has_permission = permission in user_permissions
            
            if not has_permission:
                self.auth_stats['permission_denials'] += 1
                self.logger.warning(f"Permission denied: {username} -> {permission}")
            
            return has_permission
            
        except Exception as e:
            self.logger.error(f"Error checking permission: {e}")
            self.auth_stats['permission_denials'] += 1
            return False
    
    async def _create_session(self, username: str) -> str:
        """创建用户会话"""
        try:
            session_token = secrets.token_urlsafe(32)
            
            session_data = {
                "token": session_token,
                "username": username,
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow() + timedelta(minutes=self.security_config["session_timeout_minutes"]),
                "last_activity": datetime.utcnow()
            }
            
            self.active_sessions[session_token] = session_data
            self.auth_stats['active_sessions'] = len(self.active_sessions)
            
            return session_token
            
        except Exception as e:
            self.logger.error(f"Error creating session: {e}")
            return ""
    
    async def _validate_session(self, session_token: str) -> Optional[Dict]:
        """验证会话有效性"""
        try:
            if session_token not in self.active_sessions:
                return None
            
            session = self.active_sessions[session_token]
            
            # 检查会话是否过期
            if datetime.utcnow() > session["expires_at"]:
                del self.active_sessions[session_token]
                self.auth_stats['active_sessions'] = len(self.active_sessions)
                return None
            
            # 更新最后活动时间
            session["last_activity"] = datetime.utcnow()
            
            return session
            
        except Exception as e:
            self.logger.error(f"Error validating session: {e}")
            return None
    
    async def _get_user_permissions(self, username: str) -> Set[str]:
        """获取用户所有权限"""
        try:
            user = self.users[username]
            permissions = set()
            
            for role_name in user["roles"]:
                if role_name in self.roles:
                    role_permissions = self.roles[role_name].get("permissions", [])
                    permissions.update(role_permissions)
            
            return permissions
            
        except Exception as e:
            self.logger.error(f"Error getting user permissions: {e}")
            return set()
    
    async def _is_user_locked(self, username: str) -> bool:
        """检查用户是否被锁定"""
        try:
            user = self.users[username]
            
            if user.get("locked_until"):
                locked_until = datetime.fromisoformat(user["locked_until"])
                if datetime.utcnow() < locked_until:
                    return True
                else:
                    # 解锁用户
                    user["locked_until"] = None
                    user["failed_attempts"] = 0
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking user lock status: {e}")
            return False
    
    async def _handle_failed_login(self, username: str):
        """处理登录失败"""
        try:
            user = self.users[username]
            user["failed_attempts"] += 1
            
            if user["failed_attempts"] >= self.security_config["max_failed_attempts"]:
                # 锁定用户
                lockout_duration = timedelta(minutes=self.security_config["lockout_duration_minutes"])
                user["locked_until"] = (datetime.utcnow() + lockout_duration).isoformat()
                
                self.logger.warning(f"User {username} locked due to failed login attempts")
            
        except Exception as e:
            self.logger.error(f"Error handling failed login: {e}")
    
    def _validate_password(self, password: str) -> bool:
        """验证密码强度"""
        try:
            min_length = self.security_config["password_min_length"]
            
            if len(password) < min_length:
                return False
            
            # 检查是否包含大小写字母、数字和特殊字符
            has_upper = any(c.isupper() for c in password)
            has_lower = any(c.islower() for c in password)
            has_digit = any(c.isdigit() for c in password)
            has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
            
            return has_upper and has_lower and has_digit and has_special
            
        except Exception as e:
            self.logger.error(f"Error validating password: {e}")
            return False
    
    def _hash_password(self, password: str) -> str:
        """哈希密码"""
        try:
            salt = secrets.token_hex(16)
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return f"{salt}:{password_hash.hex()}"
        except Exception as e:
            self.logger.error(f"Error hashing password: {e}")
            return ""
    
    def _verify_password(self, password: str, stored_hash: str) -> bool:
        """验证密码"""
        try:
            salt, hash_hex = stored_hash.split(':')
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash.hex() == hash_hex
        except Exception as e:
            self.logger.error(f"Error verifying password: {e}")
            return False
    
    def _verify_2fa(self, secret: str, code: str) -> bool:
        """验证双因素认证码"""
        try:
            # 简化的2FA验证（实际应使用TOTP库）
            return len(code) == 6 and code.isdigit()
        except Exception as e:
            self.logger.error(f"Error verifying 2FA: {e}")
            return False
    
    async def _load_default_permissions(self):
        """加载默认权限"""
        try:
            self.permissions.update(self.default_permissions)
            self.logger.info(f"Loaded {len(self.default_permissions)} default permissions")
        except Exception as e:
            self.logger.error(f"Error loading default permissions: {e}")
    
    async def _load_default_roles(self):
        """加载默认角色"""
        try:
            self.roles.update(self.default_roles)
            self.logger.info(f"Loaded {len(self.default_roles)} default roles")
        except Exception as e:
            self.logger.error(f"Error loading default roles: {e}")
    
    async def logout_user(self, session_token: str) -> bool:
        """用户登出"""
        try:
            if session_token in self.active_sessions:
                del self.active_sessions[session_token]
                self.auth_stats['active_sessions'] = len(self.active_sessions)
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error logging out user: {e}")
            return False
    
    async def get_auth_statistics(self) -> Dict[str, Any]:
        """获取授权统计信息"""
        try:
            success_rate = 0.0
            if self.auth_stats['total_login_attempts'] > 0:
                success_rate = (self.auth_stats['successful_logins'] / 
                              self.auth_stats['total_login_attempts'])
            
            return {
                'auth_stats': self.auth_stats,
                'success_rate': round(success_rate, 3),
                'total_users': len(self.users),
                'total_roles': len(self.roles),
                'total_permissions': len(self.permissions),
                'security_config': self.security_config
            }
        except Exception as e:
            self.logger.error(f"Error getting auth statistics: {e}")
            return {}
'''
    
    return additional_content

def create_permission_scanner():
    """创建Permission Scanner"""
    
    scanner_content = '''"""
Permission Scanner

权限扫描器，负责扫描和分析系统权限配置。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime


class PermissionScanner:
    """
    权限扫描器
    
    负责扫描系统权限配置，检测权限异常和安全风险。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化权限扫描器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 扫描结果存储
        self.scan_results: List[Dict] = []
        
        # 扫描统计
        self.scan_stats = {
            'total_scans': 0,
            'vulnerabilities_found': 0,
            'high_risk_issues': 0,
            'last_scan_time': None
        }
        
        # 权限风险规则
        self.risk_rules = {
            "excessive_permissions": {
                "description": "用户拥有过多权限",
                "risk_level": "medium",
                "threshold": 10
            },
            "admin_without_2fa": {
                "description": "管理员未启用双因素认证",
                "risk_level": "high"
            },
            "inactive_high_privilege": {
                "description": "高权限用户长期未活动",
                "risk_level": "medium",
                "threshold_days": 30
            }
        }
    
    async def initialize(self) -> bool:
        """
        初始化权限扫描器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("Permission Scanner initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Permission Scanner: {e}")
            return False
    
    async def scan_permissions(self, auth_manager) -> Dict[str, Any]:
        """
        扫描权限配置
        
        Args:
            auth_manager: 授权管理器实例
            
        Returns:
            Dict[str, Any]: 扫描结果
        """
        try:
            self.scan_stats['total_scans'] += 1
            
            scan_result = {
                "scan_id": f"scan_{len(self.scan_results) + 1}",
                "scan_time": datetime.utcnow().isoformat(),
                "vulnerabilities": [],
                "recommendations": [],
                "risk_score": 0.0
            }
            
            # 执行各种权限检查
            await self._check_excessive_permissions(auth_manager, scan_result)
            await self._check_admin_2fa(auth_manager, scan_result)
            await self._check_inactive_users(auth_manager, scan_result)
            
            # 计算风险评分
            scan_result["risk_score"] = self._calculate_risk_score(scan_result["vulnerabilities"])
            
            # 生成建议
            scan_result["recommendations"] = self._generate_recommendations(scan_result["vulnerabilities"])
            
            self.scan_results.append(scan_result)
            self.scan_stats['vulnerabilities_found'] += len(scan_result["vulnerabilities"])
            self.scan_stats['last_scan_time'] = datetime.utcnow().isoformat()
            
            # 统计高风险问题
            high_risk_count = len([v for v in scan_result["vulnerabilities"] if v["risk_level"] == "high"])
            self.scan_stats['high_risk_issues'] += high_risk_count
            
            self.logger.info(f"Permission scan completed: {len(scan_result['vulnerabilities'])} issues found")
            return scan_result
            
        except Exception as e:
            self.logger.error(f"Error scanning permissions: {e}")
            return {"scan_id": "error", "vulnerabilities": [], "error": str(e)}
    
    async def _check_excessive_permissions(self, auth_manager, scan_result: Dict):
        """检查过度权限"""
        try:
            threshold = self.risk_rules["excessive_permissions"]["threshold"]
            
            for username, user_data in auth_manager.users.items():
                user_permissions = await auth_manager._get_user_permissions(username)
                
                if len(user_permissions) > threshold:
                    vulnerability = {
                        "type": "excessive_permissions",
                        "user": username,
                        "description": f"用户拥有 {len(user_permissions)} 个权限，超过阈值 {threshold}",
                        "risk_level": "medium",
                        "permissions_count": len(user_permissions),
                        "detected_at": datetime.utcnow().isoformat()
                    }
                    scan_result["vulnerabilities"].append(vulnerability)
                    
        except Exception as e:
            self.logger.error(f"Error checking excessive permissions: {e}")
    
    async def _check_admin_2fa(self, auth_manager, scan_result: Dict):
        """检查管理员双因素认证"""
        try:
            for username, user_data in auth_manager.users.items():
                if "admin" in user_data.get("roles", []):
                    if not user_data.get("two_factor_enabled", False):
                        vulnerability = {
                            "type": "admin_without_2fa",
                            "user": username,
                            "description": "管理员用户未启用双因素认证",
                            "risk_level": "high",
                            "detected_at": datetime.utcnow().isoformat()
                        }
                        scan_result["vulnerabilities"].append(vulnerability)
                        
        except Exception as e:
            self.logger.error(f"Error checking admin 2FA: {e}")
    
    async def _check_inactive_users(self, auth_manager, scan_result: Dict):
        """检查不活跃用户"""
        try:
            threshold_days = self.risk_rules["inactive_high_privilege"]["threshold_days"]
            cutoff_time = datetime.utcnow() - timedelta(days=threshold_days)
            
            for username, user_data in auth_manager.users.items():
                last_login = user_data.get("last_login")
                if last_login:
                    last_login_time = datetime.fromisoformat(last_login)
                    if last_login_time < cutoff_time:
                        # 检查是否有高权限
                        user_permissions = await auth_manager._get_user_permissions(username)
                        high_risk_permissions = [p for p in user_permissions 
                                               if auth_manager.permissions.get(p, {}).get("risk_level") in ["high", "critical"]]
                        
                        if high_risk_permissions:
                            vulnerability = {
                                "type": "inactive_high_privilege",
                                "user": username,
                                "description": f"高权限用户 {threshold_days} 天未活动",
                                "risk_level": "medium",
                                "last_login": last_login,
                                "high_risk_permissions": high_risk_permissions,
                                "detected_at": datetime.utcnow().isoformat()
                            }
                            scan_result["vulnerabilities"].append(vulnerability)
                            
        except Exception as e:
            self.logger.error(f"Error checking inactive users: {e}")
    
    def _calculate_risk_score(self, vulnerabilities: List[Dict]) -> float:
        """计算风险评分"""
        try:
            risk_weights = {"low": 1, "medium": 3, "high": 7, "critical": 10}
            total_score = 0
            
            for vuln in vulnerabilities:
                risk_level = vuln.get("risk_level", "low")
                total_score += risk_weights.get(risk_level, 1)
            
            # 标准化到0-100分
            max_possible_score = len(vulnerabilities) * 10
            if max_possible_score > 0:
                return min(100, (total_score / max_possible_score) * 100)
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating risk score: {e}")
            return 0.0
    
    def _generate_recommendations(self, vulnerabilities: List[Dict]) -> List[str]:
        """生成安全建议"""
        try:
            recommendations = []
            
            vuln_types = set(v["type"] for v in vulnerabilities)
            
            if "excessive_permissions" in vuln_types:
                recommendations.append("审查用户权限，移除不必要的权限")
            
            if "admin_without_2fa" in vuln_types:
                recommendations.append("为所有管理员用户启用双因素认证")
            
            if "inactive_high_privilege" in vuln_types:
                recommendations.append("定期审查不活跃的高权限用户，考虑禁用或降权")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error generating recommendations: {e}")
            return []
'''
    
    return scanner_content

def main():
    """主函数"""
    print("🚀 Fund Management Agent - Task 13")
    print("=" * 50)
    
    # 完成Authorization Manager
    additional_content = complete_authorization_manager()
    
    try:
        with open("fund_management/security/authorization_manager.py", 'a', encoding='utf-8') as f:
            f.write(additional_content)
        print("✅ Completed Authorization Manager methods")
    except Exception as e:
        print(f"❌ Failed to complete Authorization Manager: {e}")
    
    # 创建Permission Scanner
    scanner_content = create_permission_scanner()
    success = create_file("fund_management/security/permission_scanner.py", scanner_content)
    
    if success:
        print("✅ Task 13 completed: Permission Scanner created!")
    else:
        print("❌ Task 13 failed")

if __name__ == "__main__":
    main()
