"""
MetaMask Manager

This module is responsible for managing MetaMask browser extension,
including setup, wallet import, and transaction signing.
"""

import os
import json
import time
import logging
import base64
from typing import Dict, Any, Optional, List, Tuple
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

logger = logging.getLogger("MetaMaskManager")

class MetaMaskManager:
    """
    Manages the MetaMask browser extension for Ethereum wallet operations.
    
    Features:
    - Extension installation and setup
    - Wallet import and creation
    - Transaction signing
    - Network management
    """
    
    def __init__(self, 
                 extension_path: str = "data/extensions/metamask",
                 data_dir: str = "data/browser_profiles/metamask",
                 headless: bool = False):
        """
        Initialize the MetaMask manager.
        
        Args:
            extension_path: Path to the MetaMask extension files
            data_dir: Path to store browser profile data
            headless: Whether to run the browser in headless mode
        """
        self.extension_path = os.path.abspath(extension_path)
        self.data_dir = os.path.abspath(data_dir)
        self.headless = headless
        self.driver = None
        self.is_initialized = False
        
        # Ensure directories exist
        os.makedirs(self.extension_path, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)
        
        logger.info("MetaMask manager initialized")
    
    def _setup_driver(self) -> webdriver.Chrome:
        """
        Set up and configure the Chrome WebDriver with MetaMask.
        
        Returns:
            webdriver.Chrome: Configured Chrome WebDriver
        """
        chrome_options = Options()
        
        # Add MetaMask extension
        chrome_options.add_argument(f"--load-extension={self.extension_path}")
        
        # Set user data directory
        chrome_options.add_argument(f"--user-data-dir={self.data_dir}")
        
        # Additional options
        if self.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Create and return the driver
        driver = webdriver.Chrome(options=chrome_options)
        logger.info("Chrome WebDriver set up with MetaMask extension")
        return driver
    
    def start(self) -> bool:
        """
        Start the MetaMask manager.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            self.driver = self._setup_driver()
            
            # Navigate to MetaMask extension page
            self.driver.get("chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn/home.html")
            
            # Wait for MetaMask to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            self.is_initialized = True
            logger.info("MetaMask manager started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start MetaMask manager: {e}")
            if self.driver:
                self.driver.quit()
                self.driver = None
            return False
    
    def stop(self) -> bool:
        """
        Stop the MetaMask manager.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
            
            self.is_initialized = False
            logger.info("MetaMask manager stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Error stopping MetaMask manager: {e}")
            return False
    
    def is_setup_required(self) -> bool:
        """
        Check if MetaMask needs initial setup.
        
        Returns:
            bool: True if setup is required, False otherwise
        """
        if not self.is_initialized or not self.driver:
            logger.error("MetaMask manager not initialized")
            return True
        
        try:
            # Navigate to MetaMask
            self.driver.get("chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn/home.html")
            
            # Wait for page to load
            WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Check for welcome screen or unlock screen
            welcome_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Welcome to MetaMask')]")
            unlock_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Unlock your wallet')]")
            
            if welcome_elements:
                logger.info("MetaMask setup is required (welcome screen detected)")
                return True
            elif unlock_elements:
                logger.info("MetaMask is locked but already set up")
                return False
            else:
                # If neither welcome nor unlock screen is present, MetaMask is already set up and unlocked
                logger.info("MetaMask is already set up and unlocked")
                return False
        except Exception as e:
            logger.error(f"Error checking MetaMask setup status: {e}")
            return True
    
    def setup_new_wallet(self, password: str) -> Tuple[bool, Optional[str]]:
        """
        Set up a new MetaMask wallet.
        
        Args:
            password: Password for the new wallet
            
        Returns:
            Tuple[bool, Optional[str]]: (Success status, seed phrase if successful)
        """
        if not self.is_initialized or not self.driver:
            logger.error("MetaMask manager not initialized")
            return False, None
        
        try:
            # Navigate to MetaMask
            self.driver.get("chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn/home.html")
            
            # Wait for welcome screen
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Welcome to MetaMask')]"))
            )
            
            # Click "Create a new wallet"
            create_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), 'Create a new wallet')]"))
            )
            create_button.click()
            
            # Agree to terms
            agree_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), 'I agree')]"))
            )
            agree_button.click()
            
            # Enter password
            password_field = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.ID, "create-password"))
            )
            password_field.send_keys(password)
            
            # Confirm password
            confirm_field = self.driver.find_element(By.ID, "confirm-password")
            confirm_field.send_keys(password)
            
            # Accept terms
            terms_checkbox = self.driver.find_element(By.CSS_SELECTOR, "input[type='checkbox']")
            terms_checkbox.click()
            
            # Click create
            create_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Create')]"))
            )
            create_button.click()
            
            # Watch the video or skip
            skip_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), 'Skip')]"))
            )
            skip_button.click()
            
            # Reveal seed phrase
            reveal_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), 'Reveal Secret Recovery Phrase')]"))
            )
            reveal_button.click()
            
            # Get seed phrase
            seed_phrase_element = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".seed-phrase-container"))
            )
            seed_phrase = seed_phrase_element.text.strip()
            
            # Continue
            continue_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Next')]"))
            )
            continue_button.click()
            
            # Confirm seed phrase (this is simplified - in reality, you'd need to select the words in order)
            confirm_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Confirm')]"))
            )
            confirm_button.click()
            
            # Complete setup
            complete_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Got it!')]"))
            )
            complete_button.click()
            
            logger.info("MetaMask wallet setup completed successfully")
            return True, seed_phrase
        except Exception as e:
            logger.error(f"Failed to set up new MetaMask wallet: {e}")
            return False, None
    
    def import_wallet(self, seed_phrase: str, password: str) -> bool:
        """
        Import an existing wallet using a seed phrase.
        
        Args:
            seed_phrase: The seed phrase (mnemonic)
            password: Password for the wallet
            
        Returns:
            bool: True if imported successfully, False otherwise
        """
        if not self.is_initialized or not self.driver:
            logger.error("MetaMask manager not initialized")
            return False
        
        try:
            # Navigate to MetaMask
            self.driver.get("chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn/home.html")
            
            # Wait for welcome screen
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Welcome to MetaMask')]"))
            )
            
            # Click "Import wallet"
            import_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), 'Import wallet')]"))
            )
            import_button.click()
            
            # Agree to terms
            agree_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), 'I agree')]"))
            )
            agree_button.click()
            
            # Enter seed phrase
            seed_field = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder='Paste Secret Recovery Phrase from clipboard']"))
            )
            seed_field.send_keys(seed_phrase)
            
            # Enter password
            password_field = self.driver.find_element(By.ID, "password")
            password_field.send_keys(password)
            
            # Confirm password
            confirm_field = self.driver.find_element(By.ID, "confirm-password")
            confirm_field.send_keys(password)
            
            # Accept terms
            terms_checkbox = self.driver.find_element(By.CSS_SELECTOR, "input[type='checkbox']")
            terms_checkbox.click()
            
            # Click import
            import_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Import')]"))
            )
            import_button.click()
            
            # Wait for import to complete
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Congratulations')]"))
            )
            
            # Complete setup
            complete_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Got it!')]"))
            )
            complete_button.click()
            
            logger.info("MetaMask wallet imported successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to import MetaMask wallet: {e}")
            return False
    
    def unlock_wallet(self, password: str) -> bool:
        """
        Unlock the MetaMask wallet.
        
        Args:
            password: Wallet password
            
        Returns:
            bool: True if unlocked successfully, False otherwise
        """
        if not self.is_initialized or not self.driver:
            logger.error("MetaMask manager not initialized")
            return False
        
        try:
            # Navigate to MetaMask
            self.driver.get("chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn/home.html")
            
            # Check if already unlocked
            try:
                WebDriverWait(self.driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Unlock your wallet')]"))
                )
            except TimeoutException:
                # If "Unlock your wallet" text is not found, it might already be unlocked
                try:
                    WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Account')]"))
                    )
                    logger.info("MetaMask wallet is already unlocked")
                    return True
                except TimeoutException:
                    pass
            
            # Enter password
            password_field = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.ID, "password"))
            )
            password_field.send_keys(password)
            
            # Click unlock
            unlock_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Unlock')]"))
            )
            unlock_button.click()
            
            # Wait for unlock to complete
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Account')]"))
            )
            
            logger.info("MetaMask wallet unlocked successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to unlock MetaMask wallet: {e}")
            return False
    
    def get_wallet_address(self) -> Optional[str]:
        """
        Get the current wallet address.
        
        Returns:
            str: Wallet address or None if failed
        """
        if not self.is_initialized or not self.driver:
            logger.error("MetaMask manager not initialized")
            return None
        
        try:
            # Navigate to MetaMask
            self.driver.get("chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn/home.html")
            
            # Click on account details
            account_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'account-menu__icon')]"))
            )
            account_button.click()
            
            # Get address
            address_element = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'account-details__address')]"))
            )
            address = address_element.text.strip()
            
            # Close account details
            self.driver.find_element(By.XPATH, "//button[contains(@class, 'account-menu__close')]").click()
            
            logger.info(f"Retrieved wallet address: {address}")
            return address
        except Exception as e:
            logger.error(f"Failed to get wallet address: {e}")
            return None
    
    def switch_network(self, network_name: str) -> bool:
        """
        Switch to a different Ethereum network.
        
        Args:
            network_name: Network name (e.g., "Ethereum Mainnet", "Goerli Test Network")
            
        Returns:
            bool: True if switched successfully, False otherwise
        """
        if not self.is_initialized or not self.driver:
            logger.error("MetaMask manager not initialized")
            return False
        
        try:
            # Navigate to MetaMask
            self.driver.get("chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn/home.html")
            
            # Click on network selector
            network_selector = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'network-display')]"))
            )
            network_selector.click()
            
            # Select the network
            network_option = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, f"//span[contains(text(), '{network_name}')]"))
            )
            network_option.click()
            
            # Wait for network to change
            time.sleep(2)
            
            logger.info(f"Switched to network: {network_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to switch network to {network_name}: {e}")
            return False
    
    def add_custom_network(self, network_data: Dict[str, Any]) -> bool:
        """
        Add a custom Ethereum network.
        
        Args:
            network_data: Network configuration data
            
        Returns:
            bool: True if added successfully, False otherwise
        """
        if not self.is_initialized or not self.driver:
            logger.error("MetaMask manager not initialized")
            return False
        
        try:
            # Navigate to MetaMask
            self.driver.get("chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn/home.html")
            
            # Click on network selector
            network_selector = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'network-display')]"))
            )
            network_selector.click()
            
            # Click "Add network"
            add_network_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), 'Add network')]"))
            )
            add_network_button.click()
            
            # Fill in network details
            network_name_field = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.ID, "network-name"))
            )
            network_name_field.send_keys(network_data["networkName"])
            
            rpc_url_field = self.driver.find_element(By.ID, "rpc-url")
            rpc_url_field.send_keys(network_data["rpcUrl"])
            
            chain_id_field = self.driver.find_element(By.ID, "chainId")
            chain_id_field.send_keys(network_data["chainId"])
            
            currency_field = self.driver.find_element(By.ID, "currency-symbol")
            currency_field.send_keys(network_data["currencySymbol"])
            
            if "blockExplorerUrl" in network_data and network_data["blockExplorerUrl"]:
                explorer_field = self.driver.find_element(By.ID, "block-explorer-url")
                explorer_field.send_keys(network_data["blockExplorerUrl"])
            
            # Save network
            save_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Save')]"))
            )
            save_button.click()
            
            # Wait for confirmation
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, f"//span[contains(text(), '{network_data['networkName']}')]"))
            )
            
            logger.info(f"Added custom network: {network_data['networkName']}")
            return True
        except Exception as e:
            logger.error(f"Failed to add custom network: {e}")
            return False
    
    def sign_transaction(self, transaction_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        Sign a transaction.
        
        Args:
            transaction_data: Optional transaction data (if None, will sign pending transaction)
            
        Returns:
            bool: True if signed successfully, False otherwise
        """
        if not self.is_initialized or not self.driver:
            logger.error("MetaMask manager not initialized")
            return False
        
        try:
            # If transaction data is provided, we would need to create the transaction
            # This is a simplified implementation that assumes a transaction is already pending
            
            # Navigate to MetaMask
            self.driver.get("chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn/notification.html")
            
            # Wait for transaction confirmation screen
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Confirm')]"))
            )
            
            # Click confirm
            confirm_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Confirm')]"))
            )
            confirm_button.click()
            
            # Wait for confirmation
            time.sleep(2)
            
            logger.info("Transaction signed successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to sign transaction: {e}")
            return False
    
    def get_balance(self) -> Optional[float]:
        """
        Get the current wallet balance.
        
        Returns:
            float: Wallet balance or None if failed
        """
        if not self.is_initialized or not self.driver:
            logger.error("MetaMask manager not initialized")
            return None
        
        try:
            # Navigate to MetaMask
            self.driver.get("chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn/home.html")
            
            # Get balance element
            balance_element = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'currency-display-component__text')]"))
            )
            balance_text = balance_element.text.strip()
            
            # Parse balance (remove currency symbol and convert to float)
            balance = float(balance_text.split()[0])
            
            logger.info(f"Retrieved wallet balance: {balance}")
            return balance
        except Exception as e:
            logger.error(f"Failed to get wallet balance: {e}")
            return None


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    manager = MetaMaskManager(headless=False)
    
    try:
        manager.start()
        
        if manager.is_setup_required():
            # Either set up a new wallet or import an existing one
            success, seed_phrase = manager.setup_new_wallet("SecurePassword123!")
            print(f"New wallet setup: {success}")
            if seed_phrase:
                print(f"Seed phrase: {seed_phrase}")
        else:
            # Unlock existing wallet
            success = manager.unlock_wallet("SecurePassword123!")
            print(f"Wallet unlock: {success}")
        
        # Get wallet address and balance
        address = manager.get_wallet_address()
        balance = manager.get_balance()
        print(f"Wallet address: {address}")
        print(f"Wallet balance: {balance}")
        
        # Switch network
        manager.switch_network("Goerli Test Network")
    finally:
        manager.stop()