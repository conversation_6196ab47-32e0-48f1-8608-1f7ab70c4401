"""
Wallet Storage

This module is responsible for securely storing and retrieving wallet data,
including backup and recovery functionality.
"""

import os
import json
import shutil
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger("WalletStorage")

class WalletStorage:
    """
    Manages the storage, backup, and retrieval of wallet data.
    
    Features:
    - Secure storage of wallet information
    - Automated backups
    - Data recovery
    - Wallet metadata management
    """
    
    def __init__(self, storage_path: str = "data/wallets", backup_path: str = "data/wallets/backups"):
        """
        Initialize the wallet storage.
        
        Args:
            storage_path: Path to store wallet data
            backup_path: Path to store wallet backups
        """
        self.storage_path = storage_path
        self.backup_path = backup_path
        
        # Ensure directories exist
        os.makedirs(storage_path, exist_ok=True)
        os.makedirs(backup_path, exist_ok=True)
        
        logger.info(f"Wallet storage initialized with path {storage_path}")
    
    def store_wallet(self, wallet_data: Dict[str, Any]) -> bool:
        """
        Store wallet data.
        
        Args:
            wallet_data: Wallet data to store
            
        Returns:
            bool: True if stored successfully, False otherwise
        """
        try:
            chain = wallet_data.get("chain")
            wallet_id = wallet_data.get("id")
            
            if not chain or not wallet_id:
                logger.error("Wallet data missing required fields (chain, id)")
                return False
            
            # Ensure chain directory exists
            chain_dir = os.path.join(self.storage_path, chain)
            os.makedirs(chain_dir, exist_ok=True)
            
            # Save wallet data
            file_path = os.path.join(chain_dir, f"{wallet_id}.json")
            with open(file_path, 'w') as f:
                json.dump(wallet_data, f, indent=2)
            
            logger.info(f"Stored wallet data for {chain}/{wallet_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to store wallet data: {e}")
            return False
    
    def retrieve_wallet(self, chain: str, wallet_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve wallet data.
        
        Args:
            chain: Blockchain name
            wallet_id: Wallet identifier
            
        Returns:
            Dict containing wallet data or None if not found
        """
        try:
            file_path = os.path.join(self.storage_path, chain, f"{wallet_id}.json")
            
            if not os.path.exists(file_path):
                logger.warning(f"Wallet not found: {chain}/{wallet_id}")
                return None
            
            with open(file_path, 'r') as f:
                wallet_data = json.load(f)
            
            logger.info(f"Retrieved wallet data for {chain}/{wallet_id}")
            return wallet_data
        except Exception as e:
            logger.error(f"Failed to retrieve wallet data for {chain}/{wallet_id}: {e}")
            return None
    
    def update_wallet(self, chain: str, wallet_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update wallet data.
        
        Args:
            chain: Blockchain name
            wallet_id: Wallet identifier
            updates: Data to update
            
        Returns:
            bool: True if updated successfully, False otherwise
        """
        try:
            wallet_data = self.retrieve_wallet(chain, wallet_id)
            
            if not wallet_data:
                logger.error(f"Cannot update non-existent wallet: {chain}/{wallet_id}")
                return False
            
            # Update wallet data
            for key, value in updates.items():
                wallet_data[key] = value
            
            # Save updated data
            return self.store_wallet(wallet_data)
        except Exception as e:
            logger.error(f"Failed to update wallet {chain}/{wallet_id}: {e}")
            return False
    
    def delete_wallet(self, chain: str, wallet_id: str) -> bool:
        """
        Delete wallet data.
        
        Args:
            chain: Blockchain name
            wallet_id: Wallet identifier
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        try:
            file_path = os.path.join(self.storage_path, chain, f"{wallet_id}.json")
            
            if not os.path.exists(file_path):
                logger.warning(f"Cannot delete non-existent wallet: {chain}/{wallet_id}")
                return False
            
            # Create a backup before deletion
            self.backup_wallet(chain, wallet_id)
            
            # Delete the wallet file
            os.remove(file_path)
            logger.info(f"Deleted wallet {chain}/{wallet_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete wallet {chain}/{wallet_id}: {e}")
            return False
    
    def list_wallets(self, chain: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List all wallets or wallets for a specific chain.
        
        Args:
            chain: Optional blockchain name to filter by
            
        Returns:
            List of wallet data dictionaries
        """
        wallets = []
        
        try:
            # If chain is specified, only check that chain's directory
            if chain:
                chain_dir = os.path.join(self.storage_path, chain)
                if os.path.exists(chain_dir):
                    wallets.extend(self._list_wallets_in_dir(chain_dir))
            else:
                # Otherwise, check all chain directories
                for item in os.listdir(self.storage_path):
                    chain_dir = os.path.join(self.storage_path, item)
                    if os.path.isdir(chain_dir) and not item.startswith('.') and item != "backups":
                        wallets.extend(self._list_wallets_in_dir(chain_dir))
            
            logger.info(f"Listed {len(wallets)} wallets{f' for {chain}' if chain else ''}")
            return wallets
        except Exception as e:
            logger.error(f"Failed to list wallets: {e}")
            return []
    
    def _list_wallets_in_dir(self, directory: str) -> List[Dict[str, Any]]:
        """
        List all wallet files in a directory.
        
        Args:
            directory: Directory to search
            
        Returns:
            List of wallet data dictionaries
        """
        wallets = []
        
        for filename in os.listdir(directory):
            if filename.endswith('.json'):
                try:
                    file_path = os.path.join(directory, filename)
                    with open(file_path, 'r') as f:
                        wallet_data = json.load(f)
                    wallets.append(wallet_data)
                except Exception as e:
                    logger.error(f"Error reading wallet file {filename}: {e}")
        
        return wallets
    
    def backup_wallet(self, chain: str, wallet_id: str) -> bool:
        """
        Create a backup of a wallet.
        
        Args:
            chain: Blockchain name
            wallet_id: Wallet identifier
            
        Returns:
            bool: True if backup successful, False otherwise
        """
        try:
            source_path = os.path.join(self.storage_path, chain, f"{wallet_id}.json")
            
            if not os.path.exists(source_path):
                logger.warning(f"Cannot backup non-existent wallet: {chain}/{wallet_id}")
                return False
            
            # Create chain backup directory if it doesn't exist
            chain_backup_dir = os.path.join(self.backup_path, chain)
            os.makedirs(chain_backup_dir, exist_ok=True)
            
            # Create a timestamped backup
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{wallet_id}_{timestamp}.json"
            backup_path = os.path.join(chain_backup_dir, backup_filename)
            
            # Copy the wallet file
            shutil.copy2(source_path, backup_path)
            logger.info(f"Created backup of wallet {chain}/{wallet_id} at {backup_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to backup wallet {chain}/{wallet_id}: {e}")
            return False
    
    def backup_all_wallets(self) -> Dict[str, int]:
        """
        Create backups of all wallets.
        
        Returns:
            Dict with chains as keys and number of backups as values
        """
        backup_counts = {}
        
        try:
            # Create a timestamp for this backup set
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_set_dir = os.path.join(self.backup_path, f"backup_set_{timestamp}")
            os.makedirs(backup_set_dir, exist_ok=True)
            
            # Iterate through all chain directories
            for item in os.listdir(self.storage_path):
                chain_dir = os.path.join(self.storage_path, item)
                if os.path.isdir(chain_dir) and not item.startswith('.') and item != "backups":
                    chain = item
                    chain_backup_dir = os.path.join(backup_set_dir, chain)
                    os.makedirs(chain_backup_dir, exist_ok=True)
                    
                    # Backup all wallets in this chain
                    count = 0
                    for filename in os.listdir(chain_dir):
                        if filename.endswith('.json'):
                            source_path = os.path.join(chain_dir, filename)
                            backup_path = os.path.join(chain_backup_dir, filename)
                            shutil.copy2(source_path, backup_path)
                            count += 1
                    
                    if count > 0:
                        backup_counts[chain] = count
            
            total = sum(backup_counts.values())
            logger.info(f"Backed up {total} wallets across {len(backup_counts)} chains")
            return backup_counts
        except Exception as e:
            logger.error(f"Failed to backup all wallets: {e}")
            return {}
    
    def restore_wallet(self, chain: str, wallet_id: str, backup_timestamp: Optional[str] = None) -> bool:
        """
        Restore a wallet from backup.
        
        Args:
            chain: Blockchain name
            wallet_id: Wallet identifier
            backup_timestamp: Optional specific backup timestamp to restore
            
        Returns:
            bool: True if restored successfully, False otherwise
        """
        try:
            chain_backup_dir = os.path.join(self.backup_path, chain)
            
            if not os.path.exists(chain_backup_dir):
                logger.error(f"No backups found for chain: {chain}")
                return False
            
            # Find the backup file
            if backup_timestamp:
                # Look for a specific backup
                backup_filename = f"{wallet_id}_{backup_timestamp}.json"
                backup_path = os.path.join(chain_backup_dir, backup_filename)
                
                if not os.path.exists(backup_path):
                    logger.error(f"Specific backup not found: {backup_filename}")
                    return False
            else:
                # Find the most recent backup
                backup_files = [f for f in os.listdir(chain_backup_dir) 
                               if f.startswith(f"{wallet_id}_") and f.endswith(".json")]
                
                if not backup_files:
                    logger.error(f"No backups found for wallet: {chain}/{wallet_id}")
                    return False
                
                # Sort by timestamp (descending)
                backup_files.sort(reverse=True)
                backup_path = os.path.join(chain_backup_dir, backup_files[0])
            
            # Ensure chain directory exists
            chain_dir = os.path.join(self.storage_path, chain)
            os.makedirs(chain_dir, exist_ok=True)
            
            # Restore the wallet file
            target_path = os.path.join(chain_dir, f"{wallet_id}.json")
            shutil.copy2(backup_path, target_path)
            
            logger.info(f"Restored wallet {chain}/{wallet_id} from backup")
            return True
        except Exception as e:
            logger.error(f"Failed to restore wallet {chain}/{wallet_id}: {e}")
            return False
    
    def clean_old_backups(self, max_age_days: int = 30) -> int:
        """
        Remove backups older than the specified age.
        
        Args:
            max_age_days: Maximum age of backups to keep (in days)
            
        Returns:
            int: Number of backups removed
        """
        try:
            count = 0
            current_time = time.time()
            max_age_seconds = max_age_days * 24 * 60 * 60
            
            # Iterate through all backup directories
            for root, dirs, files in os.walk(self.backup_path):
                for file in files:
                    if file.endswith('.json'):
                        file_path = os.path.join(root, file)
                        file_age = current_time - os.path.getmtime(file_path)
                        
                        if file_age > max_age_seconds:
                            os.remove(file_path)
                            count += 1
            
            logger.info(f"Cleaned {count} old backups (older than {max_age_days} days)")
            return count
        except Exception as e:
            logger.error(f"Failed to clean old backups: {e}")
            return 0


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    storage = WalletStorage()
    
    # Test wallet storage
    test_wallet = {
        "id": "test-wallet-456",
        "chain": "ethereum",
        "address": "******************************************",
        "created_at": int(time.time()),
        "balance": 0.0
    }
    
    storage.store_wallet(test_wallet)
    retrieved = storage.retrieve_wallet("ethereum", "test-wallet-456")
    
    print(f"Stored wallet: {test_wallet}")
    print(f"Retrieved wallet: {retrieved}")
    print(f"All wallets: {storage.list_wallets()}")
    
    # Test backup and restore
    storage.backup_wallet("ethereum", "test-wallet-456")
    storage.delete_wallet("ethereum", "test-wallet-456")
    storage.restore_wallet("ethereum", "test-wallet-456")
    
    # Clean up test data
    storage.delete_wallet("ethereum", "test-wallet-456")