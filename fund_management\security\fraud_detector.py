"""
Fraud Detector

欺诈检测器，负责检测和防范欺诈行为。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta


class FraudDetector:
    """
    欺诈检测器

    负责检测可疑的欺诈行为和异常交易模式。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化欺诈检测器

        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 检测规则
        self.fraud_rules = {
            "rapid_succession": {
                "description": "快速连续交易",
                "threshold": 5,  # 5分钟内超过阈值
                "time_window": 300  # 秒
            },
            "amount_spike": {
                "description": "交易金额异常增长",
                "multiplier": 10  # 超过平均值10倍
            },
            "unusual_time": {
                "description": "异常时间交易",
                "hours": [2, 3, 4, 5]  # 凌晨2-5点
            }
        }

        # 检测历史
        self.fraud_alerts: List[Dict] = []

        # 统计信息
        self.fraud_stats = {
            'total_checks': 0,
            'fraud_detected': 0,
            'false_positives': 0,
            'blocked_transactions': 0
        }

    async def initialize(self) -> bool:
        """
        初始化欺诈检测器

        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("Fraud Detector initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Fraud Detector: {e}")
            return False

    async def detect_fraud(self, transaction_data: Dict[str, Any],
                          user_history: List[Dict] = None) -> Dict[str, Any]:
        """
        检测欺诈行为

        Args:
            transaction_data: 交易数据
            user_history: 用户历史交易

        Returns:
            Dict[str, Any]: 检测结果
        """
        try:
            self.fraud_stats['total_checks'] += 1

            fraud_indicators = []
            fraud_score = 0.0

            # 检查快速连续交易
            if user_history:
                rapid_check = await self._check_rapid_succession(transaction_data, user_history)
                if rapid_check["is_fraud"]:
                    fraud_indicators.append(rapid_check)
                    fraud_score += rapid_check["score"]

            # 检查金额异常
            amount_check = await self._check_amount_anomaly(transaction_data, user_history or [])
            if amount_check["is_fraud"]:
                fraud_indicators.append(amount_check)
                fraud_score += amount_check["score"]

            # 检查异常时间
            time_check = await self._check_unusual_timing(transaction_data)
            if time_check["is_fraud"]:
                fraud_indicators.append(time_check)
                fraud_score += time_check["score"]

            # 标准化评分
            normalized_score = min(1.0, fraud_score)
            is_fraud = normalized_score > 0.7

            detection_result = {
                "detection_id": f"fraud_{len(self.fraud_alerts) + 1}",
                "transaction_id": transaction_data.get("id", "unknown"),
                "is_fraud": is_fraud,
                "fraud_score": round(normalized_score, 3),
                "fraud_indicators": fraud_indicators,
                "detected_at": datetime.utcnow().isoformat(),
                "recommended_action": "block" if is_fraud else "allow"
            }

            if is_fraud:
                self.fraud_alerts.append(detection_result)
                self.fraud_stats['fraud_detected'] += 1
                self.logger.warning(f"Fraud detected: {detection_result['detection_id']}")

            return detection_result

        except Exception as e:
            self.logger.error(f"Error detecting fraud: {e}")
            return {"is_fraud": False, "error": str(e)}

    async def _check_rapid_succession(self, transaction_data: Dict, user_history: List[Dict]) -> Dict:
        """检查快速连续交易"""
        try:
            current_time = datetime.utcnow()
            time_window = self.fraud_rules["rapid_succession"]["time_window"]
            threshold = self.fraud_rules["rapid_succession"]["threshold"]

            # 计算时间窗口内的交易数量
            recent_transactions = 0
            for tx in user_history:
                tx_time = datetime.fromisoformat(tx.get("timestamp", current_time.isoformat()))
                if (current_time - tx_time).seconds <= time_window:
                    recent_transactions += 1

            is_fraud = recent_transactions >= threshold
            score = min(1.0, recent_transactions / threshold) if is_fraud else 0.0

            return {
                "type": "rapid_succession",
                "is_fraud": is_fraud,
                "score": score,
                "description": f"{recent_transactions} 笔交易在 {time_window} 秒内",
                "threshold": threshold,
                "actual_count": recent_transactions
            }

        except Exception as e:
            self.logger.error(f"Error checking rapid succession: {e}")
            return {"type": "rapid_succession", "is_fraud": False, "score": 0.0}

    async def _check_amount_anomaly(self, transaction_data: Dict, user_history: List[Dict]) -> Dict:
        """检查金额异常"""
        try:
            current_amount = transaction_data.get("value_usd", 0.0)

            if not user_history:
                return {"type": "amount_anomaly", "is_fraud": False, "score": 0.0}

            # 计算历史平均金额
            amounts = [tx.get("value_usd", 0.0) for tx in user_history if tx.get("value_usd", 0.0) > 0]
            if not amounts:
                return {"type": "amount_anomaly", "is_fraud": False, "score": 0.0}

            avg_amount = sum(amounts) / len(amounts)
            multiplier = self.fraud_rules["amount_spike"]["multiplier"]

            is_fraud = current_amount > avg_amount * multiplier
            score = min(1.0, current_amount / (avg_amount * multiplier)) if is_fraud else 0.0

            return {
                "type": "amount_anomaly",
                "is_fraud": is_fraud,
                "score": score,
                "description": f"交易金额 ${current_amount:,.2f} 超过平均值 ${avg_amount:,.2f} 的 {multiplier} 倍",
                "current_amount": current_amount,
                "average_amount": avg_amount,
                "multiplier": current_amount / avg_amount if avg_amount > 0 else 0
            }

        except Exception as e:
            self.logger.error(f"Error checking amount anomaly: {e}")
            return {"type": "amount_anomaly", "is_fraud": False, "score": 0.0}

    async def _check_unusual_timing(self, transaction_data: Dict) -> Dict:
        """检查异常时间"""
        try:
            current_time = datetime.utcnow()
            current_hour = current_time.hour
            unusual_hours = self.fraud_rules["unusual_time"]["hours"]

            is_fraud = current_hour in unusual_hours
            score = 0.5 if is_fraud else 0.0

            return {
                "type": "unusual_timing",
                "is_fraud": is_fraud,
                "score": score,
                "description": f"交易时间 {current_hour}:00 属于异常时段",
                "transaction_hour": current_hour,
                "unusual_hours": unusual_hours
            }

        except Exception as e:
            self.logger.error(f"Error checking unusual timing: {e}")
            return {"type": "unusual_timing", "is_fraud": False, "score": 0.0}
