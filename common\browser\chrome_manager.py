"""
Chrome browser manager

This module provides utilities for managing Chrome browser instances.
"""

import os
import json
import shutil
import logging
import tempfile
from typing import Dict, List, Optional
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

class ChromeManager:
    """Manager for Chrome browser instances"""
    
    def __init__(
        self,
        user_data_dir: Optional[str] = None,
        headless: bool = False,
        proxy: Optional[Dict[str, str]] = None
    ):
        """
        Initialize Chrome manager
        
        Args:
            user_data_dir: Directory for user data
            headless: Whether to run in headless mode
            proxy: Proxy configuration
        """
        self.user_data_dir = user_data_dir or tempfile.mkdtemp()
        self.headless = headless
        self.proxy = proxy
        self.driver = None
        self.logger = logging.getLogger(__name__)
    
    def _create_options(self) -> Options:
        """
        Create Chrome options
        
        Returns:
            Options: Configured Chrome options
        """
        options = Options()
        
        # Basic options
        options.add_argument(f'--user-data-dir={self.user_data_dir}')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        # Headless mode if specified
        if self.headless:
            options.add_argument('--headless')
            options.add_argument('--disable-gpu')
        
        # Proxy settings if specified
        if self.proxy:
            proxy_str = f"{self.proxy['protocol']}://{self.proxy['host']}:{self.proxy['port']}"
            options.add_argument(f'--proxy-server={proxy_str}')
        
        # Performance options
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--disable-blink-features=AutomationControlled')
        
        return options
    
    def launch(
        self,
        extensions: List[str] = None,
        arguments: List[str] = None,
        preferences: Dict = None
    ) -> webdriver.Chrome:
        """
        Launch Chrome browser
        
        Args:
            extensions: List of extension paths to load
            arguments: Additional Chrome arguments
            preferences: Chrome preferences to set
            
        Returns:
            webdriver.Chrome: Chrome WebDriver instance
        """
        options = self._create_options()
        
        # Add extensions
        if extensions:
            for ext_path in extensions:
                options.add_extension(ext_path)
        
        # Add additional arguments
        if arguments:
            for arg in arguments:
                options.add_argument(arg)
        
        # Set preferences
        if preferences:
            options.add_experimental_option('prefs', preferences)
        
        # Create driver
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)
            self.logger.info("Chrome browser launched successfully")
            return self.driver
        except Exception as e:
            self.logger.error(f"Failed to launch Chrome: {str(e)}")
            raise
    
    def set_preferences(self, preferences: Dict) -> None:
        """
        Set Chrome preferences
        
        Args:
            preferences: Dictionary of preferences
        """
        prefs_file = os.path.join(self.user_data_dir, 'Default', 'Preferences')
        os.makedirs(os.path.dirname(prefs_file), exist_ok=True)
        
        try:
            with open(prefs_file, 'w') as f:
                json.dump(preferences, f)
        except Exception as e:
            self.logger.error(f"Failed to set preferences: {str(e)}")
            raise
    
    def install_extension(self, extension_path: str) -> None:
        """
        Install Chrome extension
        
        Args:
            extension_path: Path to extension file (.crx)
        """
        if not self.driver:
            raise RuntimeError("Browser not launched")
            
        try:
            self.driver.install_addon(extension_path, temporary=True)
        except Exception as e:
            self.logger.error(f"Failed to install extension: {str(e)}")
            raise
    
    def set_proxy(self, proxy: Dict[str, str]) -> None:
        """
        Set proxy configuration
        
        Args:
            proxy: Proxy configuration dictionary
        """
        self.proxy = proxy
        if self.driver:
            self.restart()
    
    def clear_data(self) -> None:
        """Clear browser data"""
        if self.driver:
            try:
                self.driver.execute_cdp_cmd('Network.clearBrowserCache', {})
                self.driver.execute_cdp_cmd('Network.clearBrowserCookies', {})
                self.logger.info("Browser data cleared")
            except Exception as e:
                self.logger.error(f"Failed to clear browser data: {str(e)}")
    
    def restart(self) -> None:
        """Restart browser"""
        self.quit()
        self.launch()
    
    def quit(self) -> None:
        """Quit browser and cleanup"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
            except Exception as e:
                self.logger.error(f"Error quitting browser: {str(e)}")
    
    def cleanup(self) -> None:
        """Clean up temporary files"""
        self.quit()
        try:
            if os.path.exists(self.user_data_dir):
                shutil.rmtree(self.user_data_dir)
        except Exception as e:
            self.logger.error(f"Error cleaning up: {str(e)}")
    
    def __enter__(self):
        """Context manager enter"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.cleanup()

# Create global chrome manager instance
chrome_manager = ChromeManager()
