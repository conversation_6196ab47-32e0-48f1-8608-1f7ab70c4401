"""
Task Planning Agent Main Module

This is the main entry point for the Task Planning Agent.
"""

import os
import sys
import json
import logging
import argparse
import time
import threading
from typing import Dict, Any, Optional

# Add parent directory to path to allow imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from task_planning.task_planning_agent import TaskPlanningAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("data/logs/task_planning.log")
    ]
)
logger = logging.getLogger("TaskPlanningMain")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Task Planning Agent")
    parser.add_argument("--config", type=str, default="config/task_planning_config.json",
                        help="Path to configuration file")
    parser.add_argument("--action", type=str, 
                        choices=["start", "stop", "status", "create_task", "create_project", 
                                "schedule", "list_tasks", "list_projects", "list_templates"],
                        default="start", help="Action to perform")
    parser.add_argument("--project_id", type=str, help="Project ID for project-specific actions")
    parser.add_argument("--task_id", type=str, help="Task ID for task-specific actions")
    parser.add_argument("--template_id", type=str, help="Template ID for template-specific actions")
    parser.add_argument("--data_file", type=str, help="JSON file containing data for create operations")
    parser.add_argument("--run_scheduler", action="store_true", help="Run the task scheduler")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from file."""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        else:
            logger.warning(f"Config file not found: {config_path}")
            return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def load_data_file(data_file: str) -> Dict[str, Any]:
    """Load data from file."""
    try:
        if os.path.exists(data_file):
            with open(data_file, 'r') as f:
                return json.load(f)
        else:
            logger.error(f"Data file not found: {data_file}")
            return {}
    except Exception as e:
        logger.error(f"Error loading data file: {e}")
        return {}

def run_scheduler(agent: TaskPlanningAgent):
    """Run the task scheduler in a separate thread."""
    stop_event = threading.Event()
    
    def scheduler_thread():
        try:
            agent.run_scheduler(stop_event)
        except Exception as e:
            logger.error(f"Error in scheduler thread: {e}")
    
    thread = threading.Thread(target=scheduler_thread)
    thread.daemon = True
    thread.start()
    
    logger.info("Task scheduler started in background")
    logger.info("Press Ctrl+C to stop")
    
    try:
        while thread.is_alive():
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping task scheduler...")
        stop_event.set()
        thread.join(timeout=60)
        logger.info("Task scheduler stopped")

def main():
    """Main entry point for the Task Planning Agent."""
    # Parse arguments
    args = parse_arguments()
    
    # Ensure logs directory exists
    os.makedirs("data/logs", exist_ok=True)
    
    # Load configuration
    config = load_config(args.config)
    
    # Create agent
    agent = TaskPlanningAgent(args.config if os.path.exists(args.config) else None)
    
    # Perform requested action
    if args.action == "start":
        logger.info("Starting Task Planning Agent")
        success = agent.start()
        logger.info(f"Agent started: {success}")
        
        if args.run_scheduler and success:
            run_scheduler(agent)
    
    elif args.action == "stop":
        logger.info("Stopping Task Planning Agent")
        success = agent.stop()
        logger.info(f"Agent stopped: {success}")
    
    elif args.action == "status":
        agent.start()
        status = agent.status()
        logger.info(f"Agent status: {status}")
        print(json.dumps(status, indent=2))
    
    elif args.action == "create_task":
        agent.start()
        
        if not args.data_file:
            logger.error("Data file is required for create_task action")
            return
        
        task_data = load_data_file(args.data_file)
        if not task_data:
            return
        
        logger.info(f"Creating task: {task_data.get('name', 'Unnamed task')}")
        task_id = agent.create_task(task_data)
        
        if task_id:
            logger.info(f"Task created with ID: {task_id}")
            print(f"Task created with ID: {task_id}")
        else:
            logger.error("Failed to create task")
    
    elif args.action == "create_project":
        agent.start()
        
        if not args.data_file:
            logger.error("Data file is required for create_project action")
            return
        
        project_data = load_data_file(args.data_file)
        if not project_data:
            return
        
        logger.info(f"Creating project: {project_data.get('name', 'Unnamed project')}")
        project_id = agent.create_project(project_data)
        
        if project_id:
            logger.info(f"Project created with ID: {project_id}")
            print(f"Project created with ID: {project_id}")
        else:
            logger.error("Failed to create project")
    
    elif args.action == "schedule":
        agent.start()
        
        logger.info("Scheduling tasks")
        scheduled_tasks = agent.schedule_tasks()
        
        logger.info(f"Scheduled {len(scheduled_tasks)} tasks")
        for task in scheduled_tasks:
            print(f"- {task['id']}: {task['name']}")
    
    elif args.action == "list_tasks":
        agent.start()
        
        filters = {}
        if args.project_id:
            filters["project_id"] = args.project_id
        
        tasks = agent.list_tasks(filters)
        
        logger.info(f"Found {len(tasks)} tasks")
        print(f"Tasks ({len(tasks)}):")
        for task in tasks:
            print(f"- {task['id']}: {task['name']}")
            print(f"  Status: {task['status']}")
            print(f"  Priority: {task.get('priority', 'N/A')}")
            print(f"  Created: {task['created_at']}")
            if "project_id" in task:
                print(f"  Project: {task['project_id']}")
            print()
    
    elif args.action == "list_projects":
        agent.start()
        
        projects = agent.list_projects()
        
        logger.info(f"Found {len(projects)} projects")
        print(f"Projects ({len(projects)}):")
        for project in projects:
            print(f"- {project['id']}: {project['name']}")
            print(f"  Status: {project['status']}")
            print(f"  Created: {project['created_at']}")
            print(f"  Tasks: {len(project.get('tasks', []))}")
            print()
    
    elif args.action == "list_templates":
        agent.start()
        
        print("Task Templates:")
        for template_id, template in agent.task_templates.items():
            print(f"- {template_id}: {template['name']}")
            print(f"  Description: {template['description']}")
            print(f"  Steps: {len(template['steps'])}")
            print(f"  Duration: {template['estimated_duration_minutes']} minutes")
            print(f"  Priority: {template['priority']}")
            print()

if __name__ == "__main__":
    main()