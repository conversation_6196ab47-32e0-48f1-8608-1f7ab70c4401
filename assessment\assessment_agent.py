"""
项目评估智能体主模块

该模块实现了项目评估智能体的主要功能，负责验证项目真实性，分析智能合约安全性，评估项目风险和潜在收益。
"""

import logging
import threading
import time
from typing import Dict, List, Any, Optional, Set, Tuple

from assessment.verification.team_verifier import TeamVerifier
from assessment.verification.social_verifier import SocialVerifier
from assessment.verification.project_verifier import ProjectVerifier
from assessment.security.contract_analyzer import ContractAnalyzer
from assessment.security.vulnerability_scanner import VulnerabilityScanner
from assessment.security.permission_analyzer import PermissionAnalyzer
from assessment.risk.risk_calculator import RiskCalculator
from assessment.risk.reward_estimator import RewardEstimator
from assessment.risk.score_generator import ScoreGenerator

from discovery.models.project import Project, ProjectStatus

# 导入错误处理模块
try:
    from common.error_handling import ErrorHandler, safe_execute
except ImportError:
    # 如果错误处理模块不存在，创建简单的替代实现
    class ErrorHandler:
        def __init__(self, logger=None):
            self.logger = logger or logging.getLogger(__name__)
        def handle_error(self, error, context=None):
            self.logger.error(f"Error: {error}", exc_info=True)
    
    def safe_execute(func, *args, default_return=None, error_handler=None, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if error_handler:
                error_handler.handle_error(e)
            return default_return



class AssessmentAgent:
    """项目评估智能体的主类，负责验证项目真实性，分析智能合约安全性，评估项目风险和潜在收益"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目评估智能体
        
        Args:
            config: 配置字典，包含项目评估智能体的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("初始化项目评估智能体...")
        
        # 初始化组件
        self.team_verifier = TeamVerifier(config.get('verification', {}).get('team', {}))
        self.social_verifier = SocialVerifier(config.get('verification', {}).get('social', {}))
        self.project_verifier = ProjectVerifier(config.get('verification', {}).get('project', {}))
        
        self.contract_analyzer = ContractAnalyzer(config.get('security', {}).get('contract', {}))
        self.vulnerability_scanner = VulnerabilityScanner(config.get('security', {}).get('vulnerability', {}))
        self.permission_analyzer = PermissionAnalyzer(config.get('security', {}).get('permission', {}))
        
        self.risk_calculator = RiskCalculator(config.get('risk', {}).get('risk', {}))
        self.reward_estimator = RewardEstimator(config.get('risk', {}).get('reward', {}))
        self.score_generator = ScoreGenerator(config.get('risk', {}).get('score', {}))
        
        # 运行状态
        self._running = False
        self._lock = threading.RLock()
        self._assessment_thread = None
        
        # 配置参数
        self.assessment_interval = config.get('assessment_interval', 300)  # 默认每5分钟评估一次
        self.max_projects_per_batch = config.get('max_projects_per_batch', 10)  # 每批最多评估的项目数
        self.min_confidence_score = config.get('min_confidence_score', 0.6)  # 最小置信度分数
        
        self.logger.info("项目评估智能体初始化完成")
    
    def start(self) -> None:
        """启动项目评估智能体"""
        with self._lock:
            if self._running:
                self.logger.warning("项目评估智能体已经在运行")
                return
            
            self._running = True
            self._assessment_thread = threading.Thread(target=self._assessment_loop, daemon=True)
            self._assessment_thread.start()
            
            self.logger.info("项目评估智能体已启动")
    
    def stop(self) -> None:
        """停止项目评估智能体"""
        with self._lock:
            if not self._running:
                self.logger.warning("项目评估智能体未在运行")
                return
            
            self._running = False
            if self._assessment_thread:
                self._assessment_thread.join(timeout=10)
                self._assessment_thread = None
            
            self.logger.info("项目评估智能体已停止")
    
    def assess_project(self, project: Project) -> Project:
        """
        评估项目
        
        Args:
            project: 要评估的项目
            
        Returns:
            评估后的项目
        """
        self.logger.info(f"开始评估项目: {project.name} (ID: {project.id})")
        
        try:
            # 更新项目状态
            project.status = ProjectStatus.ANALYZING
            project.update()
            
            # 验证项目团队
            team_result = self.team_verifier.verify(project)
            self.logger.debug(f"团队验证结果: {team_result}")
            
            # 验证社交媒体
            social_result = self.social_verifier.verify(project)
            self.logger.debug(f"社交媒体验证结果: {social_result}")
            
            # 验证项目真实性
            project_result = self.project_verifier.verify(project)
            self.logger.debug(f"项目真实性验证结果: {project_result}")
            
            # 分析智能合约
            if project.token_info and project.token_info.contract_address:
                contract_result = self.contract_analyzer.analyze(project)
                self.logger.debug(f"智能合约分析结果: {contract_result}")
                
                # 扫描漏洞
                vulnerability_result = self.vulnerability_scanner.scan(project)
                self.logger.debug(f"漏洞扫描结果: {vulnerability_result}")
                
                # 分析权限
                permission_result = self.permission_analyzer.analyze(project)
                self.logger.debug(f"权限分析结果: {permission_result}")
            else:
                contract_result = None
                vulnerability_result = None
                permission_result = None
                self.logger.debug("项目没有智能合约信息，跳过合约分析")
            
            # 计算风险分数
            risk_score = self.risk_calculator.calculate(
                project, 
                team_result, 
                social_result, 
                project_result, 
                contract_result, 
                vulnerability_result, 
                permission_result
            )
            self.logger.debug(f"风险分数: {risk_score}")
            
            # 估算潜在收益
            potential_reward = self.reward_estimator.estimate(project)
            self.logger.debug(f"潜在收益: {potential_reward}")
            
            # 生成评分
            community_score, team_score = self.score_generator.generate(
                project, 
                team_result, 
                social_result, 
                project_result
            )
            self.logger.debug(f"社区评分: {community_score}, 团队评分: {team_score}")
            
            # 更新项目信息
            project.risk_score = risk_score
            project.potential_reward = potential_reward
            project.community_score = community_score
            project.team_score = team_score
            
            # 根据评估结果更新项目状态
            if risk_score is not None and risk_score > 80:
                project.status = ProjectStatus.SUSPICIOUS
                project.tags.append('high_risk')
                self.logger.warning(f"项目 {project.name} 风险较高")
            elif team_result and team_result.get('confidence', 0) < self.min_confidence_score:
                project.status = ProjectStatus.SUSPICIOUS
                project.tags.append('suspicious_team')
                self.logger.warning(f"项目 {project.name} 团队可疑")
            elif social_result and social_result.get('confidence', 0) < self.min_confidence_score:
                project.status = ProjectStatus.SUSPICIOUS
                project.tags.append('suspicious_social')
                self.logger.warning(f"项目 {project.name} 社交媒体可疑")
            elif project_result and project_result.get('confidence', 0) < self.min_confidence_score:
                project.status = ProjectStatus.SUSPICIOUS
                project.tags.append('suspicious_project')
                self.logger.warning(f"项目 {project.name} 项目真实性可疑")
            elif vulnerability_result and vulnerability_result.get('critical_vulnerabilities', 0) > 0:
                project.status = ProjectStatus.SUSPICIOUS
                project.tags.append('critical_vulnerabilities')
                self.logger.warning(f"项目 {project.name} 存在严重漏洞")
            else:
                project.status = ProjectStatus.VERIFIED
                self.logger.info(f"项目 {project.name} 验证通过")
            
            # 更新项目
            project.update()
            
            self.logger.info(f"项目评估完成: {project.name} (ID: {project.id}), 状态: {project.status.value}")
            
            return project
        
        except Exception as e:
            self.logger.error(f"评估项目 {project.name} (ID: {project.id}) 时出错: {str(e)}")
            
            # 出错时将项目标记为可疑
            project.status = ProjectStatus.SUSPICIOUS
            project.tags.append('assessment_error')
            project.notes += f"\n评估出错: {str(e)}"
            project.update()
            
            return project
    
    def assess_projects(self, projects: List[Project]) -> List[Project]:
        """
        批量评估项目
        
        Args:
            projects: 要评估的项目列表
            
        Returns:
            评估后的项目列表
        """
        self.logger.info(f"开始批量评估 {len(projects)} 个项目")
        
        assessed_projects = []
        
        for project in projects:
            try:
                assessed_project = self.assess_project(project)
                assessed_projects.append(assessed_project)
            except Exception as e:
                self.logger.error(f"评估项目 {project.name} (ID: {project.id}) 时出错: {str(e)}")
        
        self.logger.info(f"批量评估完成，共评估 {len(assessed_projects)} 个项目")
        
        return assessed_projects
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取项目评估智能体统计信息
        
        Returns:
            统计信息字典
        """
        with self._lock:
            return {
                'running': self._running,
                'assessment_interval': self.assessment_interval,
                'max_projects_per_batch': self.max_projects_per_batch,
                'min_confidence_score': self.min_confidence_score,
                'team_verifier': self.team_verifier.get_stats(),
                'social_verifier': self.social_verifier.get_stats(),
                'project_verifier': self.project_verifier.get_stats(),
                'contract_analyzer': self.contract_analyzer.get_stats(),
                'vulnerability_scanner': self.vulnerability_scanner.get_stats(),
                'permission_analyzer': self.permission_analyzer.get_stats(),
                'risk_calculator': self.risk_calculator.get_stats(),
                'reward_estimator': self.reward_estimator.get_stats(),
                'score_generator': self.score_generator.get_stats()
            }
    
    def _assessment_loop(self) -> None:
        """评估循环"""
        self.logger.info("评估循环开始")
        
        while self._running:
            try:
                # 获取需要评估的项目
                projects = self._get_projects_to_assess()
                
                if projects:
                    self.logger.info(f"获取到 {len(projects)} 个需要评估的项目")
                    
                    # 评估项目
                    self.assess_projects(projects)
                else:
                    self.logger.debug("没有需要评估的项目")
                
                # 等待下一次评估
                for _ in range(self.assessment_interval):
                    if not self._running:
                        break
                    time.sleep(1)
            
            except Exception as e:
                self.logger.error(f"评估循环出错: {str(e)}")
                time.sleep(10)  # 出错后等待一段时间再继续
        
        self.logger.info("评估循环结束")
    
    def _get_projects_to_assess(self) -> List[Project]:
        """
        获取需要评估的项目
        
        Returns:
            需要评估的项目列表
        """
        # 这里需要与项目发现智能体交互，获取新发现的项目
        # 在实际实现中，可以通过消息队列、API调用等方式获取
        # 这里简化处理，假设已经有了获取项目的方法
        
        # 示例实现，实际中需要替换为真实的获取方法
        try:
            from discovery.discovery_agent import DiscoveryAgent
            
            # 获取发现智能体实例
            # 实际中应该通过依赖注入或服务定位器获取
            discovery_agent = self._get_discovery_agent()
            
            if discovery_agent:
                # 获取新项目
                projects = discovery_agent.get_projects(status=ProjectStatus.NEW)
                
                # 限制每批处理的项目数量
                return projects[:self.max_projects_per_batch]
            else:
                self.logger.warning("无法获取项目发现智能体实例")
                return []
        
        except ImportError:
            self.logger.error("无法导入项目发现智能体模块")
            return []
        
        except Exception as e:
            self.logger.error(f"获取需要评估的项目时出错: {str(e)}")
            return []
    
    def _get_discovery_agent(self) -> Optional[Any]:
        """
        获取项目发现智能体实例
        
        Returns:
            项目发现智能体实例，如果无法获取则返回None
        """
        # 实际中应该通过依赖注入或服务定位器获取
        # 这里简化处理，假设已经有了获取实例的方法
        
        # 示例实现，实际中需要替换为真实的获取方法
        try:
            # 这里可以通过全局注册表、服务定位器等方式获取实例
            # 或者通过消息队列、API调用等方式与项目发现智能体交互
            
            # 简化处理，假设通过某种方式获取了实例
            from coordinator.core.agent_registry import AgentRegistry
            
            registry = AgentRegistry()
            return registry.get_agent('discovery')
        
        except ImportError:
            self.logger.error("无法导入智能体注册表模块")
            return None
        
        except Exception as e:
            self.logger.error(f"获取项目发现智能体实例时出错: {str(e)}")
            return None