"""
Event Bus

Provides event-driven communication system for decoupled component interaction
with support for event filtering, transformation, and asynchronous handling.
"""

import asyncio
import logging
import threading
from typing import Dict, List, Any, Optional, Callable, Set
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, asdict
from collections import defaultdict
import uuid
import inspect


class EventType(Enum):
    """Event types for different system events."""
    SYSTEM_START = "system.start"
    SYSTEM_STOP = "system.stop"
    AGENT_START = "agent.start"
    AGENT_STOP = "agent.stop"
    AGENT_ERROR = "agent.error"
    PROJECT_DISCOVERED = "project.discovered"
    PROJECT_ASSESSED = "project.assessed"
    TASK_CREATED = "task.created"
    TASK_COMPLETED = "task.completed"
    TASK_FAILED = "task.failed"
    WALLET_CREATED = "wallet.created"
    PROXY_VERIFIED = "proxy.verified"
    HEALTH_ALERT = "health.alert"
    CUSTOM = "custom"


@dataclass
class Event:
    """Event data structure."""
    id: str
    type: EventType
    source: str
    data: Dict[str, Any]
    timestamp: datetime = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.tags is None:
            self.tags = []
        if not self.id:
            self.id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary."""
        data = asdict(self)
        data['type'] = self.type.value
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Event':
        """Create event from dictionary."""
        data = data.copy()
        data['type'] = EventType(data['type'])
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


class EventFilter:
    """Event filter for conditional event handling."""
    
    def __init__(self, event_types: Set[EventType] = None, 
                 sources: Set[str] = None, tags: Set[str] = None,
                 custom_filter: Callable[[Event], bool] = None):
        """
        Initialize event filter.
        
        Args:
            event_types: Set of event types to match
            sources: Set of event sources to match
            tags: Set of tags to match
            custom_filter: Custom filter function
        """
        self.event_types = event_types or set()
        self.sources = sources or set()
        self.tags = tags or set()
        self.custom_filter = custom_filter
    
    def matches(self, event: Event) -> bool:
        """Check if event matches filter criteria."""
        # Check event type
        if self.event_types and event.type not in self.event_types:
            return False
        
        # Check source
        if self.sources and event.source not in self.sources:
            return False
        
        # Check tags
        if self.tags and not any(tag in event.tags for tag in self.tags):
            return False
        
        # Check custom filter
        if self.custom_filter and not self.custom_filter(event):
            return False
        
        return True


class EventHandler:
    """Event handler wrapper."""
    
    def __init__(self, handler: Callable, filter_obj: EventFilter = None,
                 async_handler: bool = None):
        """
        Initialize event handler.
        
        Args:
            handler: Handler function
            filter_obj: Event filter
            async_handler: Whether handler is async (auto-detected if None)
        """
        self.handler = handler
        self.filter = filter_obj or EventFilter()
        self.is_async = async_handler if async_handler is not None else inspect.iscoroutinefunction(handler)
        self.call_count = 0
        self.error_count = 0
        self.last_called = None
        self.last_error = None
    
    async def handle(self, event: Event) -> bool:
        """
        Handle event if it matches filter.
        
        Args:
            event: Event to handle
            
        Returns:
            bool: True if event was handled
        """
        try:
            if not self.filter.matches(event):
                return False
            
            self.call_count += 1
            self.last_called = datetime.utcnow()
            
            if self.is_async:
                await self.handler(event)
            else:
                self.handler(event)
            
            return True
            
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            logging.getLogger(__name__).error(f"Error in event handler: {e}")
            return False


class EventBus:
    """
    Event bus for decoupled event-driven communication.
    
    Provides publish-subscribe pattern for events with support for
    filtering, transformation, and both sync and async handlers.
    """
    
    def __init__(self, max_event_history: int = 1000):
        """
        Initialize event bus.
        
        Args:
            max_event_history: Maximum number of events to keep in history
        """
        self.max_event_history = max_event_history
        self.logger = logging.getLogger(__name__)
        
        # Event handlers
        self._handlers: List[EventHandler] = []
        
        # Event history
        self._event_history: List[Event] = []
        
        # Statistics
        self._stats = {
            'events_published': 0,
            'events_handled': 0,
            'handler_errors': 0,
            'active_handlers': 0
        }
        
        # Threading
        self._lock = threading.RLock()
        self._running = False
        self._event_queue = asyncio.Queue()
        self._processor_task = None
    
    def start(self):
        """Start the event bus."""
        with self._lock:
            if self._running:
                return
            
            self._running = True
            
            # Start event processor in background
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            self._processor_task = loop.create_task(self._event_processor())
            
            self.logger.info("Event bus started")
    
    def stop(self):
        """Stop the event bus."""
        with self._lock:
            if not self._running:
                return
            
            self._running = False
            
            if self._processor_task:
                self._processor_task.cancel()
            
            self.logger.info("Event bus stopped")
    
    def subscribe(self, handler: Callable, event_types: Set[EventType] = None,
                 sources: Set[str] = None, tags: Set[str] = None,
                 custom_filter: Callable[[Event], bool] = None) -> str:
        """
        Subscribe to events with optional filtering.
        
        Args:
            handler: Event handler function
            event_types: Set of event types to listen for
            sources: Set of event sources to listen for
            tags: Set of tags to listen for
            custom_filter: Custom filter function
            
        Returns:
            str: Handler ID for unsubscribing
        """
        with self._lock:
            event_filter = EventFilter(event_types, sources, tags, custom_filter)
            event_handler = EventHandler(handler, event_filter)
            
            self._handlers.append(event_handler)
            self._stats['active_handlers'] = len(self._handlers)
            
            handler_id = str(uuid.uuid4())
            event_handler.id = handler_id
            
            self.logger.debug(f"Subscribed handler {handler_id}")
            return handler_id
    
    def unsubscribe(self, handler_id: str) -> bool:
        """
        Unsubscribe event handler.
        
        Args:
            handler_id: Handler ID returned from subscribe
            
        Returns:
            bool: True if handler was found and removed
        """
        with self._lock:
            for i, handler in enumerate(self._handlers):
                if getattr(handler, 'id', None) == handler_id:
                    del self._handlers[i]
                    self._stats['active_handlers'] = len(self._handlers)
                    self.logger.debug(f"Unsubscribed handler {handler_id}")
                    return True
            return False
    
    def publish(self, event_type: EventType, source: str, data: Dict[str, Any],
               tags: List[str] = None) -> str:
        """
        Publish an event.
        
        Args:
            event_type: Type of event
            source: Event source identifier
            data: Event data
            tags: Optional event tags
            
        Returns:
            str: Event ID
        """
        event = Event(
            id=str(uuid.uuid4()),
            type=event_type,
            source=source,
            data=data,
            tags=tags or []
        )
        
        return self.publish_event(event)
    
    def publish_event(self, event: Event) -> str:
        """
        Publish an event object.
        
        Args:
            event: Event to publish
            
        Returns:
            str: Event ID
        """
        try:
            with self._lock:
                # Add to history
                self._event_history.append(event)
                if len(self._event_history) > self.max_event_history:
                    self._event_history.pop(0)
                
                self._stats['events_published'] += 1
            
            # Queue for async processing
            if self._running:
                try:
                    loop = asyncio.get_event_loop()
                    loop.create_task(self._queue_event(event))
                except RuntimeError:
                    # No event loop, process synchronously
                    asyncio.run(self._process_event(event))
            
            self.logger.debug(f"Published event {event.id} of type {event.type.value}")
            return event.id
            
        except Exception as e:
            self.logger.error(f"Error publishing event: {e}")
            return ""
    
    def get_event_history(self, event_types: Set[EventType] = None,
                         sources: Set[str] = None, limit: int = 100) -> List[Event]:
        """
        Get event history with optional filtering.
        
        Args:
            event_types: Filter by event types
            sources: Filter by sources
            limit: Maximum number of events to return
            
        Returns:
            List[Event]: Filtered event history
        """
        with self._lock:
            events = self._event_history.copy()
        
        # Apply filters
        if event_types:
            events = [e for e in events if e.type in event_types]
        
        if sources:
            events = [e for e in events if e.source in sources]
        
        # Return most recent events
        return events[-limit:] if limit else events
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get event bus statistics.
        
        Returns:
            Dict[str, Any]: Statistics
        """
        with self._lock:
            stats = self._stats.copy()
            
            # Add handler statistics
            handler_stats = []
            for handler in self._handlers:
                handler_stats.append({
                    'id': getattr(handler, 'id', 'unknown'),
                    'call_count': handler.call_count,
                    'error_count': handler.error_count,
                    'last_called': handler.last_called.isoformat() if handler.last_called else None,
                    'last_error': handler.last_error
                })
            
            stats['handlers'] = handler_stats
            stats['event_history_size'] = len(self._event_history)
            stats['running'] = self._running
            
            return stats
    
    async def _queue_event(self, event: Event):
        """Queue event for async processing."""
        try:
            await self._event_queue.put(event)
        except Exception as e:
            self.logger.error(f"Error queueing event: {e}")
    
    async def _event_processor(self):
        """Background event processor."""
        self.logger.info("Event processor started")
        
        while self._running:
            try:
                # Get event from queue with timeout
                event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)
                await self._process_event(event)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error in event processor: {e}")
                await asyncio.sleep(1.0)
        
        self.logger.info("Event processor stopped")
    
    async def _process_event(self, event: Event):
        """Process event by calling matching handlers."""
        try:
            with self._lock:
                handlers = self._handlers.copy()
            
            handled_count = 0
            
            for handler in handlers:
                try:
                    if await handler.handle(event):
                        handled_count += 1
                except Exception as e:
                    self.logger.error(f"Error in event handler: {e}")
                    with self._lock:
                        self._stats['handler_errors'] += 1
            
            with self._lock:
                self._stats['events_handled'] += handled_count
            
            self.logger.debug(f"Event {event.id} handled by {handled_count} handlers")
            
        except Exception as e:
            self.logger.error(f"Error processing event: {e}")
    
    def wait_for_event(self, event_type: EventType, source: str = None,
                      timeout: float = 30.0) -> Optional[Event]:
        """
        Wait for a specific event.
        
        Args:
            event_type: Event type to wait for
            source: Optional source filter
            timeout: Timeout in seconds
            
        Returns:
            Optional[Event]: Event if received within timeout
        """
        import threading
        
        event_received = threading.Event()
        received_event = None
        
        def handler(event: Event):
            nonlocal received_event
            if event.type == event_type and (source is None or event.source == source):
                received_event = event
                event_received.set()
        
        # Subscribe temporarily
        handler_id = self.subscribe(
            handler,
            event_types={event_type},
            sources={source} if source else None
        )
        
        try:
            # Wait for event
            if event_received.wait(timeout):
                return received_event
            else:
                return None
        finally:
            # Cleanup subscription
            self.unsubscribe(handler_id)
