# AirHunter 安装指南

本文档提供了安装和配置 AirHunter 系统的详细步骤。

## 系统要求

- Python 3.8 或更高版本
- Chrome 浏览器 (版本 90 或更高)
- 至少 4GB RAM
- 至少 10GB 可用磁盘空间
- 稳定的互联网连接

## 安装步骤

### 1. 克隆仓库

```bash
git clone https://github.com/yourusername/AirHunter.git
cd AirHunter
```

### 2. 创建虚拟环境

```bash
# 使用 venv
python -m venv venv

# 在 Windows 上激活
venv\Scripts\activate

# 在 Linux/Mac 上激活
source venv/bin/activate
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 安装浏览器驱动

AirHunter 使用 Selenium 进行浏览器自动化，需要安装与您的 Chrome 浏览器版本兼容的 ChromeDriver。

1. 检查您的 Chrome 版本：打开 Chrome，点击右上角的三点菜单 > 帮助 > 关于 Google Chrome
2. 下载对应版本的 ChromeDriver：https://sites.google.com/chromium.org/driver/
3. 将 ChromeDriver 解压并放置在系统 PATH 中，或放在项目根目录下

### 5. 配置系统

1. 复制示例配置文件：

```bash
cp config/config.example.json config/config.json
```

2. 编辑配置文件，填入您的个人设置：

```bash
# 使用您喜欢的编辑器
nano config/config.json
```

主要配置项包括：

- 钱包设置
- 代理设置
- 社交媒体账户
- 项目偏好
- 风险偏好

### 6. 初始化数据目录

```bash
python -m scripts.initialize_data_directories
```

## 运行系统

### 基本运行

```bash
python main.py
```

### 指定配置文件

```bash
python main.py --config config/my_custom_config.json
```

### 运行特定模式

```bash
# 仅运行发现模式
python main.py --mode discovery

# 仅运行执行模式
python main.py --mode execution

# 仅运行监控模式
python main.py --mode monitoring
```

### 调试模式

```bash
python main.py --debug
```

## 验证安装

安装完成后，您可以运行以下命令来验证系统是否正常工作：

```bash
python -m scripts.system_check
```

如果一切正常，您将看到所有组件的状态检查通过。

## 常见问题

### Q: 系统无法启动浏览器

A: 确保您已正确安装 ChromeDriver 并且版本与您的 Chrome 浏览器匹配。

### Q: 无法连接到社交媒体平台

A: 检查您的网络连接和代理设置。某些平台可能需要特定的 IP 地址或有访问限制。

### Q: 系统资源使用过高

A: 在配置文件中调整并发任务数量和资源限制。

## 更新系统

要更新到最新版本，请运行：

```bash
git pull
pip install -r requirements.txt
```

## 支持

如果您遇到任何问题，请查看文档或提交 GitHub issue。