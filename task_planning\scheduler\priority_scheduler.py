"""
Priority Scheduler

This module implements a priority-based task scheduler that considers
task priorities, dependencies, and resource constraints.
"""

import logging
import time
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger("PriorityScheduler")

class PriorityScheduler:
    """
    Priority-based task scheduler.
    
    Features:
    - Priority-based scheduling
    - Dependency resolution
    - Resource allocation
    - Deadline management
    - Fairness across projects
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the priority scheduler.
        
        Args:
            config: Optional configuration
        """
        self.config = config or {}
        self.default_priority = self.config.get("default_priority", 5)
        self.max_concurrent_tasks = self.config.get("max_concurrent_tasks", 10)
        self.priority_levels = self.config.get("priority_levels", 10)
        self.resource_weights = self.config.get("resource_weights", {})
        
        logger.info("Priority scheduler initialized")
    
    def schedule(self, tasks: Dict[str, Any], running_tasks: List[str], resources: Optional[Dict[str, Any]] = None) -> List[str]:
        """
        Schedule tasks based on priority and constraints.
        
        Args:
            tasks: Dictionary of all tasks
            running_tasks: List of currently running task IDs
            resources: Optional resource availability
            
        Returns:
            List of task IDs to schedule
        """
        try:
            # Filter out tasks that are not pending
            pending_tasks = {task_id: task for task_id, task in tasks.items() 
                            if task.get("status") == "pending"}
            
            # Check how many more tasks we can schedule
            slots_available = self.max_concurrent_tasks - len(running_tasks)
            if slots_available <= 0:
                logger.info("No scheduling slots available")
                return []
            
            # Get tasks that are ready to run (dependencies satisfied)
            ready_tasks = self._filter_ready_tasks(pending_tasks, tasks)
            
            # Sort tasks by priority and other factors
            sorted_tasks = self._sort_tasks(ready_tasks, tasks, resources)
            
            # Select tasks to schedule
            scheduled_task_ids = []
            for task_id in sorted_tasks:
                if len(scheduled_task_ids) >= slots_available:
                    break
                
                # Check if this task can be scheduled with available resources
                if self._can_schedule(task_id, pending_tasks[task_id], running_tasks + scheduled_task_ids, resources):
                    scheduled_task_ids.append(task_id)
            
            logger.info(f"Scheduled {len(scheduled_task_ids)} tasks")
            return scheduled_task_ids
        except Exception as e:
            logger.error(f"Error scheduling tasks: {e}")
            return []
    
    def _filter_ready_tasks(self, pending_tasks: Dict[str, Any], all_tasks: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter tasks that are ready to run (dependencies satisfied).
        
        Args:
            pending_tasks: Dictionary of pending tasks
            all_tasks: Dictionary of all tasks
            
        Returns:
            Dictionary of ready tasks
        """
        ready_tasks = {}
        
        for task_id, task in pending_tasks.items():
            # Check if task has a scheduled_for time and it's in the future
            if "scheduled_for" in task:
                scheduled_time = datetime.fromisoformat(task["scheduled_for"])
                if scheduled_time > datetime.now():
                    continue
            
            # Check if task has a retry_at time and it's in the future
            if "retry_at" in task:
                retry_time = datetime.fromisoformat(task["retry_at"])
                if retry_time > datetime.now():
                    continue
            
            # Check dependencies
            dependencies_met = True
            if "dependencies" in task:
                for dep_id in task["dependencies"]:
                    if dep_id not in all_tasks:
                        # Dependency doesn't exist
                        dependencies_met = False
                        break
                    
                    dep_status = all_tasks[dep_id].get("status")
                    if dep_status != "completed":
                        # Dependency not completed
                        dependencies_met = False
                        break
            
            if dependencies_met:
                ready_tasks[task_id] = task
        
        return ready_tasks
    
    def _sort_tasks(self, ready_tasks: Dict[str, Any], all_tasks: Dict[str, Any], resources: Optional[Dict[str, Any]] = None) -> List[str]:
        """
        Sort tasks by priority and other factors.
        
        Args:
            ready_tasks: Dictionary of ready tasks
            all_tasks: Dictionary of all tasks
            resources: Optional resource availability
            
        Returns:
            List of task IDs sorted by priority
        """
        # Calculate scores for each task
        task_scores = {}
        
        for task_id, task in ready_tasks.items():
            # Base score is the priority
            priority = task.get("priority", self.default_priority)
            score = priority * (10 / self.priority_levels)  # Normalize to 0-10 scale
            
            # Adjust for waiting time
            if "created_at" in task:
                created_time = datetime.fromisoformat(task["created_at"])
                wait_hours = (datetime.now() - created_time).total_seconds() / 3600
                wait_factor = min(wait_hours / 24, 1.0)  # Cap at 1.0 (24 hours)
                score += wait_factor * 2  # Add up to 2 points for waiting
            
            # Adjust for deadline
            if "deadline" in task:
                deadline_time = datetime.fromisoformat(task["deadline"])
                time_left = (deadline_time - datetime.now()).total_seconds() / 3600
                if time_left < 0:
                    # Past deadline, highest priority
                    score += 5
                elif time_left < 24:
                    # Less than 24 hours left
                    urgency_factor = 1.0 - (time_left / 24)
                    score += urgency_factor * 3  # Add up to 3 points for urgency
            
            # Adjust for resource efficiency
            if resources and "required_resources" in task:
                efficiency_score = 0
                for resource, amount in task["required_resources"].items():
                    if resource in resources:
                        available = resources[resource]
                        usage_ratio = amount / available if available > 0 else 1.0
                        weight = self.resource_weights.get(resource, 1.0)
                        efficiency_score += (1.0 - usage_ratio) * weight
                
                # Normalize and add to score
                if task["required_resources"]:
                    efficiency_score /= len(task["required_resources"])
                    score += efficiency_score
            
            # Adjust for retry count (prioritize tasks that have failed before)
            retry_count = task.get("retry_count", 0)
            if retry_count > 0:
                retry_factor = min(retry_count / self.config.get("max_retries", 3), 1.0)
                score += retry_factor * 1.5  # Add up to 1.5 points for retries
            
            # Store the score
            task_scores[task_id] = score
        
        # Sort task IDs by score (descending)
        sorted_task_ids = sorted(task_scores.keys(), key=lambda x: task_scores[x], reverse=True)
        
        return sorted_task_ids
    
    def _can_schedule(self, task_id: str, task: Dict[str, Any], scheduled_tasks: List[str], resources: Optional[Dict[str, Any]] = None) -> bool:
        """
        Check if a task can be scheduled with available resources.
        
        Args:
            task_id: Task ID
            task: Task data
            scheduled_tasks: List of already scheduled task IDs
            resources: Optional resource availability
            
        Returns:
            bool: True if the task can be scheduled, False otherwise
        """
        # Check resource constraints
        if resources and "required_resources" in task:
            # Calculate total resources used by scheduled tasks
            used_resources = {}
            for res_name, res_amount in resources.items():
                used_resources[res_name] = 0
            
            # Add this task's resource requirements
            for res_name, res_amount in task["required_resources"].items():
                if res_name in used_resources:
                    used_resources[res_name] += res_amount
            
            # Check if we have enough resources
            for res_name, used_amount in used_resources.items():
                if used_amount > resources[res_name]:
                    return False
        
        # Check project concurrency limits
        if "project_id" in task:
            project_id = task["project_id"]
            project_tasks = [t for t in scheduled_tasks if project_id == task.get("project_id")]
            max_project_tasks = self.config.get("max_tasks_per_project", 5)
            
            if len(project_tasks) >= max_project_tasks:
                return False
        
        # Check agent concurrency limits
        if "agent" in task:
            agent_name = task["agent"]
            agent_tasks = [t for t in scheduled_tasks if agent_name == task.get("agent")]
            max_agent_tasks = self.config.get("max_tasks_per_agent", 3)
            
            if len(agent_tasks) >= max_agent_tasks:
                return False
        
        return True
    
    def estimate_completion_time(self, task: Dict[str, Any], all_tasks: Dict[str, Any]) -> Optional[datetime]:
        """
        Estimate when a task will be completed.
        
        Args:
            task: Task data
            all_tasks: Dictionary of all tasks
            
        Returns:
            datetime: Estimated completion time or None if cannot estimate
        """
        try:
            # Start with current time
            estimated_time = datetime.now()
            
            # If task is already scheduled or running, use its estimated duration
            if task.get("status") in ["scheduled", "running"]:
                duration_minutes = task.get("estimated_duration_minutes", 30)
                
                if "started_at" in task:
                    # Task is running, calculate remaining time
                    start_time = datetime.fromisoformat(task["started_at"])
                    elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                    remaining_minutes = max(0, duration_minutes - elapsed_minutes)
                    
                    estimated_time += timedelta(minutes=remaining_minutes)
                else:
                    # Task is scheduled but not started yet
                    estimated_time += timedelta(minutes=duration_minutes)
                
                return estimated_time
            
            # If task is pending, estimate when it will be scheduled and completed
            if task.get("status") == "pending":
                # Check dependencies
                if "dependencies" in task:
                    max_dependency_time = estimated_time
                    
                    for dep_id in task["dependencies"]:
                        if dep_id in all_tasks:
                            dep_task = all_tasks[dep_id]
                            dep_completion = self.estimate_completion_time(dep_task, all_tasks)
                            
                            if dep_completion and dep_completion > max_dependency_time:
                                max_dependency_time = dep_completion
                    
                    estimated_time = max_dependency_time
                
                # Add task's estimated duration
                duration_minutes = task.get("estimated_duration_minutes", 30)
                estimated_time += timedelta(minutes=duration_minutes)
                
                # Add scheduling delay (estimate)
                scheduling_delay = self.config.get("estimated_scheduling_delay_minutes", 15)
                estimated_time += timedelta(minutes=scheduling_delay)
                
                return estimated_time
            
            # For other statuses, cannot estimate
            return None
        except Exception as e:
            logger.error(f"Error estimating completion time: {e}")
            return None


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    
    # Create scheduler
    config = {
        "max_concurrent_tasks": 5,
        "default_priority": 5,
        "priority_levels": 10,
        "max_tasks_per_project": 3,
        "max_tasks_per_agent": 2
    }
    scheduler = PriorityScheduler(config)
    
    # Create some test tasks
    tasks = {
        "task1": {
            "id": "task1",
            "name": "High Priority Task",
            "status": "pending",
            "priority": 9,
            "created_at": (datetime.now() - timedelta(hours=2)).isoformat(),
            "estimated_duration_minutes": 30,
            "project_id": "project1",
            "agent": "task_execution"
        },
        "task2": {
            "id": "task2",
            "name": "Medium Priority Task",
            "status": "pending",
            "priority": 5,
            "created_at": (datetime.now() - timedelta(hours=5)).isoformat(),
            "estimated_duration_minutes": 45,
            "project_id": "project1",
            "agent": "monitoring"
        },
        "task3": {
            "id": "task3",
            "name": "Low Priority Task",
            "status": "pending",
            "priority": 2,
            "created_at": datetime.now().isoformat(),
            "estimated_duration_minutes": 15,
            "project_id": "project2",
            "agent": "task_execution"
        },
        "task4": {
            "id": "task4",
            "name": "Dependent Task",
            "status": "pending",
            "priority": 7,
            "created_at": datetime.now().isoformat(),
            "estimated_duration_minutes": 20,
            "project_id": "project2",
            "agent": "fund_management",
            "dependencies": ["task1"]
        },
        "task5": {
            "id": "task5",
            "name": "Running Task",
            "status": "running",
            "priority": 8,
            "created_at": (datetime.now() - timedelta(hours=1)).isoformat(),
            "started_at": (datetime.now() - timedelta(minutes=10)).isoformat(),
            "estimated_duration_minutes": 30,
            "project_id": "project3",
            "agent": "monitoring"
        }
    }
    
    # Schedule tasks
    running_tasks = ["task5"]
    scheduled = scheduler.schedule(tasks, running_tasks)
    
    print(f"Scheduled tasks: {scheduled}")
    
    # Estimate completion times
    for task_id, task in tasks.items():
        completion_time = scheduler.estimate_completion_time(task, tasks)
        if completion_time:
            print(f"Task {task_id} ({task['name']}) estimated completion: {completion_time}")
        else:
            print(f"Task {task_id} ({task['name']}) completion time cannot be estimated")