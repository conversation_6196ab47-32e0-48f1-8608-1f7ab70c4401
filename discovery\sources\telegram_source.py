"""
Telegram 项目源

该模块实现了从 Telegram 获取项目信息的功能。
"""

import logging
import time
import random
import re
from typing import Dict, List, Any, Optional

from discovery.sources.base_source import BaseProjectSource


class TelegramSource(BaseProjectSource):
    """Telegram 项目源"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 Telegram 项目源
        
        Args:
            config: 配置字典，包含项目源的配置信息
        """
        super().__init__(config)
        self.api_id = config.get('api_id', 0)
        self.api_hash = config.get('api_hash', '')
        self.phone = config.get('phone', '')
        self.use_api = config.get('use_api', False) and bool(self.api_id) and bool(self.api_hash)
        self.channels = config.get('channels', [])
        self.search_terms = config.get('search_terms', [
            'airdrop', 'crypto airdrop', 'token airdrop', 'free tokens',
            'blockchain airdrop', 'defi airdrop', 'nft airdrop'
        ])
        
        # 初始化 Telegram API 客户端
        if self.use_api:
            self._init_api_client()
    
    def _init_api_client(self) -> None:
        """初始化 Telegram API 客户端"""
        try:
            from telethon import TelegramClient
            
            self.api_client = TelegramClient('airhunter_session', self.api_id, self.api_hash)
            self.logger.info("Telegram API 客户端初始化成功")
        
        except ImportError:
            self.logger.warning("无法导入 telethon 库，将使用模拟数据")
            self.use_api = False
        except Exception as e:
            self.logger.error(f"初始化 Telegram API 客户端时出错: {str(e)}")
            self.use_api = False
    
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        从 Telegram 获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        self.logger.info(f"从 Telegram 获取 {count} 个项目")
        
        if self.use_api:
            return self._get_projects_via_api(count)
        else:
            return self._get_projects_via_simulation(count)
    
    def _get_projects_via_api(self, count: int) -> List[Dict[str, Any]]:
        """
        通过 API 获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        try:
            from telethon import TelegramClient
            import asyncio
            
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 定义异步函数
            async def fetch_messages():
                # 启动客户端
                await self.api_client.start(self.phone)
                
                messages = []
                
                # 获取指定频道的消息
                for channel in self.channels:
                    try:
                        entity = await self.api_client.get_entity(channel)
                        
                        # 获取最近的消息
                        async for message in self.api_client.iter_messages(entity, limit=50):
                            if message.text:
                                # 检查消息是否包含关键词
                                if any(term.lower() in message.text.lower() for term in self.search_terms):
                                    messages.append({
                                        'text': message.text,
                                        'channel': channel,
                                        'date': message.date.timestamp(),
                                        'id': message.id,
                                        'link': f"https://t.me/{channel}/{message.id}" if channel.startswith('@') else f"https://t.me/c/{channel}/{message.id}"
                                    })
                                    
                                    # 如果已经获取到足够的消息，就停止
                                    if len(messages) >= count * 2:  # 获取更多消息，以便过滤后仍有足够的项目
                                        break
                    
                    except Exception as e:
                        self.logger.error(f"获取频道 {channel} 的消息时出错: {str(e)}")
                    
                    # 如果已经获取到足够的消息，就停止
                    if len(messages) >= count * 2:
                        break
                
                # 断开客户端
                await self.api_client.disconnect()
                
                return messages
            
            # 运行异步函数
            messages = loop.run_until_complete(fetch_messages())
            
            # 关闭事件循环
            loop.close()
            
            # 提取项目信息
            projects = []
            for message in messages:
                project_info = self._extract_project_info_from_message(message)
                if project_info:
                    projects.append(project_info)
                    
                    # 如果已经获取到足够的项目，就停止
                    if len(projects) >= count:
                        break
            
            self.logger.info(f"从 Telegram API 获取到 {len(projects)} 个项目")
            return projects[:count]
        
        except Exception as e:
            self.logger.error(f"通过 API 获取项目时出错: {str(e)}")
            return []
    
    def _get_projects_via_simulation(self, count: int) -> List[Dict[str, Any]]:
        """
        通过模拟数据获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        self.logger.info("使用模拟数据获取 Telegram 项目")
        
        # 模拟数据
        simulated_messages = [
            {
                'text': '🚀 New Airdrop Alert! 🚀\n\nProject: TeleToken\nBlockchain: Ethereum\nAirdrop: 500 TELE tokens\n\nJoin now: https://teletoken.io',
                'channel': '@airdropalerts',
                'date': time.time() - 3600,
                'id': 12345,
                'link': 'https://t.me/airdropalerts/12345'
            },
            {
                'text': '💰 Binance Smart Chain Airdrop! 💰\n\nEarn free BNB tokens by completing simple tasks.\n\nVisit: https://bnbproject.com',
                'channel': '@cryptoairdrops',
                'date': time.time() - 7200,
                'id': 23456,
                'link': 'https://t.me/cryptoairdrops/23456'
            },
            {
                'text': '🔥 DeFi Telegram Airdrop 🔥\n\nJoin the future of finance!\nPlatform: Solana\nToken: DEFT\n\nRegister: https://defitelegram.finance',
                'channel': '@defiprojects',
                'date': time.time() - 10800,
                'id': 34567,
                'link': 'https://t.me/defiprojects/34567'
            },
            {
                'text': '🎮 GameFi Telegram Airdrop: TeleGame 🎮\n\nEarn while you play!\nPlatform: Polygon\nAirdrop: 300 TG tokens\n\nJoin: https://telegame.io',
                'channel': '@gamingcrypto',
                'date': time.time() - 14400,
                'id': 45678,
                'link': 'https://t.me/gamingcrypto/45678'
            },
            {
                'text': '🌟 Layer 2 Ecosystem Airdrop 🌟\n\nProject: L2Finance\nPlatform: Arbitrum\nAirdrop: 1000 L2F tokens\n\nClaim now: https://l2finance.io',
                'channel': '@layer2projects',
                'date': time.time() - 18000,
                'id': 56789,
                'link': 'https://t.me/layer2projects/56789'
            }
        ]
        
        # 随机选择消息
        selected_messages = random.sample(simulated_messages, min(count, len(simulated_messages)))
        
        # 提取项目信息
        projects = []
        for message in selected_messages:
            project_info = self._extract_project_info_from_message(message)
            if project_info:
                projects.append(project_info)
        
        self.logger.info(f"从 Telegram 模拟数据获取到 {len(projects)} 个项目")
        return projects
    
    def _extract_project_info_from_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        从消息中提取项目信息
        
        Args:
            message: 消息字典
            
        Returns:
            项目信息字典，如果无法提取则返回None
        """
        text = message.get('text', '')
        
        # 检查消息是否包含关键词
        keywords = ['airdrop', 'token', 'crypto', 'blockchain', 'defi', 'nft']
        if not any(keyword in text.lower() for keyword in keywords):
            return None
        
        # 提取项目名称
        name_match = re.search(r'(?i)(?:project|token|airdrop)[\s:]+([A-Za-z0-9\s]+)', text)
        name = name_match.group(1).strip() if name_match else "Unknown Project"
        
        # 提取项目URL
        url_match = re.search(r'(?i)(?:website|site|join|register|claim|visit)[\s:]+\s*(https?://[^\s]+)', text)
        if not url_match:
            url_match = re.search(r'(https?://[^\s]+)', text)
        
        project_url = url_match.group(1) if url_match else None
        
        if not project_url:
            return None
        
        # 创建项目信息
        project_info = {
            'name': name,
            'description': text[:200] + ('...' if len(text) > 200 else ''),
            'url': project_url,
            'project_type': 'airdrop',
            'blockchain': 'other',
            'source_url': message.get('link', ''),
            'discovery_source': 'telegram',
            'social_channels': [
                {
                    'platform': 'telegram',
                    'url': f"https://t.me/{message.get('channel', '').lstrip('@')}",
                    'followers': None
                }
            ]
        }
        
        # 尝试识别区块链平台
        blockchain_keywords = {
            'ethereum': ['ethereum', 'eth', 'erc20', 'erc721'],
            'binance': ['binance', 'bsc', 'bnb', 'bep20'],
            'solana': ['solana', 'sol'],
            'polygon': ['polygon', 'matic'],
            'avalanche': ['avalanche', 'avax'],
            'arbitrum': ['arbitrum', 'arb'],
            'optimism': ['optimism', 'op'],
            'base': ['base'],
            'cosmos': ['cosmos', 'atom'],
            'polkadot': ['polkadot', 'dot'],
            'near': ['near'],
            'aptos': ['aptos', 'apt'],
            'sui': ['sui']
        }
        
        for blockchain, keywords in blockchain_keywords.items():
            if any(keyword in text.lower() for keyword in keywords):
                project_info['blockchain'] = blockchain
                break
        
        return project_info