"""
Project Model

Database model for airdrop projects including project information,
requirements, status tracking, and participation history.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import json


class ProjectStatus(Enum):
    """Project status enumeration."""
    DISCOVERED = "discovered"
    ANALYZING = "analyzing"
    APPROVED = "approved"
    REJECTED = "rejected"
    ACTIVE = "active"
    COMPLETED = "completed"
    FAILED = "failed"
    BLACKLISTED = "blacklisted"


class ProjectType(Enum):
    """Project type enumeration."""
    TESTNET = "testnet"
    MAINNET = "mainnet"
    SOCIAL = "social"
    HYBRID = "hybrid"


@dataclass
class ProjectModel:
    """
    Project data model.
    
    Represents an airdrop project with all associated metadata,
    requirements, and tracking information.
    """
    
    # Primary identification
    id: Optional[int] = None
    name: str = ""
    symbol: str = ""
    project_url: str = ""
    
    # Project details
    description: str = ""
    project_type: ProjectType = ProjectType.TESTNET
    blockchain: str = ""
    contract_address: str = ""
    
    # Status and tracking
    status: ProjectStatus = ProjectStatus.DISCOVERED
    discovered_at: datetime = None
    updated_at: datetime = None
    
    # Requirements and conditions
    requirements: Dict[str, Any] = None
    social_requirements: Dict[str, Any] = None
    technical_requirements: Dict[str, Any] = None
    
    # Assessment scores
    risk_score: float = 0.0
    reward_score: float = 0.0
    difficulty_score: float = 0.0
    overall_score: float = 0.0
    
    # Participation tracking
    participants_count: int = 0
    estimated_reward: float = 0.0
    actual_reward: float = 0.0
    
    # Metadata
    source: str = ""
    tags: List[str] = None
    notes: str = ""
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.discovered_at is None:
            self.discovered_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        if self.requirements is None:
            self.requirements = {}
        if self.social_requirements is None:
            self.social_requirements = {}
        if self.technical_requirements is None:
            self.technical_requirements = {}
        if self.tags is None:
            self.tags = []
    
    @classmethod
    def create_table_sql(cls) -> str:
        """
        Get SQL for creating projects table.
        
        Returns:
            str: CREATE TABLE SQL statement
        """
        return """
        CREATE TABLE IF NOT EXISTS projects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            symbol TEXT,
            project_url TEXT,
            description TEXT,
            project_type TEXT NOT NULL,
            blockchain TEXT,
            contract_address TEXT,
            status TEXT NOT NULL,
            discovered_at TIMESTAMP NOT NULL,
            updated_at TIMESTAMP NOT NULL,
            requirements TEXT,
            social_requirements TEXT,
            technical_requirements TEXT,
            risk_score REAL DEFAULT 0.0,
            reward_score REAL DEFAULT 0.0,
            difficulty_score REAL DEFAULT 0.0,
            overall_score REAL DEFAULT 0.0,
            participants_count INTEGER DEFAULT 0,
            estimated_reward REAL DEFAULT 0.0,
            actual_reward REAL DEFAULT 0.0,
            source TEXT,
            tags TEXT,
            notes TEXT,
            UNIQUE(name, project_url)
        )
        """
    
    @classmethod
    def create_indexes_sql(cls) -> List[str]:
        """
        Get SQL for creating indexes.
        
        Returns:
            List[str]: List of CREATE INDEX SQL statements
        """
        return [
            "CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status)",
            "CREATE INDEX IF NOT EXISTS idx_projects_type ON projects(project_type)",
            "CREATE INDEX IF NOT EXISTS idx_projects_blockchain ON projects(blockchain)",
            "CREATE INDEX IF NOT EXISTS idx_projects_discovered ON projects(discovered_at)",
            "CREATE INDEX IF NOT EXISTS idx_projects_score ON projects(overall_score)",
            "CREATE INDEX IF NOT EXISTS idx_projects_source ON projects(source)"
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert model to dictionary for database storage.
        
        Returns:
            Dict[str, Any]: Model data as dictionary
        """
        data = asdict(self)
        
        # Convert enums to strings
        data['status'] = self.status.value if isinstance(self.status, ProjectStatus) else self.status
        data['project_type'] = self.project_type.value if isinstance(self.project_type, ProjectType) else self.project_type
        
        # Convert datetime to ISO string
        if self.discovered_at:
            data['discovered_at'] = self.discovered_at.isoformat()
        if self.updated_at:
            data['updated_at'] = self.updated_at.isoformat()
        
        # Convert complex fields to JSON
        data['requirements'] = json.dumps(self.requirements) if self.requirements else "{}"
        data['social_requirements'] = json.dumps(self.social_requirements) if self.social_requirements else "{}"
        data['technical_requirements'] = json.dumps(self.technical_requirements) if self.technical_requirements else "{}"
        data['tags'] = json.dumps(self.tags) if self.tags else "[]"
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectModel':
        """
        Create model instance from dictionary.
        
        Args:
            data: Dictionary data from database
            
        Returns:
            ProjectModel: Model instance
        """
        # Convert string enums back to enum objects
        if 'status' in data and isinstance(data['status'], str):
            data['status'] = ProjectStatus(data['status'])
        if 'project_type' in data and isinstance(data['project_type'], str):
            data['project_type'] = ProjectType(data['project_type'])
        
        # Convert ISO strings back to datetime
        if 'discovered_at' in data and isinstance(data['discovered_at'], str):
            data['discovered_at'] = datetime.fromisoformat(data['discovered_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # Convert JSON strings back to objects
        if 'requirements' in data and isinstance(data['requirements'], str):
            data['requirements'] = json.loads(data['requirements'])
        if 'social_requirements' in data and isinstance(data['social_requirements'], str):
            data['social_requirements'] = json.loads(data['social_requirements'])
        if 'technical_requirements' in data and isinstance(data['technical_requirements'], str):
            data['technical_requirements'] = json.loads(data['technical_requirements'])
        if 'tags' in data and isinstance(data['tags'], str):
            data['tags'] = json.loads(data['tags'])
        
        return cls(**data)
    
    def update_status(self, new_status: ProjectStatus):
        """
        Update project status and timestamp.
        
        Args:
            new_status: New project status
        """
        self.status = new_status
        self.updated_at = datetime.utcnow()
    
    def add_tag(self, tag: str):
        """
        Add tag to project.
        
        Args:
            tag: Tag to add
        """
        if tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = datetime.utcnow()
    
    def remove_tag(self, tag: str):
        """
        Remove tag from project.
        
        Args:
            tag: Tag to remove
        """
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = datetime.utcnow()
    
    def update_scores(self, risk: float = None, reward: float = None, 
                     difficulty: float = None):
        """
        Update assessment scores.
        
        Args:
            risk: Risk score (0-1)
            reward: Reward score (0-1)
            difficulty: Difficulty score (0-1)
        """
        if risk is not None:
            self.risk_score = max(0.0, min(1.0, risk))
        if reward is not None:
            self.reward_score = max(0.0, min(1.0, reward))
        if difficulty is not None:
            self.difficulty_score = max(0.0, min(1.0, difficulty))
        
        # Calculate overall score (weighted average)
        self.overall_score = (
            self.reward_score * 0.4 +
            (1.0 - self.risk_score) * 0.4 +
            (1.0 - self.difficulty_score) * 0.2
        )
        
        self.updated_at = datetime.utcnow()
    
    def is_eligible(self) -> bool:
        """
        Check if project is eligible for participation.
        
        Returns:
            bool: True if eligible
        """
        return self.status in [ProjectStatus.APPROVED, ProjectStatus.ACTIVE]
    
    def __str__(self) -> str:
        """String representation."""
        return f"Project({self.name}, {self.status.value}, score={self.overall_score:.2f})"
