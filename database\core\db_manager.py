"""
Database Manager

Central database management component that handles database connections,
initialization, and high-level database operations.
"""

import sqlite3
import logging
import threading
from pathlib import Path
from typing import Optional, Dict, Any, List
from contextlib import contextmanager

from .connection_pool import ConnectionPool
from .transaction_manager import TransactionManager
from ..migrations.migration_manager import MigrationManager


class DatabaseManager:
    """
    Central database manager for AirHunter system.
    
    Handles database initialization, connection management, and provides
    high-level database operations with connection pooling and transaction support.
    """
    
    def __init__(self, db_path: str = "data/airhunter.db", pool_size: int = 10):
        """
        Initialize database manager.
        
        Args:
            db_path: Path to SQLite database file
            pool_size: Maximum number of connections in pool
        """
        self.db_path = Path(db_path)
        self.pool_size = pool_size
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        self._initialized = False
        
        # Initialize components
        self.connection_pool: Optional[ConnectionPool] = None
        self.transaction_manager: Optional[TransactionManager] = None
        self.migration_manager: Optional[MigrationManager] = None
    
    def initialize(self) -> bool:
        """
        Initialize database system.
        
        Returns:
            bool: True if initialization successful
        """
        try:
            with self._lock:
                if self._initialized:
                    return True
                
                # Ensure database directory exists
                self.db_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Initialize connection pool
                self.connection_pool = ConnectionPool(
                    str(self.db_path), 
                    self.pool_size
                )
                
                # Initialize transaction manager
                self.transaction_manager = TransactionManager(self.connection_pool)
                
                # Initialize migration manager
                self.migration_manager = MigrationManager(self)
                
                # Run migrations
                self.migration_manager.run_migrations()
                
                self._initialized = True
                self.logger.info("Database manager initialized successfully")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            return False
    
    @contextmanager
    def get_connection(self):
        """
        Get database connection from pool.
        
        Yields:
            sqlite3.Connection: Database connection
        """
        if not self._initialized:
            raise RuntimeError("Database manager not initialized")
        
        connection = None
        try:
            connection = self.connection_pool.get_connection()
            yield connection
        finally:
            if connection:
                self.connection_pool.return_connection(connection)
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        Execute SELECT query and return results.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            List[Dict[str, Any]]: Query results
        """
        with self.get_connection() as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        Execute INSERT/UPDATE/DELETE query.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            int: Number of affected rows
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount
    
    def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """
        Execute query with multiple parameter sets.
        
        Args:
            query: SQL query string
            params_list: List of parameter tuples
            
        Returns:
            int: Total number of affected rows
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount
    
    def begin_transaction(self):
        """
        Begin database transaction.
        
        Returns:
            TransactionContext: Transaction context manager
        """
        return self.transaction_manager.begin_transaction()
    
    def backup_database(self, backup_path: str) -> bool:
        """
        Create database backup.
        
        Args:
            backup_path: Path for backup file
            
        Returns:
            bool: True if backup successful
        """
        try:
            backup_path = Path(backup_path)
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            with self.get_connection() as source_conn:
                backup_conn = sqlite3.connect(str(backup_path))
                source_conn.backup(backup_conn)
                backup_conn.close()
            
            self.logger.info(f"Database backup created: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to backup database: {e}")
            return False
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """
        Get table schema information.
        
        Args:
            table_name: Name of table
            
        Returns:
            List[Dict[str, Any]]: Table schema info
        """
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query)
    
    def get_table_list(self) -> List[str]:
        """
        Get list of all tables in database.
        
        Returns:
            List[str]: Table names
        """
        query = "SELECT name FROM sqlite_master WHERE type='table'"
        results = self.execute_query(query)
        return [row['name'] for row in results]
    
    def close(self):
        """Close database manager and cleanup resources."""
        try:
            if self.connection_pool:
                self.connection_pool.close()
            self._initialized = False
            self.logger.info("Database manager closed")
        except Exception as e:
            self.logger.error(f"Error closing database manager: {e}")
