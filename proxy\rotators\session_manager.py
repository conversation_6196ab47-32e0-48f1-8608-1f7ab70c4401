"""Session Manager - 会话管理器"""
import logging

class SessionManager:
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.active_sessions = {}
    
    def create_session(self, proxy: dict) -> str:
        """创建会话"""
        session_id = f"session_{len(self.active_sessions)}"
        self.active_sessions[session_id] = {"proxy": proxy, "status": "active"}
        return session_id
    
    def end_session(self, session_id: str) -> bool:
        """结束会话"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            return True
        return False
