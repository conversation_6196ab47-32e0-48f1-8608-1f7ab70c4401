#!/usr/bin/env python3
"""
Fund Management Agent - Task 4

完成Transaction Builder并创建Transaction Signer
"""

from pathlib import Path

def create_file(path: str, content: str):
    """Create a file with the given content."""
    file_path = Path(path)
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Created: {path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create {path}: {e}")
        return False

def complete_transaction_builder():
    """完成Transaction Builder的剩余方法"""
    
    additional_content = '''
    async def build_erc20_transfer_transaction(self, from_address: str, to_address: str,
                                             token_address: str, amount: Union[int, float],
                                             decimals: int, gas_settings: Dict[str, Any],
                                             nonce: Optional[int] = None) -> Dict[str, Any]:
        """
        构建ERC20代币转账交易
        
        Args:
            from_address: 发送方地址
            to_address: 接收方地址
            token_address: 代币合约地址
            amount: 转账金额
            decimals: 代币精度
            gas_settings: Gas设置
            nonce: 交易nonce
            
        Returns:
            Dict[str, Any]: 构建的交易数据
        """
        try:
            # 转换金额为最小单位
            amount_units = int(amount * (10 ** decimals))
            
            # 构建transfer函数调用数据
            # transfer(address,uint256) = 0xa9059cbb
            function_selector = "0xa9059cbb"
            to_address_padded = to_address[2:].zfill(64) if to_address.startswith("0x") else to_address.zfill(64)
            amount_hex = hex(amount_units)[2:].zfill(64)
            
            call_data = function_selector + to_address_padded + amount_hex
            
            transaction = {
                "type": "erc20_transfer",
                "from": from_address,
                "to": token_address,
                "value": 0,  # ERC20转账不发送ETH
                "gas": gas_settings.get("gas_limit", 60000),
                "gasPrice": gas_settings.get("gas_price", 20) * 1e9,
                "nonce": nonce,
                "data": call_data,
                "chainId": self.config.get("chain_id", 1),
                "token_address": token_address,
                "token_amount": amount,
                "token_decimals": decimals,
                "created_at": datetime.utcnow().isoformat()
            }
            
            # EIP-1559支持
            if "max_fee_per_gas" in gas_settings:
                transaction.update({
                    "maxFeePerGas": gas_settings["max_fee_per_gas"] * 1e9,
                    "maxPriorityFeePerGas": gas_settings["max_priority_fee_per_gas"] * 1e9
                })
                del transaction["gasPrice"]
            
            self.logger.info(f"Built ERC20 transfer: {amount} tokens from {from_address} to {to_address}")
            return transaction
            
        except Exception as e:
            self.logger.error(f"Failed to build ERC20 transfer transaction: {e}")
            return {}
    
    async def build_contract_call_transaction(self, from_address: str, contract_address: str,
                                            function_data: str, value: int,
                                            gas_settings: Dict[str, Any],
                                            nonce: Optional[int] = None) -> Dict[str, Any]:
        """
        构建合约调用交易
        
        Args:
            from_address: 调用方地址
            contract_address: 合约地址
            function_data: 函数调用数据
            value: 发送的ETH数量 (wei)
            gas_settings: Gas设置
            nonce: 交易nonce
            
        Returns:
            Dict[str, Any]: 构建的交易数据
        """
        try:
            transaction = {
                "type": "contract_call",
                "from": from_address,
                "to": contract_address,
                "value": value,
                "gas": gas_settings.get("gas_limit", 100000),
                "gasPrice": gas_settings.get("gas_price", 20) * 1e9,
                "nonce": nonce,
                "data": function_data,
                "chainId": self.config.get("chain_id", 1),
                "created_at": datetime.utcnow().isoformat()
            }
            
            # EIP-1559支持
            if "max_fee_per_gas" in gas_settings:
                transaction.update({
                    "maxFeePerGas": gas_settings["max_fee_per_gas"] * 1e9,
                    "maxPriorityFeePerGas": gas_settings["max_priority_fee_per_gas"] * 1e9
                })
                del transaction["gasPrice"]
            
            self.logger.info(f"Built contract call transaction to {contract_address}")
            return transaction
            
        except Exception as e:
            self.logger.error(f"Failed to build contract call transaction: {e}")
            return {}
    
    async def build_approve_transaction(self, from_address: str, token_address: str,
                                      spender_address: str, amount: Union[int, float],
                                      decimals: int, gas_settings: Dict[str, Any],
                                      nonce: Optional[int] = None) -> Dict[str, Any]:
        """
        构建代币授权交易
        
        Args:
            from_address: 授权方地址
            token_address: 代币合约地址
            spender_address: 被授权方地址
            amount: 授权金额
            decimals: 代币精度
            gas_settings: Gas设置
            nonce: 交易nonce
            
        Returns:
            Dict[str, Any]: 构建的交易数据
        """
        try:
            # 转换金额为最小单位
            amount_units = int(amount * (10 ** decimals))
            
            # 构建approve函数调用数据
            # approve(address,uint256) = 0x095ea7b3
            function_selector = "0x095ea7b3"
            spender_padded = spender_address[2:].zfill(64) if spender_address.startswith("0x") else spender_address.zfill(64)
            amount_hex = hex(amount_units)[2:].zfill(64)
            
            call_data = function_selector + spender_padded + amount_hex
            
            transaction = {
                "type": "approve",
                "from": from_address,
                "to": token_address,
                "value": 0,
                "gas": gas_settings.get("gas_limit", 50000),
                "gasPrice": gas_settings.get("gas_price", 20) * 1e9,
                "nonce": nonce,
                "data": call_data,
                "chainId": self.config.get("chain_id", 1),
                "token_address": token_address,
                "spender_address": spender_address,
                "approve_amount": amount,
                "created_at": datetime.utcnow().isoformat()
            }
            
            # EIP-1559支持
            if "max_fee_per_gas" in gas_settings:
                transaction.update({
                    "maxFeePerGas": gas_settings["max_fee_per_gas"] * 1e9,
                    "maxPriorityFeePerGas": gas_settings["max_priority_fee_per_gas"] * 1e9
                })
                del transaction["gasPrice"]
            
            self.logger.info(f"Built approve transaction: {amount} tokens for {spender_address}")
            return transaction
            
        except Exception as e:
            self.logger.error(f"Failed to build approve transaction: {e}")
            return {}
    
    def validate_transaction(self, transaction: Dict[str, Any]) -> bool:
        """
        验证交易数据
        
        Args:
            transaction: 交易数据
            
        Returns:
            bool: 验证是否通过
        """
        try:
            required_fields = ["from", "to", "gas", "nonce", "chainId"]
            
            for field in required_fields:
                if field not in transaction:
                    self.logger.error(f"Missing required field: {field}")
                    return False
            
            # 验证地址格式
            if not self._is_valid_address(transaction["from"]):
                self.logger.error("Invalid from address")
                return False
            
            if not self._is_valid_address(transaction["to"]):
                self.logger.error("Invalid to address")
                return False
            
            # 验证gas限制
            if transaction["gas"] <= 0:
                self.logger.error("Invalid gas limit")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to validate transaction: {e}")
            return False
    
    def _is_valid_address(self, address: str) -> bool:
        """验证以太坊地址格式"""
        if not isinstance(address, str):
            return False
        if not address.startswith("0x"):
            return False
        if len(address) != 42:
            return False
        try:
            int(address[2:], 16)
            return True
        except ValueError:
            return False
'''
    
    return additional_content

def create_transaction_signer():
    """创建Transaction Signer"""
    
    signer_content = '''"""
Transaction Signer

交易签名器，负责使用私钥对交易进行数字签名。
"""

import logging
import hashlib
from typing import Dict, Optional, Any
from datetime import datetime


class TransactionSigner:
    """
    交易签名器
    
    负责使用私钥对区块链交易进行数字签名，支持多种签名算法。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化交易签名器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 签名统计
        self.signature_stats = {
            'total_signatures': 0,
            'successful_signatures': 0,
            'failed_signatures': 0
        }
        
        # 支持的签名类型
        self.supported_signature_types = ["ecdsa", "eip155", "eip1559"]
    
    async def initialize(self) -> bool:
        """
        初始化交易签名器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("Transaction Signer initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Transaction Signer: {e}")
            return False
    
    async def sign_transaction(self, transaction: Dict[str, Any], 
                             private_key: str) -> Optional[Dict[str, Any]]:
        """
        签名交易
        
        Args:
            transaction: 待签名的交易数据
            private_key: 私钥
            
        Returns:
            Optional[Dict[str, Any]]: 签名后的交易数据
        """
        try:
            self.signature_stats['total_signatures'] += 1
            
            # 验证私钥格式
            if not self._validate_private_key(private_key):
                self.logger.error("Invalid private key format")
                self.signature_stats['failed_signatures'] += 1
                return None
            
            # 选择签名方法
            if "maxFeePerGas" in transaction:
                # EIP-1559交易
                signed_tx = await self._sign_eip1559_transaction(transaction, private_key)
            elif transaction.get("chainId"):
                # EIP-155交易
                signed_tx = await self._sign_eip155_transaction(transaction, private_key)
            else:
                # 传统交易
                signed_tx = await self._sign_legacy_transaction(transaction, private_key)
            
            if signed_tx:
                self.signature_stats['successful_signatures'] += 1
                self.logger.info(f"Successfully signed transaction")
                return signed_tx
            else:
                self.signature_stats['failed_signatures'] += 1
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to sign transaction: {e}")
            self.signature_stats['failed_signatures'] += 1
            return None
    
    async def _sign_eip1559_transaction(self, transaction: Dict[str, Any], 
                                      private_key: str) -> Optional[Dict[str, Any]]:
        """
        签名EIP-1559交易
        
        Args:
            transaction: 交易数据
            private_key: 私钥
            
        Returns:
            Optional[Dict[str, Any]]: 签名后的交易
        """
        try:
            # 模拟EIP-1559签名过程
            # 实际实现需要使用cryptography库
            
            # 构建签名数据
            signature_data = {
                "v": 27,  # 恢复ID
                "r": "0x" + "1" * 64,  # 签名r值
                "s": "0x" + "2" * 64   # 签名s值
            }
            
            # 添加签名到交易
            signed_transaction = transaction.copy()
            signed_transaction.update(signature_data)
            signed_transaction["signed"] = True
            signed_transaction["signature_type"] = "eip1559"
            signed_transaction["signed_at"] = datetime.utcnow().isoformat()
            
            return signed_transaction
            
        except Exception as e:
            self.logger.error(f"Failed to sign EIP-1559 transaction: {e}")
            return None
'''
    
    return signer_content

def main():
    """主函数"""
    print("🚀 Fund Management Agent - Task 4")
    print("=" * 50)
    
    # 完成Transaction Builder
    additional_content = complete_transaction_builder()
    
    try:
        with open("fund_management/transactions/transaction_builder.py", 'a', encoding='utf-8') as f:
            f.write(additional_content)
        print("✅ Completed Transaction Builder methods")
    except Exception as e:
        print(f"❌ Failed to complete Transaction Builder: {e}")
    
    # 创建Transaction Signer
    signer_content = create_transaction_signer()
    success = create_file("fund_management/transactions/transaction_signer.py", signer_content)
    
    if success:
        print("✅ Task 4 completed: Transaction Signer created!")
    else:
        print("❌ Task 4 failed")

if __name__ == "__main__":
    main()
