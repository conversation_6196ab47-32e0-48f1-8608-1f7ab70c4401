#!/usr/bin/env python3
"""
项目发现智能体单元测试

测试项目发现智能体的核心功能
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from discovery.discovery_agent import DiscoveryAgent
from discovery.models.project import Project, ProjectType, ProjectStatus, Blockchain


class TestDiscoveryAgent(unittest.TestCase):
    """项目发现智能体测试类"""

    def setUp(self):
        """测试前准备"""
        self.config = {
            'sources': {
                'twitter': {'enabled': True, 'api_key': 'test_key'},
                'telegram': {'enabled': True, 'bot_token': 'test_token'},
                'discord': {'enabled': False}
            },
            'filters': {
                'min_followers': 100,
                'keywords': ['airdrop', 'testnet'],
                'blacklist': ['scam', 'fake']
            },
            'storage': {
                'type': 'memory',
                'connection_string': ':memory:'
            }
        }
        
        # 模拟依赖组件
        with patch.multiple(
            'discovery.discovery_agent',
            TwitterMonitor=MagicMock(),
            TelegramMonitor=MagicMock(),
            DiscordMonitor=MagicMock(),
            ProjectFilter=MagicMock(),
            ProjectStorage=MagicMock()
        ):
            self.agent = DiscoveryAgent(self.config)

    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.agent.agent_name, "discovery")
        self.assertEqual(self.agent.agent_type, "discovery")
        self.assertIsNotNone(self.agent.version)
        self.assertEqual(self.agent.status, "initialized")

    def test_start_monitoring(self):
        """测试开始监控"""
        # 模拟监控器启动
        self.agent.twitter_monitor.start = Mock()
        self.agent.telegram_monitor.start = Mock()
        
        self.agent.start_monitoring()
        
        self.agent.twitter_monitor.start.assert_called_once()
        self.agent.telegram_monitor.start.assert_called_once()

    def test_stop_monitoring(self):
        """测试停止监控"""
        # 模拟监控器停止
        self.agent.twitter_monitor.stop = Mock()
        self.agent.telegram_monitor.stop = Mock()
        
        self.agent.stop_monitoring()
        
        self.agent.twitter_monitor.stop.assert_called_once()
        self.agent.telegram_monitor.stop.assert_called_once()

    def test_add_project_success(self):
        """测试成功添加项目"""
        # 创建测试项目
        project = Project(
            id="test_project_1",
            name="Test Project",
            description="A test project for airdrop",
            project_type=ProjectType.AIRDROP,
            blockchain=Blockchain.ETHEREUM,
            website="https://test-project.com",
            discovery_time=datetime.now().timestamp()
        )
        
        # 模拟过滤器通过
        self.agent.project_filter.should_include.return_value = True
        
        # 模拟存储成功
        self.agent.project_storage.save_project.return_value = True
        
        result = self.agent.add_project(project)
        
        self.assertTrue(result)
        self.agent.project_filter.should_include.assert_called_once_with(project)
        self.agent.project_storage.save_project.assert_called_once_with(project)

    def test_add_project_filtered_out(self):
        """测试项目被过滤器排除"""
        project = Project(
            id="test_project_2",
            name="Scam Project",
            description="This is a scam project",
            project_type=ProjectType.AIRDROP,
            blockchain=Blockchain.ETHEREUM,
            website="https://scam-project.com",
            discovery_time=datetime.now().timestamp()
        )
        
        # 模拟过滤器拒绝
        self.agent.project_filter.should_include.return_value = False
        
        result = self.agent.add_project(project)
        
        self.assertFalse(result)
        self.agent.project_filter.should_include.assert_called_once_with(project)
        self.agent.project_storage.save_project.assert_not_called()

    def test_get_project(self):
        """测试获取项目"""
        project_id = "test_project_1"
        expected_project = Mock()
        
        # 模拟存储返回项目
        self.agent.project_storage.get_project.return_value = expected_project
        
        result = self.agent.get_project(project_id)
        
        self.assertEqual(result, expected_project)
        self.agent.project_storage.get_project.assert_called_once_with(project_id)

    def test_get_projects_by_status(self):
        """测试按状态获取项目"""
        status = ProjectStatus.DISCOVERED
        expected_projects = [Mock(), Mock()]
        
        # 模拟存储返回项目列表
        self.agent.project_storage.get_projects_by_status.return_value = expected_projects
        
        result = self.agent.get_projects_by_status(status)
        
        self.assertEqual(result, expected_projects)
        self.agent.project_storage.get_projects_by_status.assert_called_once_with(status)

    def test_update_project(self):
        """测试更新项目"""
        project = Mock()
        project.id = "test_project_1"
        
        # 模拟存储更新成功
        self.agent.project_storage.update_project.return_value = True
        
        result = self.agent.update_project(project)
        
        self.assertTrue(result)
        self.agent.project_storage.update_project.assert_called_once_with(project)

    def test_search_projects(self):
        """测试搜索项目"""
        query = "airdrop ethereum"
        expected_projects = [Mock(), Mock()]
        
        # 模拟存储搜索结果
        self.agent.project_storage.search_projects.return_value = expected_projects
        
        result = self.agent.search_projects(query)
        
        self.assertEqual(result, expected_projects)
        self.agent.project_storage.search_projects.assert_called_once_with(query)

    def test_get_stats(self):
        """测试获取统计信息"""
        expected_stats = {
            'total_projects': 100,
            'discovered_projects': 80,
            'assessed_projects': 60,
            'active_projects': 40
        }
        
        # 模拟存储统计信息
        self.agent.project_storage.get_stats.return_value = expected_stats
        
        result = self.agent.get_stats()
        
        self.assertEqual(result, expected_stats)

    def test_get_agent_info(self):
        """测试获取智能体信息"""
        info = self.agent.get_agent_info()
        
        self.assertEqual(info["name"], "discovery")
        self.assertEqual(info["type"], "discovery")
        self.assertIn("capabilities", info)
        self.assertIn("status", info)
        self.assertIn("sources", info)

    @patch('discovery.discovery_agent.TwitterMonitor')
    def test_process_twitter_mention(self, mock_twitter_monitor):
        """测试处理Twitter提及"""
        # 模拟Twitter提及数据
        mention_data = {
            'id': 'tweet_123',
            'text': 'Check out this new airdrop project!',
            'user': {'followers_count': 1000},
            'entities': {'urls': [{'expanded_url': 'https://new-project.com'}]}
        }
        
        # 模拟项目创建
        with patch.object(self.agent, '_create_project_from_mention') as mock_create:
            mock_project = Mock()
            mock_create.return_value = mock_project
            
            with patch.object(self.agent, 'add_project') as mock_add:
                mock_add.return_value = True
                
                self.agent._process_twitter_mention(mention_data)
                
                mock_create.assert_called_once_with(mention_data, 'twitter')
                mock_add.assert_called_once_with(mock_project)

    def test_validate_project_data(self):
        """测试验证项目数据"""
        # 有效项目数据
        valid_project = Project(
            id="valid_project",
            name="Valid Project",
            description="A valid project description",
            project_type=ProjectType.AIRDROP,
            blockchain=Blockchain.ETHEREUM,
            website="https://valid-project.com",
            discovery_time=datetime.now().timestamp()
        )
        
        self.assertTrue(self.agent._validate_project_data(valid_project))
        
        # 无效项目数据（缺少名称）
        invalid_project = Project(
            id="invalid_project",
            name="",  # 空名称
            description="Invalid project",
            project_type=ProjectType.AIRDROP,
            blockchain=Blockchain.ETHEREUM,
            website="https://invalid-project.com",
            discovery_time=datetime.now().timestamp()
        )
        
        self.assertFalse(self.agent._validate_project_data(invalid_project))


class TestDiscoveryAgentIntegration(unittest.TestCase):
    """项目发现智能体集成测试"""

    def setUp(self):
        """测试前准备"""
        self.config = {
            'sources': {'twitter': {'enabled': False}},  # 禁用外部服务
            'storage': {'type': 'memory'}
        }

    @patch('discovery.discovery_agent.ProjectStorage')
    @patch('discovery.discovery_agent.ProjectFilter')
    def test_full_discovery_workflow(self, mock_filter, mock_storage):
        """测试完整发现工作流"""
        # 创建智能体
        agent = DiscoveryAgent(self.config)
        
        # 模拟发现新项目
        project = Project(
            id="workflow_test",
            name="Workflow Test Project",
            description="Testing the full workflow",
            project_type=ProjectType.TESTNET,
            blockchain=Blockchain.ARBITRUM,
            website="https://workflow-test.com",
            discovery_time=datetime.now().timestamp()
        )
        
        # 模拟过滤器和存储
        mock_filter.return_value.should_include.return_value = True
        mock_storage.return_value.save_project.return_value = True
        
        # 执行添加项目
        result = agent.add_project(project)
        
        # 验证结果
        self.assertTrue(result)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
