"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/BytesContracts.sol:BytesContract
BYTES_CONTRACT_BYTECODE = "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"  # noqa: E501
BYTES_CONTRACT_RUNTIME = "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"  # noqa: E501
BYTES_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "bytes", "name": "_value", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [],
        "name": "constValue",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getValue",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes", "name": "_value", "type": "bytes"}],
        "name": "setValue",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
BYTES_CONTRACT_DATA = {
    "bytecode": BYTES_CONTRACT_BYTECODE,
    "bytecode_runtime": BYTES_CONTRACT_RUNTIME,
    "abi": BYTES_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/BytesContracts.sol:Bytes32Contract
BYTES32_CONTRACT_BYTECODE = "0x60806040527f01230123012301230123012301230123012301230123012301230123012301235f553480156031575f5ffd5b50604051610238380380610238833981810160405281019060519190608f565b806001819055505060b5565b5f5ffd5b5f819050919050565b6071816061565b8114607a575f5ffd5b50565b5f81519050608981606a565b92915050565b5f6020828403121560a15760a0605d565b5b5f60ac84828501607d565b91505092915050565b610176806100c25f395ff3fe608060405234801561000f575f5ffd5b506004361061003f575f3560e01c8063209652551461004357806330de3cee1461006157806358825b101461007f575b5f5ffd5b61004b61009b565b60405161005891906100ce565b60405180910390f35b6100696100a4565b60405161007691906100ce565b60405180910390f35b61009960048036038101906100949190610115565b6100ac565b005b5f600154905090565b5f5f54905090565b8060018190555050565b5f819050919050565b6100c8816100b6565b82525050565b5f6020820190506100e15f8301846100bf565b92915050565b5f5ffd5b6100f4816100b6565b81146100fe575f5ffd5b50565b5f8135905061010f816100eb565b92915050565b5f6020828403121561012a576101296100e7565b5b5f61013784828501610101565b9150509291505056fea2646970667358221220c158440d9344fca45315eee01e851c4a2624e94a37ca3b0012b31b3b2c85dd6364736f6c634300081e0033"  # noqa: E501
BYTES32_CONTRACT_RUNTIME = "0x608060405234801561000f575f5ffd5b506004361061003f575f3560e01c8063209652551461004357806330de3cee1461006157806358825b101461007f575b5f5ffd5b61004b61009b565b60405161005891906100ce565b60405180910390f35b6100696100a4565b60405161007691906100ce565b60405180910390f35b61009960048036038101906100949190610115565b6100ac565b005b5f600154905090565b5f5f54905090565b8060018190555050565b5f819050919050565b6100c8816100b6565b82525050565b5f6020820190506100e15f8301846100bf565b92915050565b5f5ffd5b6100f4816100b6565b81146100fe575f5ffd5b50565b5f8135905061010f816100eb565b92915050565b5f6020828403121561012a576101296100e7565b5b5f61013784828501610101565b9150509291505056fea2646970667358221220c158440d9344fca45315eee01e851c4a2624e94a37ca3b0012b31b3b2c85dd6364736f6c634300081e0033"  # noqa: E501
BYTES32_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "bytes32", "name": "_value", "type": "bytes32"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [],
        "name": "constValue",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getValue",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes32", "name": "_value", "type": "bytes32"}],
        "name": "setValue",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
BYTES32_CONTRACT_DATA = {
    "bytecode": BYTES32_CONTRACT_BYTECODE,
    "bytecode_runtime": BYTES32_CONTRACT_RUNTIME,
    "abi": BYTES32_CONTRACT_ABI,
}
