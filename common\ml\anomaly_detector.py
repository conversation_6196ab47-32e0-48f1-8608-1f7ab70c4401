"""
Anomaly Detector

Machine learning component for detecting anomalies in system behavior,
project patterns, and user activities to identify potential issues or fraud.
"""

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    # Fallback implementations
    class np:
        @staticmethod
        def mean(values):
            return sum(values) / len(values) if values else 0

        @staticmethod
        def std(values):
            if not values:
                return 0
            mean_val = sum(values) / len(values)
            variance = sum((x - mean_val) ** 2 for x in values) / len(values)
            return variance ** 0.5

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import deque
import json


class AnomalyDetector:
    """
    Anomaly detection system for AirHunter.
    
    Detects unusual patterns in:
    - System performance and resource usage
    - Project characteristics and behavior
    - User interaction patterns
    - Network and security events
    """
    
    def __init__(self, sensitivity: float = 0.8, window_size: int = 100):
        """
        Initialize anomaly detector.
        
        Args:
            sensitivity: Detection sensitivity (0.0 to 1.0)
            window_size: Size of data window for analysis
        """
        self.sensitivity = sensitivity
        self.window_size = window_size
        self.logger = logging.getLogger(__name__)
        
        # Data windows for different metrics
        self.data_windows = {
            'cpu_usage': deque(maxlen=window_size),
            'memory_usage': deque(maxlen=window_size),
            'response_time': deque(maxlen=window_size),
            'error_rate': deque(maxlen=window_size),
            'task_completion_rate': deque(maxlen=window_size),
            'project_score': deque(maxlen=window_size)
        }
        
        # Baseline statistics
        self.baselines = {}
        
        # Anomaly history
        self.anomaly_history = deque(maxlen=1000)
        
        # Statistics
        self.stats = {
            'total_detections': 0,
            'false_positives': 0,
            'true_positives': 0,
            'last_detection': None
        }
    
    def add_data_point(self, metric_name: str, value: float, timestamp: datetime = None) -> bool:
        """
        Add data point for anomaly detection.
        
        Args:
            metric_name: Name of the metric
            value: Metric value
            timestamp: Data timestamp
            
        Returns:
            bool: True if anomaly detected
        """
        try:
            if timestamp is None:
                timestamp = datetime.utcnow()
            
            # Add to data window
            if metric_name in self.data_windows:
                self.data_windows[metric_name].append({
                    'value': value,
                    'timestamp': timestamp
                })
                
                # Update baseline if we have enough data
                if len(self.data_windows[metric_name]) >= 10:
                    self._update_baseline(metric_name)
                
                # Check for anomaly
                return self._detect_anomaly(metric_name, value, timestamp)
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error adding data point: {e}")
            return False
    
    def detect_batch_anomalies(self, data: Dict[str, List[float]]) -> Dict[str, List[bool]]:
        """
        Detect anomalies in batch data.
        
        Args:
            data: Dictionary of metric names to value lists
            
        Returns:
            Dict[str, List[bool]]: Anomaly flags for each data point
        """
        results = {}
        
        for metric_name, values in data.items():
            anomalies = []
            for value in values:
                is_anomaly = self.add_data_point(metric_name, value)
                anomalies.append(is_anomaly)
            results[metric_name] = anomalies
        
        return results
    
    def get_anomaly_score(self, metric_name: str, value: float) -> float:
        """
        Get anomaly score for a value.
        
        Args:
            metric_name: Name of the metric
            value: Value to score
            
        Returns:
            float: Anomaly score (0.0 to 1.0)
        """
        try:
            if metric_name not in self.baselines:
                return 0.0
            
            baseline = self.baselines[metric_name]
            mean = baseline['mean']
            std = baseline['std']
            
            if std == 0:
                return 0.0
            
            # Calculate z-score
            z_score = abs(value - mean) / std
            
            # Convert to anomaly score (0-1)
            # Values beyond 2 standard deviations are considered highly anomalous
            anomaly_score = min(1.0, z_score / 3.0)
            
            return anomaly_score
            
        except Exception as e:
            self.logger.error(f"Error calculating anomaly score: {e}")
            return 0.0
    
    def get_system_health_score(self) -> float:
        """
        Get overall system health score based on recent anomalies.
        
        Returns:
            float: Health score (0.0 to 1.0)
        """
        try:
            # Look at recent anomalies (last hour)
            recent_time = datetime.utcnow() - timedelta(hours=1)
            recent_anomalies = [
                a for a in self.anomaly_history 
                if a['timestamp'] > recent_time
            ]
            
            if not recent_anomalies:
                return 1.0
            
            # Calculate weighted health score
            total_weight = 0
            weighted_score = 0
            
            severity_weights = {
                'low': 0.1,
                'medium': 0.3,
                'high': 0.6,
                'critical': 1.0
            }
            
            for anomaly in recent_anomalies:
                severity = anomaly.get('severity', 'medium')
                weight = severity_weights.get(severity, 0.3)
                score = 1.0 - anomaly.get('score', 0.5)
                
                weighted_score += weight * score
                total_weight += weight
            
            if total_weight > 0:
                health_score = weighted_score / total_weight
            else:
                health_score = 1.0
            
            return max(0.0, min(1.0, health_score))
            
        except Exception as e:
            self.logger.error(f"Error calculating health score: {e}")
            return 0.5
    
    def get_anomaly_report(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get anomaly report for specified time period.
        
        Args:
            hours: Number of hours to include in report
            
        Returns:
            Dict[str, Any]: Anomaly report
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            recent_anomalies = [
                a for a in self.anomaly_history 
                if a['timestamp'] > cutoff_time
            ]
            
            # Group by metric
            by_metric = {}
            by_severity = {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
            
            for anomaly in recent_anomalies:
                metric = anomaly['metric']
                severity = anomaly.get('severity', 'medium')
                
                if metric not in by_metric:
                    by_metric[metric] = []
                by_metric[metric].append(anomaly)
                by_severity[severity] += 1
            
            return {
                'period_hours': hours,
                'total_anomalies': len(recent_anomalies),
                'by_metric': {k: len(v) for k, v in by_metric.items()},
                'by_severity': by_severity,
                'health_score': self.get_system_health_score(),
                'most_anomalous_metric': max(by_metric.keys(), key=lambda k: len(by_metric[k])) if by_metric else None,
                'recent_anomalies': recent_anomalies[-10:],  # Last 10 anomalies
                'statistics': self.stats
            }
            
        except Exception as e:
            self.logger.error(f"Error generating anomaly report: {e}")
            return {'error': str(e)}
    
    def _detect_anomaly(self, metric_name: str, value: float, timestamp: datetime) -> bool:
        """Detect if value is anomalous."""
        try:
            anomaly_score = self.get_anomaly_score(metric_name, value)
            
            if anomaly_score > self.sensitivity:
                # Determine severity
                if anomaly_score > 0.9:
                    severity = 'critical'
                elif anomaly_score > 0.7:
                    severity = 'high'
                elif anomaly_score > 0.5:
                    severity = 'medium'
                else:
                    severity = 'low'
                
                # Record anomaly
                anomaly = {
                    'metric': metric_name,
                    'value': value,
                    'score': anomaly_score,
                    'severity': severity,
                    'timestamp': timestamp,
                    'baseline_mean': self.baselines[metric_name]['mean'],
                    'baseline_std': self.baselines[metric_name]['std']
                }
                
                self.anomaly_history.append(anomaly)
                self.stats['total_detections'] += 1
                self.stats['last_detection'] = timestamp
                
                self.logger.warning(
                    f"Anomaly detected in {metric_name}: {value} "
                    f"(score: {anomaly_score:.3f}, severity: {severity})"
                )
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting anomaly: {e}")
            return False
    
    def _update_baseline(self, metric_name: str):
        """Update baseline statistics for metric."""
        try:
            data_window = self.data_windows[metric_name]
            values = [point['value'] for point in data_window]
            
            if len(values) < 2:
                return
            
            mean = np.mean(values)
            std = np.std(values)
            
            self.baselines[metric_name] = {
                'mean': mean,
                'std': std,
                'min': min(values),
                'max': max(values),
                'count': len(values),
                'updated_at': datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error updating baseline for {metric_name}: {e}")
    
    def save_model(self, filepath: str) -> bool:
        """Save anomaly detection model."""
        try:
            data = {
                'baselines': self.baselines,
                'sensitivity': self.sensitivity,
                'window_size': self.window_size,
                'stats': self.stats,
                'saved_at': datetime.utcnow().isoformat()
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            self.logger.info(f"Anomaly detection model saved to {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save model: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """Load anomaly detection model."""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            self.baselines = data.get('baselines', {})
            self.sensitivity = data.get('sensitivity', self.sensitivity)
            self.window_size = data.get('window_size', self.window_size)
            self.stats.update(data.get('stats', {}))
            
            self.logger.info(f"Anomaly detection model loaded from {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            return False
