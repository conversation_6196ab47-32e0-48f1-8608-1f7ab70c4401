"""
Sybil Defense Agent Main Module

This is the main entry point for the Sybil Defense Agent.
"""

import os
import sys
import json
import logging
import argparse
import time
from typing import Dict, Any, Optional

# Add parent directory to path to allow imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sybil_defense.sybil_defense_agent import SybilDefenseAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("data/logs/sybil_defense.log")
    ]
)
logger = logging.getLogger("SybilDefenseMain")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Sybil Defense Agent")
    parser.add_argument("--config", type=str, default="config/sybil_defense_config.json",
                        help="Path to configuration file")
    parser.add_argument("--action", type=str, 
                        choices=["start", "stop", "status", "create_identity", "select_identity", 
                                "rotate_fingerprint", "rotate_proxy", "list_identities"],
                        default="start", help="Action to perform")
    parser.add_argument("--project_id", type=str, help="Project ID for identity-related actions")
    parser.add_argument("--identity_id", type=str, help="Identity ID for identity-specific actions")
    parser.add_argument("--identity_type", type=str, choices=["default", "social", "wallet"],
                        default="default", help="Type of identity to create")
    parser.add_argument("--risk_level", type=str, choices=["low", "medium", "high"],
                        help="Maximum risk level for identity selection")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from file."""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        else:
            logger.warning(f"Config file not found: {config_path}")
            return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def main():
    """Main entry point for the Sybil Defense Agent."""
    # Parse arguments
    args = parse_arguments()
    
    # Ensure logs directory exists
    os.makedirs("data/logs", exist_ok=True)
    
    # Load configuration
    config = load_config(args.config)
    
    # Create agent
    agent = SybilDefenseAgent(args.config if os.path.exists(args.config) else None)
    
    # Perform requested action
    if args.action == "start":
        logger.info("Starting Sybil Defense Agent")
        success = agent.start()
        logger.info(f"Agent started: {success}")
    
    elif args.action == "stop":
        logger.info("Stopping Sybil Defense Agent")
        success = agent.stop()
        logger.info(f"Agent stopped: {success}")
    
    elif args.action == "status":
        agent.start()
        status = agent.status()
        logger.info(f"Agent status: {status}")
        print(json.dumps(status, indent=2))
    
    elif args.action == "create_identity":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for create_identity action")
            return
        
        logger.info(f"Creating identity for project {args.project_id} of type {args.identity_type}")
        identity = agent.create_identity(args.project_id, args.identity_type)
        
        if identity:
            logger.info(f"Identity created with ID: {identity['id']}")
            print(json.dumps(identity, indent=2))
        else:
            logger.error("Failed to create identity")
    
    elif args.action == "select_identity":
        agent.start()
        
        if not args.project_id:
            logger.error("Project ID is required for select_identity action")
            return
        
        logger.info(f"Selecting identity for project {args.project_id}")
        identity = agent.select_identity(args.project_id, args.identity_type, args.risk_level)
        
        if identity:
            logger.info(f"Selected identity: {identity['id']}")
            print(json.dumps(identity, indent=2))
        else:
            logger.error("No suitable identity found")
    
    elif args.action == "rotate_fingerprint":
        agent.start()
        
        if not args.identity_id:
            logger.error("Identity ID is required for rotate_fingerprint action")
            return
        
        logger.info(f"Rotating fingerprint for identity {args.identity_id}")
        success = agent.rotate_fingerprint(args.identity_id)
        
        if success:
            logger.info("Fingerprint rotated successfully")
            identity = agent.get_identity(args.identity_id)
            if identity:
                print(json.dumps(identity["fingerprint"], indent=2))
        else:
            logger.error("Failed to rotate fingerprint")
    
    elif args.action == "rotate_proxy":
        agent.start()
        
        if not args.identity_id:
            logger.error("Identity ID is required for rotate_proxy action")
            return
        
        logger.info(f"Rotating proxy for identity {args.identity_id}")
        success = agent.rotate_proxy(args.identity_id)
        
        if success:
            logger.info("Proxy rotated successfully")
            identity = agent.get_identity(args.identity_id)
            if identity and identity.get("proxy"):
                print(json.dumps(identity["proxy"], indent=2))
        else:
            logger.error("Failed to rotate proxy")
    
    elif args.action == "list_identities":
        agent.start()
        
        if args.project_id:
            identities = agent.get_project_identities(args.project_id)
            logger.info(f"Found {len(identities)} identities for project {args.project_id}")
        else:
            identities = list(agent.identities.values())
            logger.info(f"Found {len(identities)} total identities")
        
        print(f"Identities ({len(identities)}):")
        for identity in identities:
            print(f"- {identity['id']}: Project {identity['project_id']}, Type {identity['type']}")
            print(f"  Status: {identity['status']}, Risk: {identity['risk_level']}")
            print(f"  Created: {identity['created_at']}")
            print(f"  Usage count: {identity['usage_count']}")
            if identity.get("last_used"):
                print(f"  Last used: {identity['last_used']}")
            print()

if __name__ == "__main__":
    main()
