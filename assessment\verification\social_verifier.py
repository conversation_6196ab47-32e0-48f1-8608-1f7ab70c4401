"""
社交媒体验证器

该模块实现了验证项目社交媒体的功能。
"""

import logging
import requests
import time
import re
from typing import Dict, List, Any, Optional

from discovery.models.project import Project


class SocialVerifier:
    """社交媒体验证器，负责验证项目社交媒体"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化社交媒体验证器
        
        Args:
            config: 配置字典，包含社交媒体验证器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.timeout = config.get('timeout', 10)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        self.min_followers = config.get('min_followers', {
            'twitter': 100,
            'telegram': 50,
            'discord': 100,
            'medium': 10,
            'github': 10
        })
        self.min_activity_age = config.get('min_activity_age', 7)  # 最小活动时间（天）
        self.min_post_count = config.get('min_post_count', {
            'twitter': 5,
            'telegram': 3,
            'discord': 5,
            'medium': 1,
            'github': 1
        })
        self.suspicious_patterns = config.get('suspicious_patterns', [
            r'buy\s+now',
            r'price\s+pump',
            r'x\d+\s+gains',
            r'get\s+rich',
            r'moon\s+soon',
            r'guaranteed\s+profit'
        ])
        
        # 统计信息
        self._stats = {
            'total_verifications': 0,
            'successful_verifications': 0,
            'failed_verifications': 0,
            'high_confidence': 0,
            'medium_confidence': 0,
            'low_confidence': 0
        }
    
    def verify(self, project: Project) -> Dict[str, Any]:
        """
        验证项目社交媒体
        
        Args:
            project: 要验证的项目
            
        Returns:
            验证结果字典
        """
        self.logger.info(f"开始验证项目 {project.name} (ID: {project.id}) 的社交媒体")
        
        try:
            # 更新统计信息
            self._stats['total_verifications'] += 1
            
            # 初始化结果
            result = {
                'verified': False,
                'confidence': 0.0,
                'channel_count': 0,
                'verified_channels': 0,
                'total_followers': 0,
                'has_suspicious_content': False,
                'activity_level': 'unknown',
                'warnings': [],
                'channel_results': {}
            }
            
            # 获取社交媒体渠道
            social_channels = project.social_channels
            
            if not social_channels:
                self.logger.warning(f"项目 {project.name} 没有社交媒体渠道")
                result['warnings'].append("没有社交媒体渠道")
                result['confidence'] = 0.2
                self._stats['failed_verifications'] += 1
                self._stats['low_confidence'] += 1
                return result
            
            # 更新渠道数量
            result['channel_count'] = len(social_channels)
            
            # 验证每个社交媒体渠道
            for channel in social_channels:
                platform = channel.platform
                url = channel.url
                
                if not platform or not url:
                    continue
                
                # 验证渠道
                channel_result = self._verify_channel(platform, url)
                
                # 更新结果
                if channel_result['verified']:
                    result['verified_channels'] += 1
                
                if channel_result['followers']:
                    result['total_followers'] += channel_result['followers']
                
                if channel_result['has_suspicious_content']:
                    result['has_suspicious_content'] = True
                    result['warnings'].append(f"{platform} 渠道包含可疑内容")
                
                if channel_result['warnings']:
                    result['warnings'].extend([f"{platform}: {warning}" for warning in channel_result['warnings']])
                
                # 保存渠道结果
                result['channel_results'][platform] = channel_result
            
            # 确定活动级别
            if result['total_followers'] >= 10000:
                result['activity_level'] = 'high'
            elif result['total_followers'] >= 1000:
                result['activity_level'] = 'medium'
            elif result['total_followers'] >= 100:
                result['activity_level'] = 'low'
            else:
                result['activity_level'] = 'very_low'
                result['warnings'].append(f"总粉丝数较少 ({result['total_followers']})")
            
            # 计算置信度
            confidence = self._calculate_confidence(result)
            result['confidence'] = confidence
            
            # 确定验证结果
            if result['has_suspicious_content']:
                result['verified'] = False
                self._stats['failed_verifications'] += 1
            elif confidence >= 0.7:
                result['verified'] = True
                self._stats['successful_verifications'] += 1
            else:
                result['verified'] = False
                self._stats['failed_verifications'] += 1
            
            # 更新置信度统计
            if confidence >= 0.7:
                self._stats['high_confidence'] += 1
            elif confidence >= 0.4:
                self._stats['medium_confidence'] += 1
            else:
                self._stats['low_confidence'] += 1
            
            self.logger.info(f"项目 {project.name} 社交媒体验证完成，置信度: {confidence:.2f}")
            
            return result
        
        except Exception as e:
            self.logger.error(f"验证项目 {project.name} 社交媒体时出错: {str(e)}")
            self._stats['failed_verifications'] += 1
            self._stats['low_confidence'] += 1
            
            return {
                'verified': False,
                'confidence': 0.0,
                'error': str(e),
                'warnings': ["验证过程中出错"]
            }
    
    def _verify_channel(self, platform: str, url: str) -> Dict[str, Any]:
        """
        验证社交媒体渠道
        
        Args:
            platform: 平台名称
            url: 渠道URL
            
        Returns:
            渠道验证结果
        """
        self.logger.debug(f"验证 {platform} 渠道: {url}")
        
        # 初始化结果
        result = {
            'verified': False,
            'url': url,
            'followers': None,
            'creation_date': None,
            'post_count': None,
            'has_suspicious_content': False,
            'warnings': []
        }
        
        try:
            # 根据平台选择验证方法
            if platform == 'twitter':
                self._verify_twitter(url, result)
            elif platform == 'telegram':
                self._verify_telegram(url, result)
            elif platform == 'discord':
                self._verify_discord(url, result)
            elif platform == 'medium':
                self._verify_medium(url, result)
            elif platform == 'github':
                self._verify_github(url, result)
            else:
                result['warnings'].append(f"不支持的平台: {platform}")
                return result
            
            # 检查粉丝数量
            min_followers = self.min_followers.get(platform, 0)
            if result['followers'] is not None and result['followers'] < min_followers:
                result['warnings'].append(f"粉丝数量较少 ({result['followers']} < {min_followers})")
            
            # 检查帖子数量
            min_posts = self.min_post_count.get(platform, 0)
            if result['post_count'] is not None and result['post_count'] < min_posts:
                result['warnings'].append(f"帖子数量较少 ({result['post_count']} < {min_posts})")
            
            # 检查创建时间
            if result['creation_date'] is not None:
                account_age_days = (time.time() - result['creation_date']) / (24 * 3600)
                if account_age_days < self.min_activity_age:
                    result['warnings'].append(f"账号创建时间较短 ({account_age_days:.1f} 天 < {self.min_activity_age} 天)")
            
            # 确定验证结果
            if not result['has_suspicious_content'] and not result['warnings']:
                result['verified'] = True
            
            return result
        
        except Exception as e:
            self.logger.error(f"验证 {platform} 渠道 {url} 时出错: {str(e)}")
            result['warnings'].append(f"验证过程中出错: {str(e)}")
            return result
    
    def _verify_twitter(self, url: str, result: Dict[str, Any]) -> None:
        """
        验证Twitter渠道
        
        Args:
            url: Twitter URL
            result: 结果字典，将直接修改
        """
        try:
            # 使用Nitter作为替代
            username = re.search(r'twitter\.com/([^/\s?]+)', url)
            if not username:
                result['warnings'].append("无效的Twitter URL")
                return
            
            username = username.group(1)
            nitter_url = f"https://nitter.net/{username}"
            
            # 发送请求
            response = requests.get(nitter_url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取粉丝数量
            followers_elem = soup.select_one('.followers .profile-stat-num')
            if followers_elem:
                followers_text = followers_elem.get_text(strip=True)
                
                # 处理数字格式
                if 'K' in followers_text:
                    result['followers'] = int(float(followers_text.replace('K', '')) * 1000)
                elif 'M' in followers_text:
                    result['followers'] = int(float(followers_text.replace('M', '')) * 1000000)
                else:
                    result['followers'] = int(followers_text.replace(',', ''))
            
            # 提取创建时间
            joined_elem = soup.select_one('.profile-joindate')
            if joined_elem:
                joined_text = joined_elem.get_text(strip=True)
                joined_match = re.search(r'Joined\s+(\w+)\s+(\d{4})', joined_text)
                
                if joined_match:
                    month = joined_match.group(1)
                    year = joined_match.group(2)
                    
                    # 简单估算创建时间
                    from datetime import datetime
                    month_num = {
                        'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                        'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
                    }.get(month[:3], 1)
                    
                    creation_date = datetime(int(year), month_num, 1).timestamp()
                    result['creation_date'] = creation_date
            
            # 提取帖子数量
            tweets_elem = soup.select_one('.tweets .profile-stat-num')
            if tweets_elem:
                tweets_text = tweets_elem.get_text(strip=True)
                
                # 处理数字格式
                if 'K' in tweets_text:
                    result['post_count'] = int(float(tweets_text.replace('K', '')) * 1000)
                elif 'M' in tweets_text:
                    result['post_count'] = int(float(tweets_text.replace('M', '')) * 1000000)
                else:
                    result['post_count'] = int(tweets_text.replace(',', ''))
            
            # 检查可疑内容
            timeline_items = soup.select('.timeline-item')
            for item in timeline_items[:10]:  # 检查最近10条推文
                tweet_text = item.select_one('.tweet-content')
                if tweet_text:
                    text = tweet_text.get_text(strip=True)
                    if self._has_suspicious_content(text):
                        result['has_suspicious_content'] = True
                        break
        
        except Exception as e:
            self.logger.error(f"验证Twitter渠道 {url} 时出错: {str(e)}")
            result['warnings'].append(f"验证过程中出错: {str(e)}")
    
    def _verify_telegram(self, url: str, result: Dict[str, Any]) -> None:
        """
        验证Telegram渠道
        
        Args:
            url: Telegram URL
            result: 结果字典，将直接修改
        """
        try:
            # 发送请求
            response = requests.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取成员数量
            members_elem = soup.select_one('.tgme_page_extra')
            if members_elem:
                members_text = members_elem.get_text(strip=True)
                members_match = re.search(r'(\d+(?:,\d+)*)\s+members', members_text)
                
                if members_match:
                    result['followers'] = int(members_match.group(1).replace(',', ''))
            
            # 提取创建时间（Telegram不直接显示，尝试从第一条消息估算）
            first_message = soup.select_one('.tgme_widget_message_wrap')
            if first_message:
                date_elem = first_message.select_one('.tgme_widget_message_date time')
                if date_elem and date_elem.has_attr('datetime'):
                    from datetime import datetime
                    creation_date = datetime.fromisoformat(date_elem['datetime'].replace('Z', '+00:00')).timestamp()
                    result['creation_date'] = creation_date
            
            # 提取帖子数量（Telegram不直接显示，使用估算）
            messages = soup.select('.tgme_widget_message_wrap')
            result['post_count'] = len(messages)
            
            # 检查可疑内容
            for message in messages[:10]:  # 检查最近10条消息
                text_elem = message.select_one('.tgme_widget_message_text')
                if text_elem:
                    text = text_elem.get_text(strip=True)
                    if self._has_suspicious_content(text):
                        result['has_suspicious_content'] = True
                        break
        
        except Exception as e:
            self.logger.error(f"验证Telegram渠道 {url} 时出错: {str(e)}")
            result['warnings'].append(f"验证过程中出错: {str(e)}")
    
    def _verify_discord(self, url: str, result: Dict[str, Any]) -> None:
        """
        验证Discord渠道
        
        Args:
            url: Discord URL
            result: 结果字典，将直接修改
        """
        try:
            # 发送请求
            response = requests.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取成员数量
            members_elem = soup.select_one('.info-text')
            if members_elem:
                members_text = members_elem.get_text(strip=True)
                members_match = re.search(r'(\d+(?:,\d+)*)\s+Members', members_text)
                
                if members_match:
                    result['followers'] = int(members_match.group(1).replace(',', ''))
            
            # Discord不显示创建时间和帖子数量，设为None
            result['creation_date'] = None
            result['post_count'] = None
            
            # 检查可疑内容（Discord邀请页面通常没有内容）
            result['has_suspicious_content'] = False
        
        except Exception as e:
            self.logger.error(f"验证Discord渠道 {url} 时出错: {str(e)}")
            result['warnings'].append(f"验证过程中出错: {str(e)}")
    
    def _verify_medium(self, url: str, result: Dict[str, Any]) -> None:
        """
        验证Medium渠道
        
        Args:
            url: Medium URL
            result: 结果字典，将直接修改
        """
        try:
            # 发送请求
            response = requests.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取关注者数量
            followers_elem = soup.select_one('a[href$="/followers"]')
            if followers_elem:
                followers_text = followers_elem.get_text(strip=True)
                followers_match = re.search(r'(\d+(?:,\d+)*)', followers_text)
                
                if followers_match:
                    result['followers'] = int(followers_match.group(1).replace(',', ''))
            
            # 提取创建时间（Medium不直接显示，尝试从第一篇文章估算）
            first_article = soup.select_one('article')
            if first_article:
                date_elem = first_article.select_one('time')
                if date_elem and date_elem.has_attr('datetime'):
                    from datetime import datetime
                    creation_date = datetime.fromisoformat(date_elem['datetime'].replace('Z', '+00:00')).timestamp()
                    result['creation_date'] = creation_date
            
            # 提取文章数量
            articles = soup.select('article')
            result['post_count'] = len(articles)
            
            # 检查可疑内容
            for article in articles[:5]:  # 检查最近5篇文章
                title_elem = article.select_one('h2, h3')
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    if self._has_suspicious_content(title):
                        result['has_suspicious_content'] = True
                        break
        
        except Exception as e:
            self.logger.error(f"验证Medium渠道 {url} 时出错: {str(e)}")
            result['warnings'].append(f"验证过程中出错: {str(e)}")
    
    def _verify_github(self, url: str, result: Dict[str, Any]) -> None:
        """
        验证GitHub渠道
        
        Args:
            url: GitHub URL
            result: 结果字典，将直接修改
        """
        try:
            # 发送请求
            response = requests.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取星标数量（作为粉丝数量的替代）
            stars_elem = soup.select_one('a[href$="/stargazers"]')
            if stars_elem:
                stars_text = stars_elem.get_text(strip=True)
                stars_match = re.search(r'(\d+(?:,\d+)*)', stars_text)
                
                if stars_match:
                    result['followers'] = int(stars_match.group(1).replace(',', ''))
            
            # 提取创建时间
            time_elem = soup.select_one('relative-time')
            if time_elem and time_elem.has_attr('datetime'):
                from datetime import datetime
                creation_date = datetime.fromisoformat(time_elem['datetime'].replace('Z', '+00:00')).timestamp()
                result['creation_date'] = creation_date
            
            # 提取提交数量
            commits_elem = soup.select_one('a[href$="/commits"]')
            if commits_elem:
                commits_text = commits_elem.get_text(strip=True)
                commits_match = re.search(r'(\d+(?:,\d+)*)', commits_text)
                
                if commits_match:
                    result['post_count'] = int(commits_match.group(1).replace(',', ''))
            
            # 检查可疑内容
            readme = soup.select_one('#readme')
            if readme:
                text = readme.get_text(strip=True)
                if self._has_suspicious_content(text):
                    result['has_suspicious_content'] = True
        
        except Exception as e:
            self.logger.error(f"验证GitHub渠道 {url} 时出错: {str(e)}")
            result['warnings'].append(f"验证过程中出错: {str(e)}")
    
    def _has_suspicious_content(self, text: str) -> bool:
        """
        检查文本是否包含可疑内容
        
        Args:
            text: 要检查的文本
            
        Returns:
            是否包含可疑内容
        """
        if not text:
            return False
        
        # 检查可疑模式
        for pattern in self.suspicious_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        
        return False
    
    def _calculate_confidence(self, result: Dict[str, Any]) -> float:
        """
        计算验证结果的置信度
        
        Args:
            result: 验证结果
            
        Returns:
            置信度 (0.0-1.0)
        """
        confidence = 0.0
        
        # 基础置信度
        if result['channel_count'] >= 3:
            confidence += 0.3
        elif result['channel_count'] >= 2:
            confidence += 0.2
        elif result['channel_count'] >= 1:
            confidence += 0.1
        
        # 已验证渠道比例
        if result['channel_count'] > 0:
            verified_ratio = result['verified_channels'] / result['channel_count']
            confidence += 0.3 * verified_ratio
        
        # 活动级别
        if result['activity_level'] == 'high':
            confidence += 0.3
        elif result['activity_level'] == 'medium':
            confidence += 0.2
        elif result['activity_level'] == 'low':
            confidence += 0.1
        
        # 可疑内容
        if result['has_suspicious_content']:
            confidence = 0.0
        
        # 警告数量
        warning_penalty = min(0.5, len(result['warnings']) * 0.1)
        confidence = max(0.0, confidence - warning_penalty)
        
        # 限制在0.0-1.0范围内
        return max(0.0, min(1.0, confidence))
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return self._stats.copy()