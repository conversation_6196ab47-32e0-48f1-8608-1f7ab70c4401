"""Proxy Database - 代理数据库"""
import logging
import json
import os
from typing import List, Dict, Optional

class ProxyDatabase:
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.db_path = self.config.get("db_path", "data/proxy_database.json")
        self.proxies: List[Dict] = []
        self._load_database()
    
    def add_proxy(self, proxy: Dict) -> bool:
        """添加代理到数据库"""
        try:
            # 检查是否已存在
            if not self._proxy_exists(proxy):
                proxy["added_at"] = "now"
                proxy["status"] = "active"
                self.proxies.append(proxy)
                self._save_database()
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to add proxy to database: {e}")
            return False
    
    def get_proxies(self, filters: Dict = None) -> List[Dict]:
        """获取代理列表"""
        try:
            if not filters:
                return self.proxies.copy()
            
            # 应用过滤器
            filtered_proxies = []
            for proxy in self.proxies:
                if self._matches_filters(proxy, filters):
                    filtered_proxies.append(proxy)
            
            return filtered_proxies
        except Exception as e:
            self.logger.error(f"Failed to get proxies: {e}")
            return []
    
    def update_proxy_status(self, proxy_id: str, status: str) -> bool:
        """更新代理状态"""
        try:
            for proxy in self.proxies:
                if proxy.get("id") == proxy_id:
                    proxy["status"] = status
                    proxy["updated_at"] = "now"
                    self._save_database()
                    return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to update proxy status: {e}")
            return False
    
    def remove_proxy(self, proxy_id: str) -> bool:
        """从数据库移除代理"""
        try:
            self.proxies = [p for p in self.proxies if p.get("id") != proxy_id]
            self._save_database()
            return True
        except Exception as e:
            self.logger.error(f"Failed to remove proxy: {e}")
            return False
    
    def get_statistics(self) -> Dict:
        """获取数据库统计信息"""
        try:
            total = len(self.proxies)
            active = len([p for p in self.proxies if p.get("status") == "active"])
            inactive = total - active
            
            return {
                "total_proxies": total,
                "active_proxies": active,
                "inactive_proxies": inactive
            }
        except Exception as e:
            self.logger.error(f"Failed to get statistics: {e}")
            return {}
    
    def _proxy_exists(self, proxy: Dict) -> bool:
        """检查代理是否已存在"""
        for existing_proxy in self.proxies:
            if (existing_proxy.get("ip") == proxy.get("ip") and 
                existing_proxy.get("port") == proxy.get("port")):
                return True
        return False
    
    def _matches_filters(self, proxy: Dict, filters: Dict) -> bool:
        """检查代理是否匹配过滤条件"""
        for key, value in filters.items():
            if proxy.get(key) != value:
                return False
        return True
    
    def _load_database(self):
        """从文件加载数据库"""
        try:
            if os.path.exists(self.db_path):
                with open(self.db_path, 'r') as f:
                    data = json.load(f)
                    self.proxies = data.get("proxies", [])
        except Exception as e:
            self.logger.error(f"Failed to load database: {e}")
            self.proxies = []
    
    def _save_database(self):
        """保存数据库到文件"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            with open(self.db_path, 'w') as f:
                json.dump({"proxies": self.proxies}, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save database: {e}")
