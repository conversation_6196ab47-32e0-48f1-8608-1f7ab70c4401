"""
Proxy Model

Database model for proxy management including proxy servers,
authentication, performance metrics, and status tracking.
"""

from datetime import datetime
from typing import Optional
from dataclasses import dataclass
from enum import Enum


class ProxyType(Enum):
    """Proxy type enumeration."""
    HTTP = "http"
    HTTPS = "https"
    SOCKS4 = "socks4"
    SOCKS5 = "socks5"


@dataclass
class ProxyModel:
    """Proxy data model."""
    
    id: Optional[int] = None
    host: str = ""
    port: int = 0
    username: str = ""
    password: str = ""
    proxy_type: ProxyType = ProxyType.HTTP
    country: str = ""
    city: str = ""
    is_active: bool = True
    is_verified: bool = False
    speed_score: float = 0.0
    reliability_score: float = 0.0
    last_checked: Optional[datetime] = None
    created_at: datetime = None
    failure_count: int = 0
    success_count: int = 0
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.created_at is None:
            self.created_at = datetime.utcnow()
    
    @classmethod
    def create_table_sql(cls) -> str:
        """Get SQL for creating proxies table."""
        return """
        CREATE TABLE IF NOT EXISTS proxies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            host TEXT NOT NULL,
            port INTEGER NOT NULL,
            username TEXT,
            password TEXT,
            proxy_type TEXT NOT NULL,
            country TEXT,
            city TEXT,
            is_active BOOLEAN DEFAULT 1,
            is_verified BOOLEAN DEFAULT 0,
            speed_score REAL DEFAULT 0.0,
            reliability_score REAL DEFAULT 0.0,
            last_checked TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            failure_count INTEGER DEFAULT 0,
            success_count INTEGER DEFAULT 0,
            UNIQUE(host, port)
        )
        """
