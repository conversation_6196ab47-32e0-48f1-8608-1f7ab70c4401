"""
社交媒体收集器

该模块实现了从社交媒体收集项目信息的功能。
"""

import logging
import requests
import time
import random
import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup

from discovery.collectors.base_collector import BaseCollector


class SocialCollector(BaseCollector):
    """社交媒体收集器，从社交媒体收集项目信息"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化社交媒体收集器
        
        Args:
            config: 配置字典，包含社交媒体收集器的配置信息
        """
        super().__init__(config)
        self.timeout = config.get('timeout', 10)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
        self.twitter_api_key = config.get('twitter_api_key', '')
        self.twitter_api_secret = config.get('twitter_api_secret', '')
        self.twitter_bearer_token = config.get('twitter_bearer_token', '')
        self.use_twitter_api = config.get('use_twitter_api', False) and bool(self.twitter_bearer_token)
        self.telegram_api_id = config.get('telegram_api_id', 0)
        self.telegram_api_hash = config.get('telegram_api_hash', '')
        self.use_telegram_api = config.get('use_telegram_api', False) and bool(self.telegram_api_id) and bool(self.telegram_api_hash)
        self.discord_token = config.get('discord_token', '')
        self.use_discord_api = config.get('use_discord_api', False) and bool(self.discord_token)
    
    def collect(self, project: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        从社交媒体收集项目信息
        
        Args:
            project: 项目字典
            
        Returns:
            收集到的项目信息字典，如果无法收集则返回None
        """
        # 检查项目是否有社交媒体渠道
        if 'social_channels' not in project or not project['social_channels']:
            return None
        
        social_channels = project['social_channels']
        
        # 收集项目信息
        project_info = {
            'social_channels': []
        }
        
        # 处理每个社交媒体渠道
        for channel in social_channels:
            platform = channel.get('platform')
            url = channel.get('url')
            
            if not platform or not url:
                continue
            
            # 收集社交媒体信息
            channel_info = self._collect_social_channel_info(platform, url)
            
            if channel_info:
                project_info['social_channels'].append(channel_info)
        
        return project_info if project_info['social_channels'] else None
    
    def _collect_social_channel_info(self, platform: str, url: str) -> Optional[Dict[str, Any]]:
        """
        收集社交媒体渠道信息
        
        Args:
            platform: 平台名称
            url: 渠道URL
            
        Returns:
            渠道信息字典，如果无法收集则返回None
        """
        try:
            if platform == 'twitter':
                return self._collect_twitter_info(url)
            elif platform == 'telegram':
                return self._collect_telegram_info(url)
            elif platform == 'discord':
                return self._collect_discord_info(url)
            elif platform == 'medium':
                return self._collect_medium_info(url)
            elif platform == 'github':
                return self._collect_github_info(url)
            else:
                return {
                    'platform': platform,
                    'url': url,
                    'followers': None,
                    'activity_level': None,
                    'verified': False
                }
        
        except Exception as e:
            self.logger.error(f"收集社交媒体渠道 {platform} 信息时出错: {str(e)}")
            return None
    
    def _collect_twitter_info(self, url: str) -> Optional[Dict[str, Any]]:
        """
        收集 Twitter 信息
        
        Args:
            url: Twitter URL
            
        Returns:
            Twitter 信息字典，如果无法收集则返回None
        """
        if self.use_twitter_api:
            return self._collect_twitter_info_via_api(url)
        else:
            return self._collect_twitter_info_via_scraping(url)
    
    def _collect_twitter_info_via_api(self, url: str) -> Optional[Dict[str, Any]]:
        """
        通过 API 收集 Twitter 信息
        
        Args:
            url: Twitter URL
            
        Returns:
            Twitter 信息字典，如果无法收集则返回None
        """
        try:
            import tweepy
            
            # 提取用户名
            username_match = re.search(r'(?:twitter\.com|x\.com)/([^/\s?]+)', url)
            if not username_match:
                return None
            
            username = username_match.group(1)
            
            # 创建 Twitter API 客户端
            client = tweepy.Client(bearer_token=self.twitter_bearer_token)
            
            # 获取用户信息
            user = client.get_user(username=username, user_fields=['public_metrics', 'verified'])
            
            if not user or not user.data:
                return None
            
            user_data = user.data
            
            # 获取关注者数量
            followers = user_data.public_metrics['followers_count'] if hasattr(user_data, 'public_metrics') else None
            
            # 确定活跃度
            activity_level = 'unknown'
            if followers is not None:
                if followers >= 10000:
                    activity_level = 'high'
                elif followers >= 1000:
                    activity_level = 'medium'
                else:
                    activity_level = 'low'
            
            return {
                'platform': 'twitter',
                'url': url,
                'followers': followers,
                'activity_level': activity_level,
                'verified': user_data.verified if hasattr(user_data, 'verified') else False
            }
        
        except ImportError:
            self.logger.warning("无法导入 tweepy 库，将使用网页抓取方式")
            return self._collect_twitter_info_via_scraping(url)
        
        except Exception as e:
            self.logger.error(f"通过 API 收集 Twitter 信息时出错: {str(e)}")
            return None
    
    def _collect_twitter_info_via_scraping(self, url: str) -> Optional[Dict[str, Any]]:
        """
        通过网页抓取收集 Twitter 信息
        
        Args:
            url: Twitter URL
            
        Returns:
            Twitter 信息字典，如果无法收集则返回None
        """
        try:
            # 提取用户名
            username_match = re.search(r'(?:twitter\.com|x\.com)/([^/\s?]+)', url)
            if not username_match:
                return None
            
            username = username_match.group(1)
            
            # 使用 Nitter 作为替代
            nitter_url = f"https://nitter.net/{username}"
            
            # 随机选择一个 User-Agent
            headers = {
                'User-Agent': random.choice(self.user_agents)
            }
            
            # 发送请求
            response = requests.get(nitter_url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取关注者数量
            followers_element = soup.select_one('.followers .profile-stat-num')
            followers = None
            if followers_element:
                followers_text = followers_element.get_text(strip=True)
                
                # 处理数字格式
                if 'K' in followers_text:
                    followers = float(followers_text.replace('K', '')) * 1000
                elif 'M' in followers_text:
                    followers = float(followers_text.replace('M', '')) * 1000000
                else:
                    followers = int(followers_text.replace(',', ''))
            
            # 确定活跃度
            activity_level = 'unknown'
            if followers is not None:
                if followers >= 10000:
                    activity_level = 'high'
                elif followers >= 1000:
                    activity_level = 'medium'
                else:
                    activity_level = 'low'
            
            # 检查是否已验证
            verified = bool(soup.select_one('.verified-icon'))
            
            return {
                'platform': 'twitter',
                'url': url,
                'followers': followers,
                'activity_level': activity_level,
                'verified': verified
            }
        
        except Exception as e:
            self.logger.error(f"通过网页抓取收集 Twitter 信息时出错: {str(e)}")
            return None
    
    def _collect_telegram_info(self, url: str) -> Optional[Dict[str, Any]]:
        """
        收集 Telegram 信息
        
        Args:
            url: Telegram URL
            
        Returns:
            Telegram 信息字典，如果无法收集则返回None
        """
        if self.use_telegram_api:
            return self._collect_telegram_info_via_api(url)
        else:
            return self._collect_telegram_info_via_scraping(url)
    
    def _collect_telegram_info_via_api(self, url: str) -> Optional[Dict[str, Any]]:
        """
        通过 API 收集 Telegram 信息
        
        Args:
            url: Telegram URL
            
        Returns:
            Telegram 信息字典，如果无法收集则返回None
        """
        try:
            from telethon import TelegramClient
            import asyncio
            
            # 提取频道名称
            channel_match = re.search(r't\.me/([^/\s?]+)', url)
            if not channel_match:
                return None
            
            channel = channel_match.group(1)
            
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 定义异步函数
            async def get_channel_info():
                # 创建客户端
                client = TelegramClient('airhunter_session', self.telegram_api_id, self.telegram_api_hash)
                
                try:
                    # 启动客户端
                    await client.start()
                    
                    # 获取频道信息
                    entity = await client.get_entity(channel)
                    
                    # 获取频道参与者数量
                    participants_count = None
                    if hasattr(entity, 'participants_count'):
                        participants_count = entity.participants_count
                    
                    # 确定活跃度
                    activity_level = 'unknown'
                    if participants_count is not None:
                        if participants_count >= 5000:
                            activity_level = 'high'
                        elif participants_count >= 1000:
                            activity_level = 'medium'
                        else:
                            activity_level = 'low'
                    
                    # 检查是否已验证
                    verified = hasattr(entity, 'verified') and entity.verified
                    
                    return {
                        'platform': 'telegram',
                        'url': url,
                        'followers': participants_count,
                        'activity_level': activity_level,
                        'verified': verified
                    }
                
                finally:
                    # 断开客户端
                    await client.disconnect()
            
            # 运行异步函数
            result = loop.run_until_complete(get_channel_info())
            
            # 关闭事件循环
            loop.close()
            
            return result
        
        except ImportError:
            self.logger.warning("无法导入 telethon 库，将使用网页抓取方式")
            return self._collect_telegram_info_via_scraping(url)
        
        except Exception as e:
            self.logger.error(f"通过 API 收集 Telegram 信息时出错: {str(e)}")
            return None
    
    def _collect_telegram_info_via_scraping(self, url: str) -> Optional[Dict[str, Any]]:
        """
        通过网页抓取收集 Telegram 信息
        
        Args:
            url: Telegram URL
            
        Returns:
            Telegram 信息字典，如果无法收集则返回None
        """
        try:
            # 随机选择一个 User-Agent
            headers = {
                'User-Agent': random.choice(self.user_agents)
            }
            
            # 发送请求
            response = requests.get(url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取成员数量
            members_element = soup.select_one('.tgme_page_extra')
            members = None
            if members_element:
                members_text = members_element.get_text(strip=True)
                members_match = re.search(r'(\d+(?:,\d+)*)\s+members', members_text)
                if members_match:
                    members = int(members_match.group(1).replace(',', ''))
            
            # 确定活跃度
            activity_level = 'unknown'
            if members is not None:
                if members >= 5000:
                    activity_level = 'high'
                elif members >= 1000:
                    activity_level = 'medium'
                else:
                    activity_level = 'low'
            
            # 检查是否已验证
            verified = bool(soup.select_one('.tgme_page_title .verified-icon'))
            
            return {
                'platform': 'telegram',
                'url': url,
                'followers': members,
                'activity_level': activity_level,
                'verified': verified
            }
        
        except Exception as e:
            self.logger.error(f"通过网页抓取收集 Telegram 信息时出错: {str(e)}")
            return None
    
    def _collect_discord_info(self, url: str) -> Optional[Dict[str, Any]]:
        """
        收集 Discord 信息
        
        Args:
            url: Discord URL
            
        Returns:
            Discord 信息字典，如果无法收集则返回None
        """
        if self.use_discord_api:
            return self._collect_discord_info_via_api(url)
        else:
            return self._collect_discord_info_via_scraping(url)
    
    def _collect_discord_info_via_api(self, url: str) -> Optional[Dict[str, Any]]:
        """
        通过 API 收集 Discord 信息
        
        Args:
            url: Discord URL
            
        Returns:
            Discord 信息字典，如果无法收集则返回None
        """
        try:
            import discord
            import asyncio
            
            # 提取邀请代码
            invite_match = re.search(r'(?:discord\.gg|discord\.com/invite)/([^/\s?]+)', url)
            if not invite_match:
                return None
            
            invite_code = invite_match.group(1)
            
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 定义异步函数
            async def get_invite_info():
                # 创建客户端
                client = discord.Client(intents=discord.Intents.default())
                
                try:
                    # 登录
                    await client.login(self.discord_token)
                    
                    # 获取邀请信息
                    invite = await client.fetch_invite(invite_code)
                    
                    # 获取服务器成员数量
                    members_count = invite.approximate_member_count if hasattr(invite, 'approximate_member_count') else None
                    
                    # 确定活跃度
                    activity_level = 'unknown'
                    if members_count is not None:
                        if members_count >= 5000:
                            activity_level = 'high'
                        elif members_count >= 1000:
                            activity_level = 'medium'
                        else:
                            activity_level = 'low'
                    
                    # 检查是否已验证
                    verified = hasattr(invite.guild, 'verified') and invite.guild.verified if hasattr(invite, 'guild') else False
                    
                    return {
                        'platform': 'discord',
                        'url': url,
                        'followers': members_count,
                        'activity_level': activity_level,
                        'verified': verified
                    }
                
                finally:
                    # 关闭客户端
                    await client.close()
            
            # 运行异步函数
            result = loop.run_until_complete(get_invite_info())
            
            # 关闭事件循环
            loop.close()
            
            return result
        
        except ImportError:
            self.logger.warning("无法导入 discord.py 库，将使用网页抓取方式")
            return self._collect_discord_info_via_scraping(url)
        
        except Exception as e:
            self.logger.error(f"通过 API 收集 Discord 信息时出错: {str(e)}")
            return None
    
    def _collect_discord_info_via_scraping(self, url: str) -> Optional[Dict[str, Any]]:
        """
        通过网页抓取收集 Discord 信息
        
        Args:
            url: Discord URL
            
        Returns:
            Discord 信息字典，如果无法收集则返回None
        """
        try:
            # 随机选择一个 User-Agent
            headers = {
                'User-Agent': random.choice(self.user_agents)
            }
            
            # 发送请求
            response = requests.get(url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取成员数量
            members_element = soup.select_one('.info-text')
            members = None
            if members_element:
                members_text = members_element.get_text(strip=True)
                members_match = re.search(r'(\d+(?:,\d+)*)\s+Members', members_text)
                if members_match:
                    members = int(members_match.group(1).replace(',', ''))
            
            # 确定活跃度
            activity_level = 'unknown'
            if members is not None:
                if members >= 5000:
                    activity_level = 'high'
                elif members >= 1000:
                    activity_level = 'medium'
                else:
                    activity_level = 'low'
            
            # 检查是否已验证
            verified = bool(soup.select_one('.verified-icon'))
            
            return {
                'platform': 'discord',
                'url': url,
                'followers': members,
                'activity_level': activity_level,
                'verified': verified
            }
        
        except Exception as e:
            self.logger.error(f"通过网页抓取收集 Discord 信息时出错: {str(e)}")
            return None
    
    def _collect_medium_info(self, url: str) -> Optional[Dict[str, Any]]:
        """
        收集 Medium 信息
        
        Args:
            url: Medium URL
            
        Returns:
            Medium 信息字典，如果无法收集则返回None
        """
        try:
            # 随机选择一个 User-Agent
            headers = {
                'User-Agent': random.choice(self.user_agents)
            }
            
            # 发送请求
            response = requests.get(url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取关注者数量
            followers_element = soup.select_one('a[href$="/followers"]')
            followers = None
            if followers_element:
                followers_text = followers_element.get_text(strip=True)
                followers_match = re.search(r'(\d+(?:,\d+)*)', followers_text)
                if followers_match:
                    followers = int(followers_match.group(1).replace(',', ''))
            
            # 确定活跃度
            activity_level = 'unknown'
            if followers is not None:
                if followers >= 1000:
                    activity_level = 'high'
                elif followers >= 100:
                    activity_level = 'medium'
                else:
                    activity_level = 'low'
            
            return {
                'platform': 'medium',
                'url': url,
                'followers': followers,
                'activity_level': activity_level,
                'verified': False  # Medium 没有验证标记
            }
        
        except Exception as e:
            self.logger.error(f"收集 Medium 信息时出错: {str(e)}")
            return None
    
    def _collect_github_info(self, url: str) -> Optional[Dict[str, Any]]:
        """
        收集 GitHub 信息
        
        Args:
            url: GitHub URL
            
        Returns:
            GitHub 信息字典，如果无法收集则返回None
        """
        try:
            # 随机选择一个 User-Agent
            headers = {
                'User-Agent': random.choice(self.user_agents)
            }
            
            # 发送请求
            response = requests.get(url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取星标数量
            stars_element = soup.select_one('a[href$="/stargazers"]')
            stars = None
            if stars_element:
                stars_text = stars_element.get_text(strip=True)
                stars_match = re.search(r'(\d+(?:,\d+)*)', stars_text)
                if stars_match:
                    stars = int(stars_match.group(1).replace(',', ''))
            
            # 确定活跃度
            activity_level = 'unknown'
            if stars is not None:
                if stars >= 1000:
                    activity_level = 'high'
                elif stars >= 100:
                    activity_level = 'medium'
                else:
                    activity_level = 'low'
            
            return {
                'platform': 'github',
                'url': url,
                'followers': stars,
                'activity_level': activity_level,
                'verified': False  # GitHub 没有验证标记
            }
        
        except Exception as e:
            self.logger.error(f"收集 GitHub 信息时出错: {str(e)}")
            return None