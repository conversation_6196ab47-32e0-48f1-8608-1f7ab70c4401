"""
任务编排器

该模块提供任务编排功能，负责协调多个智能体之间的任务执行。
"""

import logging
import threading
import time
import uuid
from typing import Dict, List, Any, Callable, Optional, Set, Tuple

from coordinator.core.agent_registry import AgentRegistry
from coordinator.communication.message_broker import MessageBroker
from coordinator.communication.command_dispatcher import CommandDispatcher
from coordinator.resources.resource_allocator import ResourceAllocator


class TaskOrchestrator:
    """任务编排器，负责协调多个智能体之间的任务执行"""
    
    def __init__(self, agent_registry: AgentRegistry, message_broker: MessageBroker,
                command_dispatcher: CommandDispatcher, resource_allocator: ResourceAllocator):
        """
        初始化任务编排器
        
        Args:
            agent_registry: 智能体注册表实例
            message_broker: 消息代理实例
            command_dispatcher: 命令分发器实例
            resource_allocator: 资源分配器实例
        """
        self.logger = logging.getLogger(__name__)
        self.agent_registry = agent_registry
        self.message_broker = message_broker
        self.command_dispatcher = command_dispatcher
        self.resource_allocator = resource_allocator
        self._lock = threading.RLock()
        self._tasks: Dict[str, Dict[str, Any]] = {}  # task_id -> task_info
        self._task_types: Dict[str, Dict[str, Any]] = {}  # task_type -> task_definition
    
    def register_task_type(self, task_type: str, agent_requirements: List[str],
                         resource_requirements: Dict[str, Any] = None,
                         description: str = "") -> None:
        """
        注册任务类型
        
        Args:
            task_type: 任务类型
            agent_requirements: 所需的智能体列表
            resource_requirements: 所需的资源
            description: 任务描述
        """
        with self._lock:
            if task_type in self._task_types:
                self.logger.warning(f"任务类型 '{task_type}' 已存在，将被覆盖")
            
            self._task_types[task_type] = {
                "type": task_type,
                "description": description,
                "agent_requirements": agent_requirements,
                "resource_requirements": resource_requirements or {},
                "created_at": time.time()
            }
            
            self.logger.info(f"已注册任务类型 '{task_type}'")
    
    def unregister_task_type(self, task_type: str) -> bool:
        """
        注销任务类型
        
        Args:
            task_type: 任务类型
            
        Returns:
            如果成功注销则返回True，否则返回False
        """
        with self._lock:
            if task_type not in self._task_types:
                self.logger.warning(f"任务类型 '{task_type}' 不存在")
                return False
            
            del self._task_types[task_type]
            self.logger.info(f"已注销任务类型 '{task_type}'")
            return True
    
    def create_task(self, task_type: str, params: Dict[str, Any] = None,
                  priority: int = 0, timeout: float = 3600.0) -> str:
        """
        创建任务
        
        Args:
            task_type: 任务类型
            params: 任务参数
            priority: 任务优先级（越高越优先）
            timeout: 任务超时时间（秒）
            
        Returns:
            任务ID
            
        Raises:
            ValueError: 如果任务类型不存在
        """
        # 检查任务类型是否存在
        with self._lock:
            if task_type not in self._task_types:
                raise ValueError(f"任务类型 '{task_type}' 不存在")
            
            task_definition = self._task_types[task_type]
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建任务信息
        task_info = {
            "id": task_id,
            "type": task_type,
            "params": params or {},
            "priority": priority,
            "timeout": timeout,
            "status": "pending",
            "created_at": time.time(),
            "started_at": None,
            "completed_at": None,
            "agent_assignments": {},
            "resource_allocations": {},
            "progress": 0.0,
            "result": None,
            "error": None
        }
        
        # 注册任务
        with self._lock:
            self._tasks[task_id] = task_info
        
        self.logger.info(f"已创建任务 '{task_id}' (类型: {task_type})")
        return task_id
    
    def start_task(self, task_id: str) -> bool:
        """
        启动任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            如果成功启动则返回True，否则返回False
        """
        # 获取任务信息
        with self._lock:
            if task_id not in self._tasks:
                self.logger.warning(f"任务 '{task_id}' 不存在")
                return False
            
            task_info = self._tasks[task_id]
            
            if task_info["status"] != "pending":
                self.logger.warning(f"任务 '{task_id}' 不是待处理状态")
                return False
            
            # 获取任务类型定义
            task_type = task_info["type"]
            if task_type not in self._task_types:
                self.logger.error(f"任务 '{task_id}' 的类型 '{task_type}' 不存在")
                task_info["status"] = "failed"
                task_info["error"] = f"任务类型 '{task_type}' 不存在"
                return False
            
            task_definition = self._task_types[task_type]
        
        # 分配智能体
        agent_assignments = self._assign_agents(task_id, task_definition["agent_requirements"])
        if not agent_assignments:
            self.logger.error(f"无法为任务 '{task_id}' 分配所需的智能体")
            with self._lock:
                task_info = self._tasks[task_id]
                task_info["status"] = "failed"
                task_info["error"] = "无法分配所需的智能体"
            return False
        
        # 分配资源
        resource_allocations = self._allocate_resources(task_id, task_definition["resource_requirements"])
        if not resource_allocations:
            self.logger.error(f"无法为任务 '{task_id}' 分配所需的资源")
            with self._lock:
                task_info = self._tasks[task_id]
                task_info["status"] = "failed"
                task_info["error"] = "无法分配所需的资源"
            return False
        
        # 更新任务信息
        with self._lock:
            task_info = self._tasks[task_id]
            task_info["status"] = "running"
            task_info["started_at"] = time.time()
            task_info["agent_assignments"] = agent_assignments
            task_info["resource_allocations"] = resource_allocations
        
        # 创建并启动任务线程
        thread = threading.Thread(
            target=self._execute_task,
            args=(task_id,),
            name=f"Task-{task_id}",
            daemon=True
        )
        thread.start()
        
        self.logger.info(f"已启动任务 '{task_id}'")
        return True
    
    def _assign_agents(self, task_id: str, agent_requirements: List[str]) -> Dict[str, str]:
        """
        为任务分配智能体
        
        Args:
            task_id: 任务ID
            agent_requirements: 所需的智能体列表
            
        Returns:
            智能体分配字典，键为需求名称，值为智能体名称
        """
        assignments = {}
        
        for requirement in agent_requirements:
            # 这里简化处理，直接使用需求名称作为智能体名称
            # 在实际实现中，可能需要更复杂的匹配逻辑
            agent_name = requirement
            
            # 检查智能体是否存在
            if not self.agent_registry.exists(agent_name):
                self.logger.error(f"智能体 '{agent_name}' 不存在")
                return {}
            
            assignments[requirement] = agent_name
        
        return assignments
    
    def _allocate_resources(self, task_id: str, resource_requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        为任务分配资源
        
        Args:
            task_id: 任务ID
            resource_requirements: 所需的资源
            
        Returns:
            资源分配字典
        """
        # 请求资源分配
        try:
            allocations = self.resource_allocator.allocate(task_id, resource_requirements)
            return allocations
        except Exception as e:
            self.logger.error(f"为任务 '{task_id}' 分配资源时出错: {str(e)}")
            return {}
    
    def _execute_task(self, task_id: str) -> None:
        """
        执行任务
        
        Args:
            task_id: 任务ID
        """
        # 获取任务信息
        with self._lock:
            if task_id not in self._tasks:
                self.logger.error(f"任务 '{task_id}' 不存在")
                return
            
            task_info = self._tasks[task_id]
            task_type = task_info["type"]
            params = task_info["params"]
            agent_assignments = task_info["agent_assignments"]
            timeout = task_info["timeout"]
        
        try:
            # 设置超时定时器
            timer = threading.Timer(timeout, self._handle_task_timeout, args=(task_id,))
            timer.daemon = True
            timer.start()
            
            # 执行任务
            result = self._execute_task_steps(task_id, task_type, params, agent_assignments)
            
            # 取消超时定时器
            timer.cancel()
            
            # 更新任务信息
            with self._lock:
                if task_id in self._tasks:
                    task_info = self._tasks[task_id]
                    if task_info["status"] == "running":  # 确保任务没有被取消或超时
                        task_info["status"] = "completed"
                        task_info["completed_at"] = time.time()
                        task_info["progress"] = 100.0
                        task_info["result"] = result
            
            self.logger.info(f"任务 '{task_id}' 已完成")
            
            # 释放资源
            self._release_resources(task_id)
        
        except Exception as e:
            # 取消超时定时器
            timer.cancel()
            
            # 更新任务信息
            with self._lock:
                if task_id in self._tasks:
                    task_info = self._tasks[task_id]
                    task_info["status"] = "failed"
                    task_info["completed_at"] = time.time()
                    task_info["error"] = str(e)
            
            self.logger.error(f"执行任务 '{task_id}' 时出错: {str(e)}")
            
            # 释放资源
            self._release_resources(task_id)
    
    def _execute_task_steps(self, task_id: str, task_type: str, params: Dict[str, Any],
                          agent_assignments: Dict[str, str]) -> Any:
        """
        执行任务步骤
        
        Args:
            task_id: 任务ID
            task_type: 任务类型
            params: 任务参数
            agent_assignments: 智能体分配
            
        Returns:
            任务执行结果
        """
        # 这里应该根据任务类型执行不同的步骤
        # 这只是一个简化的示例
        
        # 假设我们有一个简单的任务流程：
        # 1. 向第一个智能体发送命令
        # 2. 处理结果
        # 3. 向第二个智能体发送命令
        # 4. 返回最终结果
        
        result = None
        
        # 获取分配的智能体
        agents = list(agent_assignments.values())
        
        if not agents:
            raise ValueError("没有分配智能体")
        
        # 更新进度
        self._update_task_progress(task_id, 10.0)
        
        # 向第一个智能体发送命令
        if len(agents) > 0:
            first_agent = agents[0]
            first_result = self.command_dispatcher.send_command(
                first_agent,
                f"{task_type}_step1",
                params
            )
            
            if first_result is None:
                raise ValueError(f"智能体 '{first_agent}' 未能处理命令")
            
            result = first_result
        
        # 更新进度
        self._update_task_progress(task_id, 50.0)
        
        # 向第二个智能体发送命令
        if len(agents) > 1:
            second_agent = agents[1]
            second_params = {**params, "previous_result": result}
            second_result = self.command_dispatcher.send_command(
                second_agent,
                f"{task_type}_step2",
                second_params
            )
            
            if second_result is None:
                raise ValueError(f"智能体 '{second_agent}' 未能处理命令")
            
            result = second_result
        
        # 更新进度
        self._update_task_progress(task_id, 90.0)
        
        # 在实际实现中，这里应该有更复杂的任务执行逻辑
        
        return result
    
    def _update_task_progress(self, task_id: str, progress: float) -> None:
        """
        更新任务进度
        
        Args:
            task_id: 任务ID
            progress: 进度百分比（0-100）
        """
        with self._lock:
            if task_id in self._tasks:
                task_info = self._tasks[task_id]
                task_info["progress"] = progress
    
    def _handle_task_timeout(self, task_id: str) -> None:
        """
        处理任务超时
        
        Args:
            task_id: 任务ID
        """
        with self._lock:
            if task_id not in self._tasks:
                return
            
            task_info = self._tasks[task_id]
            if task_info["status"] != "running":
                return
            
            task_info["status"] = "timeout"
            task_info["completed_at"] = time.time()
            task_info["error"] = "任务超时"
        
        self.logger.warning(f"任务 '{task_id}' 超时")
        
        # 释放资源
        self._release_resources(task_id)
    
    def _release_resources(self, task_id: str) -> None:
        """
        释放任务分配的资源
        
        Args:
            task_id: 任务ID
        """
        try:
            self.resource_allocator.release(task_id)
        except Exception as e:
            self.logger.error(f"释放任务 '{task_id}' 的资源时出错: {str(e)}")
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            如果成功取消则返回True，否则返回False
        """
        with self._lock:
            if task_id not in self._tasks:
                self.logger.warning(f"任务 '{task_id}' 不存在")
                return False
            
            task_info = self._tasks[task_id]
            
            if task_info["status"] not in ["pending", "running"]:
                self.logger.warning(f"任务 '{task_id}' 不能被取消，当前状态: {task_info['status']}")
                return False
            
            task_info["status"] = "cancelled"
            task_info["completed_at"] = time.time()
        
        self.logger.info(f"已取消任务 '{task_id}'")
        
        # 释放资源
        self._release_resources(task_id)
        
        return True
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务信息，如果不存在则返回None
        """
        with self._lock:
            return self._tasks.get(task_id)
    
    def get_all_tasks(self, status: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        获取所有任务
        
        Args:
            status: 可选的状态过滤器
            
        Returns:
            任务字典，键为任务ID，值为任务信息
        """
        with self._lock:
            if status:
                return {k: v for k, v in self._tasks.items() if v["status"] == status}
            else:
                return self._tasks.copy()
    
    def get_task_type(self, task_type: str) -> Optional[Dict[str, Any]]:
        """
        获取任务类型定义
        
        Args:
            task_type: 任务类型
            
        Returns:
            任务类型定义，如果不存在则返回None
        """
        with self._lock:
            return self._task_types.get(task_type)
    
    def get_all_task_types(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有任务类型
        
        Returns:
            任务类型字典，键为任务类型，值为任务类型定义
        """
        with self._lock:
            return self._task_types.copy()
    
    def cleanup_tasks(self, max_age: float = 3600.0) -> int:
        """
        清理已完成的任务
        
        Args:
            max_age: 最大任务年龄（秒）
            
        Returns:
            清理的任务数量
        """
        now = time.time()
        to_remove = []
        
        with self._lock:
            for task_id, task_info in self._tasks.items():
                if task_info["status"] in ["completed", "failed", "cancelled", "timeout"]:
                    if now - task_info["completed_at"] > max_age:
                        to_remove.append(task_id)
            
            for task_id in to_remove:
                del self._tasks[task_id]
        
        if to_remove:
            self.logger.info(f"已清理 {len(to_remove)} 个已完成的任务")
        
        return len(to_remove)