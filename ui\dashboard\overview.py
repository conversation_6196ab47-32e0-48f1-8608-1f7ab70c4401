"""
Overview Panel

Main dashboard overview panel showing system status,
key metrics, and recent activity.
"""

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
        QLabel, QFrame, QPushButton, QProgressBar
    )
    from PyQt6.QtCore import Qt, QTimer
    from PyQt6.QtGui import QFont, QPalette
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    class QWidget:
        pass


class OverviewPanel(QWidget if PYQT_AVAILABLE else object):
    """
    Overview dashboard panel.
    
    Displays system status, key metrics, and provides
    quick access to main functions.
    """
    
    def __init__(self, parent=None):
        """Initialize overview panel."""
        if not PYQT_AVAILABLE:
            return
            
        super().__init__(parent)
        self._init_ui()
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(5000)  # Update every 5 seconds
    
    def _init_ui(self):
        """Initialize user interface."""
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("System Overview")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # Metrics grid
        metrics_layout = QGridLayout()
        
        # System status
        self.system_status_frame = self._create_metric_frame("System Status", "Stopped", "red")
        metrics_layout.addWidget(self.system_status_frame, 0, 0)
        
        # Active projects
        self.projects_frame = self._create_metric_frame("Active Projects", "0", "blue")
        metrics_layout.addWidget(self.projects_frame, 0, 1)
        
        # Running agents
        self.agents_frame = self._create_metric_frame("Running Agents", "0/10", "green")
        metrics_layout.addWidget(self.agents_frame, 0, 2)
        
        # Pending tasks
        self.tasks_frame = self._create_metric_frame("Pending Tasks", "0", "orange")
        metrics_layout.addWidget(self.tasks_frame, 1, 0)
        
        # Proxy status
        self.proxy_frame = self._create_metric_frame("Proxy Status", "Disconnected", "red")
        metrics_layout.addWidget(self.proxy_frame, 1, 1)
        
        # Success rate
        self.success_frame = self._create_metric_frame("Success Rate", "0%", "purple")
        metrics_layout.addWidget(self.success_frame, 1, 2)
        
        layout.addLayout(metrics_layout)
        
        # Quick actions
        actions_layout = QHBoxLayout()
        
        self.start_button = QPushButton("Start System")
        self.start_button.clicked.connect(self._start_system)
        actions_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("Stop System")
        self.stop_button.clicked.connect(self._stop_system)
        actions_layout.addWidget(self.stop_button)
        
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.refresh_data)
        actions_layout.addWidget(self.refresh_button)
        
        layout.addLayout(actions_layout)
        
        # Progress indicators
        self.progress_layout = QVBoxLayout()
        layout.addLayout(self.progress_layout)
        
        layout.addStretch()
    
    def _create_metric_frame(self, title: str, value: str, color: str) -> QFrame:
        """Create a metric display frame."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box)
        frame.setLineWidth(1)
        
        layout = QVBoxLayout(frame)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet(f"color: {color};")
        layout.addWidget(value_label)
        
        # Store reference to value label for updates
        frame.value_label = value_label
        
        return frame
    
    def update_status(self, status_data: dict):
        """Update status display with new data."""
        if not PYQT_AVAILABLE:
            return
            
        # Update system status
        if status_data.get('system_health') == 'good':
            self.system_status_frame.value_label.setText("Running")
            self.system_status_frame.value_label.setStyleSheet("color: green;")
        else:
            self.system_status_frame.value_label.setText("Issues")
            self.system_status_frame.value_label.setStyleSheet("color: red;")
        
        # Update other metrics
        self.projects_frame.value_label.setText(str(status_data.get('active_projects', 0)))
        
        agents_running = status_data.get('agents_running', 0)
        agents_total = status_data.get('agents_total', 0)
        self.agents_frame.value_label.setText(f"{agents_running}/{agents_total}")
        
        self.tasks_frame.value_label.setText(str(status_data.get('pending_tasks', 0)))
        
        proxy_status = status_data.get('proxy_status', 'disconnected')
        self.proxy_frame.value_label.setText(proxy_status.title())
        if proxy_status == 'connected':
            self.proxy_frame.value_label.setStyleSheet("color: green;")
        else:
            self.proxy_frame.value_label.setStyleSheet("color: red;")
    
    def refresh_data(self):
        """Refresh panel data."""
        # This would typically fetch data from the application controller
        pass
    
    def _start_system(self):
        """Start system button handler."""
        # This would trigger system start
        pass
    
    def _stop_system(self):
        """Stop system button handler."""
        # This would trigger system stop
        pass
