"""
Fund Management Agent Main Module

This is the main entry point for the Fund Management Agent.
"""

import os
import sys
import json
import logging
import argparse
from typing import Dict, Any, Optional

# Add parent directory to path to allow imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fund_management.fund_management_agent import FundManagementAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("data/logs/fund_management.log")
    ]
)
logger = logging.getLogger("FundManagementMain")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Fund Management Agent")
    parser.add_argument("--config", type=str, default="config/fund_management_config.json",
                        help="Path to configuration file")
    parser.add_argument("--action", type=str, choices=["start", "stop", "status", "create_wallet", "list_wallets"],
                        default="start", help="Action to perform")
    parser.add_argument("--chain", type=str, help="Blockchain for wallet operations")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from file."""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        else:
            logger.warning(f"Config file not found: {config_path}")
            return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def main():
    """Main entry point for the Fund Management Agent."""
    # Parse arguments
    args = parse_arguments()
    
    # Ensure logs directory exists
    os.makedirs("data/logs", exist_ok=True)
    
    # Load configuration
    config = load_config(args.config)
    
    # Create agent
    agent = FundManagementAgent(args.config if os.path.exists(args.config) else None)
    
    # Perform requested action
    if args.action == "start":
        logger.info("Starting Fund Management Agent")
        success = agent.start()
        logger.info(f"Agent started: {success}")
    
    elif args.action == "stop":
        logger.info("Stopping Fund Management Agent")
        success = agent.stop()
        logger.info(f"Agent stopped: {success}")
    
    elif args.action == "status":
        status = agent.status()
        logger.info(f"Agent status: {status}")
        print(json.dumps(status, indent=2))
    
    elif args.action == "create_wallet":
        if not args.chain:
            logger.error("Chain parameter is required for create_wallet action")
            return
        
        logger.info(f"Creating wallet for chain: {args.chain}")
        wallet = agent.create_wallet(args.chain)
        if wallet:
            logger.info(f"Wallet created: {wallet['id']}")
            print(json.dumps(wallet, indent=2))
        else:
            logger.error("Failed to create wallet")
    
    elif args.action == "list_wallets":
        # This would need to be implemented in the agent
        logger.info("Listing wallets")
        print("Wallet listing not implemented yet")

if __name__ == "__main__":
    main()