"""
资源分配器

该模块提供资源分配功能，负责管理和分配系统资源。
"""

import logging
import threading
import time
from typing import Dict, List, Any, Optional, Set


class ResourceAllocator:
    """资源分配器，负责管理和分配系统资源"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化资源分配器
        
        Args:
            config: 资源配置
        """
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        self._config = config or {}
        
        # 初始化资源池
        self._resources: Dict[str, Dict[str, Any]] = {
            "cpu": {
                "total": self._config.get("cpu_total", 100),
                "available": self._config.get("cpu_total", 100),
                "allocations": {}
            },
            "memory": {
                "total": self._config.get("memory_total", 1024),
                "available": self._config.get("memory_total", 1024),
                "allocations": {}
            },
            "disk": {
                "total": self._config.get("disk_total", 10240),
                "available": self._config.get("disk_total", 10240),
                "allocations": {}
            },
            "network": {
                "total": self._config.get("network_total", 100),
                "available": self._config.get("network_total", 100),
                "allocations": {}
            }
        }
        
        # 添加自定义资源
        custom_resources = self._config.get("custom_resources", {})
        for resource_name, resource_config in custom_resources.items():
            self._resources[resource_name] = {
                "total": resource_config.get("total", 100),
                "available": resource_config.get("total", 100),
                "allocations": {}
            }
        
        # 任务资源分配映射
        self._task_allocations: Dict[str, Dict[str, Any]] = {}
    
    def allocate(self, task_id: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        分配资源
        
        Args:
            task_id: 任务ID
            requirements: 资源需求
            
        Returns:
            资源分配结果
            
        Raises:
            ValueError: 如果资源不足
        """
        with self._lock:
            # 检查资源是否足够
            for resource_name, amount in requirements.items():
                if resource_name not in self._resources:
                    raise ValueError(f"未知的资源类型: {resource_name}")
                
                resource = self._resources[resource_name]
                if resource["available"] < amount:
                    raise ValueError(f"资源不足: {resource_name} (需要: {amount}, 可用: {resource['available']})")
            
            # 分配资源
            allocations = {}
            for resource_name, amount in requirements.items():
                resource = self._resources[resource_name]
                resource["available"] -= amount
                resource["allocations"][task_id] = amount
                allocations[resource_name] = amount
            
            # 记录任务分配
            self._task_allocations[task_id] = {
                "allocations": allocations,
                "timestamp": time.time()
            }
            
            self.logger.info(f"已为任务 '{task_id}' 分配资源: {allocations}")
            return allocations
    
    def release(self, task_id: str) -> None:
        """
        释放资源
        
        Args:
            task_id: 任务ID
        """
        with self._lock:
            if task_id not in self._task_allocations:
                self.logger.warning(f"任务 '{task_id}' 没有分配的资源")
                return
            
            task_allocation = self._task_allocations[task_id]
            allocations = task_allocation["allocations"]
            
            # 释放资源
            for resource_name, amount in allocations.items():
                if resource_name in self._resources:
                    resource = self._resources[resource_name]
                    resource["available"] += amount
                    if task_id in resource["allocations"]:
                        del resource["allocations"][task_id]
            
            # 删除任务分配记录
            del self._task_allocations[task_id]
            
            self.logger.info(f"已释放任务 '{task_id}' 的资源")
    
    def get_resource_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取资源状态
        
        Returns:
            资源状态字典
        """
        with self._lock:
            status = {}
            for resource_name, resource in self._resources.items():
                status[resource_name] = {
                    "total": resource["total"],
                    "available": resource["available"],
                    "used": resource["total"] - resource["available"],
                    "usage_percent": (resource["total"] - resource["available"]) / resource["total"] * 100 if resource["total"] > 0 else 0
                }
            return status
    
    def get_task_allocations(self, task_id: str = None) -> Dict[str, Dict[str, Any]]:
        """
        获取任务资源分配
        
        Args:
            task_id: 可选的任务ID
            
        Returns:
            任务资源分配字典
        """
        with self._lock:
            if task_id:
                return {task_id: self._task_allocations.get(task_id)}
            else:
                return self._task_allocations.copy()
    
    def add_resource(self, resource_name: str, total: float) -> None:
        """
        添加资源
        
        Args:
            resource_name: 资源名称
            total: 资源总量
        """
        with self._lock:
            if resource_name in self._resources:
                self.logger.warning(f"资源 '{resource_name}' 已存在，将被覆盖")
            
            self._resources[resource_name] = {
                "total": total,
                "available": total,
                "allocations": {}
            }
            
            self.logger.info(f"已添加资源 '{resource_name}' (总量: {total})")
    
    def remove_resource(self, resource_name: str) -> bool:
        """
        移除资源
        
        Args:
            resource_name: 资源名称
            
        Returns:
            如果成功移除则返回True，否则返回False
        """
        with self._lock:
            if resource_name not in self._resources:
                self.logger.warning(f"资源 '{resource_name}' 不存在")
                return False
            
            # 检查是否有任务正在使用该资源
            resource = self._resources[resource_name]
            if resource["allocations"]:
                self.logger.warning(f"资源 '{resource_name}' 正在被使用，无法移除")
                return False
            
            del self._resources[resource_name]
            self.logger.info(f"已移除资源 '{resource_name}'")
            return True
    
    def update_resource(self, resource_name: str, total: float) -> bool:
        """
        更新资源总量
        
        Args:
            resource_name: 资源名称
            total: 新的资源总量
            
        Returns:
            如果成功更新则返回True，否则返回False
        """
        with self._lock:
            if resource_name not in self._resources:
                self.logger.warning(f"资源 '{resource_name}' 不存在")
                return False
            
            resource = self._resources[resource_name]
            
            # 计算已分配的资源量
            allocated = resource["total"] - resource["available"]
            
            # 检查新的总量是否足够
            if total < allocated:
                self.logger.warning(f"新的资源总量 {total} 小于已分配的资源量 {allocated}")
                return False
            
            # 更新资源总量和可用量
            resource["available"] = total - allocated
            resource["total"] = total
            
            self.logger.info(f"已更新资源 '{resource_name}' 的总量为 {total}")
            return True
    
    def reserve_resource(self, resource_name: str, amount: float) -> bool:
        """
        预留资源
        
        Args:
            resource_name: 资源名称
            amount: 预留量
            
        Returns:
            如果成功预留则返回True，否则返回False
        """
        with self._lock:
            if resource_name not in self._resources:
                self.logger.warning(f"资源 '{resource_name}' 不存在")
                return False
            
            resource = self._resources[resource_name]
            
            if resource["available"] < amount:
                self.logger.warning(f"资源 '{resource_name}' 不足，无法预留 {amount}")
                return False
            
            # 预留资源
            resource["available"] -= amount
            resource["allocations"]["reserved"] = resource["allocations"].get("reserved", 0) + amount
            
            self.logger.info(f"已预留资源 '{resource_name}' {amount}")
            return True
    
    def release_reserved_resource(self, resource_name: str, amount: float) -> bool:
        """
        释放预留资源
        
        Args:
            resource_name: 资源名称
            amount: 释放量
            
        Returns:
            如果成功释放则返回True，否则返回False
        """
        with self._lock:
            if resource_name not in self._resources:
                self.logger.warning(f"资源 '{resource_name}' 不存在")
                return False
            
            resource = self._resources[resource_name]
            
            reserved = resource["allocations"].get("reserved", 0)
            if reserved < amount:
                self.logger.warning(f"预留的资源 '{resource_name}' 不足，无法释放 {amount}")
                return False
            
            # 释放预留资源
            resource["available"] += amount
            resource["allocations"]["reserved"] -= amount
            
            # 如果预留量为0，删除预留记录
            if resource["allocations"]["reserved"] == 0:
                del resource["allocations"]["reserved"]
            
            self.logger.info(f"已释放预留资源 '{resource_name}' {amount}")
            return True