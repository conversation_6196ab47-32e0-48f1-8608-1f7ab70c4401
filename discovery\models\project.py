"""
项目模型

该模块定义了空投项目的数据模型。
"""

import time
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid


class ProjectStatus(Enum):
    """项目状态枚举"""
    NEW = "new"                  # 新发现的项目
    ANALYZING = "analyzing"      # 正在分析中
    VERIFIED = "verified"        # 已验证
    SUSPICIOUS = "suspicious"    # 可疑的
    SCAM = "scam"                # 诈骗项目
    ACTIVE = "active"            # 活跃项目
    COMPLETED = "completed"      # 已完成
    CANCELLED = "cancelled"      # 已取消


class ProjectType(Enum):
    """项目类型枚举"""
    AIRDROP = "airdrop"          # 空投项目
    TESTNET = "testnet"          # 测试网项目
    PRESALE = "presale"          # 预售项目
    IDO = "ido"                  # 初始DEX发行
    ICO = "ico"                  # 初始代币发行
    FARMING = "farming"          # 流动性挖矿
    STAKING = "staking"          # 质押项目
    UNKNOWN = "unknown"          # 未知类型


class BlockchainPlatform(Enum):
    """区块链平台枚举"""
    ETHEREUM = "ethereum"        # 以太坊
    BINANCE = "binance"          # 币安智能链
    SOLANA = "solana"            # 索拉纳
    POLYGON = "polygon"          # Polygon
    AVALANCHE = "avalanche"      # Avalanche
    ARBITRUM = "arbitrum"        # Arbitrum
    OPTIMISM = "optimism"        # Optimism
    BASE = "base"                # Base
    COSMOS = "cosmos"            # Cosmos
    POLKADOT = "polkadot"        # Polkadot
    NEAR = "near"                # NEAR
    APTOS = "aptos"              # Aptos
    SUI = "sui"                  # Sui
    OTHER = "other"              # 其他平台


@dataclass
class SocialChannel:
    """社交媒体渠道"""
    platform: str                # 平台名称 (twitter, discord, telegram, etc.)
    url: str                     # 渠道URL
    followers: Optional[int] = None  # 关注者数量
    activity_level: Optional[str] = None  # 活跃度 (high, medium, low)
    verified: bool = False       # 是否已验证
    last_checked: float = field(default_factory=time.time)  # 最后检查时间


@dataclass
class ProjectRequirement:
    """项目参与要求"""
    type: str                    # 要求类型 (social, onchain, kyc, etc.)
    description: str             # 要求描述
    platform: Optional[str] = None  # 平台 (twitter, discord, ethereum, etc.)
    difficulty: Optional[str] = None  # 难度 (easy, medium, hard)
    estimated_time: Optional[int] = None  # 估计完成时间（分钟）
    url: Optional[str] = None    # 相关URL
    mandatory: bool = True       # 是否必须完成


@dataclass
class TokenInfo:
    """代币信息"""
    name: Optional[str] = None   # 代币名称
    symbol: Optional[str] = None  # 代币符号
    contract_address: Optional[str] = None  # 合约地址
    blockchain: Optional[str] = None  # 区块链平台
    total_supply: Optional[float] = None  # 总供应量
    airdrop_amount: Optional[float] = None  # 空投数量
    estimated_value: Optional[float] = None  # 估计价值（USD）
    tokenomics: Optional[Dict[str, Any]] = None  # 代币经济学


@dataclass
class Project:
    """空投项目数据模型"""
    
    # 基本信息
    name: str                    # 项目名称
    description: str             # 项目描述
    url: str                     # 项目官网
    project_type: ProjectType    # 项目类型
    blockchain: BlockchainPlatform  # 区块链平台
    discovery_source: str        # 发现来源
    source_url: str              # 来源URL
    id: str = field(default_factory=lambda: str(uuid.uuid4()))  # 项目唯一ID
    status: ProjectStatus = ProjectStatus.NEW  # 项目状态
    
    # 发现信息
    discovery_time: float = field(default_factory=time.time)  # 发现时间
    
    # 社交媒体
    social_channels: List[SocialChannel] = field(default_factory=list)  # 社交媒体渠道
    
    # 项目时间线
    start_time: Optional[float] = None  # 开始时间
    end_time: Optional[float] = None    # 结束时间
    
    # 参与要求
    requirements: List[ProjectRequirement] = field(default_factory=list)  # 参与要求
    
    # 代币信息
    token_info: Optional[TokenInfo] = None  # 代币信息
    
    # 评估信息
    risk_score: Optional[float] = None  # 风险评分 (0-100)
    potential_reward: Optional[float] = None  # 潜在收益 (0-100)
    community_score: Optional[float] = None  # 社区评分 (0-100)
    team_score: Optional[float] = None  # 团队评分 (0-100)
    
    # 系统信息
    last_updated: float = field(default_factory=time.time)  # 最后更新时间
    update_count: int = 0        # 更新次数
    tags: List[str] = field(default_factory=list)  # 标签
    notes: str = ""              # 备注
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保枚举类型正确
        if isinstance(self.project_type, str):
            self.project_type = ProjectType(self.project_type)
        
        if isinstance(self.blockchain, str):
            self.blockchain = BlockchainPlatform(self.blockchain)
        
        if isinstance(self.status, str):
            self.status = ProjectStatus(self.status)
    
    def update(self) -> None:
        """更新项目信息"""
        self.last_updated = time.time()
        self.update_count += 1
    
    def add_social_channel(self, platform: str, url: str, followers: Optional[int] = None,
                          activity_level: Optional[str] = None, verified: bool = False) -> None:
        """
        添加社交媒体渠道
        
        Args:
            platform: 平台名称
            url: 渠道URL
            followers: 关注者数量
            activity_level: 活跃度
            verified: 是否已验证
        """
        # 检查是否已存在
        for channel in self.social_channels:
            if channel.platform == platform and channel.url == url:
                # 更新现有渠道
                channel.followers = followers
                channel.activity_level = activity_level
                channel.verified = verified
                channel.last_checked = time.time()
                return
        
        # 添加新渠道
        self.social_channels.append(SocialChannel(
            platform=platform,
            url=url,
            followers=followers,
            activity_level=activity_level,
            verified=verified
        ))
        
        self.update()
    
    def add_requirement(self, type: str, description: str, platform: Optional[str] = None,
                       difficulty: Optional[str] = None, estimated_time: Optional[int] = None,
                       url: Optional[str] = None, mandatory: bool = True) -> None:
        """
        添加参与要求
        
        Args:
            type: 要求类型
            description: 要求描述
            platform: 平台
            difficulty: 难度
            estimated_time: 估计完成时间
            url: 相关URL
            mandatory: 是否必须完成
        """
        self.requirements.append(ProjectRequirement(
            type=type,
            description=description,
            platform=platform,
            difficulty=difficulty,
            estimated_time=estimated_time,
            url=url,
            mandatory=mandatory
        ))
        
        self.update()
    
    def set_token_info(self, name: Optional[str] = None, symbol: Optional[str] = None,
                      contract_address: Optional[str] = None, blockchain: Optional[str] = None,
                      total_supply: Optional[float] = None, airdrop_amount: Optional[float] = None,
                      estimated_value: Optional[float] = None, tokenomics: Optional[Dict[str, Any]] = None) -> None:
        """
        设置代币信息
        
        Args:
            name: 代币名称
            symbol: 代币符号
            contract_address: 合约地址
            blockchain: 区块链平台
            total_supply: 总供应量
            airdrop_amount: 空投数量
            estimated_value: 估计价值
            tokenomics: 代币经济学
        """
        if self.token_info is None:
            self.token_info = TokenInfo()
        
        if name is not None:
            self.token_info.name = name
        
        if symbol is not None:
            self.token_info.symbol = symbol
        
        if contract_address is not None:
            self.token_info.contract_address = contract_address
        
        if blockchain is not None:
            self.token_info.blockchain = blockchain
        
        if total_supply is not None:
            self.token_info.total_supply = total_supply
        
        if airdrop_amount is not None:
            self.token_info.airdrop_amount = airdrop_amount
        
        if estimated_value is not None:
            self.token_info.estimated_value = estimated_value
        
        if tokenomics is not None:
            self.token_info.tokenomics = tokenomics
        
        self.update()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            项目字典
        """
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "url": self.url,
            "project_type": self.project_type.value,
            "blockchain": self.blockchain.value,
            "status": self.status.value,
            "discovery_time": self.discovery_time,
            "discovery_source": self.discovery_source,
            "source_url": self.source_url,
            "social_channels": [
                {
                    "platform": channel.platform,
                    "url": channel.url,
                    "followers": channel.followers,
                    "activity_level": channel.activity_level,
                    "verified": channel.verified,
                    "last_checked": channel.last_checked
                }
                for channel in self.social_channels
            ],
            "start_time": self.start_time,
            "end_time": self.end_time,
            "requirements": [
                {
                    "type": req.type,
                    "description": req.description,
                    "platform": req.platform,
                    "difficulty": req.difficulty,
                    "estimated_time": req.estimated_time,
                    "url": req.url,
                    "mandatory": req.mandatory
                }
                for req in self.requirements
            ],
            "token_info": None if self.token_info is None else {
                "name": self.token_info.name,
                "symbol": self.token_info.symbol,
                "contract_address": self.token_info.contract_address,
                "blockchain": self.token_info.blockchain,
                "total_supply": self.token_info.total_supply,
                "airdrop_amount": self.token_info.airdrop_amount,
                "estimated_value": self.token_info.estimated_value,
                "tokenomics": self.token_info.tokenomics
            },
            "risk_score": self.risk_score,
            "potential_reward": self.potential_reward,
            "community_score": self.community_score,
            "team_score": self.team_score,
            "last_updated": self.last_updated,
            "update_count": self.update_count,
            "tags": self.tags,
            "notes": self.notes
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Project':
        """
        从字典创建项目
        
        Args:
            data: 项目字典
            
        Returns:
            项目对象
        """
        # 创建基本项目
        project = cls(
            name=data["name"],
            description=data["description"],
            url=data["url"],
            project_type=ProjectType(data["project_type"]),
            blockchain=BlockchainPlatform(data["blockchain"]),
            discovery_source=data["discovery_source"],
            source_url=data["source_url"],
            id=data.get("id", str(uuid.uuid4())),
            status=ProjectStatus(data["status"])
        )
        
        # 设置发现时间（如果提供）
        if "discovery_time" in data:
            project.discovery_time = data["discovery_time"]
        
        # 设置可选字段
        if "start_time" in data:
            project.start_time = data["start_time"]
        
        if "end_time" in data:
            project.end_time = data["end_time"]
        
        if "risk_score" in data:
            project.risk_score = data["risk_score"]
        
        if "potential_reward" in data:
            project.potential_reward = data["potential_reward"]
        
        if "community_score" in data:
            project.community_score = data["community_score"]
        
        if "team_score" in data:
            project.team_score = data["team_score"]
        
        if "last_updated" in data:
            project.last_updated = data["last_updated"]
        
        if "update_count" in data:
            project.update_count = data["update_count"]
        
        if "tags" in data:
            project.tags = data["tags"]
        
        if "notes" in data:
            project.notes = data["notes"]
        
        # 添加社交媒体渠道
        for channel_data in data.get("social_channels", []):
            project.add_social_channel(
                platform=channel_data["platform"],
                url=channel_data["url"],
                followers=channel_data.get("followers"),
                activity_level=channel_data.get("activity_level"),
                verified=channel_data.get("verified", False)
            )
        
        # 添加参与要求
        for req_data in data.get("requirements", []):
            project.add_requirement(
                type=req_data["type"],
                description=req_data["description"],
                platform=req_data.get("platform"),
                difficulty=req_data.get("difficulty"),
                estimated_time=req_data.get("estimated_time"),
                url=req_data.get("url"),
                mandatory=req_data.get("mandatory", True)
            )
        
        # 设置代币信息
        token_info = data.get("token_info")
        if token_info:
            project.set_token_info(
                name=token_info.get("name"),
                symbol=token_info.get("symbol"),
                contract_address=token_info.get("contract_address"),
                blockchain=token_info.get("blockchain"),
                total_supply=token_info.get("total_supply"),
                airdrop_amount=token_info.get("airdrop_amount"),
                estimated_value=token_info.get("estimated_value"),
                tokenomics=token_info.get("tokenomics")
            )
        
        return project
    
    def to_json(self) -> str:
        """
        转换为JSON字符串
        
        Returns:
            JSON字符串
        """
        return json.dumps(self.to_dict(), indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Project':
        """
        从JSON字符串创建项目
        
        Args:
            json_str: JSON字符串
            
        Returns:
            项目对象
        """
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def __str__(self) -> str:
        """
        获取项目的字符串表示
        
        Returns:
            字符串表示
        """
        return f"Project({self.name}, {self.project_type.value}, {self.status.value})"