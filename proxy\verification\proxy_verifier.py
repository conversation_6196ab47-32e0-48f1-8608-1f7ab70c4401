"""
代理验证器

该模块负责验证代理的可用性和性能。
"""

import logging
import threading
import time
import requests
import concurrent.futures
from typing import Dict, List, Any, Optional, Set, Tuple

from proxy.models.proxy import Proxy


class ProxyVerifier:
    """代理验证器，负责验证代理的可用性和性能"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化代理验证器
        
        Args:
            config: 配置字典，包含代理验证器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        
        # 配置参数
        self.timeout = config.get('timeout', 10)
        self.max_workers = config.get('max_workers', 10)
        self.test_urls = config.get('test_urls', [
            'http://httpbin.org/ip',
            'https://httpbin.org/ip',
            'http://www.google.com',
            'https://www.google.com'
        ])
        self.retry_count = config.get('retry_count', 2)
        self.anonymity_test_url = config.get('anonymity_test_url', 'http://httpbin.org/headers')
        self.country_test_url = config.get('country_test_url', 'http://ip-api.com/json')
    
    def verify_proxy(self, proxy: Proxy) -> bool:
        """
        验证代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            如果代理可用则返回True，否则返回False
        """
        self.logger.debug(f"验证代理: {proxy}")
        
        # 选择适合的测试 URL
        test_urls = self._select_test_urls(proxy.protocol)
        
        if not test_urls:
            self.logger.warning(f"没有适合协议 '{proxy.protocol}' 的测试 URL")
            return False
        
        # 验证代理
        success = False
        response_times = []
        
        for url in test_urls:
            result, response_time = self._test_proxy(proxy, url)
            if result:
                success = True
                if response_time:
                    response_times.append(response_time)
        
        # 更新代理信息
        proxy.is_valid = success
        proxy.last_checked = time.time()
        
        if response_times:
            proxy.response_times = response_times
        
        # 如果代理可用，尝试获取更多信息
        if success:
            self._enrich_proxy_info(proxy)
        
        self.logger.debug(f"代理验证结果: {proxy.host}:{proxy.port} - {'可用' if success else '不可用'}")
        return success
    
    def verify_proxies(self, proxies: List[Proxy]) -> List[Proxy]:
        """
        验证多个代理
        
        Args:
            proxies: 代理对象列表
            
        Returns:
            可用的代理对象列表
        """
        self.logger.info(f"验证 {len(proxies)} 个代理")
        
        valid_proxies = []
        
        # 使用线程池并行验证
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有验证任务
            future_to_proxy = {executor.submit(self.verify_proxy, proxy): proxy for proxy in proxies}
            
            # 获取结果
            for future in concurrent.futures.as_completed(future_to_proxy):
                proxy = future_to_proxy[future]
                try:
                    result = future.result()
                    if result:
                        valid_proxies.append(proxy)
                except Exception as e:
                    self.logger.error(f"验证代理 {proxy.host}:{proxy.port} 时出错: {str(e)}")
        
        self.logger.info(f"验证结果: {len(valid_proxies)}/{len(proxies)} 个代理可用")
        return valid_proxies
    
    def _select_test_urls(self, protocol: str) -> List[str]:
        """
        选择适合的测试 URL
        
        Args:
            protocol: 代理协议
            
        Returns:
            测试 URL 列表
        """
        if protocol in ['http', 'https']:
            return [url for url in self.test_urls if url.startswith(protocol)]
        elif protocol in ['socks4', 'socks5']:
            return self.test_urls
        else:
            return []
    
    def _test_proxy(self, proxy: Proxy, url: str) -> Tuple[bool, Optional[float]]:
        """
        测试代理
        
        Args:
            proxy: 代理对象
            url: 测试 URL
            
        Returns:
            (是否成功, 响应时间)
        """
        # 构建代理字典
        proxies = {
            'http': proxy.url,
            'https': proxy.url
        }
        
        # 重试机制
        for i in range(self.retry_count + 1):
            try:
                start_time = time.time()
                response = requests.get(
                    url,
                    proxies=proxies,
                    timeout=self.timeout,
                    verify=False  # 禁用 SSL 验证
                )
                end_time = time.time()
                
                # 检查响应状态
                if response.status_code == 200:
                    response_time = end_time - start_time
                    return True, response_time
            
            except requests.exceptions.RequestException as e:
                self.logger.debug(f"测试代理 {proxy.host}:{proxy.port} 失败 (尝试 {i+1}/{self.retry_count+1}): {str(e)}")
                
                # 最后一次重试失败
                if i == self.retry_count:
                    return False, None
                
                # 等待一段时间再重试
                time.sleep(1)
        
        return False, None
    
    def _enrich_proxy_info(self, proxy: Proxy) -> None:
        """
        丰富代理信息
        
        Args:
            proxy: 代理对象
        """
        # 如果没有国家信息，尝试获取
        if not proxy.country:
            self._get_country_info(proxy)
        
        # 如果没有匿名级别信息，尝试获取
        if not proxy.anonymity:
            self._get_anonymity_info(proxy)
    
    def _get_country_info(self, proxy: Proxy) -> None:
        """
        获取代理的国家信息
        
        Args:
            proxy: 代理对象
        """
        try:
            # 构建代理字典
            proxies = {
                'http': proxy.url,
                'https': proxy.url
            }
            
            # 发送请求
            response = requests.get(
                self.country_test_url,
                proxies=proxies,
                timeout=self.timeout,
                verify=False  # 禁用 SSL 验证
            )
            
            # 检查响应状态
            if response.status_code == 200:
                data = response.json()
                proxy.country = data.get('country')
                proxy.city = data.get('city')
        
        except Exception as e:
            self.logger.debug(f"获取代理 {proxy.host}:{proxy.port} 的国家信息时出错: {str(e)}")
    
    def _get_anonymity_info(self, proxy: Proxy) -> None:
        """
        获取代理的匿名级别信息
        
        Args:
            proxy: 代理对象
        """
        try:
            # 构建代理字典
            proxies = {
                'http': proxy.url,
                'https': proxy.url
            }
            
            # 发送请求
            response = requests.get(
                self.anonymity_test_url,
                proxies=proxies,
                timeout=self.timeout,
                verify=False  # 禁用 SSL 验证
            )
            
            # 检查响应状态
            if response.status_code == 200:
                data = response.json()
                headers = data.get('headers', {})
                
                # 检查是否暴露了原始 IP
                if 'X-Forwarded-For' in headers or 'Via' in headers:
                    proxy.anonymity = 'transparent'
                elif 'Proxy-Connection' in headers:
                    proxy.anonymity = 'anonymous'
                else:
                    proxy.anonymity = 'elite'
        
        except Exception as e:
            self.logger.debug(f"获取代理 {proxy.host}:{proxy.port} 的匿名级别信息时出错: {str(e)}")
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取配置
        
        Returns:
            配置字典
        """
        return self.config.copy()