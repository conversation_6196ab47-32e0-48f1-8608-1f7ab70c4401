"""
Encryption utilities for AirHunter

This module provides encryption and decryption utilities used throughout the system.
Includes AES encryption, key management, hashing, and secure random number generation.
"""

import os
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

class Encryption:
    """Handle all encryption/decryption operations"""
    
    def __init__(self):
        """Initialize encryption utilities"""
        self.backend = default_backend()
        self._key = None
    
    def generate_key(self):
        """Generate a new encryption key"""
        return Fernet.generate_key()
    
    def set_key(self, key):
        """Set encryption key"""
        self._key = key
        self.fernet = Fernet(key)
    
    def encrypt(self, data: str) -> bytes:
        """
        Encrypt data using the set key
        
        Args:
            data: String data to encrypt
            
        Returns:
            bytes: Encrypted data
        """
        if not self._key:
            raise ValueError("Encryption key not set")
        return self.fernet.encrypt(data.encode())
    
    def decrypt(self, encrypted_data: bytes) -> str:
        """
        Decrypt encrypted data using the set key
        
        Args:
            encrypted_data: Encrypted bytes to decrypt
            
        Returns:
            str: Decrypted data
        """
        if not self._key:
            raise ValueError("Encryption key not set")
        return self.fernet.decrypt(encrypted_data).decode()
    
    @staticmethod
    def generate_hash(data: str) -> str:
        """
        Generate SHA-256 hash of data
        
        Args:
            data: Data to hash
            
        Returns:
            str: Hex digest of hash
        """
        hasher = hashes.Hash(hashes.SHA256(), default_backend())
        hasher.update(data.encode())
        return hasher.finalize().hex()
    
    @staticmethod
    def secure_random(length: int = 32) -> bytes:
        """
        Generate secure random bytes
        
        Args:
            length: Number of bytes to generate
            
        Returns:
            bytes: Secure random bytes
        """
        return os.urandom(length)
    
    def derive_key(self, password: str, salt: bytes = None) -> bytes:
        """
        Derive encryption key from password using PBKDF2
        
        Args:
            password: Password to derive key from
            salt: Optional salt bytes
            
        Returns:
            bytes: Derived key
        """
        if salt is None:
            salt = self.secure_random(16)
            
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=self.backend
        )
        return kdf.derive(password.encode())

# Create global encryption instance
encryptor = Encryption()
