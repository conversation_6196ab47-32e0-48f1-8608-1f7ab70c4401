"""
DNS resolver for network operations

This module provides DNS resolution and caching functionality.
"""

import time
import socket
import logging
import threading
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from collections import defaultdict
import dns.resolver
import dns.exception

@dataclass
class DNSRecord:
    """DNS record data class"""
    ip: str
    ttl: int
    timestamp: float
    record_type: str

class DNSResolver:
    """Manager for DNS resolution"""
    
    def __init__(
        self,
        cache_size: int = 1000,
        default_ttl: int = 300,
        nameservers: List[str] = None
    ):
        """
        Initialize DNS resolver
        
        Args:
            cache_size: Maximum cache size
            default_ttl: Default TTL in seconds
            nameservers: List of DNS servers
        """
        self.cache_size = cache_size
        self.default_ttl = default_ttl
        self.nameservers = nameservers or ['*******', '*******']
        
        self._cache: Dict[str, Dict[str, DNSRecord]] = {}
        self._failed_domains = defaultdict(int)
        self._lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # Create resolver instance
        self._resolver = dns.resolver.Resolver()
        self._resolver.nameservers = [
            socket.inet_aton(ns)[0] for ns in self.nameservers
        ]
        
        # Start cache maintenance thread
        self._maintenance_thread = threading.Thread(target=self._maintain_cache)
        self._maintenance_thread.daemon = True
        self._maintenance_thread.start()
    
    def resolve(
        self,
        domain: str,
        record_type: str = 'A',
        force_refresh: bool = False
    ) -> List[str]:
        """
        Resolve domain name
        
        Args:
            domain: Domain name to resolve
            record_type: DNS record type
            force_refresh: Whether to force cache refresh
            
        Returns:
            List[str]: Resolved IP addresses
        """
        domain = domain.lower()
        
        # Check cache first
        if not force_refresh:
            cached = self._get_cached(domain, record_type)
            if cached:
                return cached
        
        try:
            # Perform DNS query
            answers = self._resolver.resolve(domain, record_type)
            
            # Extract IP addresses
            ips = [str(rdata) for rdata in answers]
            
            # Update cache
            with self._lock:
                if domain not in self._cache:
                    self._cache[domain] = {}
                    
                for ip in ips:
                    self._cache[domain][ip] = DNSRecord(
                        ip=ip,
                        ttl=answers.ttl or self.default_ttl,
                        timestamp=time.time(),
                        record_type=record_type
                    )
                
                # Remove from failed domains if success
                self._failed_domains.pop(domain, None)
            
            return ips
            
        except dns.exception.DNSException as e:
            self.logger.error(f"DNS resolution failed for {domain}: {str(e)}")
            
            # Track failed resolutions
            with self._lock:
                self._failed_domains[domain] += 1
            
            # Return cached results if available
            cached = self._get_cached(domain, record_type)
            if cached:
                return cached
                
            raise
    
    def _get_cached(
        self,
        domain: str,
        record_type: str
    ) -> Optional[List[str]]:
        """
        Get cached DNS records
        
        Args:
            domain: Domain name
            record_type: DNS record type
            
        Returns:
            Optional[List[str]]: Cached IP addresses
        """
        with self._lock:
            if domain not in self._cache:
                return None
                
            current_time = time.time()
            valid_records = []
            
            for ip, record in list(self._cache[domain].items()):
                if record.record_type != record_type:
                    continue
                    
                # Check if record is expired
                if current_time - record.timestamp > record.ttl:
                    del self._cache[domain][ip]
                else:
                    valid_records.append(ip)
            
            # Remove domain if no valid records
            if not self._cache[domain]:
                del self._cache[domain]
                
            return valid_records if valid_records else None
    
    def _maintain_cache(self) -> None:
        """Maintain DNS cache"""
        while True:
            time.sleep(60)  # Run maintenance every minute
            try:
                self._clean_cache()
                self._refresh_frequent()
            except Exception as e:
                self.logger.error(f"Cache maintenance error: {str(e)}")
    
    def _clean_cache(self) -> None:
        """Clean expired cache entries"""
        current_time = time.time()
        
        with self._lock:
            for domain in list(self._cache.keys()):
                for ip, record in list(self._cache[domain].items()):
                    if current_time - record.timestamp > record.ttl:
                        del self._cache[domain][ip]
                
                if not self._cache[domain]:
                    del self._cache[domain]
            
            # Enforce cache size limit
            while len(self._cache) > self.cache_size:
                # Remove oldest entry
                oldest_domain = min(
                    self._cache.keys(),
                    key=lambda d: min(
                        r.timestamp for r in self._cache[d].values()
                    )
                )
                del self._cache[oldest_domain]
    
    def _refresh_frequent(self) -> None:
        """Refresh frequently accessed domains"""
        domains_to_refresh = []
        
        with self._lock:
            current_time = time.time()
            
            for domain, records in self._cache.items():
                # Check if any record is nearing expiration
                for record in records.values():
                    if (record.ttl - (current_time - record.timestamp)) < 60:
                        domains_to_refresh.append(domain)
                        break
        
        # Refresh domains
        for domain in domains_to_refresh:
            try:
                self.resolve(domain, force_refresh=True)
            except Exception as e:
                self.logger.error(f"Failed to refresh {domain}: {str(e)}")
    
    def add_nameserver(self, nameserver: str) -> None:
        """
        Add DNS nameserver
        
        Args:
            nameserver: Nameserver IP address
        """
        if nameserver not in self.nameservers:
            self.nameservers.append(nameserver)
            self._resolver.nameservers = [
                socket.inet_aton(ns)[0] for ns in self.nameservers
            ]
    
    def remove_nameserver(self, nameserver: str) -> None:
        """
        Remove DNS nameserver
        
        Args:
            nameserver: Nameserver IP address
        """
        if nameserver in self.nameservers:
            self.nameservers.remove(nameserver)
            self._resolver.nameservers = [
                socket.inet_aton(ns)[0] for ns in self.nameservers
            ]
    
    def clear_cache(self) -> None:
        """Clear DNS cache"""
        with self._lock:
            self._cache.clear()
            self._failed_domains.clear()
    
    def get_cache_stats(self) -> Dict[str, int]:
        """
        Get cache statistics
        
        Returns:
            Dict[str, int]: Cache statistics
        """
        with self._lock:
            return {
                'total_domains': len(self._cache),
                'total_records': sum(len(records) for records in self._cache.values()),
                'failed_domains': len(self._failed_domains)
            }

# Create global DNS resolver instance
dns_resolver = DNSResolver()
