"""
事件系统

该模块提供事件发布和订阅功能，允许系统组件之间通过事件进行松耦合通信。
"""

import logging
import threading
from typing import Dict, List, Any, Callable, Set, Optional

from coordinator.communication.message_broker import MessageBroker


class EventSystem:
    """事件系统，负责事件的发布和订阅"""
    
    def __init__(self, message_broker: MessageBroker):
        """
        初始化事件系统
        
        Args:
            message_broker: 消息代理实例
        """
        self.logger = logging.getLogger(__name__)
        self.message_broker = message_broker
        self._lock = threading.RLock()
        self._event_topic = "system.events"
        self._subscribers: Dict[str, Set[str]] = {}  # event_type -> set of subscriber_ids
        self._subscriber_id = None
    
    def start(self) -> None:
        """启动事件系统"""
        with self._lock:
            if self._subscriber_id is not None:
                self.logger.warning("事件系统已经在运行")
                return
            
            # 创建事件主题
            self.message_broker.create_topic(self._event_topic)
            
            # 订阅事件主题
            self._subscriber_id = self.message_broker.subscribe(
                self._handle_event,
                self._event_topic
            )
            
            self.logger.info("事件系统已启动")
    
    def stop(self) -> None:
        """停止事件系统"""
        with self._lock:
            if self._subscriber_id is None:
                self.logger.warning("事件系统未在运行")
                return
            
            # 取消订阅事件主题
            self.message_broker.unsubscribe(self._subscriber_id, self._event_topic)
            self._subscriber_id = None
            
            self.logger.info("事件系统已停止")
    
    def subscribe(self, event_type: str, callback: Callable[[Dict[str, Any]], None]) -> str:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数，接收事件数据作为参数
            
        Returns:
            订阅者ID
        """
        with self._lock:
            # 生成订阅者ID
            subscriber_id = f"event_{event_type}_{id(callback)}"
            
            # 如果事件类型不存在，创建它
            if event_type not in self._subscribers:
                self._subscribers[event_type] = set()
            
            # 添加到订阅者列表
            self._subscribers[event_type].add(subscriber_id)
            
            self.logger.info(f"订阅者 '{subscriber_id}' 已订阅事件类型 '{event_type}'")
            return subscriber_id
    
    def unsubscribe(self, subscriber_id: str, event_type: str) -> bool:
        """
        取消订阅事件
        
        Args:
            subscriber_id: 订阅者ID
            event_type: 事件类型
            
        Returns:
            如果成功取消订阅则返回True，否则返回False
        """
        with self._lock:
            if event_type not in self._subscribers:
                self.logger.warning(f"事件类型 '{event_type}' 不存在")
                return False
            
            if subscriber_id not in self._subscribers[event_type]:
                self.logger.warning(f"订阅者 '{subscriber_id}' 未订阅事件类型 '{event_type}'")
                return False
            
            # 从订阅者列表中移除
            self._subscribers[event_type].remove(subscriber_id)
            
            self.logger.info(f"订阅者 '{subscriber_id}' 已取消订阅事件类型 '{event_type}'")
            return True
    
    def publish(self, event_type: str, data: Optional[Dict[str, Any]] = None) -> bool:
        """
        发布事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            
        Returns:
            如果成功发布则返回True，否则返回False
        """
        # 创建事件对象
        event = {
            "type": event_type,
            "data": data or {}
        }
        
        # 发布到消息代理
        result = self.message_broker.publish(self._event_topic, event)
        
        if result:
            self.logger.debug(f"已发布事件类型 '{event_type}'")
        else:
            self.logger.error(f"发布事件类型 '{event_type}' 失败")
        
        return result
    
    def _handle_event(self, topic: str, event: Dict[str, Any]) -> None:
        """
        处理事件
        
        Args:
            topic: 主题名称
            event: 事件对象
        """
        event_type = event.get("type")
        event_data = event.get("data", {})
        
        if not event_type:
            self.logger.warning("收到没有类型的事件")
            return
        
        # 获取订阅者列表的副本
        with self._lock:
            if event_type not in self._subscribers:
                return
            
            subscribers = list(self._subscribers[event_type])
        
        # 调用所有订阅者的回调函数
        for subscriber_id in subscribers:
            try:
                # 这里我们需要找到与subscriber_id关联的回调函数
                # 由于我们没有存储这种映射，这里只是一个示例
                # 在实际实现中，你需要维护一个subscriber_id到callback的映射
                callback = self._find_callback(subscriber_id)
                if callback:
                    callback(event_data)
            except Exception as e:
                self.logger.error(f"调用订阅者 '{subscriber_id}' 的回调函数时出错: {str(e)}")
    
    def _find_callback(self, subscriber_id: str) -> Optional[Callable]:
        """
        查找与订阅者ID关联的回调函数
        
        Args:
            subscriber_id: 订阅者ID
            
        Returns:
            回调函数，如果找不到则返回None
        """
        # 这里只是一个示例，实际实现中需要维护一个映射
        # 在这个简化的实现中，我们无法真正找到回调函数
        return None
    
    def get_event_types(self) -> List[str]:
        """
        获取所有事件类型
        
        Returns:
            事件类型列表
        """
        with self._lock:
            return list(self._subscribers.keys())
    
    def get_subscribers(self, event_type: str) -> List[str]:
        """
        获取事件类型的所有订阅者
        
        Args:
            event_type: 事件类型
            
        Returns:
            订阅者ID列表
        """
        with self._lock:
            if event_type not in self._subscribers:
                return []
            
            return list(self._subscribers[event_type])