"""
Authentication Service

Provides authentication and session management for the AirHunter system.
"""

try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False
    # Fallback JWT implementation
    class jwt:
        @staticmethod
        def encode(payload, secret, algorithm='HS256'):
            import json
            import base64
            # Simple fallback - not secure, just for testing
            return base64.b64encode(json.dumps(payload).encode()).decode()

        @staticmethod
        def decode(token, secret, algorithms=None):
            import json
            import base64
            try:
                return json.loads(base64.b64decode(token).decode())
            except:
                raise Exception("Invalid token")

        class ExpiredSignatureError(Exception):
            pass

        class InvalidTokenError(Exception):
            pass

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from .encryption_service import EncryptionService


class AuthService:
    """Authentication service for user and system authentication."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.secret_key = config.get('secret_key', 'default-secret-key')
        self.token_expiry = config.get('token_expiry_hours', 24)
        self.encryption_service = EncryptionService()

        # User sessions
        self._sessions = {}

        # Statistics
        self._stats = {
            'logins': 0,
            'failed_logins': 0,
            'active_sessions': 0
        }

    def authenticate(self, username: str, password: str) -> Optional[str]:
        """Authenticate user and return token."""
        try:
            # In a real implementation, this would check against a database
            # For now, we'll use a simple hardcoded check
            if username == "admin" and password == "admin123":
                token = self._generate_token(username)
                self._stats['logins'] += 1
                self._stats['active_sessions'] += 1
                return token
            else:
                self._stats['failed_logins'] += 1
                return None

        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return None

    def _generate_token(self, username: str) -> str:
        """Generate JWT token."""
        payload = {
            'username': username,
            'exp': datetime.utcnow() + timedelta(hours=self.token_expiry),
            'iat': datetime.utcnow()
        }
        return jwt.encode(payload, self.secret_key, algorithm='HS256')

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None

    def logout(self, token: str) -> bool:
        """Logout user."""
        try:
            payload = self.verify_token(token)
            if payload:
                self._stats['active_sessions'] = max(0, self._stats['active_sessions'] - 1)
                return True
            return False
        except Exception as e:
            self.logger.error(f"Logout error: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """Get authentication statistics."""
        return self._stats.copy()
