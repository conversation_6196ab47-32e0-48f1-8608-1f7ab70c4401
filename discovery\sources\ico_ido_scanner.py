"""
ICO/IDO/IEO 项目发现模块

该模块专门用于发现和跟踪即将推出的区块链项目，包括ICO、IDO和IEO。
它从多个专业平台获取数据，如ICODrops、CryptoRank、CoinMarketCap等。
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from fake_useragent import UserAgent
import cloudscraper
from urllib.parse import urljoin
import logging
import time
import random
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set, Tuple
import requests
from bs4 import BeautifulSoup

from discovery.sources.base_source import BaseProjectSource
from discovery.models.project import ProjectType, BlockchainPlatform

class ICOIDOScanner(BaseProjectSource):
    """ICO/IDO/IEO 项目扫描器，专门用于发现即将推出的区块链项目"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化ICO/IDO/IEO项目扫描器
        
        Args:
            config: 配置字典
        """
        super().__init__(config)
        
        # 配置参数
        self.timeout = config.get('timeout', 30)
        self.max_retries = config.get('max_retries', 3)
        self.use_selenium = config.get('use_selenium', True)
        self.proxy_list = config.get('proxy_list', [])
        
        # 初始化 cloudscraper
        self.scraper = cloudscraper.create_scraper(
            browser={'browser': 'chrome', 'platform': 'windows', 'mobile': False}
        )
        
        # 初始化 Selenium (如果启用)
        if self.use_selenium:
            self.chrome_options = Options()
            self.chrome_options.add_argument('--headless')
            self.chrome_options.add_argument('--no-sandbox')
            self.chrome_options.add_argument('--disable-dev-shm-usage')
        
        # 启用的平台
        self.platforms = {
            'icodrops': config.get('icodrops', {}).get('enabled', True),
            'cryptorank': config.get('cryptorank', {}).get('enabled', True),
            'coinmarketcap': config.get('coinmarketcap', {}).get('enabled', True),
            'ico_analytics': config.get('ico_analytics', {}).get('enabled', True),
            'binance_launchpad': config.get('binance_launchpad', {}).get('enabled', True),
            'kucoin_spotlight': config.get('kucoin_spotlight', {}).get('enabled', True),
            'gate_startup': config.get('gate_startup', {}).get('enabled', True),
            'trustswap': config.get('trustswap', {}).get('enabled', True),
            'seedify': config.get('seedify', {}).get('enabled', True),
            'polkastarter': config.get('polkastarter', {}).get('enabled', True),
            'dao_maker': config.get('dao_maker', {}).get('enabled', True),
            'bsc_pad': config.get('bsc_pad', {}).get('enabled', True),
            'avax_pad': config.get('avax_pad', {}).get('enabled', True),
            'solana_pad': config.get('solana_pad', {}).get('enabled', True),
        }
        
        # API密钥
        self.api_keys = {
            'coinmarketcap': config.get('coinmarketcap', {}).get('api_key', ''),
            'cryptorank': config.get('cryptorank', {}).get('api_key', ''),
        }
        
        # 用户代理列表
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        ]
        
        self.logger.info(f"已初始化ICO/IDO/IEO项目扫描器，启用的平台: {[k for k, v in self.platforms.items() if v]}")
    
    def get_projects(self, count: int) -> List[Dict[str, Any]]:
        """
        获取项目
        
        Args:
            count: 要获取的项目数量
            
        Returns:
            项目列表
        """
        all_projects = []
        
        # 从ICODrops获取项目
        if self.platforms.get('icodrops'):
            try:
                icodrops_projects = self._get_icodrops_projects()
                all_projects.extend(icodrops_projects)
                self.logger.info(f"从ICODrops获取到 {len(icodrops_projects)} 个项目")
            except Exception as e:
                self.logger.error(f"从ICODrops获取项目时出错: {str(e)}")
        
        # 从CryptoRank获取项目
        if self.platforms.get('cryptorank'):
            try:
                cryptorank_projects = self._get_cryptorank_projects()
                all_projects.extend(cryptorank_projects)
                self.logger.info(f"从CryptoRank获取到 {len(cryptorank_projects)} 个项目")
            except Exception as e:
                self.logger.error(f"从CryptoRank获取项目时出错: {str(e)}")
        
        # 从CoinMarketCap获取项目
        if self.platforms.get('coinmarketcap'):
            try:
                coinmarketcap_projects = self._get_coinmarketcap_projects()
                all_projects.extend(coinmarketcap_projects)
                self.logger.info(f"从CoinMarketCap获取到 {len(coinmarketcap_projects)} 个项目")
            except Exception as e:
                self.logger.error(f"从CoinMarketCap获取项目时出错: {str(e)}")
        
        # 从Binance Launchpad获取项目
        if self.platforms.get('binance_launchpad'):
            try:
                binance_projects = self._get_binance_launchpad_projects()
                all_projects.extend(binance_projects)
                self.logger.info(f"从Binance Launchpad获取到 {len(binance_projects)} 个项目")
            except Exception as e:
                self.logger.error(f"从Binance Launchpad获取项目时出错: {str(e)}")
        
        # 从KuCoin Spotlight获取项目
        if self.platforms.get('kucoin_spotlight'):
            try:
                kucoin_projects = self._get_kucoin_spotlight_projects()
                all_projects.extend(kucoin_projects)
                self.logger.info(f"从KuCoin Spotlight获取到 {len(kucoin_projects)} 个项目")
            except Exception as e:
                self.logger.error(f"从KuCoin Spotlight获取项目时出错: {str(e)}")
        
        # 从Gate.io Startup获取项目
        if self.platforms.get('gate_startup'):
            try:
                gate_projects = self._get_gate_startup_projects()
                all_projects.extend(gate_projects)
                self.logger.info(f"从Gate.io Startup获取到 {len(gate_projects)} 个项目")
            except Exception as e:
                self.logger.error(f"从Gate.io Startup获取项目时出错: {str(e)}")
        
        # 从TrustSwap获取项目
        if self.platforms.get('trustswap'):
            try:
                trustswap_projects = self._get_trustswap_projects()
                all_projects.extend(trustswap_projects)
                self.logger.info(f"从TrustSwap获取到 {len(trustswap_projects)} 个项目")
            except Exception as e:
                self.logger.error(f"从TrustSwap获取项目时出错: {str(e)}")
        
        # 从Seedify获取项目
        if self.platforms.get('seedify'):
            try:
                seedify_projects = self._get_seedify_projects()
                all_projects.extend(seedify_projects)
                self.logger.info(f"从Seedify获取到 {len(seedify_projects)} 个项目")
            except Exception as e:
                self.logger.error(f"从Seedify获取项目时出错: {str(e)}")
        
        # 从Polkastarter获取项目
        if self.platforms.get('polkastarter'):
            try:
                polkastarter_projects = self._get_polkastarter_projects()
                all_projects.extend(polkastarter_projects)
                self.logger.info(f"从Polkastarter获取到 {len(polkastarter_projects)} 个项目")
            except Exception as e:
                self.logger.error(f"从Polkastarter获取项目时出错: {str(e)}")
        
        # 从DAO Maker获取项目
        if self.platforms.get('dao_maker'):
            try:
                dao_maker_projects = self._get_dao_maker_projects()
                all_projects.extend(dao_maker_projects)
                self.logger.info(f"从DAO Maker获取到 {len(dao_maker_projects)} 个项目")
            except Exception as e:
                self.logger.error(f"从DAO Maker获取项目时出错: {str(e)}")
        
        # 去重
        unique_projects = self._deduplicate_projects(all_projects)
        
        # 按发布时间排序
        sorted_projects = sorted(unique_projects, key=lambda x: x.get('discovery_time', 0), reverse=True)
        
        # 限制返回的项目数量
        return sorted_projects[:count]
    
    def _get_random_user_agent(self) -> str:
        """获取随机用户代理"""
        return random.choice(self.user_agents)
    
    def _get_random_proxy(self) -> Optional[str]:
        """获取随机代理"""
        return random.choice(self.proxy_list) if self.proxy_list else None
    
    def _get_advanced_headers(self) -> Dict[str, str]:
        """获取增强的请求头"""
        ua = UserAgent()
        return {
            'User-Agent': ua.random,
            'Accept': 'text/html,application/json,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }

    def _make_selenium_request(self, url: str) -> str:
        """使用 Selenium 发送请求"""
        driver = webdriver.Chrome(options=self.chrome_options)
        try:
            driver.get(url)
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            sleep(random.uniform(2, 5))  # 随机等待
            return driver.page_source
        finally:
            driver.quit()

    def _make_request(self, url: str, headers: Dict[str, str] = None, params: Dict[str, Any] = None) -> requests.Response:
        """改进的请求方法"""
        if headers is None:
            headers = self._get_advanced_headers()
        
        proxy = self._get_random_proxy()
        proxies = {'http': proxy, 'https': proxy} if proxy else None
        
        for attempt in range(self.max_retries):
            try:
                # 首先尝试使用 cloudscraper
                response = self.scraper.get(
                    url,
                    headers=headers,
                    params=params,
                    proxies=proxies,
                    timeout=self.timeout
                )
                response.raise_for_status()
                return response
            except Exception as e:
                self.logger.warning(f"Cloudscraper请求失败 (尝试 {attempt+1}/{self.max_retries}): {str(e)}")
                
                if self.use_selenium and attempt == self.max_retries - 1:
                    # 最后一次尝试时使用 Selenium
                    try:
                        html_content = self._make_selenium_request(url)
                        return type('SeleniumResponse', (), {
                            'text': html_content,
                            'status_code': 200,
                            'raise_for_status': lambda: None
                        })
                    except Exception as se:
                        self.logger.error(f"Selenium请求也失败: {str(se)}")
                        raise
                
                # 指数退避策略
                wait_time = (2 ** attempt + random.uniform(1, 3))
                self.logger.info(f"等待 {wait_time:.2f} 秒后重试...")
                sleep(wait_time)
        
        raise Exception("所有请求尝试均失败")
    
    def _deduplicate_projects(self, projects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去除重复项目
        
        Args:
            projects: 项目列表
            
        Returns:
            去重后的项目列表
        """
        unique_projects = {}
        
        for project in projects:
            # 使用项目名称作为唯一标识
            project_name = project.get('name', '').lower()
            if not project_name:
                continue
            
            # 如果项目已存在，选择描述更长的版本
            if project_name in unique_projects:
                existing_desc = unique_projects[project_name].get('description', '')
                current_desc = project.get('description', '')
                
                if len(current_desc) > len(existing_desc):
                    unique_projects[project_name] = project
            else:
                unique_projects[project_name] = project
        
        return list(unique_projects.values())
    
    def _get_icodrops_projects(self) -> List[Dict[str, Any]]:
        """
        从ICODrops获取项目
        
        Returns:
            项目列表
        """
        projects = []
        
        try:
            # 直接使用API获取项目数据
            api_url = 'https://api.icodrops.com/v2/projects/active'
            headers = {
                'User-Agent': self._get_random_user_agent(),
                'Accept': 'application/json',
                'Referer': 'https://icodrops.com/category/active-ico/'
            }
            
            self.logger.info(f"尝试从ICODrops API获取数据: {api_url}")
            
            try:
                response = self._make_request(api_url, headers=headers)
                data = response.json()
                
                self.logger.info(f"ICODrops API响应状态码: {response.status_code}")
                
                # 处理API响应
                if 'data' in data and 'projects' in data['data']:
                    api_projects = data['data']['projects']
                    self.logger.info(f"从ICODrops API获取到 {len(api_projects)} 个项目")
                    
                    for project in api_projects:
                        try:
                            name = project.get('name', '')
                            symbol = project.get('ticker', '')
                            description = project.get('description', '')
                            slug = project.get('slug', '')
                            url = f"https://icodrops.com/{slug}/" if slug else ""
                            
                            # 获取区块链平台
                            platform = "Unknown"
                            if 'ecosystem' in project and project['ecosystem']:
                                platform = project['ecosystem'].get('name', 'Unknown')
                            
                            # 映射区块链平台
                            blockchain = self._map_blockchain_platform(platform)
                            
                            # 创建项目
                            project_data = {
                                'id': f"icodrops_{int(time.time())}_{len(projects)}",
                                'name': f"{name} ({symbol})" if symbol else name,
                                'description': description,
                                'url': url,
                                'project_type': ProjectType.ICO.value,
                                'blockchain': blockchain.value,
                                'discovery_source': 'icodrops_api',
                                'source_url': 'https://icodrops.com/',
                                'discovery_time': time.time()
                            }
                            
                            self.logger.info(f"从ICODrops API添加项目: {name}")
                            projects.append(project_data)
                            
                        except Exception as e:
                            self.logger.error(f"解析ICODrops API项目时出错: {str(e)}")
                else:
                    self.logger.warning(f"ICODrops API响应格式不正确: {data}")
            except Exception as e:
                self.logger.error(f"从ICODrops API获取数据时出错: {str(e)}")
            
            # 如果API获取失败，尝试获取即将到来的ICO
            if not projects:
                self.logger.info("尝试从ICODrops API获取即将到来的项目")
                api_url = 'https://api.icodrops.com/v2/projects/upcoming'
                
                try:
                    response = self._make_request(api_url, headers=headers)
                    data = response.json()
                    
                    # 处理API响应
                    if 'data' in data and 'projects' in data['data']:
                        api_projects = data['data']['projects']
                        self.logger.info(f"从ICODrops API获取到 {len(api_projects)} 个即将到来的项目")
                        
                        for project in api_projects:
                            try:
                                name = project.get('name', '')
                                symbol = project.get('ticker', '')
                                description = project.get('description', '')
                                slug = project.get('slug', '')
                                url = f"https://icodrops.com/{slug}/" if slug else ""
                                
                                # 获取区块链平台
                                platform = "Unknown"
                                if 'ecosystem' in project and project['ecosystem']:
                                    platform = project['ecosystem'].get('name', 'Unknown')
                                
                                # 映射区块链平台
                                blockchain = self._map_blockchain_platform(platform)
                                
                                # 创建项目
                                project_data = {
                                    'id': f"icodrops_{int(time.time())}_{len(projects)}",
                                    'name': f"{name} ({symbol})" if symbol else name,
                                    'description': description,
                                    'url': url,
                                    'project_type': ProjectType.ICO.value,
                                    'blockchain': blockchain.value,
                                    'discovery_source': 'icodrops_api',
                                    'source_url': 'https://icodrops.com/',
                                    'discovery_time': time.time()
                                }
                                
                                self.logger.info(f"从ICODrops API添加即将到来的项目: {name}")
                                projects.append(project_data)
                                
                            except Exception as e:
                                self.logger.error(f"解析ICODrops API即将到来的项目时出错: {str(e)}")
                    else:
                        self.logger.warning(f"ICODrops API响应格式不正确: {data}")
                except Exception as e:
                    self.logger.error(f"从ICODrops API获取即将到来的数据时出错: {str(e)}")
            
            # 如果API获取失败，尝试使用网页抓取
            if not projects:
                self.logger.info("API获取失败，尝试使用网页抓取")
                
                # 获取活跃ICO
                url = 'https://icodrops.com/category/active-ico/'
                response = self._make_request(url)
                
                # 保存HTML以便调试
                with open('icodrops_debug.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                self.logger.info("已保存ICODrops HTML到icodrops_debug.html")
                
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 尝试直接提取所有链接
                links = soup.select('a')
                self.logger.info(f"找到 {len(links)} 个链接")
                
                for link in links:
                    try:
                        href = link.get('href', '')
                        if href and ('icodrops.com' in href) and ('/ico/' in href or '/' in href and href.count('/') == 1):
                            name = link.text.strip()
                            if not name and href.rstrip('/').count('/') > 0:
                                name = href.rstrip('/').split('/')[-1].replace('-', ' ').title()
                            
                            if name and len(name) > 2 and not name.lower() in ['home', 'about', 'contact', 'privacy', 'terms']:
                                self.logger.info(f"找到ICO链接: {href}, 名称: {name}")
                                
                                # 创建项目
                                project = {
                                    'id': f"icodrops_{int(time.time())}_{len(projects)}",
                                    'name': name,
                                    'description': f"ICO项目: {name}",
                                    'url': href if href.startswith('http') else f"https://icodrops.com{href}" if href.startswith('/') else f"https://icodrops.com/{href}",
                                    'project_type': ProjectType.ICO.value,
                                    'blockchain': BlockchainPlatform.OTHER.value,
                                    'discovery_source': 'icodrops',
                                    'source_url': 'https://icodrops.com/',
                                    'discovery_time': time.time()
                                }
                                
                                projects.append(project)
                    except Exception as e:
                        self.logger.error(f"解析ICO链接时出错: {str(e)}")
        
        except Exception as e:
            self.logger.error(f"获取ICODrops项目时出错: {str(e)}")
            
        return projects
    
    def _get_cryptorank_projects(self) -> List[Dict[str, Any]]:
        """
        从CryptoRank获取项目
        
        Returns:
            项目列表
        """
        projects = []
        
        # 获取即将到来的ICO
        url = 'https://cryptorank.io/upcoming-ico'
        response = self._make_request(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找项目行
        rows = soup.select('.table-row')
        
        for row in rows:
            try:
                # 提取项目信息
                name_elem = row.select_one('.coin-name')
                if not name_elem:
                    continue
                
                name = name_elem.text.strip()
                
                # 提取项目URL
                url_elem = row.select_one('a.table-row-link')
                url = f"https://cryptorank.io{url_elem.get('href', '')}" if url_elem else ""
                
                # 提取项目描述
                desc_elem = row.select_one('.coin-description')
                description = desc_elem.text.strip() if desc_elem else ""
                
                # 提取区块链平台
                platform_elem = row.select_one('.coin-category')
                platform = platform_elem.text.strip() if platform_elem else "Unknown"
                
                # 映射区块链平台
                blockchain = self._map_blockchain_platform(platform)
                
                # 创建项目
                project = {
                    'id': f"cryptorank_{int(time.time())}_{len(projects)}",
                    'name': name,
                    'description': description,
                    'url': url,
                    'project_type': ProjectType.ICO.value,
                    'blockchain': blockchain.value,
                    'discovery_source': 'cryptorank',
                    'source_url': 'https://cryptorank.io/upcoming-ico',
                    'discovery_time': time.time()
                }
                
                projects.append(project)
                
            except Exception as e:
                self.logger.error(f"解析CryptoRank项目时出错: {str(e)}")
        
        # 如果有API密钥，使用API获取更多信息
        if self.api_keys.get('cryptorank'):
            try:
                api_url = 'https://api.cryptorank.io/v1/currencies/upcoming'
                headers = {
                    'X-API-KEY': self.api_keys['cryptorank']
                }
                
                response = self._make_request(api_url, headers=headers)
                data = response.json()
                
                for item in data.get('data', []):
                    try:
                        name = item.get('name', '')
                        symbol = item.get('symbol', '')
                        description = item.get('description', '')
                        url = item.get('website', '')
                        
                        # 提取区块链平台
                        platform = item.get('category', 'Unknown')
                        
                        # 映射区块链平台
                        blockchain = self._map_blockchain_platform(platform)
                        
                        # 创建项目
                        project = {
                            'id': f"cryptorank_api_{int(time.time())}_{len(projects)}",
                            'name': name,
                            'description': description,
                            'url': url,
                            'project_type': ProjectType.ICO.value,
                            'blockchain': blockchain.value,
                            'discovery_source': 'cryptorank_api',
                            'source_url': 'https://cryptorank.io/',
                            'discovery_time': time.time()
                        }
                        
                        projects.append(project)
                        
                    except Exception as e:
                        self.logger.error(f"解析CryptoRank API项目时出错: {str(e)}")
                
            except Exception as e:
                self.logger.error(f"从CryptoRank API获取项目时出错: {str(e)}")
        
        return projects
    
    def _get_coinmarketcap_projects(self) -> List[Dict[str, Any]]:
        """
        从CoinMarketCap获取项目
        
        Returns:
            项目列表
        """
        projects = []
        
        try:
            # 使用CoinGecko API获取新上线的代币，作为替代方案
            url = 'https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&category=recently_added&order=market_cap_desc&per_page=50&page=1'
            self.logger.info(f"尝试从CoinGecko API获取新上线代币: {url}")
            
            response = self._make_request(url)
            data = response.json()
            
            self.logger.info(f"从CoinGecko API获取到 {len(data)} 个新上线代币")
            
            for coin in data:
                try:
                    name = coin.get('name', '')
                    symbol = coin.get('symbol', '').upper()
                    description = f"新上线的代币: {name} ({symbol})"
                    coin_url = f"https://www.coingecko.com/en/coins/{coin.get('id', '')}"
                    
                    # 获取更多详细信息
                    detail_url = f"https://api.coingecko.com/api/v3/coins/{coin.get('id', '')}?localization=false&tickers=false&market_data=false&community_data=false&developer_data=false"
                    try:
                        detail_response = self._make_request(detail_url)
                        detail_data = detail_response.json()
                        
                        if 'description' in detail_data and 'en' in detail_data['description'] and detail_data['description']['en']:
                            description = detail_data['description']['en'][:500] + '...' if len(detail_data['description']['en']) > 500 else detail_data['description']['en']
                    except Exception as e:
                        self.logger.warning(f"获取代币详细信息时出错: {str(e)}")
                    
                    # 确定区块链平台
                    platform = "Unknown"
                    if 'asset_platform_id' in coin and coin['asset_platform_id']:
                        platform = coin['asset_platform_id']
                    
                    # 映射区块链平台
                    blockchain = self._map_blockchain_platform(platform)
                    
                    # 确定项目类型
                    # 由于CoinGecko API不提供项目类型信息，我们根据一些规则来猜测
                    project_type = ProjectType.ICO  # 默认为ICO
                    
                    # 创建项目
                    project = {
                        'id': f"coingecko_{int(time.time())}_{len(projects)}",
                        'name': f"{name} ({symbol})",
                        'description': description,
                        'url': coin_url,
                        'project_type': project_type.value,
                        'blockchain': blockchain.value,
                        'discovery_source': 'coingecko',
                        'source_url': 'https://www.coingecko.com/',
                        'discovery_time': time.time()
                    }
                    
                    projects.append(project)
                    
                except Exception as e:
                    self.logger.error(f"解析CoinGecko代币时出错: {str(e)}")
            
        except Exception as e:
            self.logger.error(f"从CoinGecko获取代币时出错: {str(e)}")
            
            # 如果CoinGecko API失败，尝试使用CoinMarketCap
            try:
                try:
            # 使用CoinGecko API获取新上线的代币，作为替代方案
                    url = 'https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&category=recently_added&order=market_cap_desc&per_page=50&page=1'
            self.logger.info(f"尝试从CoinGecko API获取新上线代币: {url}")
                    
                            response = self._make_request(url)
                                                
                                      data = response.json()
            
            self.logger.info(f"从CoinGecko API获取到 {len(data)} 个新上线代币")
            
            for coin in data:
                              try:
                            name = coin.get('name', '')
                    symbol = coin.get('symbol', '').upper()
                              
                 description = f"新上线的代币: {name} ({symbol})"
                    coin_url = f"https://www.coingecko.com/en/coins/{coin.get('id', '')}"
                                 
                                                    # 获取更多详细信息
                    detail_url = f"https://api.coingecko.com/api/v3/coins/{coin.get('id', '')}?localization=false&tickers=false&market_data=false&community_data=false&developer_data=false"
                            try:
                        detail_response = self._make_request(detail_url)
                                        detail_data = detail_response.json()
                                
                        if         'description' in detail_data and 'en' in detail_data['description'] and detail_data['description']['en']:
                                               }
                             description = detail_data['description']['en'][:500] + '...' if len(detail_data['description']['en']) > 500 else detail_data['description']['en']
                        
                    except Exception as e:
                        self.logger.error(f"从CoinMarketCap获取项目时出错: {str(e)}")
                    except Exception as e:
                        self.logger.warning(f"获取代币详细信息时出错: {str(e)}")
                    
                    # 确定区块链平台
                    platform = "Unknown"
                    if 'asset_platform_id' in coin and coin['asset_platform_id']:
                        platform = coin['asset_platform_id']
                    
                    # 映射区块链平台
                    blockchain = self._map_blockchain_platform(platform)
                    
                    # 确定项目类型
                    # 由于CoinGecko API不提供项目类型信息，我们根据一些规则来猜测
                    project_type = ProjectType.ICO  # 默认为ICO
                    
                    # 创建项目
                    project = {
                        'id': f"coingecko_{int(time.time())}_{len(projects)}",
                        'name': f"{name} ({symbol})",
                        'description': description,
                        'url': coin_url,
                        'project_type': project_type.value,
                        'blockchain': blockchain.value,
                        'discovery_source': 'coingecko',
                        'source_url': 'https://www.coingecko.com/',
                        'discovery_time': time.time()
                    }
                    
                    projects.append(project)
                    
                except Exception as e:
                    self.logger.error(f"解析CoinGecko代币时出错: {str(e)}")
            
        except Exception as e:
            self.logger.error(f"从CoinGecko获取代币时出错: {str(e)}")
            
            # 如果CoinGecko API失败，尝试使用CoinMarketCap
            try:
                # 获取即将到来的ICO
                url = 'https://coinmarketcap.com/ico-calendar/'
                response = self._make_request(url)
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找项目卡片
                cards = soup.select('.cmc-card')
                
                for card in cards:
                    try:
                        # 提取项目信息
                        name_elem = card.select_one('.cmc-link')
                        if not name_elem:
                            continue
                        
                        name = name_elem.text.strip()
                        
                        # 提取项目URL
                        url = f"https://coinmarketcap.com{name_elem.get('href', '')}" if name_elem and 'href' in name_elem.attrs else ""
                        
                        # 提取项目描述
                        desc_elem = card.select_one('.cmc-card-description')
                        description = desc_elem.text.strip() if desc_elem else ""
                        
                        # 创建项目
                        project = {
                            'id': f"coinmarketcap_{int(time.time())}_{len(projects)}",
                            'name': name,
                            'description': description,
                            'url': url,
                            'project_type': ProjectType.ICO.value,
                            'blockchain': BlockchainPlatform.OTHER.value,
                            'discovery_source': 'coinmarketcap',
                            'source_url': 'https://coinmarketcap.com/ico-calendar/',
                            'discovery_time': time.time()
                        }
                        
                        projects.append(project)
                        
                    except Exception as e:
                        self.logger.error(f"解析CoinMarketCap项目时出错: {str(e)}")
                
            except Exception as e:
                self.logger.error(f"从CoinMarketCap获取项目时出错: {str(e)}")
        
        # 如果有API密钥，使用API获取更多信息
        if self.api_keys.get('coinmarketcap'):
            try:
                api_url = 'https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest'
                headers = {
                    'X-CMC_PRO_API_KEY': self.api_keys['coinmarketcap']
                }
                params = {
                    'start': 1,
                    'limit': 100,
                    'sort': 'date_added',
                    'sort_dir': 'desc',
                    'convert': 'USD'
                }
                
                response = self._make_request(api_url, headers=headers, params=params)
                data = response.json()
                
                for item in data.get('data', []):
                    try:
                        name = item.get('name', '')
                        symbol = item.get('symbol', '')
                        description = f"{name} ({symbol}) - 最新上线的加密货币"
                        url = f"https://coinmarketcap.com/currencies/{item.get('slug', '')}"
                        
                        # 创建项目
                        project = {
                            'id': f"coinmarketcap_api_{int(time.time())}_{len(projects)}",
                            'name': name,
                            'description': description,
                            'url': url,
                            'project_type': ProjectType.AIRDROP.value,
                            'blockchain': BlockchainPlatform.OTHER.value,
                            'discovery_source': 'coinmarketcap_api',
                            'source_url': 'https://coinmarketcap.com/',
                            'discovery_time': time.time()
                        }
                        
                        projects.append(project)
                        
                    except Exception as e:
                        self.logger.error(f"解析CoinMarketCap API项目时出错: {str(e)}")
                
            except Exception as e:
                self.logger.error(f"从CoinMarketCap API获取项目时出错: {str(e)}")
        
        return projects
    
    def _get_binance_launchpad_projects(self) -> List[Dict[str, Any]]:
        """
        从Binance Launchpad获取项目
        
        Returns:
            项目列表
        """
        projects = []
        
        # 获取Binance Launchpad项目
        url = 'https://launchpad.binance.com/en/allProjects'
        response = self._make_request(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找项目卡片
        cards = soup.select('.css-1ej1wj4')
        
        for card in cards:
            try:
                # 提取项目信息
                name_elem = card.select_one('.css-1ap5wc6')
                if not name_elem:
                    continue
                
                name = name_elem.text.strip()
                
                # 提取项目URL
                url_elem = card.select_one('a')
                url = f"https://launchpad.binance.com{url_elem.get('href', '')}" if url_elem and 'href' in url_elem.attrs else ""
                
                # 提取项目描述
                desc_elem = card.select_one('.css-1qm0lfz')
                description = desc_elem.text.strip() if desc_elem else ""
                
                # 创建项目
                project = {
                    'id': f"binance_launchpad_{int(time.time())}_{len(projects)}",
                    'name': name,
                    'description': description,
                    'url': url,
                    'project_type': ProjectType.IEO.value,
                    'blockchain': BlockchainPlatform.BINANCE.value,
                    'discovery_source': 'binance_launchpad',
                    'source_url': 'https://launchpad.binance.com/',
                    'discovery_time': time.time()
                }
                
                projects.append(project)
                
            except Exception as e:
                self.logger.error(f"解析Binance Launchpad项目时出错: {str(e)}")
        
        return projects
    
    def _get_kucoin_spotlight_projects(self) -> List[Dict[str, Any]]:
        """
        从KuCoin Spotlight获取项目
        
        Returns:
            项目列表
        """
        projects = []
        
        # 获取KuCoin Spotlight项目
        url = 'https://www.kucoin.com/spotlight/list'
        response = self._make_request(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找项目卡片
        cards = soup.select('.spotlight-item')
        
        for card in cards:
            try:
                # 提取项目信息
                name_elem = card.select_one('.spotlight-name')
                if not name_elem:
                    continue
                
                name = name_elem.text.strip()
                
                # 提取项目URL
                url_elem = card.select_one('a')
                url = f"https://www.kucoin.com{url_elem.get('href', '')}" if url_elem and 'href' in url_elem.attrs else ""
                
                # 提取项目描述
                desc_elem = card.select_one('.spotlight-desc')
                description = desc_elem.text.strip() if desc_elem else ""
                
                # 创建项目
                project = {
                    'id': f"kucoin_spotlight_{int(time.time())}_{len(projects)}",
                    'name': name,
                    'description': description,
                    'url': url,
                    'project_type': ProjectType.IEO.value,
                    'blockchain': BlockchainPlatform.OTHER.value,
                    'discovery_source': 'kucoin_spotlight',
                    'source_url': 'https://www.kucoin.com/spotlight/list',
                    'discovery_time': time.time()
                }
                
                projects.append(project)
                
            except Exception as e:
                self.logger.error(f"解析KuCoin Spotlight项目时出错: {str(e)}")
        
        return projects
    
    def _get_gate_startup_projects(self) -> List[Dict[str, Any]]:
        """
        从Gate.io Startup获取项目
        
        Returns:
            项目列表
        """
        projects = []
        
        # 获取Gate.io Startup项目
        url = 'https://www.gate.io/startup'
        response = self._make_request(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找项目卡片
        cards = soup.select('.startup-item')
        
        for card in cards:
            try:
                # 提取项目信息
                name_elem = card.select_one('.startup-name')
                if not name_elem:
                    continue
                
                name = name_elem.text.strip()
                
                # 提取项目URL
                url_elem = card.select_one('a')
                url = f"https://www.gate.io{url_elem.get('href', '')}" if url_elem and 'href' in url_elem.attrs else ""
                
                # 提取项目描述
                desc_elem = card.select_one('.startup-desc')
                description = desc_elem.text.strip() if desc_elem else ""
                
                # 创建项目
                project = {
                    'id': f"gate_startup_{int(time.time())}_{len(projects)}",
                    'name': name,
                    'description': description,
                    'url': url,
                    'project_type': ProjectType.IEO.value,
                    'blockchain': BlockchainPlatform.OTHER.value,
                    'discovery_source': 'gate_startup',
                    'source_url': 'https://www.gate.io/startup',
                    'discovery_time': time.time()
                }
                
                projects.append(project)
                
            except Exception as e:
                self.logger.error(f"解析Gate.io Startup项目时出错: {str(e)}")
        
        return projects
    
    def _get_trustswap_projects(self) -> List[Dict[str, Any]]:
        """
        从TrustSwap获取项目
        
        Returns:
            项目列表
        """
        projects = []
        
        # 获取TrustSwap项目
        url = 'https://trustswap.com/launchpad'
        response = self._make_request(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找项目卡片
        cards = soup.select('.launchpad-card')
        
        for card in cards:
            try:
                # 提取项目信息
                name_elem = card.select_one('.launchpad-name')
                if not name_elem:
                    continue
                
                name = name_elem.text.strip()
                
                # 提取项目URL
                url_elem = card.select_one('a')
                url = url_elem.get('href', '') if url_elem and 'href' in url_elem.attrs else ""
                
                # 提取项目描述
                desc_elem = card.select_one('.launchpad-desc')
                description = desc_elem.text.strip() if desc_elem else ""
                
                # 创建项目
                project = {
                    'id': f"trustswap_{int(time.time())}_{len(projects)}",
                    'name': name,
                    'description': description,
                    'url': url,
                    'project_type': ProjectType.IDO.value,
                    'blockchain': BlockchainPlatform.ETHEREUM.value,
                    'discovery_source': 'trustswap',
                    'source_url': 'https://trustswap.com/launchpad',
                    'discovery_time': time.time()
                }
                
                projects.append(project)
                
            except Exception as e:
                self.logger.error(f"解析TrustSwap项目时出错: {str(e)}")
        
        return projects
    
    def _get_seedify_projects(self) -> List[Dict[str, Any]]:
        """
        从Seedify获取项目
        
        Returns:
            项目列表
        """
        projects = []
        
        # 获取Seedify项目
        url = 'https://launchpad.seedify.fund/'
        response = self._make_request(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找项目卡片
        cards = soup.select('.ido-card')
        
        for card in cards:
            try:
                # 提取项目信息
                name_elem = card.select_one('.ido-name')
                if not name_elem:
                    continue
                
                name = name_elem.text.strip()
                
                # 提取项目URL
                url_elem = card.select_one('a')
                url = url_elem.get('href', '') if url_elem and 'href' in url_elem.attrs else ""
                
                # 提取项目描述
                desc_elem = card.select_one('.ido-desc')
                description = desc_elem.text.strip() if desc_elem else ""
                
                # 创建项目
                project = {
                    'id': f"seedify_{int(time.time())}_{len(projects)}",
                    'name': name,
                    'description': description,
                    'url': url,
                    'project_type': ProjectType.IDO.value,
                    'blockchain': BlockchainPlatform.BINANCE.value,
                    'discovery_source': 'seedify',
                    'source_url': 'https://launchpad.seedify.fund/',
                    'discovery_time': time.time()
                }
                
                projects.append(project)
                
            except Exception as e:
                self.logger.error(f"解析Seedify项目时出错: {str(e)}")
        
        return projects
    
    def _get_polkastarter_projects(self) -> List[Dict[str, Any]]:
        """
        从Polkastarter获取项目
        
        Returns:
            项目列表
        """
        projects = []
        
        # 获取Polkastarter项目
        url = 'https://polkastarter.com/projects'
        response = self._make_request(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找项目卡片
        cards = soup.select('.project-card')
        
        for card in cards:
            try:
                # 提取项目信息
                name_elem = card.select_one('.project-name')
                if not name_elem:
                    continue
                
                name = name_elem.text.strip()
                
                # 提取项目URL
                url_elem = card.select_one('a')
                url = f"https://polkastarter.com{url_elem.get('href', '')}" if url_elem and 'href' in url_elem.attrs else ""
                
                # 提取项目描述
                desc_elem = card.select_one('.project-desc')
                description = desc_elem.text.strip() if desc_elem else ""
                
                # 创建项目
                project = {
                    'id': f"polkastarter_{int(time.time())}_{len(projects)}",
                    'name': name,
                    'description': description,
                    'url': url,
                    'project_type': ProjectType.IDO.value,
                    'blockchain': BlockchainPlatform.POLKADOT.value,
                    'discovery_source': 'polkastarter',
                    'source_url': 'https://polkastarter.com/projects',
                    'discovery_time': time.time()
                }
                
                projects.append(project)
                
            except Exception as e:
                self.logger.error(f"解析Polkastarter项目时出错: {str(e)}")
        
        return projects
    
    def _get_dao_maker_projects(self) -> List[Dict[str, Any]]:
        """
        从DAO Maker获取项目
        
        Returns:
            项目列表
        """
        projects = []
        
        # 获取DAO Maker项目
        url = 'https://daomaker.com/projects'
        response = self._make_request(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找项目卡片
        cards = soup.select('.project-card')
        
        for card in cards:
            try:
                # 提取项目信息
                name_elem = card.select_one('.project-name')
                if not name_elem:
                    continue
                
                name = name_elem.text.strip()
                
                # 提取项目URL
                url_elem = card.select_one('a')
                url = url_elem.get('href', '') if url_elem and 'href' in url_elem.attrs else ""
                
                # 提取项目描述
                desc_elem = card.select_one('.project-desc')
                description = desc_elem.text.strip() if desc_elem else ""
                
                # 创建项目
                project = {
                    'id': f"dao_maker_{int(time.time())}_{len(projects)}",
                    'name': name,
                    'description': description,
                    'url': url,
                    'project_type': ProjectType.IDO.value,
                    'blockchain': BlockchainPlatform.ETHEREUM.value,
                    'discovery_source': 'dao_maker',
                    'source_url': 'https://daomaker.com/projects',
                    'discovery_time': time.time()
                }
                
                projects.append(project)
                
            except Exception as e:
                self.logger.error(f"解析DAO Maker项目时出错: {str(e)}")
        
        return projects
    
    def _map_blockchain_platform(self, platform_name: str) -> BlockchainPlatform:
        """
        映射区块链平台名称到枚举值
        
        Args:
            platform_name: 平台名称
            
        Returns:
            区块链平台枚举
        """
        platform_name = platform_name.lower()
        
        if 'ethereum' in platform_name or 'eth' in platform_name:
            return BlockchainPlatform.ETHEREUM
        elif 'binance' in platform_name or 'bsc' in platform_name or 'bnb' in platform_name:
            return BlockchainPlatform.BINANCE
        elif 'solana' in platform_name or 'sol' in platform_name:
            return BlockchainPlatform.SOLANA
        elif 'polygon' in platform_name or 'matic' in platform_name:
            return BlockchainPlatform.POLYGON
        elif 'avalanche' in platform_name or 'avax' in platform_name:
            return BlockchainPlatform.AVALANCHE
        elif 'arbitrum' in platform_name or 'arb' in platform_name:
            return BlockchainPlatform.ARBITRUM
        elif 'optimism' in platform_name or 'op' in platform_name:
            return BlockchainPlatform.OPTIMISM
        elif 'base' in platform_name:
            return BlockchainPlatform.BASE
        elif 'cosmos' in platform_name or 'atom' in platform_name:
            return BlockchainPlatform.COSMOS
        elif 'polkadot' in platform_name or 'dot' in platform_name:
            return BlockchainPlatform.POLKADOT
        elif 'near' in platform_name:
            return BlockchainPlatform.NEAR
        elif 'fantom' in platform_name or 'ftm' in platform_name:
            return BlockchainPlatform.FANTOM
        elif 'tron' in platform_name or 'trx' in platform_name:
            return BlockchainPlatform.TRON
        elif 'cardano' in platform_name or 'ada' in platform_name:
            return BlockchainPlatform.CARDANO
        else:
            return BlockchainPlatform.OTHER