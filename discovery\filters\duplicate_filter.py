"""
重复项过滤器

该模块实现了基于URL和内容的重复项过滤器。
"""

import logging
import hashlib
from typing import Dict, List, Any, Set


class DuplicateFilter:
    """重复项过滤器，基于URL和内容过滤重复项目"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化重复项过滤器
        
        Args:
            config: 配置字典，包含重复项过滤器的配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 是否启用URL过滤
        self.url_filter_enabled = config.get('url_filter_enabled', True)
        
        # 是否启用内容过滤
        self.content_filter_enabled = config.get('content_filter_enabled', True)
        
        # 内容相似度阈值
        self.content_similarity_threshold = config.get('content_similarity_threshold', 0.8)
        
        # 已知的URL集合
        self._known_urls: Set[str] = set()
        
        # 已知的内容哈希集合
        self._known_content_hashes: Set[str] = set()
    
    def filter(self, projects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤项目
        
        Args:
            projects: 项目列表
            
        Returns:
            过滤后的项目列表
        """
        filtered_projects = []
        
        for project in projects:
            if self._is_unique_project(project):
                filtered_projects.append(project)
        
        self.logger.debug(f"重复项过滤: {len(projects)} -> {len(filtered_projects)}")
        return filtered_projects
    
    def _is_unique_project(self, project: Dict[str, Any]) -> bool:
        """
        检查项目是否唯一
        
        Args:
            project: 项目字典
            
        Returns:
            如果项目唯一则返回True，否则返回False
        """
        # 检查URL是否唯一
        if self.url_filter_enabled and 'url' in project:
            url = project['url']
            if url in self._known_urls:
                return False
            
            self._known_urls.add(url)
        
        # 检查内容是否唯一
        if self.content_filter_enabled:
            content_hash = self._calculate_content_hash(project)
            if content_hash in self._known_content_hashes:
                return False
            
            self._known_content_hashes.add(content_hash)
        
        return True
    
    def _calculate_content_hash(self, project: Dict[str, Any]) -> str:
        """
        计算项目内容的哈希值
        
        Args:
            project: 项目字典
            
        Returns:
            内容哈希值
        """
        # 提取项目内容
        content_parts = []
        
        # 添加项目名称
        if 'name' in project:
            content_parts.append(str(project['name']).lower())
        
        # 添加项目描述
        if 'description' in project:
            content_parts.append(str(project['description']).lower())
        
        # 添加项目类型
        if 'project_type' in project:
            content_parts.append(str(project['project_type']).lower())
        
        # 添加区块链平台
        if 'blockchain' in project:
            content_parts.append(str(project['blockchain']).lower())
        
        # 连接内容部分
        content = ' '.join(content_parts)
        
        # 计算哈希值
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def reset(self) -> None:
        """重置过滤器"""
        self._known_urls.clear()
        self._known_content_hashes.clear()
        self.logger.info("已重置重复项过滤器")
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            config: 新的配置字典
        """
        self.config = config
        
        # 更新配置参数
        self.url_filter_enabled = config.get('url_filter_enabled', self.url_filter_enabled)
        self.content_filter_enabled = config.get('content_filter_enabled', self.content_filter_enabled)
        self.content_similarity_threshold = config.get('content_similarity_threshold', self.content_similarity_threshold)
        
        self.logger.info("已更新重复项过滤器配置")