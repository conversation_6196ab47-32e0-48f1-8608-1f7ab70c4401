"""
修复profit_optimization_agent.py文件
"""

import os

def fix_file():
    file_path = 'profit_optimization/profit_optimization_agent.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复文件内容
    fixed_content = content.replace('agent.stop()"""', 'agent.stop()')
    
    with open(file_path + '.new', 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    # 先删除原文件，再重命名
    if os.path.exists(file_path):
        os.remove(file_path)
    os.rename(file_path + '.new', file_path)
    print('文件修复完成')

if __name__ == '__main__':
    fix_file()