"""Proxy Pool - 代理池管理器"""
import logging
from typing import List, Dict, Optional

class ProxyPool:
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.proxies: List[Dict] = []
        self.active_proxies: List[Dict] = []
    
    def add_proxy(self, proxy: Dict) -> bool:
        """添加代理到池中"""
        try:
            self.proxies.append(proxy)
            return True
        except Exception as e:
            self.logger.error(f"Failed to add proxy: {e}")
            return False
    
    def get_proxy(self) -> Optional[Dict]:
        """从池中获取可用代理"""
        if self.active_proxies:
            return self.active_proxies[0]
        return None
    
    def remove_proxy(self, proxy: Dict) -> bool:
        """从池中移除代理"""
        try:
            if proxy in self.proxies:
                self.proxies.remove(proxy)
            if proxy in self.active_proxies:
                self.active_proxies.remove(proxy)
            return True
        except Exception as e:
            self.logger.error(f"Failed to remove proxy: {e}")
            return False
    
    def get_pool_status(self) -> Dict:
        """获取代理池状态"""
        return {
            "total_proxies": len(self.proxies),
            "active_proxies": len(self.active_proxies),
            "available_proxies": len([p for p in self.proxies if p.get("status") == "available"])
        }
