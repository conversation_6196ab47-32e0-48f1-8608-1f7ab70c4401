"""
Value Estimator

价值估算器，负责估算代币和投资组合的价值。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime


class ValueEstimator:
    """
    价值估算器
    
    负责估算代币价值、投资组合总价值和收益计算。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化价值估算器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 价格数据源
        self.price_sources = config.get("price_sources", ["coingecko", "coinmarketcap"])
        
        # 价格缓存
        self.price_cache: Dict[str, Dict] = {}
        
        # 估算历史
        self.estimation_history: List[Dict] = []
        
        # 估算统计
        self.estimation_stats = {
            'total_estimations': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'last_estimation_time': None
        }
        
        # 默认价格（用于未知代币）
        self.default_prices = {
            "ETH": 2000.0,  # USD
            "BTC": 40000.0,
            "USDC": 1.0,
            "USDT": 1.0,
            "DAI": 1.0
        }
    
    async def initialize(self) -> bool:
        """
        初始化价值估算器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 预加载一些常用代币价格
            await self._preload_common_prices()
            
            self.logger.info("Value Estimator initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Value Estimator: {e}")
            return False
    
    async def estimate_token_value(self, token_address: str, amount: float, 
                                 token_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        估算代币价值
        
        Args:
            token_address: 代币合约地址
            amount: 代币数量
            token_info: 代币信息
            
        Returns:
            Dict[str, Any]: 价值估算结果
        """
        try:
            self.estimation_stats['total_estimations'] += 1
            
            # 获取代币价格
            price_data = await self._get_token_price(token_address, token_info)
            
            if not price_data:
                return {
                    "token_address": token_address,
                    "amount": amount,
                    "value_usd": 0.0,
                    "value_eth": 0.0,
                    "price_usd": 0.0,
                    "price_eth": 0.0,
                    "estimation_confidence": 0.0,
                    "estimated_at": datetime.utcnow().isoformat(),
                    "error": "Price data not available"
                }
            
            # 计算价值
            price_usd = price_data.get("price_usd", 0.0)
            price_eth = price_data.get("price_eth", 0.0)
            
            value_usd = amount * price_usd
            value_eth = amount * price_eth
            
            estimation_result = {
                "token_address": token_address,
                "amount": amount,
                "value_usd": value_usd,
                "value_eth": value_eth,
                "price_usd": price_usd,
                "price_eth": price_eth,
                "estimation_confidence": price_data.get("confidence", 0.5),
                "estimated_at": datetime.utcnow().isoformat(),
                "price_source": price_data.get("source", "unknown"),
                "token_info": token_info or {}
            }
            
            # 记录估算历史
            self.estimation_history.append(estimation_result.copy())
            
            # 清理旧历史
            if len(self.estimation_history) > 1000:
                self.estimation_history = self.estimation_history[-1000:]
            
            self.estimation_stats['last_estimation_time'] = datetime.utcnow().isoformat()
            
            return estimation_result
            
        except Exception as e:
            self.logger.error(f"Error estimating token value: {e}")
            return {
                "token_address": token_address,
                "amount": amount,
                "value_usd": 0.0,
                "value_eth": 0.0,
                "error": str(e)
            }

    async def estimate_portfolio_value(self, portfolio: Dict[str, Any]) -> Dict[str, Any]:
        """
        估算投资组合价值
        
        Args:
            portfolio: 投资组合数据
            
        Returns:
            Dict[str, Any]: 投资组合价值估算
        """
        try:
            total_value_usd = 0.0
            total_value_eth = 0.0
            token_valuations = []
            
            holdings = portfolio.get("holdings", {})
            
            for token_address, holding_data in holdings.items():
                amount = holding_data.get("amount", 0.0)
                token_info = holding_data.get("token_info", {})
                
                # 估算单个代币价值
                valuation = await self.estimate_token_value(token_address, amount, token_info)
                token_valuations.append(valuation)
                
                total_value_usd += valuation.get("value_usd", 0.0)
                total_value_eth += valuation.get("value_eth", 0.0)
            
            portfolio_valuation = {
                "portfolio_id": portfolio.get("id", "unknown"),
                "total_value_usd": total_value_usd,
                "total_value_eth": total_value_eth,
                "token_count": len(holdings),
                "token_valuations": token_valuations,
                "estimated_at": datetime.utcnow().isoformat(),
                "estimation_confidence": self._calculate_portfolio_confidence(token_valuations)
            }
            
            return portfolio_valuation
            
        except Exception as e:
            self.logger.error(f"Error estimating portfolio value: {e}")
            return {"total_value_usd": 0.0, "total_value_eth": 0.0, "error": str(e)}
    
    async def _get_token_price(self, token_address: str, token_info: Dict = None) -> Optional[Dict]:
        """获取代币价格"""
        try:
            # 检查缓存
            if token_address in self.price_cache:
                cache_data = self.price_cache[token_address]
                # 检查缓存是否过期（5分钟）
                cache_time = datetime.fromisoformat(cache_data["cached_at"])
                if (datetime.utcnow() - cache_time).seconds < 300:
                    self.estimation_stats['cache_hits'] += 1
                    return cache_data
            
            self.estimation_stats['cache_misses'] += 1
            
            # 获取新价格数据
            price_data = await self._fetch_price_from_sources(token_address, token_info)
            
            if price_data:
                # 更新缓存
                price_data["cached_at"] = datetime.utcnow().isoformat()
                self.price_cache[token_address] = price_data
            
            return price_data
            
        except Exception as e:
            self.logger.error(f"Error getting token price: {e}")
            return None
    
    async def _fetch_price_from_sources(self, token_address: str, token_info: Dict = None) -> Optional[Dict]:
        """从数据源获取价格"""
        try:
            # 模拟从价格API获取数据
            import random
            
            # 检查是否是已知代币
            symbol = token_info.get("symbol", "UNKNOWN") if token_info else "UNKNOWN"
            
            if symbol in self.default_prices:
                price_usd = self.default_prices[symbol]
                confidence = 0.95
                source = "default"
            else:
                # 模拟未知代币价格
                price_usd = round(random.uniform(0.001, 100.0), 6)
                confidence = 0.3  # 低置信度
                source = "estimated"
            
            # 计算ETH价格
            eth_price = self.default_prices["ETH"]
            price_eth = price_usd / eth_price
            
            return {
                "price_usd": price_usd,
                "price_eth": price_eth,
                "confidence": confidence,
                "source": source,
                "market_cap": random.randint(1000000, 1000000000),
                "volume_24h": random.randint(100000, 10000000),
                "change_24h": round(random.uniform(-20.0, 20.0), 2)
            }
            
        except Exception as e:
            self.logger.error(f"Error fetching price from sources: {e}")
            return None
    
    async def _preload_common_prices(self):
        """预加载常用代币价格"""
        try:
            common_tokens = [
                ("ETH", {"symbol": "ETH"}),
                ("******************************************", {"symbol": "USDC"}),
                ("******************************************", {"symbol": "USDT"})
            ]
            
            for token_address, token_info in common_tokens:
                await self._get_token_price(token_address, token_info)
            
            self.logger.info("Preloaded common token prices")
            
        except Exception as e:
            self.logger.error(f"Error preloading common prices: {e}")
    
    def _calculate_portfolio_confidence(self, token_valuations: List[Dict]) -> float:
        """计算投资组合估算置信度"""
        try:
            if not token_valuations:
                return 0.0
            
            total_value = sum(val.get("value_usd", 0.0) for val in token_valuations)
            if total_value == 0:
                return 0.0
            
            weighted_confidence = 0.0
            for valuation in token_valuations:
                value = valuation.get("value_usd", 0.0)
                confidence = valuation.get("estimation_confidence", 0.0)
                weight = value / total_value
                weighted_confidence += confidence * weight
            
            return round(weighted_confidence, 3)
            
        except Exception as e:
            self.logger.error(f"Error calculating portfolio confidence: {e}")
            return 0.0
    
    async def get_price_history(self, token_address: str, days: int = 7) -> List[Dict]:
        """
        获取代币价格历史
        
        Args:
            token_address: 代币合约地址
            days: 历史天数
            
        Returns:
            List[Dict]: 价格历史记录
        """
        try:
            # 从估算历史中筛选
            token_history = [
                record for record in self.estimation_history
                if record.get("token_address") == token_address
            ]
            
            # 按时间排序
            token_history.sort(key=lambda x: x.get("estimated_at", ""))
            
            return token_history[-days*24:] if days > 0 else token_history
            
        except Exception as e:
            self.logger.error(f"Error getting price history: {e}")
            return []
    
    async def calculate_profit_loss(self, current_value: float, initial_value: float) -> Dict[str, Any]:
        """
        计算盈亏
        
        Args:
            current_value: 当前价值
            initial_value: 初始价值
            
        Returns:
            Dict[str, Any]: 盈亏计算结果
        """
        try:
            profit_loss = current_value - initial_value
            profit_loss_percent = (profit_loss / initial_value * 100) if initial_value > 0 else 0.0
            
            return {
                "current_value": current_value,
                "initial_value": initial_value,
                "profit_loss": profit_loss,
                "profit_loss_percent": round(profit_loss_percent, 2),
                "is_profit": profit_loss > 0,
                "calculated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating profit/loss: {e}")
            return {"profit_loss": 0.0, "profit_loss_percent": 0.0}
    
    async def get_estimation_statistics(self) -> Dict[str, Any]:
        """
        获取估算统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            cache_hit_rate = 0.0
            if self.estimation_stats['total_estimations'] > 0:
                cache_hit_rate = (self.estimation_stats['cache_hits'] / 
                                self.estimation_stats['total_estimations'])
            
            return {
                'estimation_stats': self.estimation_stats,
                'cache_hit_rate': round(cache_hit_rate, 3),
                'cached_prices': len(self.price_cache),
                'estimation_history_count': len(self.estimation_history),
                'supported_sources': self.price_sources
            }
            
        except Exception as e:
            self.logger.error(f"Error getting estimation statistics: {e}")
            return {}
    
    async def clear_cache(self) -> bool:
        """
        清理价格缓存
        
        Returns:
            bool: 清理是否成功
        """
        try:
            self.price_cache.clear()
            self.logger.info("Price cache cleared")
            return True
        except Exception as e:
            self.logger.error(f"Error clearing cache: {e}")
            return False
