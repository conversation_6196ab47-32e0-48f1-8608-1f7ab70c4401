"""
Key Manager

This module is responsible for securely managing cryptographic keys
for cryptocurrency wallets.
"""

import os
import json
import base64
import logging
from typing import Dict, Any, Optional, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger("KeyManager")

class KeyManager:
    """
    Manages cryptographic keys for cryptocurrency wallets.
    
    Features:
    - Secure key encryption and decryption
    - Key rotation
    - Access control
    """
    
    def __init__(self, storage_path: str = "data/wallets/keys", master_password: Optional[str] = None):
        """
        Initialize the key manager.
        
        Args:
            storage_path: Path to store encrypted keys
            master_password: Master password for encryption (if None, will prompt or generate)
        """
        self.storage_path = storage_path
        os.makedirs(storage_path, exist_ok=True)
        
        # In a real implementation, you would have a secure way to handle the master password
        # This is a simplified implementation for demonstration
        self.master_password = master_password or "default_secure_password"
        self.encryption_key = self._derive_key(self.master_password)
        self.cipher = Fernet(self.encryption_key)
        
        logger.info("Key manager initialized")
    
    def _derive_key(self, password: str) -> bytes:
        """
        Derive an encryption key from the password.
        
        Args:
            password: Password to derive key from
            
        Returns:
            bytes: Derived key
        """
        # In production, use a secure random salt and store it
        salt = b'airhunter_salt'  # In production, this should be random and stored
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    def encrypt_key(self, private_key: str) -> str:
        """
        Encrypt a private key.
        
        Args:
            private_key: Private key to encrypt
            
        Returns:
            str: Encrypted key
        """
        encrypted = self.cipher.encrypt(private_key.encode())
        return encrypted.decode()
    
    def decrypt_key(self, encrypted_key: str) -> str:
        """
        Decrypt an encrypted private key.
        
        Args:
            encrypted_key: Encrypted key to decrypt
            
        Returns:
            str: Decrypted private key
        """
        decrypted = self.cipher.decrypt(encrypted_key.encode())
        return decrypted.decode()
    
    def store_key(self, wallet_id: str, private_key: str, metadata: Dict[str, Any]) -> bool:
        """
        Encrypt and store a private key.
        
        Args:
            wallet_id: Wallet identifier
            private_key: Private key to store
            metadata: Additional metadata
            
        Returns:
            bool: True if stored successfully, False otherwise
        """
        try:
            encrypted_key = self.encrypt_key(private_key)
            
            key_data = {
                "wallet_id": wallet_id,
                "encrypted_key": encrypted_key,
                "metadata": metadata,
                "version": 1
            }
            
            file_path = os.path.join(self.storage_path, f"{wallet_id}.key")
            with open(file_path, 'w') as f:
                json.dump(key_data, f)
            
            logger.info(f"Stored encrypted key for wallet {wallet_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to store key for wallet {wallet_id}: {e}")
            return False
    
    def retrieve_key(self, wallet_id: str) -> Optional[str]:
        """
        Retrieve and decrypt a private key.
        
        Args:
            wallet_id: Wallet identifier
            
        Returns:
            str: Decrypted private key or None if failed
        """
        try:
            file_path = os.path.join(self.storage_path, f"{wallet_id}.key")
            
            if not os.path.exists(file_path):
                logger.error(f"No key file found for wallet {wallet_id}")
                return None
            
            with open(file_path, 'r') as f:
                key_data = json.load(f)
            
            encrypted_key = key_data["encrypted_key"]
            decrypted_key = self.decrypt_key(encrypted_key)
            
            logger.info(f"Retrieved key for wallet {wallet_id}")
            return decrypted_key
        except Exception as e:
            logger.error(f"Failed to retrieve key for wallet {wallet_id}: {e}")
            return None
    
    def rotate_key(self, wallet_id: str, new_password: Optional[str] = None) -> bool:
        """
        Rotate the encryption key for a wallet.
        
        Args:
            wallet_id: Wallet identifier
            new_password: Optional new password for encryption
            
        Returns:
            bool: True if rotated successfully, False otherwise
        """
        try:
            # Retrieve the current key
            current_key = self.retrieve_key(wallet_id)
            if not current_key:
                return False
            
            # If a new password is provided, update the encryption key
            if new_password:
                old_cipher = self.cipher
                self.master_password = new_password
                self.encryption_key = self._derive_key(new_password)
                self.cipher = Fernet(self.encryption_key)
            
            # Re-encrypt with the current or new key
            file_path = os.path.join(self.storage_path, f"{wallet_id}.key")
            with open(file_path, 'r') as f:
                key_data = json.load(f)
            
            # Update the encrypted key and version
            key_data["encrypted_key"] = self.encrypt_key(current_key)
            key_data["version"] += 1
            
            with open(file_path, 'w') as f:
                json.dump(key_data, f)
            
            logger.info(f"Rotated encryption key for wallet {wallet_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to rotate key for wallet {wallet_id}: {e}")
            return False
    
    def delete_key(self, wallet_id: str) -> bool:
        """
        Delete a stored key.
        
        Args:
            wallet_id: Wallet identifier
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        try:
            file_path = os.path.join(self.storage_path, f"{wallet_id}.key")
            
            if not os.path.exists(file_path):
                logger.warning(f"No key file found for wallet {wallet_id}")
                return False
            
            os.remove(file_path)
            logger.info(f"Deleted key for wallet {wallet_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete key for wallet {wallet_id}: {e}")
            return False
    
    def list_keys(self) -> List[str]:
        """
        List all stored wallet IDs.
        
        Returns:
            List of wallet IDs
        """
        try:
            wallet_ids = []
            for filename in os.listdir(self.storage_path):
                if filename.endswith('.key'):
                    wallet_id = filename.replace('.key', '')
                    wallet_ids.append(wallet_id)
            return wallet_ids
        except Exception as e:
            logger.error(f"Failed to list keys: {e}")
            return []


if __name__ == "__main__":
    # Simple test
    logging.basicConfig(level=logging.INFO)
    key_manager = KeyManager()
    
    # Test key encryption and storage
    wallet_id = "test-wallet-123"
    private_key = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
    metadata = {"chain": "ethereum", "created_at": "2023-01-01"}
    
    key_manager.store_key(wallet_id, private_key, metadata)
    retrieved_key = key_manager.retrieve_key(wallet_id)
    
    print(f"Original key: {private_key}")
    print(f"Retrieved key: {retrieved_key}")
    print(f"Keys match: {private_key == retrieved_key}")
    print(f"All wallet IDs: {key_manager.list_keys()}")
    
    # Clean up test data
    key_manager.delete_key(wallet_id)