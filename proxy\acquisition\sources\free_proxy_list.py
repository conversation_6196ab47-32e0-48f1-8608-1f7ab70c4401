"""
Free Proxy List 代理源

该模块实现了从 Free Proxy List 网站获取代理的功能。
"""

import logging
import requests
import time
import random
from typing import Dict, List, Any
from bs4 import BeautifulSoup

from proxy.models.proxy import Proxy
from proxy.acquisition.sources.base_source import BaseProxySource


class FreeProxyListSource(BaseProxySource):
    """Free Proxy List 代理源"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 Free Proxy List 代理源
        
        Args:
            config: 配置字典，包含代理源的配置信息
        """
        super().__init__(config)
        self.url = config.get('url', 'https://free-proxy-list.net/')
        self.timeout = config.get('timeout', 10)
        self.user_agents = config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
    
    def get_proxies(self, count: int) -> List[Proxy]:
        """
        从 Free Proxy List 获取代理
        
        Args:
            count: 要获取的代理数量
            
        Returns:
            代理列表
        """
        self.logger.info(f"从 Free Proxy List 获取 {count} 个代理")
        
        try:
            # 随机选择一个 User-Agent
            headers = {
                'User-Agent': random.choice(self.user_agents)
            }
            
            # 发送请求
            response = requests.get(self.url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找代理表格
            table = soup.find('table', {'id': 'proxylisttable'})
            if not table:
                self.logger.warning("未找到代理表格")
                return []
            
            # 解析表格
            proxies = []
            rows = table.find('tbody').find_all('tr')
            
            for row in rows:
                try:
                    cols = row.find_all('td')
                    if len(cols) < 8:
                        continue
                    
                    ip = cols[0].text.strip()
                    port = int(cols[1].text.strip())
                    anonymity = cols[4].text.strip().lower()
                    https = cols[6].text.strip().lower() == 'yes'
                    country = cols[2].text.strip()
                    
                    # 创建代理对象
                    protocol = 'https' if https else 'http'
                    
                    # 映射匿名级别
                    if anonymity == 'elite proxy':
                        anonymity_level = 'elite'
                    elif anonymity == 'anonymous':
                        anonymity_level = 'anonymous'
                    else:
                        anonymity_level = 'transparent'
                    
                    proxy = Proxy(
                        host=ip,
                        port=port,
                        protocol=protocol,
                        country=country if country else None,
                        anonymity=anonymity_level,
                        source='free_proxy_list'
                    )
                    
                    proxies.append(proxy)
                    
                    # 如果已经获取到足够的代理，就停止
                    if len(proxies) >= count:
                        break
                
                except Exception as e:
                    self.logger.error(f"解析代理行时出错: {str(e)}")
            
            self.logger.info(f"从 Free Proxy List 获取到 {len(proxies)} 个代理")
            return proxies
        
        except Exception as e:
            self.logger.error(f"从 Free Proxy List 获取代理时出错: {str(e)}")
            return []