"""
Permission Manager

Manages user permissions and access control for the AirHunter system.
"""

import logging
from typing import Dict, Any, Set, List
from enum import Enum


class Permission(Enum):
    """System permissions."""
    READ_PROJECTS = "read_projects"
    WRITE_PROJECTS = "write_projects"
    DELETE_PROJECTS = "delete_projects"
    READ_WALLETS = "read_wallets"
    WRITE_WALLETS = "write_wallets"
    DELETE_WALLETS = "delete_wallets"
    READ_TASKS = "read_tasks"
    WRITE_TASKS = "write_tasks"
    DELETE_TASKS = "delete_tasks"
    ADMIN = "admin"
    SYSTEM = "system"


class Role(Enum):
    """User roles."""
    ADMIN = "admin"
    USER = "user"
    VIEWER = "viewer"
    SYSTEM = "system"


class PermissionManager:
    """Permission manager for access control."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Role permissions mapping
        self._role_permissions = {
            Role.ADMIN: {
                Permission.READ_PROJECTS, Permission.WRITE_PROJECTS, Permission.DELETE_PROJECTS,
                Permission.READ_WALLETS, Permission.WRITE_WALLETS, Permission.DELETE_WALLETS,
                Permission.READ_TASKS, Permission.WRITE_TASKS, Permission.DELETE_TASKS,
                Permission.ADMIN
            },
            Role.USER: {
                Permission.READ_PROJECTS, Permission.WRITE_PROJECTS,
                Permission.READ_WALLETS, Permission.WRITE_WALLETS,
                Permission.READ_TASKS, Permission.WRITE_TASKS
            },
            Role.VIEWER: {
                Permission.READ_PROJECTS, Permission.READ_WALLETS, Permission.READ_TASKS
            },
            Role.SYSTEM: {
                Permission.SYSTEM, Permission.READ_PROJECTS, Permission.WRITE_PROJECTS,
                Permission.READ_WALLETS, Permission.WRITE_WALLETS,
                Permission.READ_TASKS, Permission.WRITE_TASKS
            }
        }

        # User roles
        self._user_roles = {}

        # Statistics
        self._stats = {
            'permission_checks': 0,
            'access_granted': 0,
            'access_denied': 0
        }

    def assign_role(self, username: str, role: Role) -> bool:
        """Assign role to user."""
        try:
            self._user_roles[username] = role
            self.logger.info(f"Assigned role {role.value} to user {username}")
            return True
        except Exception as e:
            self.logger.error(f"Error assigning role: {e}")
            return False

    def check_permission(self, username: str, permission: Permission) -> bool:
        """Check if user has permission."""
        try:
            self._stats['permission_checks'] += 1

            user_role = self._user_roles.get(username)
            if not user_role:
                self._stats['access_denied'] += 1
                return False

            role_permissions = self._role_permissions.get(user_role, set())

            if permission in role_permissions or Permission.ADMIN in role_permissions:
                self._stats['access_granted'] += 1
                return True
            else:
                self._stats['access_denied'] += 1
                return False

        except Exception as e:
            self.logger.error(f"Permission check error: {e}")
            self._stats['access_denied'] += 1
            return False

    def get_user_permissions(self, username: str) -> Set[Permission]:
        """Get all permissions for user."""
        user_role = self._user_roles.get(username)
        if user_role:
            return self._role_permissions.get(user_role, set())
        return set()

    def get_statistics(self) -> Dict[str, Any]:
        """Get permission statistics."""
        return self._stats.copy()
